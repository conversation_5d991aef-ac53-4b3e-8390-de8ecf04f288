{"extends": "./paths.json", "compilerOptions": {"target": "esnext", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "noEmit": true, "jsx": "react-jsx", "noImplicitAny": false, "plugins": [{"name": "typescript-styled-plugin"}], "isolatedModules": true, "lib": ["dom", "dom.iterable", "esnext"], "types": ["cypress", "@4tw/cypress-drag-drop"], "noFallthroughCasesInSwitch": true}, "include": ["src", "typings"]}