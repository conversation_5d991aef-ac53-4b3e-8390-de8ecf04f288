# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Typescript v1 declaration files
#typings/

# Optional npm cache directory
.npm
.yarn

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# IDEs and editors
.idea
.DS_Store
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.history

# IDE - VSCode
.vscode

# misc
.sass-cache
connect.lock
libpeerconnection.log
npm-debug.log
testem.log
#typings

# e2e
e2e/*.js
e2e/*.map

package-lock.json
Thumbs.db
tmp
dist
build
out-tsc
