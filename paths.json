{"compilerOptions": {"baseUrl": ".", "paths": {"remax/one": ["src/components/Remax/One/index.tsx"], "remax/wechat": ["src/components/Remax/wechat/index.tsx"], "@src/*": ["src/*"], "@apiHooks": ["src/configs/apis"], "@utils/*": ["src/utils/*"], "@templates/*": ["src/templates/*"], "@configs/*": ["src/configs/*"], "@configs": ["src/configs/index.tsx"], "@components/*": ["src/components/*"], "@layouts/*": ["src/layouts/*"], "@pages/*": ["src/pages/*"], "@stores/*": ["src/stores/*"], "@themed-components": ["src/utils/themed-components"]}}}