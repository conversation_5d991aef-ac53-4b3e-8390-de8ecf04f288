stages:
  - build
  - deploy
  - deploy42

variables:
  # 如果需要新增医院，这里添加医院编号会以后ci会自动打包到测试环境、开发环境、正式环境对应的目录下
  HIS_IDS: 10001 2214 2218 2219 2300 40001 40002 40003 40005 40007 40008 40009 40011 40019 40020 40022 40024 40026 40028 40030 40060 40061 40063 40064 40065 40066 40067 40068 40069 40070 40074 40075 41122 6601 6602 7701 8800 8900 9901 40012 40013 40015 40006 40076 40077 40060 40079 40025 40052 40027 40007 40082
  # 这是预览环境需要打包的地址
  PREVIEW_HIS_IDS: 2218 9000 8900 40009
  # 如果在开发环境中需要指定后端的api版本，请修改这个变量
  # API_VERSION: 4.0.13
  # 部署的测试环境和开发环境的nginx根目录
  TEST_DEPLOY_ROOT_PATH: /opt/web/app/oper/static/websites/kaiqiao
  # 部署的正式环境的nginx根目录
  PROD_DEPLOY_ROOT_PATH: /opt/web/web-projects/kaiqiao
  # 部署的等保环境的nginx根目录
  PROTECTION_DEPLOY_ROOT_PATH: /data/nginx/html/kaiqiao
  NODE_VERSION: 16
  BUILD_IMAGE: 'registry.cn-hangzhou.aliyuncs.com/keys-images/node:16'
  DIST_PATH: build #打包结果的路径
  GIT_SUBMODULE_STRATEGY: recursive
  # 取消git clean删除node_modules的操作  https://docs.gitlab.com/ee/ci/large_repositories/#store-custom-clone-options-in-configtoml
  GIT_CLEAN_FLAGS: -ffdx -e node_modules/
  # 本地化医院配置(YAML格式)
  LOCALIZATION_CONFIG: |
    # "2214":
    #   test:
    #     paths:
    #       - "/opt/web/app/oper/static/websites/kaiqiao/admin-2214/dev/"
    #     runners:
    #       - ih-front-test-dx-15
    #   prod:
    #     paths:
    #       - "/opt/web/newchcmu/admin-icu-2214/"
    #     runners:
    #       - chcmu-icuonline-az1-18
    #       - chcmu-icuonline-az1-19
    # '40066':
    #   test: # 测试环境配置
    #     path: /opt/web/app/oper/static/websites/chcmu/admin-doctor-icu/
    #     runner: local-test2
    #   prod: # 生产环境配置(双节点)
    #     paths:
    #       - /data/nginx/html/kaiqiao/admin-doctor-40066/
    #       - /data/nginx/html/kaiqiao2/admin-doctor-40066/
    #     runners:
    #       - local-prod2-node1
    #       - local-prod2-node2
before_script:
  # 这段代码的意思是： 1、创建打包目录文件夹；2、如果之前有文件部署，这里删除对应的文件;3、粘贴打包产物到打包目录
  - copyAndDeploy(){  mkdir -p $1/;rm -rf $1/*; cp -R $2/* $1/; echo "打包路径$1成功！"; };

include:
  - local: 'env/yibing/.gitlab-ci.yml'
  - local: 'env/icu/.gitlab-ci.yml'

# 定义 fe-shell-build 标签作业的模板
.fe_build_239_template:
  before_script:
    - fnm use $NODE_VERSION
  tags:
    - fe-shell-build

build:
  stage: build
  image: $BUILD_IMAGE
  interruptible: true
  # extends: .fe_build_239_template
  variables:
    CI: 'false'
    BUILD_CMD: yarn build #打包的命令
  script:
    - yarn --ignore-scripts
    - echo $BUILD_CMD
    - $BUILD_CMD
  artifacts:
    paths:
      - $DIST_PATH/
    expire_in: 24 hours
  tags:
    # - fe-docker-build
    - kq-front-test

# 开发环境和测试环境
deploy_dev_test:
  stage: deploy
  script:
    - |
      for HIS_ID in $HIS_IDS
      do
      if [ $API_VERSION ];then
        DEV_DEPLOY_PATH=$TEST_DEPLOY_ROOT_PATH/admin-dev-${HIS_ID}/${API_VERSION}/
        TEST_DEPLOY_PATH=$TEST_DEPLOY_ROOT_PATH/admin-${HIS_ID}/${API_VERSION}/
      else
        DEV_DEPLOY_PATH=$TEST_DEPLOY_ROOT_PATH/admin-dev-${HIS_ID}/dev/
        TEST_DEPLOY_PATH=$TEST_DEPLOY_ROOT_PATH/admin-${HIS_ID}/dev/
      fi
      copyAndDeploy $DEV_DEPLOY_PATH $DIST_PATH
      copyAndDeploy $TEST_DEPLOY_PATH $DIST_PATH
      done
  dependencies:
    - build
  except:
    - /^v.*/
  tags:
    - ih-front-test-dx-15

# # 国产化测试环境
# gcdeploy_dev_test:
#   stage: deploy
#   script:
#     - |
#       for HIS_ID in $HIS_IDS
#       do
#       TEST_DEPLOY_PATH=/data/nginx/html/kqgc/admin-gc-${HIS_ID}/dev/
#       copyAndDeploy $TEST_DEPLOY_PATH $DIST_PATH
#       done
#   dependencies:
#     - build
#   except:
#     - /^v.*/
#   tags:
#     - paycenter_localization119

# 华为鲲鹏认证环境
deploy_kunpeng:
  stage: deploy
  script:
    - |
      for HIS_ID in $HIS_IDS
      do
      DEPLOY_PATH=$TEST_DEPLOY_ROOT_PATH/admin-kp-${HIS_ID}/dev/
      copyAndDeploy $DEPLOY_PATH $DIST_PATH
      done
  dependencies:
    - build
  when: manual
  except:
    - /^v.*/
  tags:
    - tihs-m-front-kp #runner名称

# 预览环境
deploy_preview:
  stage: deploy
  script:
    - |
      for HIS_ID in $PREVIEW_HIS_IDS
      do
      if [ $API_VERSION ];then
        DEPLOY_PATH=$TEST_DEPLOY_ROOT_PATH/admin-preview-${HIS_ID}/${API_VERSION}/
      else
         DEPLOY_PATH=$TEST_DEPLOY_ROOT_PATH/admin-preview-${HIS_ID}/
      fi
      copyAndDeploy $DEPLOY_PATH $DIST_PATH
      done
  dependencies:
    - build
  except:
    - /^v.*/
  when: manual
  tags:
    - ih-front-test-dx-15

# 等保演示环境
.protection_preview_template:
  stage: deploy
  script:
    - |
      for HIS_ID in $PREVIEW_HIS_IDS
      do
      DEPLOY_PATH=$PROTECTION_DEPLOY_ROOT_PATH/admin-preview-${HIS_ID}/
      copyAndDeploy $DEPLOY_PATH $DIST_PATH
      done
  dependencies:
    - build
  except:
    - /^v.*/
  when: manual

# 等保演示环境
deploy_protection_preview-9:
  extends: .protection_preview_template
  tags:
    - dxonlinenginx9

# 等保演示环境
deploy_protection_preview-19:
  extends: .protection_preview_template
  tags:
    - dxonlinenginx19

# 正式环境包含等保环境
.deploy_prod_template:
  stage: deploy
  variables:
    # 有两个正式环境（等保环境、正式环境），需要后面覆盖这个路径
    PROD_PATH: $PROTECTION_DEPLOY_ROOT_PATH
  script:
    - |
      for HIS_ID in $HIS_IDS
      do
      DEPLOY_PATH=$PROD_PATH/admin-${HIS_ID}/
      copyAndDeploy $DEPLOY_PATH $DIST_PATH
      done
  dependencies:
    - build
  when: manual
  allow_failure: false
  only:
    - /^v.*/
    - master
  tags:
    - dxonlinenginx9 #runner名称

# 等保正式环境
deploy_protection-9:
  extends: .deploy_prod_template

# 等保多节点正式环境
deploy_protection-19:
  extends: .deploy_prod_template
  stage: deploy42
  when: on_success
  tags:
    - dxonlinenginx19

# 本地化部署模板
.deploy_localization_template:
  dependencies:
    - build
  script:
    - |
      # 解析并部署
      echo "$LOCALIZATION_CONFIG" | while IFS= read -r line; do
        # 检测医院ID行（以引号开头的行）
        if [[ $line =~ ^[[:space:]]*\"([0-9]+)\" ]]; then
         current_his_id="${BASH_REMATCH[1]}"
          in_target_section=false
          paths_section=false

          # 继续读取配置
          while IFS= read -r subline; do
            # 如果遇到下一个医院ID，则跳出
            [[ $subline =~ ^[[:space:]]*\"([0-9]+)\" ]] && break

            # 检查是否在目标环境节点
            if [[ $subline =~ ^[[:space:]]*${ENV_TYPE}: ]]; then
              in_target_section=true
              continue
            fi

            # 检查是否在paths节点
            if [[ $in_target_section == true && $subline =~ ^[[:space:]]*paths: ]]; then
              paths_section=true
              continue
            fi

            # 处理路径
            if [[ $paths_section == true && $subline =~ ^[[:space:]]*-[[:space:]]*(.+)$ ]]; then
              deploy_path="${BASH_REMATCH[1]}"
              deploy_path="${deploy_path%\"}"  # 移除结尾的引号
              deploy_path="${deploy_path#\"}"  # 移除开头的引号
              echo "正在部署医院 ${current_his_id} 到 ${ENV_TYPE} 环境: ${deploy_path}"
              copyAndDeploy "$deploy_path" "$DIST_PATH"
            fi
          done
        fi
      done

# 测试环境部署
deploy_localization_test:
  extends: .deploy_localization_template
  stage: deploy
  variables:
    ENV_TYPE: 'test'
  parallel:
    matrix:
      - RUNNER: ['ih-front-test-dx-15']
  tags:
    - $RUNNER
  except:
    - /^v.*/

# 正式环境部署模板
deploy_localization_prod:
  extends: .deploy_localization_template
  stage: deploy
  variables:
    ENV_TYPE: 'prod'
  parallel:
    matrix:
      - RUNNER: ['chcmu-icuonline-az1-18', 'chcmu-icuonline-az1-19']
  dependencies:
    - build
  # only:
  #   - /^v.*/
  when: manual
  tags:
    - $RUNNER

# ===============================院内部署模板====================================

# 院内部署打包模版
.local_build_template:
  stage: build
  image: $BUILD_IMAGE
  interruptible: true
  variables:
    CI: 'false'
    BUILD_CMD: yarn build #打包的命令(需要被覆盖)
  script:
    - yarn --ignore-scripts
    - echo $BUILD_CMD
    - $BUILD_CMD
  artifacts:
    paths:
      - $DIST_PATH/
    expire_in: 24 hours
  when: manual
  tags:
    - kq-front-test

# 院内部署模版(开发)
.local_deploy_dev_template:
  stage: deploy
  script:
    - copyAndDeploy $TEST_DEPLOY_PATH $DIST_PATH
  when: manual

# 院内部署模版(生产)
.local_deploy_prod_template:
  stage: deploy
  script:
    - copyAndDeploy $PROD_DEPLOY_PATH $DIST_PATH
  when: manual
# 院内部署生产模版
# .local_prod_template:
#   stage: deploy
#   script:
#     - copyAndDeploy $PROD_DEPLOY_PATH $DIST_PATH
#   dependencies:
#     - build
#   only:
#     - /^v.*/
#     - feat-40067-localDeploy # 后续删除
#   when: manual

# # 宜宾儿童开发环境
# deploy_yiping_dev_126:
#   extends: .local_dev_template
#   variables:
#     TEST_DEPLOY_PATH: /opt/web/web-projects/pc/admin-dev-40076/
#   tags:
#     - yibing-dev-126

# # 宜宾儿童生产环境
# deploy_yiping_prod_126:
#   extends: .local_prod_template
#   variables:
#     PROD_DEPLOY_PATH: /opt/web/web-projects/pc/admin-40076/
#   tags:
#     - yibing-prod-126

# deploy_yiping_prod_127:
#   extends: .local_prod_template
#   variables:
#     PROD_DEPLOY_PATH: /opt/web/web-projects/pc/admin-40076/
#   tags:
#     - yibing-prod-127
