{"name": "parsec-app", "version": "1.0.77", "private": true, "author": "", "description": "凯桥互联网医院后台管理系统", "repository": {"type": "git", "url": ""}, "dependencies": {"@ant-design/icons": "^4.0.2", "@ant-design/pro-components": "^1.1.15", "@kqinfo/ui": "1.14.17", "@types/crypto-js": "^4.0.1", "@types/echarts": "^4.6.0", "@types/js-cookie": "^2.2.7", "@types/jspdf": "^2.0.0", "@types/react-copy-to-clipboard": "^4.3.0", "@types/react-grid-layout": "^1.1.0", "@types/uuid": "^8.3.0", "@umijs/hooks": "^1.8.0", "antd": "^4.22.4", "array-move": "^3.0.1", "axios": "^0.19.0", "babel-loader": "^8.2.3", "better-react-qmap": "^1.0.1", "bignumber.js": "^9.0.1", "braft-editor": "^2.3.9", "china-division": "^2.3.1", "classnames": "^2.2.6", "clipboard": "^2.0.6", "create-api-hooks": "^0.0.39", "crypto-js": "^4.0.0", "currency.js": "^2.0.4", "echarts": "^4.7.0", "file-saver": "^2.0.2", "idcard": "^4.1.0", "js-base64": "^3.6.1", "js-cookie": "^3.0.1", "jsbarcode": "^3.11.5", "jspdf": "^2.4.0", "moment": "^2.24.0", "mutative": "^1.1.0", "use-mutative": "^1.2.0", "parsec-admin": "^2.4.34-alpha.2", "parsec-hooks": "^1.0.18", "parsec-libs": "^0.0.105", "postcss-unit-transforms": "^0.0.0-alpha.4", "qqmap": "^1.0.1", "qrcode": "^1.4.4", "qs": "^6.7.0", "rc-util": "^5.8.1", "react": "^17.0.2", "react-copy-to-clipboard": "^5.0.2", "react-dom": "^17.0.2", "react-grid-layout": "^1.1.0", "react-router": "^5.2.1", "react-router-dom": "^5.3.0", "react-sortable-hoc": "^2.0.0", "react-sortablejs": "^6.1.4", "react-to-copy": "^0.1.1", "react-use": "^14.1.1", "resize-observer-polyfill": "^1.5.1", "sortablejs": "^1.15.0", "styled-components": "^5.3.3", "tailwindcss": "^3.4.6", "unstated-next": "^1.1.0", "uuid": "^8.3.0", "wangeditor": "^3.1.1", "yapi-to-typescript": "^3.37.1"}, "scripts": {"start": "env-cmd -f .env react-app-rewired start", "start:inner": "env-cmd -f ./env/yibing/.env.development react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-app-rewired eject", "lint": "eslint --fix --ext .ts,.tsx ./src", "start:ci": "serve build -l 3000 -s", "cypress:open": "cypress open", "e2e": "cypress run --record --key 32335b22-2ab8-4578-bf3d-1708db739112", "release": "npm version patch", "postversion": "git push --follow-tags", "prepare": "husky install", "eslint": "eslint --fix --ext .tsx", "build:yibing:prod": "env-cmd -f ./env/yibing/.env.production react-app-rewired build", "build:yibing:dev": "env-cmd -f ./env/yibing/.env.development react-app-rewired build", "build:icu:prod": "env-cmd -f ./env/icu/.env.production react-app-rewired build", "analyze": "REACT_APP_ANALYZE=true react-app-rewired build"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "lint-staged": {"*": ["pretty-quick --staged"], "*.{ts,tsx,js}": ["eslint"]}, "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "devDependencies": {"@4tw/cypress-drag-drop": "^2.1.0", "@types/classnames": "^2.2.7", "@types/file-saver": "^2.0.1", "@types/jest": "24.0.13", "@types/node": "12.0.2", "@types/qs": "^6.5.3", "@types/react": "^17.0.34", "@types/react-dom": "^17.0.9", "@types/react-router": "^5.1.16", "@types/react-router-dom": "^5.1.8", "@types/styled-components": "^5.1.23", "@typescript-eslint/parser": "^4.28.5", "autoprefixer": "^9", "babel-plugin-import": "^1.11.0", "core-js": "^3.0.1", "crypto-browserify": "^3.12.0", "customize-cra": "^1.0.0", "cypress": "^9.5.1", "cypress-network-idle": "^1.3.3", "env-cmd": "^10.1.0", "eslint-config-prettier": "^8.3.0", "eslint-config-react-app": "^6.0.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-prettier": "^3.4.0", "hard-source-webpack-plugin": "^0.13.1", "http-proxy-middleware": "^0.19.1", "husky": "^7.0.0", "less": "^3.9.0", "less-loader": "^5.0.0", "lint-staged": "^8.1.0", "postcss": "^8", "postcss-normalize": "^10.0.1", "prettier": "^1.17.1", "pretty-quick": "^1.10.0", "react-app-rewire-styled-components": "^3.0.2", "react-app-rewired": "^2.1.11", "react-scripts": "^5.0.0", "regenerator-runtime": "^0.13.2", "serve": "^13.0.2", "stream-browserify": "^3.0.0", "tslib": "^2.3.0", "typescript": "^4.3.5", "typescript-styled-plugin": "^0.14.0", "webpack-bundle-analyzer": "^3.7.0"}, "resolutions": {"postcss": "8.x", "canvas": "2.6.1"}}