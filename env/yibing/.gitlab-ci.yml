build_yibing_dev:
  extends: .local_build_template
  variables:
    BUILD_CMD: yarn build:yibing:dev

build_yibing_prod:
  extends: .local_build_template
  variables:
    BUILD_CMD: yarn build:yibing:prod
  only:
    - /^v.*/

deploy_yibing_dev_126:
  extends: .local_deploy_dev_template
  variables:
    TEST_DEPLOY_PATH: /opt/web/web-projects/pc/admin-dev-40076/
  dependencies:
    - build_yibing_dev
  except:
    - /^v.*/
  tags:
    - yibing-dev-126

deploy_yibing_prod_126:
  extends: .local_deploy_prod_template
  variables:
    PROD_DEPLOY_PATH: /opt/web/web-projects/pc/admin-40076/
  dependencies:
    - build_yibing_prod
  only:
    - /^v.*/
    - feat-40067-localDeploy # 后续删除
  tags:
    - yibing-prod-126

deploy_yibing_prod_127:
  extends: deploy_yibing_prod_126
  tags:
    - yibing-prod-127
