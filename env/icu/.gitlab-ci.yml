build_icu_prod:
  extends: .local_build_template
  variables:
    BUILD_CMD: yarn build:icu:prod
  only:
    - /^v-2214.*/

deploy_icu_prod_18:
  extends: .local_deploy_prod_template
  variables:
    PROD_DEPLOY_PATH: /opt/web/newchcmu/admin-icu-2214/
  dependencies:
    - build_icu_prod
  only:
    - /^v-2214.*/
  tags:
    - chcmu-icuonline-az1-18

deploy_icu_prod_19:
  extends: deploy_icu_prod_18
  tags:
    - chcmu-icuonline-az1-19
