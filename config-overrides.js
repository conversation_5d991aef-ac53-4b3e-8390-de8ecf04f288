const {
  override,
  fixBabelImports,
  add<PERSON><PERSON><PERSON><PERSON><PERSON>,
  overrideDevServer,
  addWebpackAlias,
  removeModuleScopePlugin,
  addWebpackPlugin
} = require('customize-cra');
const rewireStyledComponents = require('react-app-rewire-styled-components');
const path = require('path');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer')
  .BundleAnalyzerPlugin;

const devServerConfig = () => config => {
  return {
    ...config,
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    historyApiFallback: true,
    hot: true,
    liveReload: false,
    proxy: {
      '/api': {
        target: `https://ih.ybachcmu.cn/test-api`,
        changeOrigin: true,
        // pathRewrite: { '^/api': '' },
        secure: false
      },
      '/test-api/api': {
        target: `https://ih.ybachcmu.cn`,
        // target: `https://ih.chcmu-com.cn/icu`, // 儿童医院
        changeOrigin: true,
        // pathRewrite: { '^/test-api': '' },
        secure: false
        // onProxyReq: (proxyReq, req) => {
        //   console.log(
        //     `原始路径: ${req.protocol}://${req.get('host')}${req.originalUrl}`
        //   );
        //   const targetUrl = `${proxyReq.protocol}//${proxyReq.host}${proxyReq.path}`;
        //   console.log(`代理路径: ${targetUrl}`);
        // }
      }
    }
  };
};
module.exports = {
  devServer: overrideDevServer(devServerConfig()),
  webpack: override(
    fixBabelImports('antd', {
      libraryDirectory: 'es',
      style: true
    }),
    fixBabelImports('@kqinfo/ui', {
      libraryDirectory: 'es'
    }),
    fixBabelImports('ahooks', {
      libraryDirectory: 'es',
      camel2DashComponentName: false
    }),
    fixBabelImports('parsec-hooks', {
      camel2DashComponentName: false,
      customName: name => {
        if (name === 'ContainerUseWrap') {
          return `parsec-hooks/lib/${name}`;
        }
        if (/^(use)/.test(name)) {
          return `parsec-hooks/lib/${name
            .replace(/^(use)/, '')
            .replace(/^\S/, s => s.toLowerCase())}Hooks`;
        } else {
          return `parsec-hooks/lib/utils/${name}`;
        }
      }
    }),
    addLessLoader({
      javascriptEnabled: true,
      modifyVars: {}
    }),
    process.env.REACT_APP_ANALYZE &&
      addWebpackPlugin(new BundleAnalyzerPlugin()),
    addWebpackAlias({
      '@apiHooks': path.resolve(__dirname, 'src/configs/apis'),
      '@src': path.resolve(__dirname, 'src'),
      '@utils': path.resolve(__dirname, 'src/utils'),
      '@templates': path.resolve(__dirname, 'src/templates'),
      '@configs': path.resolve(__dirname, 'src/configs'),
      '@components': path.resolve(__dirname, 'src/components'),
      '@layouts': path.resolve(__dirname, 'src/layouts'),
      stream: require.resolve('stream-browserify'),
      '@pages': path.resolve(__dirname, 'src/pages'),
      crypto: require.resolve('crypto-browserify'),
      '@stores': path.resolve(__dirname, 'src/stores'),
      '@themed-components': path.resolve(
        __dirname,
        'src/utils/themed-components'
      ),
      react: path.resolve('./node_modules/react'),
      'react-router-dom': path.resolve('./node_modules/react-router-dom'),
      'parsec-hooks': path.resolve('./node_modules/parsec-hooks')
    }),
    removeModuleScopePlugin(),
    config =>
      rewireStyledComponents(config, process.env.NODE_ENV, {
        pure: true
      }),
    config => {
      config.output = {
        ...config.output,
        library: `监管端标准版-[name]`,
        libraryTarget: 'umd',
        globalObject: 'window',
        chunkLoadingGlobal: `webpackJsonp_监管端标准版`
      };
      return config;
    },
    config => {
      config.module.rules.forEach(item => {
        if (item.oneOf) {
          item.oneOf.forEach(item => {
            if (item.loader?.includes('url-loader')) {
              item.options = {};
            }
            item.use?.forEach(item => {
              if (
                item.loader?.includes('postcss-loader') &&
                !item?.options?.postcssOptions
              ) {
                const postcssOptions = item.options;
                item.options = { postcssOptions };
              }
            });
          });
        }
      });
      config.ignoreWarnings = [
        // Ignore warnings raised by source-map-loader.
        // some third party packages may ship miss-configured sourcemaps, that interrupts the build
        // See: https://github.com/facebook/create-react-app/discussions/11278#discussioncomment-1780169
        /**
         *
         * @param {import('webpack').WebpackError} warning
         * @returns {boolean}
         */
        function ignoreSourcemapsloaderWarnings(warning) {
          return (
            warning.module &&
            warning.module.resource.includes('node_modules') &&
            warning.details &&
            warning.details.includes('source-map-loader')
          );
        }
      ];
      return config;
    }
  )
};
