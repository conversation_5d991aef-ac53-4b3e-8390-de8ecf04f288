import { defineConfig } from 'yapi-to-typescript';

export default defineConfig([
  {
    serverUrl: 'https://yapi.cqkqinfo.com/',
    typesOnly: false,
    target: 'typescript',
    reactHooks: {
      enabled: true
    },
    prodEnvName: 'production',
    outputFilePath: interfaceInfo =>
      `src/api/${interfaceInfo._category.name}.ts`,
    requestFunctionFilePath: 'src/api/request.ts',
    dataKey: 'data',
    projects: [
      {
        token:
          '24a3e8f174aebd802ef928419deebf08f9f62492ffcb2c721f58e6597a37162b',
        categories: [
          {
            id: 10541
          }
        ]
      }
    ],
    getRequestFunctionName(interfaceInfo, changeCase) {
      // 以接口全路径生成请求函数名
      // return changeCase.camelCase(interfaceInfo.path);

      // 若生成的请求函数名存在语法关键词报错、或想通过某个关键词触发 IDE 自动引入提示，可考虑加前缀，如:
      // return changeCase.camelCase(`api_${interfaceInfo.path}`)

      // 若生成的请求函数名有重复报错，可考虑将接口请求方式纳入生成条件，如:
      return changeCase.camelCase(
        `api${interfaceInfo.method}${interfaceInfo.path}`
      );
    }
  }
]);
