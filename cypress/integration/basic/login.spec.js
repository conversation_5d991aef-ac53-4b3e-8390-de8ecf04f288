import qs from 'qs';

describe('登录', () => {
  it('授权跳转首页', () => {
    // cy.visit(
    //   `https://tihs.cqkqinfo.com/kaiqiao/smart-follow-up-dev/?${qs.stringify({
    //     microAppHost: 'http://localhost:3000',
    //     doctorMicroAppHost:
    //       'https://tihs.cqkqinfo.com/kaiqiao/admin-doctor-dev/#'
    //   })}#/login`
    // );
    // cy.contains('欢迎使用互联网医院医务端');
    // cy.get('#医生端标准版 input[placeholder=请输入您的账号]')
    //   .clear()
    //   .type('999');
    // cy.get('#医生端标准版 input[placeholder=请输入您的密码]')
    //   .clear()
    //   .type('kq123456.');
    // cy.get('#医生端标准版 input[placeholder=请输入图形验证码]')
    //   .clear()
    //   .type('KQ123');
    // cy.contains('登 录').click();
    // cy.waitForNetworkIdle(3000);
    // cy.contains('首页', {
    //   timeout: 10000
    // });
  });
});
