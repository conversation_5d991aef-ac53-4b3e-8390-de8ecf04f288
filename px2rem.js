const postcss = require('postcss');

module.exports = () => ({
  postcssPlugin: 'px2rem',
  Once(css, { result }) {
    const from = result.opts.from;
    if (/components[/\\]*ih-standard-message/.test(from)) {
      // Message组件 处理后返回
      const oldCssText = css.toString();
      const newCssText = oldCssText.replace(/\d+\.*\d*px/gi, str => {
        const px = parseFloat(str);
        if (isNaN(px) || !px || px === 1) {
          return str;
        }
        return px / 2 + 'px';
      });
      const newCssObj = postcss.parse(newCssText);
      debugger;
      result.root = newCssObj;
    } else {
      // 原样返回
    }
  }
});
module.exports.postcss = true;
