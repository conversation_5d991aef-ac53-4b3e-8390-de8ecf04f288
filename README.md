# 仓库迁移

https://fegit.cqkqinfo.com/frontend/hospitalStandardEdition/ih-standard.git

## 互联网医院监管端

互联网医院监管端提供了医院配置、科室配置、医生权限设置、统计信息等功能。

### 开发必看

这个项目对应了多家医院，每家医院通过`hisId`进行区分，不同的医院具有不同的`hisId`，通过每个接口带有`hisId`来获取一些医院强相关的配置信息，并使用配置信息来实现登录鉴权以及功能开闭的控制。

### 项目资料

[仓库地址](https://gitlab2.cqkqinfo.com/ih-his/ih-standard.git)

注意下面链接中的`hisId`需要替换为实际的医院的`hisId`，比如口腔医院为`2219`, 大足中医院为`8800`等等

正式环境地址：`https://ihs.cqkqinfo.com/kaiqiao/admin-{hisId}/#/`

测试环境地址：`https://tihs.cqkqinfo.com/kaiqiao/admin-{hisId}/{dev or apiVersion}/#/`， 测试环境可以指定后端接口的版本号（如`3.8.0`）或者没有版本号时为默认值`dev`

开发环境地址：`https://tihs.cqkqinfo.com/kaiqiao/admin-dev-{hisId}/{dev or apiVersion}/#/` 开发环境相对于测试环境在`hisId`前面加上了`dev`前缀

账号：15110275593/ChinaCQKQinfo2024#
云阳
18426421419/wfz@2024

开发测试环境验证码随意

18942825445
Gpc123456

### 主要流程

监管端功能流程相对比较简单， 维护和功能新增找到对应的页面即可。!

### 部署须知

该项目集成了**标准版部署**和**院内部署**。

针对标准版部署直接新增 hisId 即可。

针对院内部署，需要在根目录 `env` 下新增一个文件，名为为**医院名称（eg: 宜宾 -> yibing）**，然后在该文件下新增该医院的**环境变量配置**和 **ci 配置**

> 可以参看 `yibing` 文件夹下的文件

运行院内部署项目：

```json
{
  "scripts": {
    // 你想运行哪家医院，就在 xxx 处填写医院名称即可
    "start:inner": "env-cmd -f ./env/xxxx/.env.development react-app-rewired start"
  }
}
```

打包院内部署项目（劣势就是，新增一家医院，就需要新增两条命令）：

```json
// xxx 需替换成医院名称
{
  "scripts": {
    "build:xxxx:prod": "env-cmd -f ./env/xxxx/.env.production react-app-rewired build",
    "build:xxxx:dev": "env-cmd -f ./env/xxxx/.env.development react-app-rewired build"
  }
}
```

然后就可以进行发布了。

**特别要求：**

针对院内部署，无论是开发环境还是生产环境，推荐手动打包和发布。

要使打包人更加清楚构建和发布，需要一个更加清晰的命名

```yml
# yibing 开发环境打包
build_yibing_dev:
  extends: .local_build_template
  variables:
    BUILD_CMD: yarn build:yibing:dev

# yibing 生产环境打包
build_yibing_prod:
  extends: .local_build_template
  variables:
    BUILD_CMD: yarn build:yibing:prod

# yibing 开发环境发布
deploy_yibing_dev_126:
  extends: .local_deploy_template
  variables:
    PROD_DEPLOY_PATH: /opt/web/web-projects/pc/admin-dev-40076/
  dependencies:
    - build_yibing_dev
  except:
    - /^v.*/
  tags:
    - yibing-dev-126

# yibing 生产环境发布（节点1）
deploy_yibing_prod_126:
  extends: .local_deploy_template
  variables:
    PROD_DEPLOY_PATH: /opt/web/web-projects/pc/admin-40076/
  dependencies:
    - build_yibing_prod
  only:
    - /^v.*/
  tags:
    - yibing-prod-126

# yibing 生产环境发布（节点2）
deploy_yibing_prod_127:
  extends: deploy_yibing_prod_126
  tags:
    - yibing-prod-127
```

> ci 文件中 `.local_build_template` 和 `.local_deploy_template` 都已经定义好了，直接使用即可。
> 只需要**命好名称**，**修改 tag**, **修改路径**即可1
