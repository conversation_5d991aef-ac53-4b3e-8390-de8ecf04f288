import React from 'react';
import useApi from '@pages/easy-meds/purchase-config/apis';

interface Option {
  label: string;
  value: string | number;
}
export const useSelectDoctor = () => {
  // 开方医生
  const [doctorList, setDoctorList] = React.useState<Option[]>([]);
  // 审方药师
  const [pharmacistList, setPharmacistList] = React.useState<Option[]>([]);
  React.useEffect(() => {
    useApi.查询医生列表.request(1).then(res => {
      const list = res?.data?.map(item => {
        return {
          label: item.name,
          value: item.doctorId
        };
      });
      setDoctorList(list as Option[]);
    });
    useApi.查询医生列表.request(4).then(res => {
      const list = res?.data?.map(item => {
        return {
          label: item.name,
          value: item.doctorId
        };
      });
      setPharmacistList(list as Option[]);
    });
  }, []);
  return {
    doctor<PERSON>ist,
    pharmacistList
  };
};
