import { useCallback } from 'react';
import useApi from '@pages/order/api';
import env from '@configs/env';

const usePaymodeWrapper = () => {
  const {
    data: { data }
  } = useApi.查询支付模式列表({
    needInit: true,
    initValue: {
      data: []
    },
    params: {
      hisId: env.hisId
    }
  });

  const dataWrapper = useCallback(
    (data: Record<string, string>[], key: string) => {
      return (
        data &&
        data?.reduce((store: Record<string, string>, item) => {
          return { ...store, [item[key]]: item?.name };
        }, {})
      );
    },
    []
  );

  return {
    payMethods: dataWrapper(data?.payMethods || [], 'payMethod'),
    payChannels: dataWrapper(data?.payChannels || [], 'payChannel')
  };
};

export default usePaymodeWrapper;
