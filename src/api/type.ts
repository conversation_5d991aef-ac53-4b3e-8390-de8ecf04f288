// eslint-disable-next-line prettier/prettier
import type {
  RequestConfig,
  RequestFunctionRestArgs,
} from 'yapi-to-typescript';
import request from './request';

export type ApiResponse<D> ={
  code: 0 | 200 | 999; // 999用户未登录
  msg: string | null;
  data?: D;
}
// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult
> = (TRequestConfig['requestDataOptional'] extends true
  ? (
      requestData?: TRequestData,
      ...args: RequestFunctionRestArgs<typeof request>
    ) => TRequestResult
  : (
      requestData: TRequestData,
      ...args: RequestFunctionRestArgs<typeof request>
    ) => TRequestResult) & {
  requestConfig: TRequestConfig;
};
