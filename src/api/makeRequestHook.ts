import createApiHooks from 'create-api-hooks';
import { RequestConfig } from 'yapi-to-typescript';
import { Request } from './type';
import baseRequest from './request';

export default function makeRequestHook<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult extends ReturnType<typeof baseRequest>
>(request: Request<TRequestData, TRequestConfig, TRequestResult>) {
  type Data = TRequestResult extends Promise<infer R> ? R : TRequestResult;
  return createApiHooks<TRequestData, Data['data']>(request);
}
