import { RequestFunctionParams } from 'yapi-to-typescript';
import { request as baseRequest } from 'parsec-admin';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import storage from '@src/utils/storage';
import { history } from '@src/App';
import { message } from 'antd';
export type ApiResponse<T = undefined> = {
  code: 0 | 200 | 401;
  msg: string;
} & (T extends undefined ? never : { data: T });
export type ListApiResponseData<D> = ApiResponse<{
  currentPage?: number;
  totalCount?: number;
  recordList?: D[];
}>;
export default async function request<TResponseData>(
  payload: RequestFunctionParams,
  config?: AxiosRequestConfig
): Promise<ApiResponse<TResponseData>> {
  const { path, method, requestHeaders, data, mockUrl } = payload;
  // 构建请求参数
  const token = storage.get('token');
  if (token) {
    requestHeaders.Authorization = `Bearer ${token}`;
  }
  // 根据环境变量切换判断是否切换为mock环境
  const url = process.env.REACT_APP_IS_MOCK ? mockUrl + path : path;
  const requestOptions: AxiosRequestConfig = {
    url,
    method,
    headers: requestHeaders,
    ...config
  };
  if (method === 'GET') {
    requestOptions.params = data;
  } else {
    requestOptions.data = data;
  }
  // 发起请求并返回响应数据
  const response: AxiosResponse<ApiResponse<TResponseData>> = await baseRequest(
    requestOptions
  );
  const { data: { code = 0, msg = '网络请求失败' } = {} } = response || {};
  if (code !== 0) {
    if (code === 401) {
      history.replace('/login');
      sessionStorage.clear();
      localStorage.clear();
    }
    message.error(msg);
    return Promise.reject(response);
  }
  return response.data;
}
