/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from './request';
// @ts-ignore
import makeRequestHook from './makeRequestHook';

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>;

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig;
};

const mockUrl_0_0_0_0 = 'https://yapi.cqkqinfo.com/mock/1460' as any;
const devUrl_0_0_0_0 = '' as any;
const prodUrl_0_0_0_0 = '' as any;
const dataKey_0_0_0_0 = 'data' as any;

/**
 * 接口 [获取首页列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54208) 的 **请求类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getIndexPageTypeList`
 * @更新时间 `2024-03-07 09:26:04`
 */
export interface ApiGetKaiqiaoHisConfigCommonGetIndexPageTypeListRequest {
  /**
   * 医院ID
   */
  hisId: string;
  /**
   * 设备类型
   */
  deviceType?: string;
  /**
   * 页码
   */
  pageNum?: string;
  /**
   * 条数
   */
  numPerPage?: string;
}

/**
 * 接口 [获取首页列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54208) 的 **返回类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getIndexPageTypeList`
 * @更新时间 `2024-03-07 09:26:04`
 */
export type ApiGetKaiqiaoHisConfigCommonGetIndexPageTypeListResponse = any;

/**
 * 接口 [获取首页列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54208) 的 **请求配置的类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getIndexPageTypeList`
 * @更新时间 `2024-03-07 09:26:04`
 */
type ApiGetKaiqiaoHisConfigCommonGetIndexPageTypeListRequestConfig = Readonly<
  RequestConfig<
    'https://yapi.cqkqinfo.com/mock/1460',
    '',
    '',
    '/kaiqiao/his/config-common/getIndexPageTypeList',
    'data',
    string,
    'hisId' | 'deviceType' | 'pageNum' | 'numPerPage',
    false
  >
>;

/**
 * 接口 [获取首页列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54208) 的 **请求配置**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getIndexPageTypeList`
 * @更新时间 `2024-03-07 09:26:04`
 */
const apiGetKaiqiaoHisConfigCommonGetIndexPageTypeListRequestConfig: ApiGetKaiqiaoHisConfigCommonGetIndexPageTypeListRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/kaiqiao/his/config-common/getIndexPageTypeList',
    method: Method.GET,
    requestHeaders: {},
    requestBodyType: RequestBodyType.query,
    responseBodyType: ResponseBodyType.raw,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: ['hisId', 'deviceType', 'pageNum', 'numPerPage'],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiGetKaiqiaoHisConfigCommonGetIndexPageTypeList',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  };

/**
 * 接口 [获取首页列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54208) 的 **请求函数**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getIndexPageTypeList`
 * @更新时间 `2024-03-07 09:26:04`
 */
export const apiGetKaiqiaoHisConfigCommonGetIndexPageTypeList = /*#__PURE__*/ (
  requestData: ApiGetKaiqiaoHisConfigCommonGetIndexPageTypeListRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiGetKaiqiaoHisConfigCommonGetIndexPageTypeListResponse>(
    prepare(apiGetKaiqiaoHisConfigCommonGetIndexPageTypeListRequestConfig, requestData),
    ...args,
  );
};

apiGetKaiqiaoHisConfigCommonGetIndexPageTypeList.requestConfig =
  apiGetKaiqiaoHisConfigCommonGetIndexPageTypeListRequestConfig;

/**
 * 接口 [获取首页列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54208) 的 **React Hook**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getIndexPageTypeList`
 * @更新时间 `2024-03-07 09:26:04`
 */
export const useApiGetKaiqiaoHisConfigCommonGetIndexPageTypeList = /*#__PURE__*/ makeRequestHook<
  ApiGetKaiqiaoHisConfigCommonGetIndexPageTypeListRequest,
  ApiGetKaiqiaoHisConfigCommonGetIndexPageTypeListRequestConfig,
  ReturnType<typeof apiGetKaiqiaoHisConfigCommonGetIndexPageTypeList>
>(apiGetKaiqiaoHisConfigCommonGetIndexPageTypeList);

/**
 * 接口 [获取功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54210) 的 **请求类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getModuleConfig`
 * @更新时间 `2024-03-07 09:26:15`
 */
export interface ApiGetKaiqiaoHisConfigCommonGetModuleConfigRequest {
  /**
   * 医院ID
   */
  hisId: string;
  /**
   * 首页类型ID
   */
  pageTypeId: string;
  /**
   * 功能名称
   */
  moduleName?: string;
  /**
   * 功能状态
   */
  status?: string;
  /**
   * 修改开始时间
   */
  updateStartTime?: string;
  /**
   * 修改结束时间
   */
  updateEndTime?: string;
  /**
   * 页码
   */
  pageNum: string;
  /**
   * 条数
   */
  numPerPage: string;
}

/**
 * 接口 [获取功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54210) 的 **返回类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getModuleConfig`
 * @更新时间 `2024-03-07 09:26:15`
 */
export type ApiGetKaiqiaoHisConfigCommonGetModuleConfigResponse = any;

/**
 * 接口 [获取功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54210) 的 **请求配置的类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getModuleConfig`
 * @更新时间 `2024-03-07 09:26:15`
 */
type ApiGetKaiqiaoHisConfigCommonGetModuleConfigRequestConfig = Readonly<
  RequestConfig<
    'https://yapi.cqkqinfo.com/mock/1460',
    '',
    '',
    '/kaiqiao/his/config-common/getModuleConfig',
    'data',
    string,
    'hisId' | 'pageTypeId' | 'moduleName' | 'status' | 'updateStartTime' | 'updateEndTime' | 'pageNum' | 'numPerPage',
    false
  >
>;

/**
 * 接口 [获取功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54210) 的 **请求配置**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getModuleConfig`
 * @更新时间 `2024-03-07 09:26:15`
 */
const apiGetKaiqiaoHisConfigCommonGetModuleConfigRequestConfig: ApiGetKaiqiaoHisConfigCommonGetModuleConfigRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/kaiqiao/his/config-common/getModuleConfig',
    method: Method.GET,
    requestHeaders: {},
    requestBodyType: RequestBodyType.query,
    responseBodyType: ResponseBodyType.raw,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: [
      'hisId',
      'pageTypeId',
      'moduleName',
      'status',
      'updateStartTime',
      'updateEndTime',
      'pageNum',
      'numPerPage',
    ],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiGetKaiqiaoHisConfigCommonGetModuleConfig',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  };

/**
 * 接口 [获取功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54210) 的 **请求函数**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getModuleConfig`
 * @更新时间 `2024-03-07 09:26:15`
 */
export const apiGetKaiqiaoHisConfigCommonGetModuleConfig = /*#__PURE__*/ (
  requestData: ApiGetKaiqiaoHisConfigCommonGetModuleConfigRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiGetKaiqiaoHisConfigCommonGetModuleConfigResponse>(
    prepare(apiGetKaiqiaoHisConfigCommonGetModuleConfigRequestConfig, requestData),
    ...args,
  );
};

apiGetKaiqiaoHisConfigCommonGetModuleConfig.requestConfig = apiGetKaiqiaoHisConfigCommonGetModuleConfigRequestConfig;

/**
 * 接口 [获取功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54210) 的 **React Hook**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getModuleConfig`
 * @更新时间 `2024-03-07 09:26:15`
 */
export const useApiGetKaiqiaoHisConfigCommonGetModuleConfig = /*#__PURE__*/ makeRequestHook<
  ApiGetKaiqiaoHisConfigCommonGetModuleConfigRequest,
  ApiGetKaiqiaoHisConfigCommonGetModuleConfigRequestConfig,
  ReturnType<typeof apiGetKaiqiaoHisConfigCommonGetModuleConfig>
>(apiGetKaiqiaoHisConfigCommonGetModuleConfig);

/**
 * 接口 [获取设备配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54212) 的 **请求类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceConfig`
 * @更新时间 `2024-03-07 09:26:22`
 */
export interface ApiGetKaiqiaoHisConfigCommonGetDeviceConfigRequest {
  /**
   * 医院ID
   */
  hisId: string;
  /**
   * 页面类型ID
   */
  pageTypeId: string;
  /**
   * 设备编号
   */
  deviceCode?: string;
  /**
   * 设备位置
   */
  deviceLocaltion?: string;
  /**
   * 院区
   */
  hospitalCode?: string;
  /**
   * 添加时间
   */
  addTime?: string;
  /**
   * 页码
   */
  pageNum: string;
  /**
   * 条数
   */
  numPerPage: string;
}

/**
 * 接口 [获取设备配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54212) 的 **返回类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceConfig`
 * @更新时间 `2024-03-07 09:26:22`
 */
export type ApiGetKaiqiaoHisConfigCommonGetDeviceConfigResponse = any;

/**
 * 接口 [获取设备配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54212) 的 **请求配置的类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceConfig`
 * @更新时间 `2024-03-07 09:26:22`
 */
type ApiGetKaiqiaoHisConfigCommonGetDeviceConfigRequestConfig = Readonly<
  RequestConfig<
    'https://yapi.cqkqinfo.com/mock/1460',
    '',
    '',
    '/kaiqiao/his/config-common/getDeviceConfig',
    'data',
    string,
    'hisId' | 'pageTypeId' | 'deviceCode' | 'deviceLocaltion' | 'hospitalCode' | 'addTime' | 'pageNum' | 'numPerPage',
    false
  >
>;

/**
 * 接口 [获取设备配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54212) 的 **请求配置**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceConfig`
 * @更新时间 `2024-03-07 09:26:22`
 */
const apiGetKaiqiaoHisConfigCommonGetDeviceConfigRequestConfig: ApiGetKaiqiaoHisConfigCommonGetDeviceConfigRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/kaiqiao/his/config-common/getDeviceConfig',
    method: Method.GET,
    requestHeaders: {},
    requestBodyType: RequestBodyType.query,
    responseBodyType: ResponseBodyType.raw,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: [
      'hisId',
      'pageTypeId',
      'deviceCode',
      'deviceLocaltion',
      'hospitalCode',
      'addTime',
      'pageNum',
      'numPerPage',
    ],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiGetKaiqiaoHisConfigCommonGetDeviceConfig',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  };

/**
 * 接口 [获取设备配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54212) 的 **请求函数**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceConfig`
 * @更新时间 `2024-03-07 09:26:22`
 */
export const apiGetKaiqiaoHisConfigCommonGetDeviceConfig = /*#__PURE__*/ (
  requestData: ApiGetKaiqiaoHisConfigCommonGetDeviceConfigRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiGetKaiqiaoHisConfigCommonGetDeviceConfigResponse>(
    prepare(apiGetKaiqiaoHisConfigCommonGetDeviceConfigRequestConfig, requestData),
    ...args,
  );
};

apiGetKaiqiaoHisConfigCommonGetDeviceConfig.requestConfig = apiGetKaiqiaoHisConfigCommonGetDeviceConfigRequestConfig;

/**
 * 接口 [获取设备配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54212) 的 **React Hook**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceConfig`
 * @更新时间 `2024-03-07 09:26:22`
 */
export const useApiGetKaiqiaoHisConfigCommonGetDeviceConfig = /*#__PURE__*/ makeRequestHook<
  ApiGetKaiqiaoHisConfigCommonGetDeviceConfigRequest,
  ApiGetKaiqiaoHisConfigCommonGetDeviceConfigRequestConfig,
  ReturnType<typeof apiGetKaiqiaoHisConfigCommonGetDeviceConfig>
>(apiGetKaiqiaoHisConfigCommonGetDeviceConfig);

/**
 * 接口 [设备列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54214) 的 **请求类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceList`
 * @更新时间 `2024-03-07 09:26:31`
 */
export interface ApiGetKaiqiaoHisConfigCommonGetDeviceListRequest {
  /**
   * 医院id
   */
  hisId: string;
  /**
   * 设备编号
   */
  deviceCode?: string;
  /**
   * 设备位置
   */
  deviceLocaltion?: string;
  /**
   * 院区
   */
  hospitalCode?: string;
  pageNum?: string;
  numPerPage?: string;
}

/**
 * 接口 [设备列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54214) 的 **返回类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceList`
 * @更新时间 `2024-03-07 09:26:31`
 */
export type ApiGetKaiqiaoHisConfigCommonGetDeviceListResponse = any;

/**
 * 接口 [设备列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54214) 的 **请求配置的类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceList`
 * @更新时间 `2024-03-07 09:26:31`
 */
type ApiGetKaiqiaoHisConfigCommonGetDeviceListRequestConfig = Readonly<
  RequestConfig<
    'https://yapi.cqkqinfo.com/mock/1460',
    '',
    '',
    '/kaiqiao/his/config-common/getDeviceList',
    'data',
    string,
    'hisId' | 'deviceCode' | 'deviceLocaltion' | 'hospitalCode' | 'pageNum' | 'numPerPage',
    false
  >
>;

/**
 * 接口 [设备列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54214) 的 **请求配置**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceList`
 * @更新时间 `2024-03-07 09:26:31`
 */
const apiGetKaiqiaoHisConfigCommonGetDeviceListRequestConfig: ApiGetKaiqiaoHisConfigCommonGetDeviceListRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/kaiqiao/his/config-common/getDeviceList',
    method: Method.GET,
    requestHeaders: {},
    requestBodyType: RequestBodyType.query,
    responseBodyType: ResponseBodyType.raw,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: ['hisId', 'deviceCode', 'deviceLocaltion', 'hospitalCode', 'pageNum', 'numPerPage'],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiGetKaiqiaoHisConfigCommonGetDeviceList',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  };

/**
 * 接口 [设备列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54214) 的 **请求函数**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceList`
 * @更新时间 `2024-03-07 09:26:31`
 */
export const apiGetKaiqiaoHisConfigCommonGetDeviceList = /*#__PURE__*/ (
  requestData: ApiGetKaiqiaoHisConfigCommonGetDeviceListRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiGetKaiqiaoHisConfigCommonGetDeviceListResponse>(
    prepare(apiGetKaiqiaoHisConfigCommonGetDeviceListRequestConfig, requestData),
    ...args,
  );
};

apiGetKaiqiaoHisConfigCommonGetDeviceList.requestConfig = apiGetKaiqiaoHisConfigCommonGetDeviceListRequestConfig;

/**
 * 接口 [设备列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54214) 的 **React Hook**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceList`
 * @更新时间 `2024-03-07 09:26:31`
 */
export const useApiGetKaiqiaoHisConfigCommonGetDeviceList = /*#__PURE__*/ makeRequestHook<
  ApiGetKaiqiaoHisConfigCommonGetDeviceListRequest,
  ApiGetKaiqiaoHisConfigCommonGetDeviceListRequestConfig,
  ReturnType<typeof apiGetKaiqiaoHisConfigCommonGetDeviceList>
>(apiGetKaiqiaoHisConfigCommonGetDeviceList);

/**
 * 接口 [添加功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54216) 的 **请求类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/addModuleConfig`
 * @更新时间 `2024-03-07 09:26:38`
 */
export interface ApiPutKaiqiaoHisConfigCommonAddModuleConfigRequest {}

/**
 * 接口 [添加功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54216) 的 **返回类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/addModuleConfig`
 * @更新时间 `2024-03-07 09:26:38`
 */
export type ApiPutKaiqiaoHisConfigCommonAddModuleConfigResponse = any;

/**
 * 接口 [添加功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54216) 的 **请求配置的类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/addModuleConfig`
 * @更新时间 `2024-03-07 09:26:38`
 */
type ApiPutKaiqiaoHisConfigCommonAddModuleConfigRequestConfig = Readonly<
  RequestConfig<
    'https://yapi.cqkqinfo.com/mock/1460',
    '',
    '',
    '/kaiqiao/his/config-common/addModuleConfig',
    'data',
    string,
    string,
    true
  >
>;

/**
 * 接口 [添加功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54216) 的 **请求配置**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/addModuleConfig`
 * @更新时间 `2024-03-07 09:26:38`
 */
const apiPutKaiqiaoHisConfigCommonAddModuleConfigRequestConfig: ApiPutKaiqiaoHisConfigCommonAddModuleConfigRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/kaiqiao/his/config-common/addModuleConfig',
    method: Method.PUT,
    requestHeaders: {},
    requestBodyType: RequestBodyType.raw,
    responseBodyType: ResponseBodyType.raw,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: [],
    requestDataOptional: true,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiPutKaiqiaoHisConfigCommonAddModuleConfig',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  };

/**
 * 接口 [添加功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54216) 的 **请求函数**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/addModuleConfig`
 * @更新时间 `2024-03-07 09:26:38`
 */
export const apiPutKaiqiaoHisConfigCommonAddModuleConfig = /*#__PURE__*/ (
  requestData?: ApiPutKaiqiaoHisConfigCommonAddModuleConfigRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiPutKaiqiaoHisConfigCommonAddModuleConfigResponse>(
    prepare(apiPutKaiqiaoHisConfigCommonAddModuleConfigRequestConfig, requestData),
    ...args,
  );
};

apiPutKaiqiaoHisConfigCommonAddModuleConfig.requestConfig = apiPutKaiqiaoHisConfigCommonAddModuleConfigRequestConfig;

/**
 * 接口 [添加功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54216) 的 **React Hook**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/addModuleConfig`
 * @更新时间 `2024-03-07 09:26:38`
 */
export const useApiPutKaiqiaoHisConfigCommonAddModuleConfig = /*#__PURE__*/ makeRequestHook<
  ApiPutKaiqiaoHisConfigCommonAddModuleConfigRequest,
  ApiPutKaiqiaoHisConfigCommonAddModuleConfigRequestConfig,
  ReturnType<typeof apiPutKaiqiaoHisConfigCommonAddModuleConfig>
>(apiPutKaiqiaoHisConfigCommonAddModuleConfig);

/**
 * 接口 [修改功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54218) 的 **请求类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeModuleConfig`
 * @更新时间 `2024-03-07 09:26:44`
 */
export interface ApiPutKaiqiaoHisConfigCommonChargeModuleConfigRequest {}

/**
 * 接口 [修改功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54218) 的 **返回类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeModuleConfig`
 * @更新时间 `2024-03-07 09:26:44`
 */
export type ApiPutKaiqiaoHisConfigCommonChargeModuleConfigResponse = any;

/**
 * 接口 [修改功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54218) 的 **请求配置的类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeModuleConfig`
 * @更新时间 `2024-03-07 09:26:44`
 */
type ApiPutKaiqiaoHisConfigCommonChargeModuleConfigRequestConfig = Readonly<
  RequestConfig<
    'https://yapi.cqkqinfo.com/mock/1460',
    '',
    '',
    '/kaiqiao/his/config-common/chargeModuleConfig',
    'data',
    string,
    string,
    true
  >
>;

/**
 * 接口 [修改功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54218) 的 **请求配置**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeModuleConfig`
 * @更新时间 `2024-03-07 09:26:44`
 */
const apiPutKaiqiaoHisConfigCommonChargeModuleConfigRequestConfig: ApiPutKaiqiaoHisConfigCommonChargeModuleConfigRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/kaiqiao/his/config-common/chargeModuleConfig',
    method: Method.PUT,
    requestHeaders: {},
    requestBodyType: RequestBodyType.raw,
    responseBodyType: ResponseBodyType.raw,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: [],
    requestDataOptional: true,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiPutKaiqiaoHisConfigCommonChargeModuleConfig',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  };

/**
 * 接口 [修改功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54218) 的 **请求函数**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeModuleConfig`
 * @更新时间 `2024-03-07 09:26:44`
 */
export const apiPutKaiqiaoHisConfigCommonChargeModuleConfig = /*#__PURE__*/ (
  requestData?: ApiPutKaiqiaoHisConfigCommonChargeModuleConfigRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiPutKaiqiaoHisConfigCommonChargeModuleConfigResponse>(
    prepare(apiPutKaiqiaoHisConfigCommonChargeModuleConfigRequestConfig, requestData),
    ...args,
  );
};

apiPutKaiqiaoHisConfigCommonChargeModuleConfig.requestConfig =
  apiPutKaiqiaoHisConfigCommonChargeModuleConfigRequestConfig;

/**
 * 接口 [修改功能配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54218) 的 **React Hook**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeModuleConfig`
 * @更新时间 `2024-03-07 09:26:44`
 */
export const useApiPutKaiqiaoHisConfigCommonChargeModuleConfig = /*#__PURE__*/ makeRequestHook<
  ApiPutKaiqiaoHisConfigCommonChargeModuleConfigRequest,
  ApiPutKaiqiaoHisConfigCommonChargeModuleConfigRequestConfig,
  ReturnType<typeof apiPutKaiqiaoHisConfigCommonChargeModuleConfig>
>(apiPutKaiqiaoHisConfigCommonChargeModuleConfig);

/**
 * 接口 [获取功能列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54220) 的 **请求类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getModuleList`
 * @更新时间 `2024-03-07 09:26:51`
 */
export interface ApiGetKaiqiaoHisConfigCommonGetModuleListRequest {
  /**
   * 医院ID
   */
  hisId: string;
  /**
   * 功能名称
   */
  moduleName?: string;
  /**
   * 功能状态
   */
  moduleStatus?: string;
  /**
   * 修改开始时间
   */
  updateStartTime?: string;
  /**
   * 修改结束时间
   */
  updateEndTime?: string;
  pageNum: string;
  numPerPage: string;
}

/**
 * 接口 [获取功能列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54220) 的 **返回类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getModuleList`
 * @更新时间 `2024-03-07 09:26:51`
 */
export type ApiGetKaiqiaoHisConfigCommonGetModuleListResponse = any;

/**
 * 接口 [获取功能列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54220) 的 **请求配置的类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getModuleList`
 * @更新时间 `2024-03-07 09:26:51`
 */
type ApiGetKaiqiaoHisConfigCommonGetModuleListRequestConfig = Readonly<
  RequestConfig<
    'https://yapi.cqkqinfo.com/mock/1460',
    '',
    '',
    '/kaiqiao/his/config-common/getModuleList',
    'data',
    string,
    'hisId' | 'moduleName' | 'moduleStatus' | 'updateStartTime' | 'updateEndTime' | 'pageNum' | 'numPerPage',
    false
  >
>;

/**
 * 接口 [获取功能列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54220) 的 **请求配置**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getModuleList`
 * @更新时间 `2024-03-07 09:26:51`
 */
const apiGetKaiqiaoHisConfigCommonGetModuleListRequestConfig: ApiGetKaiqiaoHisConfigCommonGetModuleListRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/kaiqiao/his/config-common/getModuleList',
    method: Method.GET,
    requestHeaders: {},
    requestBodyType: RequestBodyType.query,
    responseBodyType: ResponseBodyType.raw,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: ['hisId', 'moduleName', 'moduleStatus', 'updateStartTime', 'updateEndTime', 'pageNum', 'numPerPage'],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiGetKaiqiaoHisConfigCommonGetModuleList',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  };

/**
 * 接口 [获取功能列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54220) 的 **请求函数**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getModuleList`
 * @更新时间 `2024-03-07 09:26:51`
 */
export const apiGetKaiqiaoHisConfigCommonGetModuleList = /*#__PURE__*/ (
  requestData: ApiGetKaiqiaoHisConfigCommonGetModuleListRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiGetKaiqiaoHisConfigCommonGetModuleListResponse>(
    prepare(apiGetKaiqiaoHisConfigCommonGetModuleListRequestConfig, requestData),
    ...args,
  );
};

apiGetKaiqiaoHisConfigCommonGetModuleList.requestConfig = apiGetKaiqiaoHisConfigCommonGetModuleListRequestConfig;

/**
 * 接口 [获取功能列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54220) 的 **React Hook**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getModuleList`
 * @更新时间 `2024-03-07 09:26:51`
 */
export const useApiGetKaiqiaoHisConfigCommonGetModuleList = /*#__PURE__*/ makeRequestHook<
  ApiGetKaiqiaoHisConfigCommonGetModuleListRequest,
  ApiGetKaiqiaoHisConfigCommonGetModuleListRequestConfig,
  ReturnType<typeof apiGetKaiqiaoHisConfigCommonGetModuleList>
>(apiGetKaiqiaoHisConfigCommonGetModuleList);

/**
 * 接口 [修改功能列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54222) 的 **请求类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeModuleList`
 * @更新时间 `2024-03-07 09:26:56`
 */
export interface ApiPutKaiqiaoHisConfigCommonChargeModuleListRequest {}

/**
 * 接口 [修改功能列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54222) 的 **返回类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeModuleList`
 * @更新时间 `2024-03-07 09:26:56`
 */
export type ApiPutKaiqiaoHisConfigCommonChargeModuleListResponse = any;

/**
 * 接口 [修改功能列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54222) 的 **请求配置的类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeModuleList`
 * @更新时间 `2024-03-07 09:26:56`
 */
type ApiPutKaiqiaoHisConfigCommonChargeModuleListRequestConfig = Readonly<
  RequestConfig<
    'https://yapi.cqkqinfo.com/mock/1460',
    '',
    '',
    '/kaiqiao/his/config-common/chargeModuleList',
    'data',
    string,
    string,
    true
  >
>;

/**
 * 接口 [修改功能列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54222) 的 **请求配置**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeModuleList`
 * @更新时间 `2024-03-07 09:26:56`
 */
const apiPutKaiqiaoHisConfigCommonChargeModuleListRequestConfig: ApiPutKaiqiaoHisConfigCommonChargeModuleListRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/kaiqiao/his/config-common/chargeModuleList',
    method: Method.PUT,
    requestHeaders: {},
    requestBodyType: RequestBodyType.raw,
    responseBodyType: ResponseBodyType.raw,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: [],
    requestDataOptional: true,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiPutKaiqiaoHisConfigCommonChargeModuleList',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  };

/**
 * 接口 [修改功能列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54222) 的 **请求函数**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeModuleList`
 * @更新时间 `2024-03-07 09:26:56`
 */
export const apiPutKaiqiaoHisConfigCommonChargeModuleList = /*#__PURE__*/ (
  requestData?: ApiPutKaiqiaoHisConfigCommonChargeModuleListRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiPutKaiqiaoHisConfigCommonChargeModuleListResponse>(
    prepare(apiPutKaiqiaoHisConfigCommonChargeModuleListRequestConfig, requestData),
    ...args,
  );
};

apiPutKaiqiaoHisConfigCommonChargeModuleList.requestConfig = apiPutKaiqiaoHisConfigCommonChargeModuleListRequestConfig;

/**
 * 接口 [修改功能列表↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54222) 的 **React Hook**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeModuleList`
 * @更新时间 `2024-03-07 09:26:56`
 */
export const useApiPutKaiqiaoHisConfigCommonChargeModuleList = /*#__PURE__*/ makeRequestHook<
  ApiPutKaiqiaoHisConfigCommonChargeModuleListRequest,
  ApiPutKaiqiaoHisConfigCommonChargeModuleListRequestConfig,
  ReturnType<typeof apiPutKaiqiaoHisConfigCommonChargeModuleList>
>(apiPutKaiqiaoHisConfigCommonChargeModuleList);

/**
 * 接口 [添加首页配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54224) 的 **请求类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/addIndexPage`
 * @更新时间 `2024-03-07 09:27:02`
 */
export interface ApiPutKaiqiaoHisConfigCommonAddIndexPageRequest {}

/**
 * 接口 [添加首页配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54224) 的 **返回类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/addIndexPage`
 * @更新时间 `2024-03-07 09:27:02`
 */
export type ApiPutKaiqiaoHisConfigCommonAddIndexPageResponse = any;

/**
 * 接口 [添加首页配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54224) 的 **请求配置的类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/addIndexPage`
 * @更新时间 `2024-03-07 09:27:02`
 */
type ApiPutKaiqiaoHisConfigCommonAddIndexPageRequestConfig = Readonly<
  RequestConfig<
    'https://yapi.cqkqinfo.com/mock/1460',
    '',
    '',
    '/kaiqiao/his/config-common/addIndexPage',
    'data',
    string,
    string,
    true
  >
>;

/**
 * 接口 [添加首页配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54224) 的 **请求配置**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/addIndexPage`
 * @更新时间 `2024-03-07 09:27:02`
 */
const apiPutKaiqiaoHisConfigCommonAddIndexPageRequestConfig: ApiPutKaiqiaoHisConfigCommonAddIndexPageRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/kaiqiao/his/config-common/addIndexPage',
    method: Method.PUT,
    requestHeaders: {},
    requestBodyType: RequestBodyType.raw,
    responseBodyType: ResponseBodyType.raw,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: [],
    requestDataOptional: true,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiPutKaiqiaoHisConfigCommonAddIndexPage',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  };

/**
 * 接口 [添加首页配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54224) 的 **请求函数**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/addIndexPage`
 * @更新时间 `2024-03-07 09:27:02`
 */
export const apiPutKaiqiaoHisConfigCommonAddIndexPage = /*#__PURE__*/ (
  requestData?: ApiPutKaiqiaoHisConfigCommonAddIndexPageRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiPutKaiqiaoHisConfigCommonAddIndexPageResponse>(
    prepare(apiPutKaiqiaoHisConfigCommonAddIndexPageRequestConfig, requestData),
    ...args,
  );
};

apiPutKaiqiaoHisConfigCommonAddIndexPage.requestConfig = apiPutKaiqiaoHisConfigCommonAddIndexPageRequestConfig;

/**
 * 接口 [添加首页配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54224) 的 **React Hook**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/addIndexPage`
 * @更新时间 `2024-03-07 09:27:02`
 */
export const useApiPutKaiqiaoHisConfigCommonAddIndexPage = /*#__PURE__*/ makeRequestHook<
  ApiPutKaiqiaoHisConfigCommonAddIndexPageRequest,
  ApiPutKaiqiaoHisConfigCommonAddIndexPageRequestConfig,
  ReturnType<typeof apiPutKaiqiaoHisConfigCommonAddIndexPage>
>(apiPutKaiqiaoHisConfigCommonAddIndexPage);

/**
 * 接口 [修改首页配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54226) 的 **请求类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeIndexPage`
 * @更新时间 `2024-03-07 09:27:08`
 */
export interface ApiPutKaiqiaoHisConfigCommonChargeIndexPageRequest {}

/**
 * 接口 [修改首页配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54226) 的 **返回类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeIndexPage`
 * @更新时间 `2024-03-07 09:27:08`
 */
export type ApiPutKaiqiaoHisConfigCommonChargeIndexPageResponse = any;

/**
 * 接口 [修改首页配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54226) 的 **请求配置的类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeIndexPage`
 * @更新时间 `2024-03-07 09:27:08`
 */
type ApiPutKaiqiaoHisConfigCommonChargeIndexPageRequestConfig = Readonly<
  RequestConfig<
    'https://yapi.cqkqinfo.com/mock/1460',
    '',
    '',
    '/kaiqiao/his/config-common/chargeIndexPage',
    'data',
    string,
    string,
    true
  >
>;

/**
 * 接口 [修改首页配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54226) 的 **请求配置**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeIndexPage`
 * @更新时间 `2024-03-07 09:27:08`
 */
const apiPutKaiqiaoHisConfigCommonChargeIndexPageRequestConfig: ApiPutKaiqiaoHisConfigCommonChargeIndexPageRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/kaiqiao/his/config-common/chargeIndexPage',
    method: Method.PUT,
    requestHeaders: {},
    requestBodyType: RequestBodyType.raw,
    responseBodyType: ResponseBodyType.raw,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: [],
    requestDataOptional: true,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiPutKaiqiaoHisConfigCommonChargeIndexPage',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  };

/**
 * 接口 [修改首页配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54226) 的 **请求函数**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeIndexPage`
 * @更新时间 `2024-03-07 09:27:08`
 */
export const apiPutKaiqiaoHisConfigCommonChargeIndexPage = /*#__PURE__*/ (
  requestData?: ApiPutKaiqiaoHisConfigCommonChargeIndexPageRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiPutKaiqiaoHisConfigCommonChargeIndexPageResponse>(
    prepare(apiPutKaiqiaoHisConfigCommonChargeIndexPageRequestConfig, requestData),
    ...args,
  );
};

apiPutKaiqiaoHisConfigCommonChargeIndexPage.requestConfig = apiPutKaiqiaoHisConfigCommonChargeIndexPageRequestConfig;

/**
 * 接口 [修改首页配置↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54226) 的 **React Hook**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeIndexPage`
 * @更新时间 `2024-03-07 09:27:08`
 */
export const useApiPutKaiqiaoHisConfigCommonChargeIndexPage = /*#__PURE__*/ makeRequestHook<
  ApiPutKaiqiaoHisConfigCommonChargeIndexPageRequest,
  ApiPutKaiqiaoHisConfigCommonChargeIndexPageRequestConfig,
  ReturnType<typeof apiPutKaiqiaoHisConfigCommonChargeIndexPage>
>(apiPutKaiqiaoHisConfigCommonChargeIndexPage);

/**
 * 接口 [获取自助机配置（功能模块、身份识别、支付方式）↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54228) 的 **请求类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getPageConfig`
 * @更新时间 `2024-03-07 09:27:39`
 */
export interface ApiGetKaiqiaoHisConfigCommonGetPageConfigRequest {
  /**
   * 医院ID
   */
  hisId: string;
  /**
   * 设备编号
   */
  deviceNo: string;
}

/**
 * 接口 [获取自助机配置（功能模块、身份识别、支付方式）↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54228) 的 **返回类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getPageConfig`
 * @更新时间 `2024-03-07 09:27:39`
 */
export type ApiGetKaiqiaoHisConfigCommonGetPageConfigResponse = any;

/**
 * 接口 [获取自助机配置（功能模块、身份识别、支付方式）↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54228) 的 **请求配置的类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getPageConfig`
 * @更新时间 `2024-03-07 09:27:39`
 */
type ApiGetKaiqiaoHisConfigCommonGetPageConfigRequestConfig = Readonly<
  RequestConfig<
    'https://yapi.cqkqinfo.com/mock/1460',
    '',
    '',
    '/kaiqiao/his/config-common/getPageConfig',
    'data',
    string,
    'hisId' | 'deviceNo',
    false
  >
>;

/**
 * 接口 [获取自助机配置（功能模块、身份识别、支付方式）↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54228) 的 **请求配置**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getPageConfig`
 * @更新时间 `2024-03-07 09:27:39`
 */
const apiGetKaiqiaoHisConfigCommonGetPageConfigRequestConfig: ApiGetKaiqiaoHisConfigCommonGetPageConfigRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/kaiqiao/his/config-common/getPageConfig',
    method: Method.GET,
    requestHeaders: {},
    requestBodyType: RequestBodyType.query,
    responseBodyType: ResponseBodyType.raw,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: ['hisId', 'deviceNo'],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiGetKaiqiaoHisConfigCommonGetPageConfig',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  };

/**
 * 接口 [获取自助机配置（功能模块、身份识别、支付方式）↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54228) 的 **请求函数**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getPageConfig`
 * @更新时间 `2024-03-07 09:27:39`
 */
export const apiGetKaiqiaoHisConfigCommonGetPageConfig = /*#__PURE__*/ (
  requestData: ApiGetKaiqiaoHisConfigCommonGetPageConfigRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiGetKaiqiaoHisConfigCommonGetPageConfigResponse>(
    prepare(apiGetKaiqiaoHisConfigCommonGetPageConfigRequestConfig, requestData),
    ...args,
  );
};

apiGetKaiqiaoHisConfigCommonGetPageConfig.requestConfig = apiGetKaiqiaoHisConfigCommonGetPageConfigRequestConfig;

/**
 * 接口 [获取自助机配置（功能模块、身份识别、支付方式）↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54228) 的 **React Hook**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getPageConfig`
 * @更新时间 `2024-03-07 09:27:39`
 */
export const useApiGetKaiqiaoHisConfigCommonGetPageConfig = /*#__PURE__*/ makeRequestHook<
  ApiGetKaiqiaoHisConfigCommonGetPageConfigRequest,
  ApiGetKaiqiaoHisConfigCommonGetPageConfigRequestConfig,
  ReturnType<typeof apiGetKaiqiaoHisConfigCommonGetPageConfig>
>(apiGetKaiqiaoHisConfigCommonGetPageConfig);

/**
 * 接口 [获取设备类型↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54230) 的 **请求类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceType`
 * @更新时间 `2024-03-07 09:27:47`
 */
export interface ApiGetKaiqiaoHisConfigCommonGetDeviceTypeRequest {
  hisId: string;
}

/**
 * 接口 [获取设备类型↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54230) 的 **返回类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceType`
 * @更新时间 `2024-03-07 09:27:47`
 */
export type ApiGetKaiqiaoHisConfigCommonGetDeviceTypeResponse = any;

/**
 * 接口 [获取设备类型↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54230) 的 **请求配置的类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceType`
 * @更新时间 `2024-03-07 09:27:47`
 */
type ApiGetKaiqiaoHisConfigCommonGetDeviceTypeRequestConfig = Readonly<
  RequestConfig<
    'https://yapi.cqkqinfo.com/mock/1460',
    '',
    '',
    '/kaiqiao/his/config-common/getDeviceType',
    'data',
    string,
    'hisId',
    false
  >
>;

/**
 * 接口 [获取设备类型↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54230) 的 **请求配置**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceType`
 * @更新时间 `2024-03-07 09:27:47`
 */
const apiGetKaiqiaoHisConfigCommonGetDeviceTypeRequestConfig: ApiGetKaiqiaoHisConfigCommonGetDeviceTypeRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/kaiqiao/his/config-common/getDeviceType',
    method: Method.GET,
    requestHeaders: {},
    requestBodyType: RequestBodyType.query,
    responseBodyType: ResponseBodyType.raw,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: ['hisId'],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiGetKaiqiaoHisConfigCommonGetDeviceType',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  };

/**
 * 接口 [获取设备类型↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54230) 的 **请求函数**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceType`
 * @更新时间 `2024-03-07 09:27:47`
 */
export const apiGetKaiqiaoHisConfigCommonGetDeviceType = /*#__PURE__*/ (
  requestData: ApiGetKaiqiaoHisConfigCommonGetDeviceTypeRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiGetKaiqiaoHisConfigCommonGetDeviceTypeResponse>(
    prepare(apiGetKaiqiaoHisConfigCommonGetDeviceTypeRequestConfig, requestData),
    ...args,
  );
};

apiGetKaiqiaoHisConfigCommonGetDeviceType.requestConfig = apiGetKaiqiaoHisConfigCommonGetDeviceTypeRequestConfig;

/**
 * 接口 [获取设备类型↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54230) 的 **React Hook**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `GET /kaiqiao/his/config-common/getDeviceType`
 * @更新时间 `2024-03-07 09:27:47`
 */
export const useApiGetKaiqiaoHisConfigCommonGetDeviceType = /*#__PURE__*/ makeRequestHook<
  ApiGetKaiqiaoHisConfigCommonGetDeviceTypeRequest,
  ApiGetKaiqiaoHisConfigCommonGetDeviceTypeRequestConfig,
  ReturnType<typeof apiGetKaiqiaoHisConfigCommonGetDeviceType>
>(apiGetKaiqiaoHisConfigCommonGetDeviceType);

/**
 * 接口 [修改设备↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54232) 的 **请求类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeDevice`
 * @更新时间 `2024-03-07 09:27:53`
 */
export interface ApiPutKaiqiaoHisConfigCommonChargeDeviceRequest {}

/**
 * 接口 [修改设备↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54232) 的 **返回类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeDevice`
 * @更新时间 `2024-03-07 09:27:53`
 */
export type ApiPutKaiqiaoHisConfigCommonChargeDeviceResponse = any;

/**
 * 接口 [修改设备↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54232) 的 **请求配置的类型**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeDevice`
 * @更新时间 `2024-03-07 09:27:53`
 */
type ApiPutKaiqiaoHisConfigCommonChargeDeviceRequestConfig = Readonly<
  RequestConfig<
    'https://yapi.cqkqinfo.com/mock/1460',
    '',
    '',
    '/kaiqiao/his/config-common/chargeDevice',
    'data',
    string,
    string,
    true
  >
>;

/**
 * 接口 [修改设备↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54232) 的 **请求配置**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeDevice`
 * @更新时间 `2024-03-07 09:27:53`
 */
const apiPutKaiqiaoHisConfigCommonChargeDeviceRequestConfig: ApiPutKaiqiaoHisConfigCommonChargeDeviceRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/kaiqiao/his/config-common/chargeDevice',
    method: Method.PUT,
    requestHeaders: {},
    requestBodyType: RequestBodyType.raw,
    responseBodyType: ResponseBodyType.raw,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: [],
    requestDataOptional: true,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiPutKaiqiaoHisConfigCommonChargeDevice',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  };

/**
 * 接口 [修改设备↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54232) 的 **请求函数**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeDevice`
 * @更新时间 `2024-03-07 09:27:53`
 */
export const apiPutKaiqiaoHisConfigCommonChargeDevice = /*#__PURE__*/ (
  requestData?: ApiPutKaiqiaoHisConfigCommonChargeDeviceRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiPutKaiqiaoHisConfigCommonChargeDeviceResponse>(
    prepare(apiPutKaiqiaoHisConfigCommonChargeDeviceRequestConfig, requestData),
    ...args,
  );
};

apiPutKaiqiaoHisConfigCommonChargeDevice.requestConfig = apiPutKaiqiaoHisConfigCommonChargeDeviceRequestConfig;

/**
 * 接口 [修改设备↗](https://yapi.cqkqinfo.com/project/1460/interface/api/54232) 的 **React Hook**
 *
 * @分类 [公共分类↗](https://yapi.cqkqinfo.com/project/1460/interface/api/cat_10541)
 * @请求头 `PUT /kaiqiao/his/config-common/chargeDevice`
 * @更新时间 `2024-03-07 09:27:53`
 */
export const useApiPutKaiqiaoHisConfigCommonChargeDevice = /*#__PURE__*/ makeRequestHook<
  ApiPutKaiqiaoHisConfigCommonChargeDeviceRequest,
  ApiPutKaiqiaoHisConfigCommonChargeDeviceRequestConfig,
  ReturnType<typeof apiPutKaiqiaoHisConfigCommonChargeDevice>
>(apiPutKaiqiaoHisConfigCommonChargeDevice);

/* prettier-ignore-end */
