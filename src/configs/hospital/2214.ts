import BaseHosp from './_BaseHosp';
import logo22214 from '@src/images/logo-2214.png';
import { request } from 'parsec-admin';
/**
 * 儿童医院医院配置
 */
class Hosp2214 extends BaseHosp {
  get isICU() {
    return true;
  }
  get logo() {
    return logo22214;
  }

  async uploadApi(file: File) {
    // 儿童医院电信云oss
    const ossSign = await request.get('/common/cooperate/getOssConfig');
    const uploadUrl = ossSign.data.data.uploadUrl;
    const resultUrl = ossSign.data.data.ossFileUrl;
    await fetch(uploadUrl, {
      body: file,
      method: 'PUT',
      headers: { 'x-amz-acl': 'public-read' }
    });
    return resultUrl;
  }
}

export default Hosp2214;
