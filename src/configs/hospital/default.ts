import BaseHosp from './_BaseHosp';
import { request } from 'parsec-admin';
import { FileSignData } from '@pages/hospital/info/apis';
import base64ToBlob from '@utils/base64ToBlob';
import randomName from '@utils/randomName';
import env from '@configs/env';
import logo from '@src/images/logo.png';

class DefaultHosp extends BaseHosp {
  get isICU() {
    return false;
  }
  get logo() {
    return logo;
  }

  async uploadApi(file: File) {
    const hisId = env.hisId;
    return request
      .post<{
        code: number;
        msg: string;
        data: FileSignData;
      }>(`/mch/his/file/sign?hisId=${hisId}`, { bucket: 'ihoss', dir: 'PIC' })
      .then(
        async ({
          data: {
            data: { policy, callback, sign, accessId, host, dir }
          }
        }) => {
          const reader = new FileReader(); //创建一个字符流对象
          const suffixArr = file.name.split('.');
          const suffix = suffixArr[suffixArr.length - 1];
          reader.readAsDataURL(file);
          await (() => new Promise(resolve => (reader.onload = resolve)))();
          const S4 = (((1 + Math.random()) * 0x10000) | 0)
            .toString(16)
            .substring(1);
          const uuid =
            S4 + S4 + '-' + S4 + '-' + S4 + '-' + S4 + '-' + S4 + S4 + S4;
          const filename = randomName(dir) + uuid + `.${suffix}`;
          try {
            await request.post<string>(host, {
              key: filename,
              policy,
              callback,
              signature: sign,
              OSSAccessKeyId: accessId,
              file: base64ToBlob(reader.result)
            });
          } catch (error) {
            console.log(error);
          }
          // 出错的时候怎么办？
          console.log(`${host}/${filename}`);
          return `${host}/${filename}`;
        }
      );
  }
}

export default DefaultHosp;
