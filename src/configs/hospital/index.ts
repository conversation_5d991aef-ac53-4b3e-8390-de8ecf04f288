import env from '@configs/env';
import BaseHosp from './default';

import Config2214 from './2214';

const caches: {
  hisId: string | null;
  data: InstanceType<typeof BaseHosp>;
} = {
  hisId: null,
  data: new BaseHosp(null)
};

export default () => {
  const hisId = env.hisId;
  if (hisId === caches.hisId) {
    return caches.data;
  }
  caches.hisId = hisId;
  let data: BaseHosp;
  switch (hisId) {
    case '2214':
      data = new Config2214(hisId);
      break;
    default:
      data = new BaseHosp(hisId);
  }
  caches.data = data;
  return data;
};
