import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { LoginInfo } from './d';
import { FileSignData } from '@pages/hospital/info/apis';
import base64ToBlob from '@utils/base64ToBlob';
import randomName from '@utils/randomName';
import env from '@configs/env';
import { Organization } from '@src/store/organizationListStore';

export interface ApiResponse<D> {
  code: 0 | 200 | 999; // 999用户未登录
  msg: string | null;
  data?: D;
}
export interface ApiResponse1<D> {
  code: 0 | 200 | 999; // 999用户未登录
  msg: string | null;
  data: D;
}

export interface ListApiRequestParams {
  pageNum?: number;
  numPerPage?: number;
}

export type ListApiResponseData<D> = ApiResponse<{
  currentPage: number;
  totalCount: number;
  recordList: D[];
}>;

export type BillingRecord<D> = ApiResponse<{
  currentPage: number;
  totalCount: number;
  recordList: D[];
}>;

export interface ListApiResponseData2<D> {
  currentPage: number;
  totalCount: number;
  recordList: D[];
}

export interface LoginData {
  token: string;
  name: string;
  id: number | string;
}

export enum State {
  未知 = 1,
  已启用,
  未启用
}
export interface ApiResponse<D> {
  code: 0 | 200 | 999; // 999用户未登录
  data?: D;
}

export default {
  verificationCode: createApiHooks(() =>
    request
      .post<{
        result: {
          client_code: string;
          code: string;
          image_url: string;
          imgUrl: string;
        };
      }>('https://third.parsec.com.cn/piccapt/fetch')
      .then(response => {
        response.data.result.imgUrl = `https://third.parsec.com.cn${response.data.result.image_url}`;
        response.data.result.code = response.data.result.client_code;
        return Promise.resolve(response);
      })
  ),
  getCode: createApiHooks((params: { phone: string; type: 'login' }) =>
    request.post('/mch/user/doctorAccount/valicode', params)
  ),
  login: createApiHooks(
    (params: {
      username: string;
      password: string;
      phone: string;
      hisId: string;
    }) => request.post<LoginInfo>('/mch/user/doctorAccount/login', params)
  ),
  doctorSearch: createApiHooks(
    (params: { hisId: string; deptId?: string; searchKey: string }) => {
      return request.get<{ id: number; name: string; doctorId: string }[]>(
        '/mch/his/doctor/briefs',
        {
          params
        }
      );
    }
  ),
  deptSearch: createApiHooks((params: { hisId: string; searchKey: string }) => {
    return request.get<{ id: number; name: string; no: string }[]>(
      '/mch/his/dept/briefs',
      {
        params
      }
    );
  }),
  获取机构列表: createApiHooks(() => {
    return request.get<ApiResponse<Organization[]>>(
      '/mch/his/ls-main/getImList'
    );
  }),
  upload: (file: File) => {
    const hisId = env.hisId;
    return request
      .post<{
        code: number;
        msg: string;
        data: FileSignData;
      }>(`/mch/his/file/sign?hisId=${hisId}`, { bucket: 'ihoss', dir: 'PIC' })
      .then(
        async ({
          data: {
            data: { policy, callback, sign, accessId, host, dir }
          }
        }) => {
          const reader = new FileReader(); //创建一个字符流对象
          const suffixArr = file.name.split('.');
          const suffix = suffixArr[suffixArr.length - 1];
          reader.readAsDataURL(file);
          await (() => new Promise(resolve => (reader.onload = resolve)))();
          const S4 = (((1 + Math.random()) * 0x10000) | 0)
            .toString(16)
            .substring(1);
          const uuid =
            S4 + S4 + '-' + S4 + '-' + S4 + '-' + S4 + '-' + S4 + S4 + S4;
          const filename = randomName(dir) + uuid + `.${suffix}`;
          try {
            await request.post<string>(host, {
              key: filename,
              policy,
              callback,
              signature: sign,
              OSSAccessKeyId: accessId,
              file: base64ToBlob(reader.result)
            });
          } catch (error) {
            console.log(error);
          }
          // 出错的时候怎么办？
          console.log(`${host}/${filename}`);
          return `${host}/${filename}`;
        }
      );
  }
};
