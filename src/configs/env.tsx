import { switchVariable } from '@kqinfo/ui';

export interface EnvConfig {
  apiHost: string;
  env: string;
  hisId: string;
  isDoctor: boolean;
  isMicroApp: boolean;
  smartFollowUp: boolean;
  healthLearn: boolean;
  articleManage: boolean;
}
// // 独立部署医院的配置
// export const STANDALONE_HOSPITALS = {
//   // 域名 -> 配置
//   'ih.chcmu-com.cn': {
//     hisId: '2214',
//     apiHost: '/icu/api',
//     env: 'prod'
//   },
//   'ih.ybachcmu.cn': {
//     hisId: '40076',
//     apiHost: '/api',
//     env: 'prod'
//   }
// } as const;

// 获取标准版配置
function getStandardConfig() {
  const host = window.location.host.split('.')[0].split(':')[0]; // => tihs
  const hostname = window.location.hostname;
  const secondaryPath = window.location.pathname.split('/')[2];
  const arr = secondaryPath?.split('-');
  const isLocal = hostname === 'localhost';
  const path = isLocal ? 'local' : arr?.slice(0, arr.length - 1).join('-');
  const LOCAL_HISID = '40009';
  const hisId = isLocal
    ? LOCAL_HISID
    : arr?.splice(arr.length - 1, 1)[0] || '2214';
  return switchVariable({
    // 标准版配置
    default: switchVariable({
      default: {
        isDoctor: false,
        isMicroApp: false,
        apiHost: 'https://tihs.cqkqinfo.com/test-api/api',
        env: 'dev',
        smartFollowUp: false,
        healthLearn: false,
        articleManage: false,
        hisId
      },
      admin: {
        apiHost: '/api',
        env: ''
      },
      local: {
        apiHost: '/test-api/api',
        env: 'dev'
      },
      'smart-follow-up': {
        apiHost: '/api',
        env: 'prod'
      },
      'health-learn': {
        apiHost: '/api',
        env: 'prod'
      },
      'smart-follow-up-test': {
        apiHost: '/api',
        env: ''
      },
      'health-learn-test': {
        apiHost: '/api',
        env: ''
      },
      'smart-follow-up-dev': {
        apiHost: '/test-api/api',
        env: 'dev'
      },
      'health-learn-dev': {
        apiHost: '/test-api/api',
        env: 'dev'
      },
      'smart-follow-up-preview': {
        apiHost: '/preview/api',
        env: 'preview'
      },
      'health-learn-preview': {
        apiHost: '/preview/api',
        env: 'preview'
      },
      'admin-dev': {
        apiHost: '/test-api/api',
        env: 'dev'
      },
      'admin-gc': {
        apiHost: 'https://tihs.cqkqinfo.com/localization-api/api',
        env: 'dev'
      },
      'admin-kp': {
        apiHost: '/test-kp/api',
        env: 'dev'
      },
      'admin-preview': {
        apiHost: '/preview/api',
        env: 'preview'
      },
      'admin-icu-2214': {
        apiHost: 'https://ih.chcmu-com.cn/icu/api',
        env: 'prod'
      }
    })(path),
    his: {
      apiHost: '/api',
      env: 'prod'
    },
    local: {
      apiHost: '/test-api/api',
      env: 'dev'
    }
  })(host) as EnvConfig;
}

// 院内部署
const innerConfig = {
  apiHost: process.env.REACT_APP_API || '/api',
  env: process.env.REACT_APP_API || 'prod',
  hisId: String(process.env.REACT_APP_HIS_ID),
  // 下面这些变量不知道是干嘛的，如果是动态的，添加到 env 文件中
  isDoctor: false,
  isMicroApp: false,
  smartFollowUp: false,
  healthLearn: false,
  articleManage: false
};

const systemConfig =
  process.env.REACT_APP_INNER === 'true' ? innerConfig : getStandardConfig();

console.log('系统环境变量信息：', systemConfig);

export default systemConfig;
