import { RouteConfig } from 'parsec-admin';
import React, { lazy } from 'react';
// import Home from '@pages/home';
import { ContainerOutlined } from '@ant-design/icons';
import BraftEditor from '@components/braftEditor';
import MyIcon from '@components/myIcon';
import Analysis from '@pages/analysis';
import BedReservation from '@pages/bedReservation';
import ConsultationOder from '@src/pages/consultation/order/index';
import ConsultationOderDetail from '@pages/consultation/detail';
import Article from '@pages/hospital/article';
import ArticleDetail from '@pages/hospital/article/detail';
import BirthCertificate from '@pages/hospital/birthCertificate';
import Campus from '@pages/hospital/campus';
import CampusSort from '@pages/hospital/campus/sort';
import DepartmentSmart from '@pages/hospital/departmentSmart';
import DepartmentSmartSort from '@pages/hospital/departmentSmart/Sort';
import DepartmentSmartConifg from '@pages/hospital/departmentSmart/deptConfig';
import HospitalDoctorDetailSmart from '@pages/hospital/doctorDetailSmart';
import HospitalDoctorSmart from '@pages/hospital/doctorSmart';
import HospitalDoctorSmartSort from '@pages/hospital/doctorSmart/Sort';
import HospitalInfoPage from '@pages/hospital/info';
import MattersNeedingAttention from '@pages/hospital/mattersNeedingAtention';
import MattersNeedingAttentionEdit from '@pages/hospital/mattersNeedingAtention/edit';
import MobileConfiguration from '@pages/hospital/mobileConfiguration';
import SignalSource from '@pages/hospital/signalSource';
import Schedule from '@pages/hospital/signalSource/schedule';
import Index from '@pages/index';
import NoticeDetail from '@pages/notice/detail';
import NoticeList from '@pages/notice/list';
import NoticeTypes from '@pages/notice/types';
import MedicalRecordTemplate from '@pages/operate/medicalRecordTemplate';
import MedicalRecordTemplateEdit from '@pages/operate/medicalRecordTemplate/edit';
import HospitalizationAppointment from '@pages/order/hospitalizationAppointment';
import Hospital from '@pages/orgManage/hospitalList';
import PreClinicQuestionnaire from '@pages/questionnaire/preClinicQuestionnaire';
import OnlineDetails from '@pages/statistics/onlineDetails';
import Doctorloginlog from '@src/pages/statistics/doctorloginlog';
import Funlog from '@src/pages/statistics/funlog';
import Doctoronlinelog from '@src/pages/statistics/doctoronlinelog';
import TransferIntroduction from '@pages/transferIntroduction';
import TransferIntroductionOrder from '@pages/transferIntroduction/order';
import Account from '@src/pages/authority/account';
import BaseSetting from '@src/pages/authority/baseSetting';
import ClinicDiagnosis from '@src/pages/authority/clinicDiagnosis';
import DiseaseManagement from '@src/pages/authority/diseaseManagement';
import Loginlog from '@src/pages/authority/loginlog';
import Maintenance from '@src/pages/authority/maintenance';
import Role from '@src/pages/authority/role';
import RoleflowUp from '@src/pages/authority/roleflowUp';
import Systemlog from '@src/pages/authority/systemlog';
import Distribute from '@src/pages/distribute';
import FamilyOrder from '@src/pages/familyOrder';
import FamilyOrderSetting from '@src/pages/familyOrder/appoint-type';
import hospitalUniteCheckCode from '@src/pages/hospitalUnite/checkCode';
import hospitalUniteCode from '@src/pages/hospitalUnite/code';
import hospitalUnite from '@src/pages/hospitalUnite/hospital';
import hospitalUniteOrder from '@src/pages/hospitalUnite/order';
import hospitalUniteOrderDetail from '@src/pages/hospitalUnite/order/detail';
import hospitalUniteOrderDetailOpen from '@src/pages/hospitalUnite/order/openOrder';
import ComplaintList from '@src/pages/operate/complaint/List';
import ComplaintReplay from '@src/pages/operate/complaint/Replay';
import Consult from '@src/pages/operate/consult';
import ConsultDetail from '@src/pages/operate/consult/consult-detail';
import DeliveryCost from '@src/pages/operate/deliveryCost';
import Evaluate from '@src/pages/operate/evaluate';
import EvaluateDetail from '@src/pages/operate/evaluate/evaluate-detail';
import EvaluateDetailZhyy from '@src/pages/operate/evaluate/evaluate-detailZh';
import IntelligentCustomerService from '@src/pages/operate/intelligentCustomerService';
import MedicalType from '@src/pages/operate/medicalType';
import RecipeTemplate from '@src/pages/operate/recipeTemplate';
import Sample from '@src/pages/operate/sample';
import AbrOrdersStatis from '@src/pages/operate/statistics/abrOrders';
import AliStatis from '@src/pages/operate/statistics/ali';
import AtmStatis from '@src/pages/operate/statistics/atm';
import AtmClassStatis from '@src/pages/operate/statistics/atmClass';
import WechatStatis from '@src/pages/operate/statistics/wechat';
import AddOperation from '@src/pages/operation/create';
import OperationDetail from '@src/pages/operation/detail';
import OperationOrderList from '@src/pages/operation/list';
import Live from '@src/pages/operations/live';
import MdtManage from '@src/pages/mdt/mdtManage';
import MdtManageEdit from '@src/pages/mdt/mdtManage/Edit';
import MdtDetail from '@src/pages/mdt/mdtManage/detail';
import MdtDetailUpload from '@src/pages/mdt/mdtManage/upload';
import ReportDetail from '@src/pages/mdt/mdtManage/reportDetail';
import MdtStatistics from '@src/pages/mdt/mdtManage/statistics';
import MdtTeam from '@src/pages/mdt/mdtTeam';
import AddMdtTeam from '@src/pages/mdt/mdtTeam/add';
import SortMdtTeam from '@src/pages/mdt/mdtTeam/sort';
import DetailMdtTeam from '@src/pages/mdt/mdtTeam/detail';
import DeliveryList from '@src/pages/order/deliveryList';
import OnlineInquiry from '@src/pages/order/inquiry-onLine';
import ProcessingRecords from '@src/pages/order/list';
import InspectionRecords from '@src/pages/order/Inspection';
import BookingManagement from '@src/pages/order/bookingManagement';
import OrderDetails from '@src/pages/order/Inspection/OrderDetails';
import AppointmentDetails from '@src/pages/order/bookingManagement/AppointmentDetails';
import ProcessingRecordsAbnormalSmart from '@src/pages/order/listAbnormalSmart';
import ListRegister from '@src/pages/order/listRegister';
import ProcessingRecordsSmart from '@src/pages/order/listSmart';
import OrderSpecialNeed from '@src/pages/order/listSpecialNeed';
import OrderMedicine from '@src/pages/order/medicine';
import OrderMecineDetail from '@src/pages/order/medicine/medicine-detail';
import OrderDetail from '@src/pages/order/order-detail';
import OrderAbnormalSmart from '@src/pages/order/order-detail-abnormal-smart';
import OrderDetailSmart from '@src/pages/order/order-detail-smart';
import PlusSignList from '@src/pages/order/plusSignList';
import PlusSignDetails from '@src/pages/order/plusSignList/plusSignDetails';
import Doctor from '@src/pages/orgManage/doctorList';
import DoctorDetail from '@src/pages/orgManage/doctorList/detail';
import HospitalDept from '@src/pages/orgManage/hospitalDept';
import ConfigOrganization from '@src/pages/organization/config';
import Organization from '@src/pages/organization/list';
import AddOrganization from '@src/pages/organization/list/add';
import ConfigReferral from '@src/pages/organization/referral';
import ReferralAudit from '@src/pages/referral/audit';
import ReferralAuditDetail from '@src/pages/referral/audit/detail';
import ReferralDepartment from '@src/pages/referral/department';
import ReferralDoctor from '@src/pages/referral/doctor';
import ReferralDoctorDetail from '@src/pages/referral/doctor/detail';
import ReferralFunctional from '@src/pages/referral/functional';
import ReferralHospital from '@src/pages/referral/hospital';
import ReferralOut from '@src/pages/referral/out';
import ReferralOutDetail from '@src/pages/referral/out/detail';
import ReferralOverview from '@src/pages/referral/overview';
import campusReader from '@src/pages/signManage/campusReader';
import StatisticsConfigure from '@src/pages/statistics/configure';
import StatisticsDept from '@src/pages/statistics/dept';
import StatisticsDoctor from '@src/pages/statistics/doctor';
import StatisticsHospital from '@src/pages/statistics/hospital';
import StaticticsIncome from '@src/pages/statistics/income';
import MedicalStatistics from '@src/pages/statistics/medicalStatistics';
import StatisticsUser from '@src/pages/statistics/user';
import User from '@src/pages/user/list';
import Listpatients from '@src/pages/user/listpatients';
import OrganizationLocal from '@pages/localAdmin/organization';
import ScanQueue from '@pages/hospital/scanQueue';
// 自助机管理
import HomepageManagement from '@src/pages/selfService/homepageManagement/index';
import HomepageType from '@src/pages/selfService/homepageType/index';
import FunctionalManagement from '@src/pages/selfService/functionalManagement/index';
import { message } from 'antd';
import env from './env';
import {
  ParsecLibProvider,
  QuestionBankList,
  // QuestionBankList,
  QuestionnaireList,
  QuestionnaireListEdit,
  QuestionnaireListPreview
} from 'parsec-libs';
import { useHistory } from 'react-router';
import styled from 'styled-components';
import ConsultationRoom from '@pages/mdt/consultationRoom';
import AddConsultationRoom from '@pages/mdt/consultationRoom/add';
import ScheduleShift from '@pages/mdt/consultationRoom/scheduleShift';
import DoctorSort from '@src/pages/hospital/doctorSort';
import PreConsultation from '@src/pages/hospital/preConsultation';
import symptomManage from '@src/pages/hospital/preConsultation/symptomManage';
import billingSettings from '@src/pages/hospital/preConsultation/billingSettings';
import billingRecord from '@src/pages/hospital/preConsultation/billingRecord';
import BuriedPointStatistics from '@src/pages/statistics/buriedPointStatistics';
import CommendationLetter from '@src/pages/operate/commendationLetter';
import TemplateLetter from '@src/pages/operate/templateLetter';
//分销
import ShareRecord from '@src/pages/share/shareRecord';
import ShareMechanism from '@src/pages/share/shareMechanism';
import ShareDept from '@src/pages/share/shareDept';
import SharePersonnel from '@src/pages/share/sharePersonnel';

// Easy meds
import ManageCates from '@src/pages/easy-meds/manage-cates';
import ManagePrescs from '@src/pages/easy-meds/manage-prescs';
import EditPresc from '@src/pages/easy-meds/manage-prescs/edit-presc';
import PurchaseConfig from '@src/pages/easy-meds/purchase-config';

// 科研培训
import ScientificType from '@src/pages/scientificTrain/scientificType';
import TrainType from '@src/pages/scientificTrain/trainType';
import TrainContent from '@src/pages/scientificTrain/trainContent';
import TrainContentEdit from '@src/pages/scientificTrain/trainContent/edit';
import ScientificContent from '@src/pages/scientificTrain/scientificContent';
import ScientificContentEdit from '@src/pages/scientificTrain/scientificContent/edit';

// 用户注册管理
import RegisterUserList from '@src/pages/registerUserManagement/registerUser';
import RegisterUserDetail from '@src/pages/registerUserManagement/registerUser/detail';

const Provider: React.FunctionComponent<Record<string, unknown>> = props => (
  <ParsecLibProvider questionnaireConfig={{}}>
    {props.children}
  </ParsecLibProvider>
);
const QuestionnaireListPage = () => {
  const history = useHistory();
  return (
    <Provider>
      <QuestionnaireList
        onToAdd={() => {
          history.push('/survey/edit?surveyId=add');
        }}
        onToEdit={(surveyId: number, publishKey?: string, userKey?: string) => {
          const jumpUrl =
            window.location.href?.split('#')[0] +
            `#/survey/preview?publishKey=${publishKey}&userKey=${userKey}`;
          history.push(
            `/survey/edit?surveyId=${surveyId}&jumpUrl=${encodeURIComponent(
              jumpUrl
            )}`
          );
        }}
        onToPreview={(publishKey: string, userKey?: string) => {
          if (publishKey) {
            history.push(
              `/survey/preview?publishKey=${publishKey}&userKey=${userKey}`
            );
          } else {
            message.error('请先启用问卷');
          }
        }}
        showDownload
        importSurvey
      />
    </Provider>
  );
};

const routes: RouteConfig[] = [
  {
    name: '首页',
    routeId: 195,
    path: '/home',
    icon: <MyIcon type={'iconshouye'} />,
    component: Index
  },
  {
    name: '随访',
    inMenu: false,
    path: '/followUp/analysis',
    icon: <MyIcon type={'iconshouye'} />,
    component: Analysis
  },
  {
    name: '问卷管理',
    routeId: 260,
    icon: <ContainerOutlined />,
    children: [
      {
        path: '/survey/list',
        routeId: 261,
        name: '问卷',
        component: QuestionnaireListPage,
        exact: true
      },
      {
        path: '/survey/banklist',
        routeId: 262,
        name: '题库',
        component: () => {
          return (
            <Provider>
              <QuestionBankList />
            </Provider>
          );
        },
        exact: true
      },
      {
        path: '/survey/edit',
        routeId: 263,
        inMenu: false,
        name: '新增问卷',
        component: () => {
          return (
            <Provider>
              <QuestionnaireListEdit
                ageRange={false}
                everyFill={false}
                frontSurvey={false}
                formatSubmitData={(data: any) => {
                  if (data?.intro?.toHTML) {
                    data.intro = data?.intro?.toHTML();
                  }
                  return data;
                }}
                formatInitialData={(data: any) => {
                  if (data?.intro) {
                    data.intro = BraftEditor.createEditorState(
                      data?.intro || '<p></p>'
                    );
                  }
                  return data;
                }}
                introRender={<Editor />}
                quote={false}
                hideCollectUserInfo={true}
              />
            </Provider>
          );
        },
        exact: true
      },
      {
        path: '/survey/preview',
        inMenu: false,
        name: '预览问卷',
        component: () => {
          let host = `${window.location.origin}/patients/p${env.hisId}-survey${
            env.env !== 'prod' && env.env ? '-' + env.env : ''
          }/#/`;
          if (window.location.hostname === 'localhost') {
            host = `https://tihs.cqkqinfo.com/patients/p${env.hisId}-survey/#/`;
          }
          return (
            <Provider>
              <QuestionnaireListPreview previewHost={host} />
            </Provider>
          );
        },
        exact: true
      },
      {
        path: '/survey/preClinicQuestionnaire',
        name: '诊前问卷',
        routeId: 255036,
        component: PreClinicQuestionnaire
      }
    ]
  },
  {
    name: '医疗机构管理',
    routeId: 125,
    icon: <MyIcon type={'iconmenu1'} />,
    children: [
      {
        routeId: 126,
        path: '/organization/list',
        name: '医疗机构',
        component: Organization,
        children: [
          {
            path: '/organization/add',
            name: '添加医院',
            component: AddOrganization,
            isDetail: true,
            inMenu: false
          },
          {
            path: '/organization/config/:id',
            name: '医院配置',
            component: ConfigOrganization,
            isDetail: true,
            inMenu: false
          },
          {
            path: '/organization/referral/:hisId/:id',
            name: '转诊配置',
            component: ConfigReferral,
            isDetail: true,
            inMenu: false
          }
        ]
      }
    ]
  },
  {
    routeId: 110,
    name: '医院管理',
    icon: <MyIcon type={'iconhospital'} />,
    children: [
      {
        path: '/info',
        routeId: 114,
        name: '医院信息',
        component: HospitalInfoPage
      },
      // {
      //   path: '/hospital/dept',
      //   routeId: 111,
      //   name: '科室管理',
      //   component: Department
      // },
      {
        path: '/hospital/campus',
        routeId: 111,
        name: '院区管理',
        component: Campus,
        children: [
          {
            routeId: 111,
            inMenu: false,
            isDetail: true,
            path: '/hospital/campus/sort',
            name: '院区排序',
            component: CampusSort
          }
        ]
      },
      {
        path: '/hospital/preConsultation',
        routeId: 137,
        name: '诊前开单管理',
        component: PreConsultation,
        children: [
          {
            routeId: 111,
            inMenu: false,
            isDetail: true,
            path: '/hospital/preConsultation/symptomManage',
            name: '症状管理',
            component: symptomManage
          },
          {
            routeId: 111,
            inMenu: false,
            isDetail: true,
            path: '/hospital/preConsultation/billingSettings/:name/:id',
            name: '开单设置',
            component: billingSettings
          },
          {
            routeId: 111,
            inMenu: false,
            isDetail: true,
            path: '/hospital/preConsultation/billingSettings/detail/:name/:id',
            name: '详情',
            component: billingSettings
          },
          {
            inMenu: false,
            isDetail: true,
            path: '/hospital/preConsultation/billingRecord/index',
            name: '开单记录',
            component: billingRecord
          }
        ]
      },
      {
        path: '/hospital/deptSmart',
        routeId: 111,
        name: '科室管理',
        component: DepartmentSmart,
        children: [
          {
            routeId: 111,
            inMenu: false,
            isDetail: true,
            path: '/hospital/deptSmart/sort',
            name: '科室排序',
            component: DepartmentSmartSort
          }
        ]
      },
      {
        path: '/nurse',
        routeId: 112,
        name: '护士管理',
        component: HospitalDoctorSmart,
        children: [
          {
            path: '/nurse/new',
            name: '护士创建',
            component: HospitalDoctorDetailSmart,
            isDetail: true,
            inMenu: false
          },
          {
            path: '/nurse/edit/:id',
            name: '护士详情',
            component: HospitalDoctorDetailSmart,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      // {
      //   path: '/doctor',
      //   routeId: 115,
      //   name: '医生管理',
      //   component: HospitalDoctor,
      //   children: [
      //     {
      //       path: '/doctor/new',
      //       name: '医生创建',
      //       component: HospitalDoctorDetail,
      //       isDetail: true,
      //       inMenu: false
      //     },
      //     {
      //       path: '/doctor/:id/:doctorId',
      //       name: '医生详情',
      //       component: HospitalDoctorDetail,
      //       isDetail: true,
      //       inMenu: false
      //     }
      //   ]
      // },
      {
        path: '/doctorSmart',
        routeId: 115,
        name: '医生管理',
        component: HospitalDoctorSmart,
        children: [
          {
            path: '/doctorSmart/new',
            name: '医生创建',
            component: HospitalDoctorDetailSmart,
            isDetail: true,
            inMenu: false
          },
          {
            routeId: 111,
            inMenu: false,
            isDetail: true,
            path: '/doctorSmart/sort',
            name: '医生排序',
            component: HospitalDoctorSmartSort
          },
          {
            path: '/doctorSmart/edit/:id',
            name: '医生详情',
            component: HospitalDoctorDetailSmart,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        path: '/drug',
        routeId: 116,
        name: '药师管理',
        component: HospitalDoctorSmart,
        children: [
          {
            path: '/drug/new',
            name: '药师创建',
            component: HospitalDoctorDetailSmart,
            isDetail: true,
            inMenu: false
          },
          {
            path: '/drug/edit/:id',
            name: '药师详情',
            component: HospitalDoctorDetailSmart,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        path: '/signalSource',
        name: '号源管理',
        routeId: 134,
        component: SignalSource,
        children: [
          {
            path: '/signalSource/pictureSchedule/:id/:type',
            name: '图文排班',
            component: Schedule,
            isDetail: true,
            inMenu: false
          },
          {
            path: '/signalSource/videoSchedule/:id/:type',
            name: '视频排班',
            component: Schedule,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        name: '移动端配置',
        path: '/mobileConfiguration',
        routeId: 130,
        component: MobileConfiguration
      },
      {
        name: '注意事项',
        path: '/mattersNeedingAttention',
        routeId: 132,
        component: MattersNeedingAttention,
        children: [
          {
            name: '编辑注意事项',
            path: '/mattersNeedingAttention/edit/:id',
            component: MattersNeedingAttentionEdit,
            inMenu: false,
            isDetail: true
          }
        ]
      },
      {
        path: '/doctorSort',
        routeId: 136,
        name: '医生排序',
        component: DoctorSort
      },
      {
        routeId: 135,
        path: '/deptConfig',
        name: '挂号科室展示',
        component: DepartmentSmartConifg
      },
      {
        routeId: 138,
        path: '/scanQueue',
        name: '扫码签到设置',
        component: ScanQueue
      }
    ]
  },
  {
    routeId: 408,
    name: '会诊管理',
    icon: <MyIcon type={'iconorder'} />,
    children: [
      {
        path: '/consultation/order',
        routeId: 409,
        name: '会诊订单',
        component: ConsultationOder,
        children: [
          {
            path: '/consultation/order/:id',
            name: '订单详情',
            component: ConsultationOderDetail,
            isDetail: true,
            inMenu: false
          }
        ]
      }
    ]
  },
  {
    routeId: 244,
    name: '内容管理',
    icon: <MyIcon type={'iconhospital'} />,
    children: [
      {
        path: '/content/deptDistribute',
        routeId: 1113,
        name: '科室分布',
        component: Distribute
      },
      {
        path: '/content/article',
        routeId: 245,
        name: '医患圈管理',
        component: Article,
        children: [
          {
            path: '/content/article/:id',
            name: '医患圈管理详情',
            component: ArticleDetail,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        path: '/content/notice',
        routeId: 255,
        name: '文章管理',
        component: NoticeList,
        children: [
          {
            path: '/content/notice/:id',
            name: '文章管理详情',
            component: NoticeDetail,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        path: '/content/noticeTypes',
        name: '文章类型',
        component: NoticeTypes,
        isDetail: true,
        inMenu: false
      },
      {
        path: '/content/scientificTrain/scientificType',
        name: '科研协作类型',
        component: ScientificType,
        routeId: 25503757
      },
      {
        path: '/content/scientificTrain/trainType',
        name: '培训类型',
        component: TrainType,
        routeId: 25503752
      },
      {
        path: '/content/scientificTrain/trainContent',
        name: '培训内容管理',
        component: TrainContent,
        routeId: 25503742,
        children: [
          {
            path: '/content/scientificTrain/trainContent/add',
            name: '新增培训',
            component: TrainContentEdit,
            isDetail: true,
            inMenu: false
          },
          {
            path: '/content/scientificTrain/trainContent/edit/:id',
            name: '编辑培训',
            component: TrainContentEdit,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        path: '/content/scientificTrain/scientificContent',
        name: '科研协作管理',
        component: ScientificContent,
        routeId: 25503747,
        children: [
          {
            path: '/content/scientificTrain/scientificContent/add',
            name: '新增科研协作',
            component: ScientificContentEdit,
            isDetail: true,
            inMenu: false
          },
          {
            path: '/content/scientificTrain/scientificContent/edit/:id',
            name: '编辑科研协作',
            component: ScientificContentEdit,
            isDetail: true,
            inMenu: false
          }
        ]
      }
    ]
  },
  {
    routeId: 225,
    name: '手术管理',
    icon: <MyIcon type={'iconhospital'} />,
    children: [
      {
        path: '/operation/list',
        routeId: 227,
        name: '手术订单',
        component: OperationOrderList,
        children: [
          {
            path: '/operation/list/add',
            name: '添加预约',
            component: AddOperation,
            isDetail: true,
            inMenu: false
          },
          {
            path: '/operation/list/operation/:id',
            name: '详情',
            component: OperationDetail,
            isDetail: true,
            inMenu: false
          }
        ]
      }
    ]
  },
  {
    path: '/operate',
    routeId: 123,
    name: '运营管理',
    icon: <MyIcon type={'iconoperate'} />,
    children: [
      {
        path: '/operate/medicalRecordTemplate',
        name: '病历模版管理',
        component: MedicalRecordTemplate,
        routeId: 255037,
        children: [
          {
            path: '/operate/medicalRecordTemplate/add',
            name: '新增病历模版',
            component: MedicalRecordTemplateEdit,
            isDetail: true,
            inMenu: false
          },
          {
            path: '/operate/medicalRecordTemplate/update/:id',
            name: '编辑病历模版',
            component: MedicalRecordTemplateEdit,
            isDetail: true,
            inMenu: false
          },
          {
            path: '/operate/medicalRecordTemplate/detail/:id/:isDetail',
            name: '病历模版详情',
            component: MedicalRecordTemplateEdit,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        routeId: 129,
        path: '/operate/consult/list',
        name: '问诊监管',
        component: Consult,
        children: [
          {
            path: '/operate/consult/list/:id',
            name: '问诊监管详情',
            component: ConsultDetail,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        routeId: 124,
        path: '/operate/evaluate',
        name: '评价管理',
        component: Evaluate,
        children: [
          {
            path: '/operate/evaluate/:id',
            name: '评价详情',
            component: EvaluateDetail,
            isDetail: true,
            inMenu: false
          },
          {
            path: '/operate/evaluateZhyy/:id',
            name: '评价详情',
            component: EvaluateDetailZhyy,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        routeId: 193,
        path: '/operate/complaint/list',
        name: '意见反馈',
        component: ComplaintList,
        children: [
          {
            path: '/operate/complaint/list/:id',
            name: '反馈回复',
            component: ComplaintReplay,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        routeId: 194,
        path: '/operate/sample',
        name: '常用语管理',
        component: Sample
      },
      {
        routeId: 282,
        path: '/operate/recipeTemplate',
        name: '经典方管理',
        component: RecipeTemplate
      },
      {
        routeId: 1116,
        path: '/operate/deliveryCost',
        name: '配送费管理',
        component: DeliveryCost
      },
      {
        // TODO需要调整
        // routeId: 123001,
        routeId: 1116,
        path: '/operate/medicalType',
        name: '疾病编码管理',
        component: MedicalType
      },
      {
        routeId: 320,
        path: '/operate/intelligentCustomerService',
        name: '智能客服管理',
        component: IntelligentCustomerService
      },
      {
        routeId: 321,
        path: '/operate/statistics/atm',
        name: '自助机功能使用率报',
        component: AtmStatis
      },
      {
        routeId: 322,
        path: '/operate/statistics/atmClass',
        name: '自助机业务分类统计',
        component: AtmClassStatis
      },
      {
        routeId: 323,
        path: '/operate/statistics/wechat',
        name: '微信业务分类统计',
        component: WechatStatis
      },
      {
        routeId: 324,
        path: '/operate/statistics/ali',
        name: '支付宝业务分类统计',
        component: AliStatis
      },
      {
        routeId: 325,
        path: '/operate/statistics/abrorder',
        name: '异常订单处理统计',
        component: AbrOrdersStatis
      },
      {
        routeId: 255015,
        path: '/order/deliveryList',
        name: '物流统计',
        component: DeliveryList
      },
      {
        routeId: 25503721,
        path: '/operate/commendationLetter',
        name: '表扬信管理',
        component: CommendationLetter
      },
      {
        routeId: 25503722,
        path: '/operate/templateLetter',
        name: '表扬信模版管理',
        component: TemplateLetter
      }
    ]
  },
  {
    name: '用户管理',
    routeId: 120,
    icon: <MyIcon type={'iconusers'} />,
    children: [
      {
        routeId: 121,
        path: '/user/list',
        name: '用户管理',
        component: User
      },
      {
        routeId: 122,
        path: '/user/listpatients',
        name: '就诊人管理',
        component: Listpatients
      }
    ]
  },
  {
    name: '员工预约管理',
    routeId: 330,
    icon: <MyIcon type={'iconusers'} />,
    children: [
      {
        path: '/transferIntroduction',
        routeId: 331,
        name: '员工管理',
        component: TransferIntroduction
      },
      {
        path: '/transferIntroductionOrder',
        routeId: 333,
        name: '员工预约订单管理',
        component: TransferIntroductionOrder
      }
    ]
  },
  {
    name: '床位预约管理',
    routeId: 335,
    icon: <MyIcon type={'iconusers'} />,
    children: [
      {
        path: '/bedReservation',
        routeId: 336,
        name: '床位预约管理',
        component: BedReservation
      }
    ]
  },
  {
    path: '/order',
    routeId: 117,
    name: '订单管理',
    icon: <MyIcon type={'iconorder'} />,
    children: [
      {
        routeId: 274,
        path: '/order/hospitalizationAppointment',
        name: '住院预约',
        component: HospitalizationAppointment
      },
      {
        routeId: 118,
        path: '/order/list',
        name: '处理记录',
        component: ProcessingRecords,
        children: [
          {
            path: '/order/list/:id',
            name: '订单详情',
            component: OrderDetail,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        routeId: 353,
        path: '/order/inspection',
        name: '检验检查',
        component: InspectionRecords,
        children: [
          {
            path: '/order/OrderDetails/:id',
            name: '订单详情',
            component: OrderDetails,
            isDetail: true,
            inMenu: false
          },
          {
            path: '/order/RefundDetails/:id',
            name: '退费详情',
            component: () => <OrderDetails isRefund={true} />,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        routeId: 354,
        path: '/order/bookingManagement',
        name: '住院预约管理',
        component: BookingManagement,
        children: [
          {
            path: '/order/AppointmentDetails/:id',
            name: '订单详情',
            component: AppointmentDetails,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        routeId: 265,
        path: '/order/listSmart',
        name: '挂缴订单',
        component: ProcessingRecordsSmart,
        children: [
          {
            path: '/order/listSmart/:id/:uniqueCode',
            name: '订单详情',
            component: OrderDetailSmart,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        routeId: 266,
        path: '/order/listAbnormalSmart',
        name: '异常订单',
        component: ProcessingRecordsAbnormalSmart,
        children: [
          {
            path: '/order/OrderAbnormalSmart/:id',
            name: '订单详情',
            component: OrderAbnormalSmart,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        name: '会员管理中心',
        routeId: 350,
        path: '/order/listSpecialNeed',
        component: OrderSpecialNeed
      },
      {
        routeId: 352,
        path: '/order/listRegister',
        name: '第三方挂号记录',
        component: ListRegister
      },
      {
        routeId: 119,
        path: '/order/inquiryOnLine',
        name: '在线问诊',
        component: OnlineInquiry,
        children: [
          {
            path: '/order/inquiryOnLine/:id',
            name: '订单详情',
            component: OrderDetail,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        routeId: 253,
        path: '/order/plusSignList',
        name: '加号登记',
        component: PlusSignList,
        children: [
          {
            path: '/order/plusSignList/PlusSignDetails/:id',
            name: '加号登记详情',
            component: PlusSignDetails,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        routeId: 224,
        path: '/order/medicine',
        name: '处方药品',
        component: OrderMedicine,
        children: [
          {
            path: '/order/medicineDetail/:id',
            name: '处方药品详情',
            component: OrderMecineDetail,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        name: '出生证明管理',
        routeId: 272,
        path: '/birthCertificate',
        component: BirthCertificate
      }
    ]
  },
  {
    name: '医联体管理',
    icon: <MyIcon type={'iconhospital'} />,
    // routeId: 2,
    children: [
      {
        path: '/orgManage/hospitalList',
        name: '医院管理',
        routeId: 401,
        component: Hospital
      },
      {
        path: '/orgManage/hospitalDept',
        name: '科室管理',
        routeId: 402,
        component: HospitalDept
      },
      {
        path: '/orgManage/doctorList',
        name: '人员管理',
        component: Doctor,
        routeId: 403,
        children: [
          {
            path: '/orgManage/doctorList/:id',
            name: '人员详情',
            isDetail: true,
            inMenu: false,
            component: DoctorDetail
          }
        ]
      }
    ]
  },
  {
    path: '/commonReferral',
    routeId: 404,
    name: '转诊管理',
    icon: <MyIcon type={'iconhospital'} />,
    children: [
      {
        path: '/referral/out',
        component: () => <ReferralOut type='out' />,
        routeId: 406,
        name: '转出列表',
        children: [
          {
            path: '/referral/out/detail/:id',
            name: '转出详情',
            component: ReferralOutDetail,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        path: '/referral/in',
        component: () => <ReferralOut type='in' />,
        routeId: 405,
        name: '转入列表',
        children: [
          {
            path: '/referral/in/detail/:id',
            name: '转入详情',
            component: ReferralOutDetail,
            isDetail: true,
            inMenu: false
          }
        ]
      }
    ]
  },
  {
    path: '/referral',
    routeId: 246,
    name: '转诊管理',
    icon: <MyIcon type={'iconhospital'} />,
    children: [
      {
        path: '/referral/overview',
        routeId: 247,
        component: ReferralOverview,
        name: '转诊概况'
      },
      {
        path: '/referral/hospital',
        routeId: 248,
        component: ReferralHospital,
        name: '医院管理'
      },
      {
        path: '/referral/department',
        routeId: 249,
        component: ReferralDepartment,
        name: '科室管理'
      },
      {
        path: '/referral/functional',
        routeId: 250,
        component: ReferralFunctional,
        name: '职能部门'
      },
      {
        path: '/referral/audit',
        component: ReferralAudit,
        routeId: 251,
        name: '转诊列表',
        children: [
          {
            path: '/referral/audit/:id',
            name: '转诊详情',
            component: ReferralAuditDetail,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        path: '/referral/doctor',
        component: ReferralDoctor,
        routeId: 252,
        name: '医生管理',
        children: [
          {
            path: '/referral/doctor/:id',
            component: ReferralDoctorDetail,
            isDetail: true,
            inMenu: false,
            name: '医生详情'
          }
        ]
      }
    ]
  },
  {
    path: '/statistics',
    routeId: 232,
    name: '数据统计',
    icon: <MyIcon type={'iconsystem'} />,
    children: [
      {
        routeId: 233,
        path: '/statistics/doctor',
        name: '医生分析',
        component: StatisticsDoctor
      },
      {
        routeId: 234,
        path: '/statistics/dept',
        name: '科室分析',
        component: StatisticsDept
      },
      {
        path: '/statistics/hospital',
        routeId: 235,
        name: '院区分析',
        component: StatisticsHospital
      },
      {
        path: '/statistics/income',
        routeId: 235,
        name: '收入统计',
        component: StaticticsIncome
      },
      {
        routeId: 236,
        path: '/statistics/user',
        name: '用户分析',
        component: StatisticsUser
      },
      {
        routeId: 233,
        path: '/onlineDetails/doctor',
        name: '在线医生详情',
        inMenu: false,
        component: () => <OnlineDetails type={'doctor'} />,
        isDetail: true
      },
      {
        routeId: 234,
        path: '/onlineDetails/dept',
        name: '在线科室详情',
        inMenu: false,
        component: () => <OnlineDetails type={'dept'} />,
        isDetail: true
      },
      {
        path: '/statistics/configure',
        routeId: 237,
        name: '配置管理',
        component: StatisticsConfigure
      },
      {
        path: '/statistics/edit',
        routeId: 238,
        name: '首页编辑',
        component: Index
      },
      {
        path: '/statistics/medicalStatistics',
        routeId: 232001,
        name: '医保月度数据',
        component: MedicalStatistics
      },
      {
        path: '/statistics/buriedPointStatistics',
        routeId: 232002,
        name: '埋点统计',
        component: BuriedPointStatistics
      },
      {
        path: '/statistics/doctoronlinelog',
        routeId: 232003,
        name: '医生在线情况',
        component: Doctoronlinelog
      },
      {
        path: '/statistics/doctorloginlog',
        routeId: 232004,
        name: '医生登录日志',
        component: Doctorloginlog
      },
      {
        path: '/statistics/funlog',
        routeId: 232005,
        name: '功能开通日志',
        component: Funlog
      }
    ]
  },
  {
    path: '/diseaseManagement',
    routeId: 271,
    name: '病种管理',
    component: DiseaseManagement,
    icon: <MyIcon type={'iconsystem'} />
  },
  {
    path: '/familyOrder',
    name: '护理管理',
    routeId: 1117,
    icon: <MyIcon type={'iconsystem'} />,
    children: [
      {
        path: '/familyOrder/m-settings',
        routeId: 255018,
        name: '护理预约上门设置',
        component: lazy(() => import('@src/pages/familyOrder/m-settings'))
      },
      {
        path: '/familyOrder',
        routeId: 255016,
        name: '预约管理',
        component: FamilyOrder
      },
      {
        path: '/familyOrder/appoint-type',
        routeId: 255017,
        name: '预约类型',
        component: FamilyOrderSetting
      },
      {
        path: '/familyOrder/m-proj',
        routeId: 25503732,
        name: '项目管理',
        component: lazy(() => import('@src/pages/familyOrder/m-proj')),
        children: [
          {
            path: '/familyOrder/m-proj/:id',
            name: '项目详情',
            component: lazy(() =>
              import('@src/pages/familyOrder/m-proj/content')
            ),
            isDetail: true,
            inMenu: false
          },
          {
            path: '/familyOrder/m-proj-detail/:id',
            name: '项目详情',
            component: lazy(() =>
              import('@src/pages/familyOrder/m-proj/detail')
            ),
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        path: '/familyOrder/m-proj-cates',
        routeId: 25503733,
        name: '项目分类',
        component: lazy(() => import('@src/pages/familyOrder/m-proj-cates'))
      },
      {
        path: '/familyOrder/m-order',
        routeId: 25503734,
        name: '订单管理',
        component: lazy(() => import('@src/pages/familyOrder/m-order')),
        children: [
          {
            path: '/familyOrder/m-order/:id',
            name: '订单详情',
            inMenu: false,
            isDetail: true,
            component: lazy(() =>
              import('@src/pages/familyOrder/m-order/content')
            )
          }
        ]
      },
      {
        path: '/familyOrder/m-consumables',
        routeId: ********,
        name: '耗材管理',
        component: lazy(() => import('@src/pages/familyOrder/m-consumables'))
      }
    ]
  },
  {
    routeId: 1118,
    path: '/clinicDiagnosis',
    name: '门诊诊断信息',
    component: ClinicDiagnosis,
    icon: <MyIcon type={'iconsystem'} />
  },
  {
    path: '/authority',
    routeId: 106,
    name: '系统管理',
    icon: <MyIcon type={'iconsystem'} />,
    children: [
      {
        routeId: 107,
        path: '/authority/account',
        name: '账号管理',
        component: Account
      },
      {
        routeId: 218,
        path: '/authority/role',
        name: '角色管理',
        component: Role
      },
      {
        routeId: 270,
        path: '/authority/roleflowUp',
        name: '随访角色管理',
        component: () => <RoleflowUp clientType='1' />
      },
      {
        routeId: 407,
        path: '/authority/unitflowUp',
        name: '医联体角色管理',
        component: () => <RoleflowUp clientType='2' />
      },
      {
        routeId: 109,
        path: '/authority/loginlog',
        name: '登录日志',
        component: Loginlog
      },
      {
        routeId: 267,
        path: '/authority/maintenance',
        name: '维护设置',
        component: Maintenance
      },
      {
        routeId: 128,
        path: '/authority/systemlog',
        name: '系统日志',
        component: Systemlog
      },
      {
        routeId: 290,
        path: '/authority/baseSetting',
        name: '基础信息设置',
        component: BaseSetting
      }
    ]
  },
  {
    name: '医联体检验检查',
    routeId: 239,
    icon: <MyIcon type={'iconmenu1'} />,
    children: [
      {
        routeId: 240,
        path: '/hospitalUnite/hostpital',
        name: '医联体医院',
        component: hospitalUnite
      },
      {
        routeId: 243,
        path: '/hospitalUnite/order',
        name: '订单管理',
        component: hospitalUniteOrder,
        children: [
          {
            path: '/hospitalUnite/order/:id',
            name: '订单详情',
            component: hospitalUniteOrderDetail,
            isDetail: true,
            inMenu: false,
            children: [
              {
                path: '/hospitalUnite/order/:id/openOrder',
                name: '确认出单',
                component: hospitalUniteOrderDetailOpen,
                isDetail: true,
                inMenu: false
              }
            ]
          }
        ]
      },
      {
        routeId: 241,
        path: '/hospitalUnite/code',
        name: '标准码管理',
        component: hospitalUniteCode
      },
      {
        routeId: 242,
        path: '/hospitalUnite/CheckCode',
        name: '对码管理',
        component: hospitalUniteCheckCode
      }
    ]
  },
  {
    routeId: 280,
    name: '签到管理',
    icon: <MyIcon type={'iconhospital'} />,
    children: [
      {
        path: '/hospitalzone/signin',
        routeId: 281,
        name: '院区打卡',
        component: campusReader
      }
    ]
  },
  {
    routeId: 1114,
    name: '运维管理',
    icon: <MyIcon type={'iconhospital'} />,
    children: [
      {
        path: '/operations/live',
        routeId: 1115,
        name: '直播管理',
        component: Live
      }
    ]
  },
  {
    name: 'MDT门诊管理',
    routeId: 410,
    icon: <MyIcon type={'iconhospital'} />,
    children: [
      {
        path: '/mdtManage/mdt',
        name: 'MDT门诊管理',
        routeId: 411,
        component: MdtManage,
        children: [
          {
            path: `/mdtManage/mdt/mdtDetail/:id`,
            name: 'MDT门诊详情',
            component: MdtDetail,
            isDetail: true,
            inMenu: false
          },
          {
            path: `/mdtManage/mdt/edit`,
            name: 'MDT申请',
            component: MdtManageEdit,
            isDetail: true,
            inMenu: false
          },
          {
            path: `/mdtManage/mdt/upload/:id`,
            name: '上传报告',
            component: MdtDetailUpload,
            isDetail: true,
            inMenu: false
          },
          {
            path: `/mdtManage/mdt/report/:id/:mode`,
            name: '查看报告',
            component: ReportDetail,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        path: '/mdtManage/mdtTeam',
        name: 'MDT团队管理',
        routeId: 412,
        component: MdtTeam,
        children: [
          {
            path: `/mdtManage/mdtTeam/add`,
            name: '创建团队',
            component: () => <AddMdtTeam type={'add'} />,
            isDetail: true,
            inMenu: false
          },
          {
            path: `/mdtManage/mdtTeam/update/:id`,
            name: '修改团队',
            component: () => <AddMdtTeam type={'update'} />,
            isDetail: true,
            inMenu: false
          },
          {
            path: `/mdtManage/mdtTeam/sort/:id`,
            name: '团队排序',
            component: SortMdtTeam,
            isDetail: true,
            inMenu: false
          },
          {
            path: `/mdtManage/mdtTeam/detail/:id`,
            name: '团队详情',
            component: DetailMdtTeam,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        path: '/mdtManage/consultationRoom',
        name: '会诊室管理',
        routeId: 413,
        component: ConsultationRoom,
        children: [
          {
            path: `/mdtManage/consultationRoom/add`,
            name: '创建会诊室',
            component: () => <AddConsultationRoom type={'add'} />,
            isDetail: true,
            inMenu: false
          },
          {
            path: `/mdtManage/consultationRoom/update/:id`,
            name: '修改创建会诊室',
            component: () => <AddConsultationRoom type={'update'} />,
            isDetail: true,
            inMenu: false
          },
          {
            path: `/mdtManage/consultationRoom/scheduleShift/:id`,
            name: '会诊室排班',
            component: ScheduleShift,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        path: `/mdt/statistics`,
        name: 'MDT统计',
        component: MdtStatistics,
        routeId: 414
      }
    ]
  },
  {
    name: '本地监管',
    routeId: 600,
    icon: <MyIcon type={'iconhospital'} />,
    children: [
      {
        path: '/localAdmin/organization',
        name: '机构管理',
        component: OrganizationLocal,
        routeId: 601
      },
      {
        path: '/localAdmin/doctorSmart',
        routeId: 602,
        name: '医生管理',
        component: () => <HospitalDoctorSmart isLocal={true} />,
        children: [
          {
            path: '/localAdmin/doctorSmart/edit/:id/:hisId/:targetHisId',
            name: '医生详情',
            component: () => <HospitalDoctorDetailSmart isLocal={true} />,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        path: '/localAdmin/drug',
        routeId: 603,
        name: '药师管理',
        component: () => <HospitalDoctorSmart isLocal={true} />,
        children: [
          {
            path: '/localAdmin/drug/edit/:id/:hisId/:targetHisId',
            name: '药师详情',
            component: () => <HospitalDoctorDetailSmart isLocal={true} />,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        path: '/localAdmin/medicalRecordTemplate',
        name: '病历模版管理',
        component: () => <MedicalRecordTemplate isLocal={true} />,
        routeId: 604,
        children: [
          {
            path:
              '/localAdmin/medicalRecordTemplate/detail/:id/:isDetail/:targetHisId',
            name: '病历模版详情',
            component: () => <MedicalRecordTemplateEdit isLocal={true} />,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        routeId: 605,
        path: '/orderLocal/inquiryOnLine',
        name: '在线问诊',
        component: () => <OnlineInquiry isLocal={true} />,
        children: [
          {
            path: '/orderLocal/inquiryOnLine/:id/:hisId/:targetHisId',
            name: '订单详情',
            component: () => <OrderDetail isLocal={true} />,
            isDetail: true,
            inMenu: false
          }
        ]
      },
      {
        routeId: 606,
        path: '/orderLocal/medicine',
        name: '处方药品',
        component: () => <OrderMedicine isLocal={true} />,
        children: [
          {
            path: '/orderLocal/medicineDetail/:id/:hisId/:targetHisId',
            name: '处方药品详情',
            component: () => <OrderMecineDetail isLocal={true} />,
            isDetail: true,
            inMenu: false
          }
        ]
      }
    ]
  },
  {
    name: '便捷购药',
    routeId: 1500,
    path: '/easy-meds',
    icon: <MyIcon type={'iconhospital'} />,
    children: [
      {
        name: '购药配置',
        routeId: 1501,
        path: '/easy-meds/purchase-config',
        component: PurchaseConfig,
        exact: true
      },
      {
        name: '类型管理',
        routeId: 1502,
        path: '/easy-meds/manage-cates',
        component: ManageCates,
        exact: true
      },
      {
        name: '药方管理',
        routeId: 1503,
        path: '/easy-meds/manage-prescs',
        component: ManagePrescs,
        exact: true,
        children: [
          {
            name: '编辑药方',
            routeId: 1503,
            path: '/easy-meds/manage-prescs/edit/:id',
            component: EditPresc,
            isDetail: true,
            inMenu: false
          }
        ]
      }
    ]
  },
  {
    name: '分销管理',
    routeId: 25503723,
    icon: <MyIcon type={'iconhospital'} />,
    children: [
      {
        path: '/share/shareMechanism',
        name: '分销机构管理',
        routeId: 25503724,
        component: ShareMechanism
      },
      {
        path: '/share/shareDept',
        name: '分销部门管理',
        routeId: 25503725,
        component: ShareDept
      },
      {
        path: '/share/sharePersonnel',
        name: '分销员管理',
        routeId: 25503726,
        component: SharePersonnel
      },
      {
        path: `/share/shareRecord`,
        name: '分销记录',
        routeId: 25503727,
        component: ShareRecord
      }
    ]
  },
  {
    path: '/selfService',
    routeId: 25503728,
    name: '自助机管理',
    icon: <MyIcon type={'iconsystem'} />,
    children: [
      {
        path: '/selfService/homepageManagement',
        name: '首页管理',
        routeId: 25503728,
        component: HomepageManagement
      },
      {
        path: '/selfService/functionalManagement',
        name: '功能管理',
        routeId: 25503728,
        component: FunctionalManagement
      },
      {
        path: '/selfService/homepageType',
        name: '首页类型',
        routeId: 25503728,
        component: HomepageType
      }
    ]
  },
  {
    path: '/registerUserManagement',
    routeId: 25503738,
    name: '用户注册管理',
    icon: <MyIcon type={'iconsystem'} />,
    children: [
      {
        path: '/registerUserManagement/registerUser',
        name: '用户注册',
        component: RegisterUserList,
        routeId: 25503739,
        children: [
          {
            path: `/registerUserManagement/registerUser/detail/:id/:phone`,
            name: '用户详情',
            component: RegisterUserDetail,
            isDetail: true,
            inMenu: false
          }
        ]
      }
    ]
  }
];

export default routes;

export const Editor = styled(BraftEditor)`
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  .bf-content {
    height: 300px !important;
  }
  &:hover {
    border-color: #4e9fe6;
  }
`;
