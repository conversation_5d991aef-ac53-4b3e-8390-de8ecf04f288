import { useEffect, useState } from 'react';
import { Menu } from 'antd';
import DiseaseCase from './components/DiseaseCase';
import ReferralList from './components/ReferralList';
import styled from 'styled-components';
import { useLocation } from 'react-router';
import qs from 'qs';
import BasicConfiguration from '@pages/organization/config/components/basicConfiguration';
import FunctionAllocation from '@pages/organization/config/components/functionAllocation';
import ConfigStore from '@src/store/ConfigStore';

export default () => {
  const { search } = useLocation();

  const { flushConfig } = ConfigStore.useContainer();

  useEffect(() => {
    flushConfig();
  }, [flushConfig]);

  const [selectMenuKey, setSelectMenuKey] = useState(
    () => (qs.parse(search?.split('?')?.[1] || '')?.tab as string) || '1'
  );

  return (
    <Wrap>
      <div>
        <Menu
          mode='inline'
          selectedKeys={[selectMenuKey]}
          style={{ width: '100%' }}
          onClick={e => {
            setSelectMenuKey(e.key);
          }}>
          <Menu.Item key={'1'}>电子病历</Menu.Item>
          <Menu.Item key={'2'}>基础配置</Menu.Item>
          <Menu.Item key={'3'}>双向转诊配置</Menu.Item>
          <Menu.Item key={'4'}>功能配置</Menu.Item>
        </Menu>
      </div>
      <div>
        {selectMenuKey === '1' && <DiseaseCase />}
        {selectMenuKey === '2' && <BasicConfiguration />}
        {selectMenuKey === '3' && <ReferralList />}
        {selectMenuKey === '4' && <FunctionAllocation />}
      </div>
    </Wrap>
  );
};

const Wrap = styled.div`
  margin: 20px 25px;
  background-color: #fff;
  min-height: 70vh;
  display: flex;
  > div:nth-child(1) {
    width: 160px;
    flex-shrink: 0;
  }
  > div:nth-child(2) {
    flex: 1;
    padding: 0 20px 10px;
  }
`;
