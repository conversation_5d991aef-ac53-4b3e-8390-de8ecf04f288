import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ApiResponse } from '@src/configs/apis';

interface StandardDescItem {
  column: 'chiefComplaint'; // 字段名
  columnDesc: '主诉'; // 字段描述
  mapperColumn: '{$chiefComplaint}'; // 模板
}

export interface DiseaseCaseItem {
  id?: number;
  type: 'input';
  rules: any[]; // 校验规则
  hisColumnDesc: '主诉'; // 医院字段名
  hisColumn: string; // 医院字段值
  standardColumnDesc: '主诉'; // 标准字段名
  standardColumn: 'illnessDesc'; // 标准字段值
  options: []; // 选项前端自定义内容
  detailDisplay: 1; // 详情是否展示 1是 0否
  illnessDesc: '{$illnessDesc}'; // 前端渲染字段
  required?: boolean;
  len?: number;
}

export interface ReferralItem {
  id: '@natural'; //转诊配置ID
  hisId: '@natural'; //上级医院ID
  configName: '@cword(6)'; //配置名称
  referralType: ''; //转诊类别（危急情况）
  referralDirection: ''; //转诊方向（转诊类型）
  toHospitalType: ''; //入院类别（转入类别）
  addInfoUrl: 'https://httpbin.org/get?q=@word(8)'; //建档地址
  addInfoShown: '@boolean'; //是否显示建档二维码
  consentUrl: 'https://httpbin.org/get?q=@word(8)'; //知情同意书url
  consentShown: '@boolean'; //是否显示知情同意书模版
  whenCanCancel: ''; //啥时候可以取消转诊
  workFlowJson: ''; //工作流配置信息
  creatorId: '@natural'; //创建人ID
  creator: '@cname'; //创建人姓名
  eventContorl: ''; //流程事件控制
  integratedConfig: ''; //集成功能配置信息
  templateContent: ''; //模板内容
  configStatus: ''; //配置状态
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //更新时间
}

interface DiseaseCaseDetail {
  id: 1;
  hisId: 2219;
  name: '西医电子病历';
  type: 1; // 病历类型 1西医病历 2中医病历
  content: DiseaseCaseItem[];
  status: 0; // 状态：0正常 1停用
  createTime: '2021-08-01 12:00:00'; // 创建时间
  updateTime: '2021-08-01 12:00:00'; // 修改时间
  mm1: '';
}
export interface Configuration {
  medicalRecordSource: '@pick(HIS,MANUAL,SQLBUILDER)'; //病历来源 HIS院内，MANUAL手动上传，SQLBUILDER数据中台
  inquirySchedulingSource: 'HIS' | 'PLATFORM'; //问诊排班来源 HIS院内,PLATFORM平台
  pharmacySource: '@pick(HIS,CIRCULATION)'; //药房来源 HIS院内,CIRCULATION处方流转平台
  isEnableInquiryPrice: number; //是否开启统一定价 0否，1是
  enableFollow?: 0 | 1;
  internetHospitalType?: 'STANDARD' | 'SHORTCUT'; //是否开启统一定价 0否，1是
  inquiryPrice: string;
  isEnableNewCrownConsult?: 1 | 0; //是否开启新冠咨询配置 1开启，0未开启
  isEnableNursingConsult?: 1 | 0; //是否开启护理咨询配置 1开启，0未开启
  isEnableHisDistrictTag?: 1 | 0; //是否开启医生所属院区标签 1开启，0未开启
  isEnableMedicarePay: 1 | 0; // 是否开启医保支付 0-关闭，1-开启
  hisDistrictTag: "@pick('上清寺院区','冉家坝院区')"; //多个院区以','分隔
}
export interface HospitalizationEnum {
  [key: string]: {
    name?: string;
    desc?: string;
    code?: number;
  }[];
}
export default {
  电子病历模板详情: createApiHooks(
    ({ sHisId, type }: { sHisId: number; type: number }) =>
      request.get<ApiResponse<DiseaseCaseDetail>>(
        '/mch/his/medicalRecordTemplate/detail',
        {
          params: {
            sHisId,
            type
          }
        }
      )
  ),
  本地电子病历模板详情: createApiHooks(
    (params: { sHisId: number; type: number; targetHisId?: string }) =>
      request.get<ApiResponse<DiseaseCaseDetail>>(
        '/mch/his/ls-main/medicalRecordTemplateDetail',
        {
          params
        }
      )
  ),
  新增电子病历模板: createApiHooks(data =>
    request.post<ApiResponse<null>>('/mch/his/medicalRecordTemplate', data, {
      headers: {
        'Content-Type': 'application/json; charset=UTF-8'
      }
    })
  ),
  修改电子病历模板: createApiHooks(data =>
    request.put<ApiResponse<null>>('/mch/his/medicalRecordTemplate', data)
  ),
  标准字段列表: createApiHooks(sHisId =>
    request.get<ApiResponse<StandardDescItem[]>>(
      '/mch/his/medicalRecordTemplate/standardColumn',
      {
        params: {
          sHisId
        }
      }
    )
  ),
  转诊配置列表查询: createApiHooks(params =>
    request.get<ApiResponse<ReferralItem[]>>('/mch/cooperate/referral-config', {
      params
    })
  ),
  转诊配置修改: createApiHooks(data =>
    request.put<ApiResponse<null>>(
      '/mch/cooperate/referral-config/config-status',
      data,
      {
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        }
      }
    )
  ),
  转诊配置删除: createApiHooks(id =>
    request.delete<ApiResponse<null>>(`/mch/cooperate/referral-config/${id}`)
  ),
  商户配置列表: createApiHooks(() =>
    request.get<
      ApiResponse<{
        OrderCenterUniqueCodeEnum: {
          name: 'IMAGE_ASKING';
          code: number;
          desc: '图文问诊';
        }[];
      }>
    >(`/common/order/enum`)
  ),
  基础配置详情: createApiHooks(() =>
    request.get<ApiResponse<Configuration>>(
      `/mch/his/hospital-config/base-settings`
    )
  ),
  本地基础配置详情: createApiHooks(
    (params: { targetHisId?: string; hisId?: string }) =>
      request.get<ApiResponse<Configuration>>(
        `/mch/his/ls-main/base-settings`,
        { params }
      )
  ),
  基础配置保存: createApiHooks((params: Configuration) =>
    request.post<ApiResponse<any>>(
      `/mch/his/hospital-config/base-setting`,
      params,
      {
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        }
      }
    )
  ),
  查询住院预约相关状态枚举: createApiHooks(() =>
    request.get<ApiResponse<HospitalizationEnum>>(
      `/common/prescription/admission-apply/enum`
    )
  ),
  查询检验检查主订单状态枚举: createApiHooks(() =>
    request.get<ApiResponse<HospitalizationEnum>>(
      `/common/prescription/enum/test-exam-main/status`
    )
  ),
  查询单个检验检查记录状态枚举: createApiHooks(() =>
    request.get<ApiResponse<HospitalizationEnum>>(
      `/common/prescription/enum/test-exam-detail/status`
    )
  )
};
