import { Wrap } from '@pages/organization/config/components/DiseaseCase';
import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Space, Switch, Radio } from 'antd';
import { PartTitle } from '@kqinfo/ui';
import apis from '../api';
import { actionConfirm, CheckboxGroup, handleSubmit } from 'parsec-admin';
import ConfigStore from '@src/store/ConfigStore';

// const arr = [
//   '图文问诊',
//   '语音问诊',
//   '视频问诊',
//   '图文咨询',
//   '语音咨询',
//   '视频咨询'
// ];

export default () => {
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const { flushConfig, config: details } = ConfigStore.useContainer();

  const {
    data: { data: configurationList }
  } = apis.商户配置列表({
    initValue: { data: [] }
  });
  const { loading, request: save } = apis.基础配置保存({
    needInit: false
  });
  useEffect(() => {
    try {
      const arr = JSON.parse(details?.inquiryPrice || '[]');
      arr.forEach(item => {
        item.remune = item.remune / 100;
        item.price = item.price / 100;
      });
      if (
        details?.isEnableInquiryPrice &&
        typeof details?.isEnableInquiryPrice === 'string'
      ) {
        details.isEnableInquiryPrice = Number(details.isEnableInquiryPrice);
      }
      setOpen(!!details?.isEnableInquiryPrice);
      form.setFieldsValue({
        ...details,
        inquiryPrice: arr,
        isEnableInquiryPrice: !!details?.isEnableInquiryPrice,
        medicalRecordSource: details?.medicalRecordSource
          ? details?.medicalRecordSource.split(',')
          : []
      });
    } catch (e) {
      console.log(e);
    }
  }, [form, details]);
  const getName = (number: number) => {
    const inquiryPriceList = form.getFieldValue('inquiryPrice');
    if (
      inquiryPriceList?.length &&
      configurationList?.OrderCenterUniqueCodeEnum?.length
    ) {
      const arr = configurationList?.OrderCenterUniqueCodeEnum?.filter(
        item => String(item.code) === inquiryPriceList[number]?.type
      );
      if (arr?.length) {
        return arr[0]?.desc;
      }
    }
    return '';
  };
  return (
    <Wrap>
      <div className={'title'}>基础配置</div>
      <Form form={form} initialValues={{ enableWisdomFrontDept: 0 }}>
        <PartTitle className={'formTitle'}>病历来源</PartTitle>
        <Form.Item name={'medicalRecordSource'}>
          <CheckboxGroup
            hideCheckAll
            options={[
              { label: 'MANUAL手动上传', value: 'MANUAL' },
              { label: 'HIS院内', value: 'HIS' }
            ]}
          />
        </Form.Item>
        <PartTitle className={'formTitle'}>院内数据对接方式</PartTitle>
        <Form.Item name={'hisInterfaceType'}>
          <Radio.Group
            options={[
              { label: 'SQLBUILDER数据中台', value: 'SQLBUILDER' },
              { label: 'HIS院内', value: 'HIS' }
            ]}
          />
        </Form.Item>
        <PartTitle className={'formTitle'}>问诊排班</PartTitle>
        <Form.Item name={'inquirySchedulingSource'}>
          <Radio.Group
            options={[
              { label: 'PLATFORM平台', value: 'PLATFORM' },
              { label: 'HIS院内', value: 'HIS' }
            ]}
          />
        </Form.Item>
        <PartTitle className={'formTitle'}>药房来源</PartTitle>
        <Form.Item name={'pharmacySource'}>
          <Radio.Group
            options={[
              { label: '院内HIS药房', value: 'HIS' },
              { label: '处方流转药房', value: 'CIRCULATION' }
            ]}
          />
        </Form.Item>
        <PartTitle className={'formTitle'}>药房地址</PartTitle>
        <Form.Item name={'pharmacyAddress'}>
          <Input
            placeholder={'请输入药房地址'}
            style={{ width: '40%', marginTop: '5px' }}
          />
        </Form.Item>

        <PartTitle className={'formTitle'}>价格设置</PartTitle>
        <Form.Item name={'isEnableInquiryPrice'} label={'统一定价'}>
          <Switch
            checked={open}
            onClick={e => {
              actionConfirm(
                () => {
                  return Promise.reject();
                },
                e ? '统一定价' : '取消统一定价',
                {
                  props: {
                    title: !e
                      ? '关闭统一定价，新增的医生问诊价格会为0元，需手动调整问诊价格，确定关闭？'
                      : '开启统一定价，会覆盖未手动调整过的医生问诊价格，确认开启？',
                    onOk: () => setOpen(e)
                  }
                }
              );
            }}
          />
        </Form.Item>
        {open && (
          <Form.List name='inquiryPrice'>
            {fields => (
              <>
                {fields.map(({ key, name, ...restField }) => {
                  return (
                    <Space
                      key={key}
                      style={{ display: 'flex', marginBottom: 8 }}
                      align='baseline'>
                      <span>{getName(name)}</span>
                      <Form.Item
                        {...restField}
                        hidden={true}
                        name={[name, 'type']}></Form.Item>
                      <Form.Item
                        {...restField}
                        valuePropName={'checked'}
                        name={[name, 'status']}>
                        <Switch />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        label={'执行价格'}
                        name={[name, 'remune']}
                        rules={[{ required: true, message: '请输入执行价格' }]}>
                        <Input placeholder='请输入价格' addonAfter={'元'} />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        label={'展示原价'}
                        name={[name, 'amount']}>
                        <Input placeholder='请输入价格' addonAfter={'元'} />
                      </Form.Item>
                    </Space>
                  );
                })}
              </>
            )}
          </Form.List>
        )}
        <PartTitle className={'formTitle'}>新冠设置</PartTitle>
        <Form.Item
          valuePropName={'checked'}
          name={'isEnableNewCrownConsult'}
          label={'新冠咨询'}>
          <Switch />
        </Form.Item>
        <PartTitle className={'formTitle'}>护理设置</PartTitle>
        <Form.Item
          valuePropName={'checked'}
          name={'isEnableNursingConsult'}
          label={'护理咨询'}>
          <Switch />
        </Form.Item>
        <PartTitle className={'formTitle'}>患者端挂号科室展示配置</PartTitle>
        <Form.Item name={'enableWisdomFrontDept'}>
          <Radio.Group
            options={[
              { label: '不启用', value: 0 },
              { label: '启用', value: 1 }
            ]}
          />
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button
            type='primary'
            htmlType='submit'
            loading={loading}
            onClick={() => {
              form.validateFields().then(value => {
                const res = JSON.parse(JSON.stringify(value || {}));
                if (res.inquiryPrice && res.inquiryPrice?.length) {
                  res.inquiryPrice.forEach(item => {
                    if (item.status) {
                      item.status = 1;
                    } else {
                      item.status = 0;
                    }
                    item.remune = item.remune * 100;
                    item.price = item.price * 100;
                  });
                }
                if (res?.inquiryPrice) {
                  res.inquiryPrice = JSON.stringify(res.inquiryPrice);
                } else {
                  res.inquiryPrice = details?.inquiryPrice;
                }
                if (res?.medicalRecordSource) {
                  res.medicalRecordSource = res.medicalRecordSource.join(',');
                }
                handleSubmit(() =>
                  save({
                    ...res,
                    isEnableInquiryPrice: res.isEnableInquiryPrice ? 1 : 0,
                    isEnableNewCrownConsult: res?.isEnableNewCrownConsult
                      ? 1
                      : 0,
                    isEnableNursingConsult: res?.isEnableNursingConsult ? 1 : 0
                  })
                ).then(() => {
                  flushConfig();
                });
              });
            }}>
            保存
          </Button>
        </Form.Item>
      </Form>
    </Wrap>
  );
};
