import { Form, Radio, Row, Switch, Button, message } from 'antd';
import React, { useEffect } from 'react';
import { Wrap } from '@pages/organization/config/components/DiseaseCase';
import ConfigStore from '@src/store/ConfigStore';
import { PartTitle } from '@kqinfo/ui';
import apis from '@pages/organization/config/api';

export default () => {
  const [form] = Form.useForm();
  const { flushConfig, config: details } = ConfigStore.useContainer();

  const { request: save, loading } = apis.基础配置保存({
    needInit: false
  });

  const handleSave = async (values: any) => {
    if (details) {
      const res = await save(values);
      if (res.code === 0) {
        message.success('保存成功');
        flushConfig();
      }
    }
  };

  useEffect(() => {
    details && form.setFieldsValue(details);
  }, [details, form]);

  return (
    <Wrap>
      <div className={'title'}>功能配置</div>
      <Form onFinish={handleSave} form={form}>
        <PartTitle className={'formTitle'}>互联网医院类型</PartTitle>
        <Row>
          <Form.Item name={'internetHospitalType'}>
            <Radio.Group>
              <Radio value={'SHORTCUT'}>快捷版互联网医院</Radio>
              <Radio value={'STANDARD'}>标准版互联网医院</Radio>
            </Radio.Group>
          </Form.Item>
        </Row>
        <PartTitle className={'formTitle'}>是否开启医保支付</PartTitle>
        <Form.Item
          name={'isEnableMedicarePay'}
          valuePropName='checked'
          normalize={v => (v ? 1 : 0)}
          label='互联网医院医保支付'>
          <Switch />
        </Form.Item>
        <PartTitle className={'formTitle'}>图文预约</PartTitle>
        <Form.Item
          name={'isEnableGraphicAppointment'}
          valuePropName='checked'
          normalize={v => (v ? 1 : 0)}
          label={'是否开启图文预约'}>
          <Switch />
        </Form.Item>
        <Form.Item
          name={'isEnableInquiryFaceAuth'}
          valuePropName='checked'
          normalize={v => (v ? 1 : 0)}
          label={'是否开启问诊人脸实名认证'}>
          <Switch />
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type='primary' htmlType='submit' loading={loading}>
            提交
          </Button>
        </Form.Item>
      </Form>
    </Wrap>
  );
};
