import React from 'react';
import { Input } from 'antd';
import { LinkButton } from 'parsec-admin';
import styled from 'styled-components';

type valueType = { label: string; value: string; id: number };

interface LabelValueProps {
  value?: valueType[];
  onChange?: (v: valueType[]) => void;
}

export default ({ value = [], onChange }: LabelValueProps) => {
  const handleAdd = () => {
    onChange?.([...value, { label: '', value: '', id: Date.now() }]);
  };

  const handleDelete = (id: number) => {
    onChange?.([...(value?.filter(item => item.id !== id) || [])]);
  };

  const handleValueChange = (index: number, val: Record<string, string>) => {
    const newValue = [...value];
    newValue[index] = { ...newValue?.[index], ...val };
    onChange?.(newValue);
  };

  return (
    <div>
      {value?.map((item, index) => (
        <Wrap key={item.id}>
          <Input
            style={{ marginRight: 20 }}
            placeholder={'显示值'}
            onChange={e => {
              const label = e.target.value;
              handleValueChange(index, { label });
            }}
            value={item.label}
          />
          <Input
            placeholder={'传递值'}
            onChange={e => {
              const value = e.target.value;
              handleValueChange(index, { value });
            }}
            value={item.value}
          />
          <div className='btns'>
            <LinkButton
              onClick={() => {
                handleDelete(item.id);
              }}>
              删除
            </LinkButton>
          </div>
        </Wrap>
      ))}
      <LinkButton onClick={handleAdd}>新增</LinkButton>
    </div>
  );
};

const Wrap = styled.div`
  display: flex;
  position: relative;
  padding-right: 60px;
  &:not(:last-child) {
    margin-bottom: 5px;
  }
  .btns {
    position: absolute;
    right: 0;
  }
`;
