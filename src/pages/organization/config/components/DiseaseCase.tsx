import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Radio, message, Input } from 'antd';
import { TableList, ArrSelect } from 'parsec-admin';
import styled from 'styled-components';
import LabelValueForm from '@pages/organization/config/components/LabelValueForm';
import apis, { DiseaseCaseItem } from '../api';
import { useParams } from 'react-router';

interface DiseaseCaseSubmitItem extends DiseaseCaseItem {
  required?: boolean;
  len?: number;
}

enum diseaseType {
  chinese = 2,
  west = 1
}

export default () => {
  const { id } = useParams<any>();
  const [type, setType] = useState<diseaseType>(diseaseType.west);
  const detailRef = useRef<any>();
  const chosenLists = useRef<string[]>([]);
  const { data: standardEnumOrigin } = apis.标准字段列表({
    params: id
  });

  const standardEnum = useMemo(() => {
    return standardEnumOrigin?.data || [];
  }, [standardEnumOrigin]);

  const transformSubmitDataToShow = (
    data: DiseaseCaseItem
  ): DiseaseCaseSubmitItem => {
    const res = { ...data } as DiseaseCaseSubmitItem;
    res.id = Math.random();
    return res;
  };

  const tableParams = useMemo(() => {
    return {
      sHisId: id,
      type
    };
  }, [type, id]);

  return (
    <Wrap>
      <div className={'title'}>病历类型</div>
      <Radio.Group value={type} onChange={e => setType(e.target.value)}>
        <Radio value={diseaseType.west}>西药病历</Radio>
        <Radio value={diseaseType.chinese}>中药病历</Radio>
      </Radio.Group>
      <div className={'title'}>病历结构</div>
      <TableList
        params={tableParams}
        edit
        succinct
        getList={useCallback(async ({ params }) => {
          return apis.电子病历模板详情.request(params).then(res => {
            detailRef.current = res.data;
            return {
              list: res?.data?.content?.map(transformSubmitDataToShow),
              total: res?.data?.content?.length
            };
          });
        }, [])}
        pageHeaderProps={false}
        columns={useMemo(
          () => [
            {
              title: '输入类型',
              dataIndex: 'type',
              width: 100,
              edit: (_: any, record: any) => {
                return (
                  <ArrSelect
                    disabled={['mainDiagnosis'].includes(record.standardColumn)}
                    options={[
                      {
                        label: '文本',
                        value: 'input'
                      },
                      {
                        label: '数字',
                        value: 'digit'
                      },
                      {
                        label: '单选',
                        value: 'radio'
                      },
                      {
                        label: '上传图片',
                        value: 'uploadImg'
                      },
                      {
                        label: '下拉选择',
                        value: 'picker'
                      }
                    ]}
                  />
                );
              }
            },
            {
              title: '医院字段名',
              dataIndex: 'hisColumnDesc',
              width: 160,
              editFormItemProps: {
                rules: [
                  {
                    required: true,
                    message: '请输入医院字段名'
                  }
                ]
              }
            },
            {
              title: '医院描述名',
              dataIndex: 'hisColumn',
              width: 160,
              editFormItemProps: {
                rules: [
                  {
                    required: true,
                    message: '请输入医院描述名'
                  }
                ]
              }
            },
            {
              title: '标准字段名',
              dataIndex: 'standardColumnDesc',
              width: 160,
              editDeps: ['standardColumnDesc'],
              edit: (
                _: any,
                __: any,
                ___: any,
                lists: any,
                { setCurrentRow }: any
              ) => {
                const standardColumnDescs = lists
                  .map((item: any) => item.standardColumnDesc)
                  .filter(Boolean);
                const options = standardEnum
                  .filter(
                    item => !standardColumnDescs.includes(item.columnDesc)
                  )
                  .map(item => ({
                    label: item.columnDesc,
                    value: item.columnDesc
                  }));
                chosenLists.current = standardColumnDescs;
                return (
                  <ArrSelect
                    allowClear={false}
                    options={options}
                    onChange={v => {
                      const rowValue = standardEnum.find(
                        item => item.columnDesc === v
                      )?.column;
                      setCurrentRow({
                        standardColumn: rowValue,
                        hisColumnDesc: v,
                        hisColumn: rowValue
                      });
                    }}
                  />
                );
              },
              editFormItemProps: {
                rules: [
                  {
                    required: true,
                    message: '请输入医院字段名'
                  }
                ]
              }
            },
            {
              title: '标准字段值',
              dataIndex: 'standardColumn',
              width: 160,
              edit: <Input disabled />,
              editFormItemProps: {
                rules: [
                  {
                    required: true,
                    message: '请输入医院字段名'
                  }
                ]
              }
            },
            {
              title: '是否必填',
              dataIndex: 'required',
              width: 100,
              edit: (_: any, record: any) => {
                return (
                  <ArrSelect
                    disabled={[
                      'chiefComplaint',
                      'medicalHistory',
                      'anamnesis',
                      'mainDiagnosis'
                    ].includes(record.standardColumn)}
                    options={[
                      {
                        label: '是',
                        value: true
                      },
                      {
                        label: '否',
                        value: false
                      }
                    ]}
                  />
                );
              },
              editFormItemProps: {
                rules: [
                  {
                    required: true,
                    message: '请选择是否必填'
                  }
                ]
              }
            },
            {
              title: '是否展示',
              dataIndex: 'detailDisplay',
              width: 100,
              edit: (_: any, record: any) => {
                return (
                  <ArrSelect
                    disabled={[
                      'chiefComplaint',
                      'medicalHistory',
                      'anamnesis',
                      'mainDiagnosis'
                    ].includes(record.standardColumn)}
                    options={[
                      {
                        label: '是',
                        value: 1
                      },
                      {
                        label: '否',
                        value: 0
                      }
                    ]}
                  />
                );
              },
              editFormItemProps: {
                rules: [
                  {
                    required: true,
                    message: '请选择是否必填'
                  }
                ]
              }
            },
            {
              title: '文字长度(字)',
              dataIndex: 'len',
              width: 130,
              editFormItemProps: {
                rules: [
                  {
                    required: true,
                    message: '请输入文字最大长度'
                  }
                ]
              }
            },
            {
              title: '可选项',
              width: 250,
              dataIndex: 'options',
              edit: <LabelValueForm />
            },
            {
              title: '操作',
              fixed: 'right',
              width: 100
            }
          ],
          [standardEnum]
        )}
        onSave={useCallback(
          async ({ list }) => {
            const submitData = {
              id: detailRef.current?.id,
              type,
              content: list
            };
            if (detailRef.current) {
              await apis.修改电子病历模板.request(submitData);
              message.success('保存成功');
              return;
            } else {
              console.log('submitData', submitData);
              await apis.新增电子病历模板.request(submitData);
              message.success('保存成功');
              return;
            }
          },
          [type]
        )}
        editDelete={useCallback(
          (_, record) =>
            !['anamnesis', 'medicalHistory', 'chiefComplaint'].includes(
              record.standardColumn
            ),
          []
        )}
        sortable
      />
    </Wrap>
  );
};

export const Wrap = styled.div`
  > .title {
    font-weight: bold;
    font-size: 18px;
    padding: 15px 0;
  }
  .formTitle {
    font-size: 15px;
    > div {
      > div {
        width: 5px;
        height: 25px;
      }
    }
  }
`;
