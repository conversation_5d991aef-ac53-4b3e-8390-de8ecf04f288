import { PlusOutlined } from '@ant-design/icons';
import { Button, Switch } from 'antd';
import {
  TableList,
  handleSubmit,
  LinkButton,
  actionConfirm,
  ActionsWrap
} from 'parsec-admin';
import { useHistory, useParams } from 'react-router';
import styled from 'styled-components';
import {
  referralDirectionOptions,
  referralTypeOptions,
  toHospitalTypeOptions
} from '@pages/organization/referral/constants';
import useApi from '../api';

export default () => {
  const { id } = useParams<any>();
  const history = useHistory();
  return (
    <Wrap>
      <div className={'title'}>双向转诊配置</div>
      <div className='table-info'>
        <div className={'title'}>配置列表</div>
        <Button
          type={'default'}
          className={'btn'}
          icon={<PlusOutlined />}
          onClick={() => history.push(`/organization/referral/${id}/add`)}>
          添加
        </Button>
        <TableList
          succinct
          columns={[
            {
              title: '配置名称',
              dataIndex: 'configName',
              width: 120
            },
            {
              title: '危急情况',
              dataIndex: 'referralType',
              render: val => {
                return getLabelFromVal(val, referralTypeOptions);
              },
              width: 120
            },
            {
              title: '转诊类型',
              dataIndex: 'referralDirection',
              render: val => {
                return getLabelFromVal(val, referralDirectionOptions);
              },
              width: 120
            },
            {
              title: '转入类别',
              dataIndex: 'toHospitalType',
              render: val => {
                return getLabelFromVal(val, toHospitalTypeOptions);
              },
              width: 120
            },
            {
              title: '创建人',
              dataIndex: 'creator',
              width: 120
            },
            {
              title: '创建时间',
              dataIndex: 'createTime',
              width: 120
            },
            {
              title: '状态',
              dataIndex: 'configStatus',
              width: 120,
              render: (_, record: any) => {
                return (
                  <Switch
                    checkedChildren='ON'
                    unCheckedChildren='OFF'
                    checked={record.configStatus}
                    onClick={() => {
                      handleSubmit(
                        () =>
                          useApi.转诊配置修改.request({
                            id: record.id,
                            configStatus: !record.configStatus
                          }),
                        '操作'
                      );
                    }}
                  />
                );
              }
            },
            {
              title: '操作',
              dataIndex: '',
              width: 120,
              render: (_, record: any) => {
                return (
                  <ActionsWrap>
                    <LinkButton
                      onClick={() => {
                        history.push(
                          `/organization/referral/${id}/${record.id}`
                        );
                      }}>
                      编辑
                    </LinkButton>
                    <LinkButton
                      onClick={() => {
                        actionConfirm(() => {
                          return useApi.转诊配置删除.request(record.id);
                        }, '删除');
                      }}>
                      删除
                    </LinkButton>
                  </ActionsWrap>
                );
              }
            }
          ]}
          getList={({ params }) => {
            return useApi.转诊配置列表查询.request(params).then(res => ({
              total: res.data?.length || 0,
              list: res.data || []
            }));
          }}
        />
      </div>
    </Wrap>
  );
};

function getLabelFromVal(val: any, options?: any[]) {
  return Array.isArray(val)
    ? val.map(i => options?.find(j => j.value === i)?.label)?.join(',')
    : '-';
}

const Wrap = styled.div`
  .title {
    font-weight: bold;
    font-size: 18px;
    padding: 15px 0;
  }
  > .table-info {
    position: relative;
    > .title {
      font-weight: bold;
      font-size: 18px;
      padding: 15px 0 30px;
    }
    > .btn {
      position: absolute;
      right: 0;
      top: 30px;
      z-index: 44;
    }
  }
`;
