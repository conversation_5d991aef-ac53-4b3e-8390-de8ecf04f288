import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ApiResponse } from '@src/configs/apis';

export default {
  检验是否存在相同的转诊配置: createApiHooks(
    (data: {
      hisId: string;
      referralType: string;
      referralDirection: string;
      toHospitalType: string;
    }) =>
      request.post<ApiResponse<null>>(
        '/mch/cooperate/referral-config/check',
        data,
        {
          headers: {
            'Content-Type': 'application/json; charset=UTF-8'
          }
        }
      )
  ),
  转诊配置详情: createApiHooks((id?: string) =>
    request.get<ApiResponse<ReferralConfigType>>(
      `/mch/cooperate/referral-config/${id}`
    )
  ),
  转诊配置新增: createApiHooks((data: any) =>
    request.post<ApiResponse<null>>('/mch/cooperate/referral-config', data, {
      headers: {
        'Content-Type': 'application/json; charset=UTF-8'
      }
    })
  ),
  转诊配置修改: createApiHooks(data =>
    request.put<ApiResponse<null>>('/mch/cooperate/referral-config', data, {
      headers: {
        'Content-Type': 'application/json; charset=UTF-8'
      }
    })
  )
};

type ReferralConfigType = {
  id: '@natural'; //转诊配置ID
  hisId: '@natural'; //上级医院ID
  configName: '@cword(6)'; //配置名称
  configInstruction: '@cword(50)'; // 配置说明
  referralType: '@pick(PZ,JZ,WZZZ,YQJJZY)'; //转诊类别（危急情况）——（PZ-平诊,JZ-急诊,WZZZ-危重转诊,YQJJZY-院前急救转运）
  referralDirection: '@pick(REFERRAL_OUT,REFERRAL_IN)'; //转诊方向（转诊类型）——（REFERRAL_OUT-转出,REFERRAL_IN-转入）
  toHospitalType: '@pick(OUTPATIENT,INHOSPITAL)'; //入院类别（转入类别）——（OUTPATIENT-门诊,INHOSPITAL-住院）
  addInfoUrl: 'https://httpbin.org/get?q=@word(8)'; //建档地址
  addInfoShown: '@boolean'; //是否显示建档二维码
  consentUrl: 'https://httpbin.org/get?q=@word(8)'; //知情同意书url
  consentShown: '@boolean'; //是否显示知情同意书模版
  whenCanChange: '@pick(NEXT_STAGE,HIDE)'; //啥时候可以修改转诊信息(NEXT_STAGE-下一节点未审核时,HIDE-隐藏按钮)
  whenCanCancel: '@pick(NEXT_STAGE_NOT_ISSUE,BEFORE_FINISH_REFERRAL,HIDE)'; //啥时候可以取消转诊(NEXT_STAGE_NOT_ISSUE-下一节点未审核时,BEFORE_FINISH_REFERRAL-转诊成功前,HIDE-隐藏按钮)
  creatorId: '@natural'; //创建人ID
  creator: '@cname'; //创建人姓名
  eventControl: {
    stepOne: {
      initiateAgain: '@boolean'; //是否可以重新发起转诊
      passedNoticeType: ['@pick(TEMPLATE,SMS)']; //审核通过消息通知方式(TEMPLATE-模版消息,SMS-短信)
      passedNoticeUser: [
        '@pick(ZCYS,ZCKSSHRY,ZCYYSZB,ZRYYSZB,ZRKSSHRY,PATIENT)'
      ]; //审核通过消息通知对象(ZCYS-转出医师、ZCKSSHRY-转出科室审核人员、ZCYYSZB-转出医院双转办、ZRYYSZB-转入医院双转办、ZRKSSHRY-转入科室审核人员、PATIENT-患者)
    }; //流程1
    stepTwo: {
      used: '@boolean'; //是否启用
      passedNoticeType: ['@pick(TEMPLATE,SMS)']; //审核通过消息通知方式(TEMPLATE-模版消息,SMS-短信)
      passedNoticeUser: [
        '@pick(ZCYS,ZCKSSHRY,ZCYYSZB,ZRYYSZB,ZRKSSHRY,PATIENT)'
      ]; //审核通过消息通知对象(ZCYS-转出医师、ZCKSSHRY-转出科室审核人员、ZCYYSZB-转出医院双转办、ZRYYSZB-转入医院双转办、ZRKSSHRY-转入科室审核人员、PATIENT-患者)
      rejectNoticeType: ['@pick(TEMPLATE,SMS)']; //审核不通过消息通知方式(TEMPLATE-模版消息,SMS-短信)
      rejectNoticeUser: [
        '@pick(ZCYS,ZCKSSHRY,ZCYYSZB,ZRYYSZB,ZRKSSHRY,PATIENT)'
      ]; //审核不通过消息通知对象(ZCYS-转出医师、ZCKSSHRY-转出科室审核人员、ZCYYSZB-转出医院双转办、ZRYYSZB-转入医院双转办、ZRKSSHRY-转入科室审核人员、PATIENT-患者)
    }; //流程2
    stepThree: {
      used: '@boolean'; //是否启用
      passedNoticeType: ['@pick(TEMPLATE,SMS)']; //审核通过消息通知方式(TEMPLATE-模版消息,SMS-短信)
      passedNoticeUser: [
        '@pick(ZCYS,ZCKSSHRY,ZCYYSZB,ZRYYSZB,ZRKSSHRY,PATIENT)'
      ]; //审核通过消息通知对象(ZCYS-转出医师、ZCKSSHRY-转出科室审核人员、ZCYYSZB-转出医院双转办、ZRYYSZB-转入医院双转办、ZRKSSHRY-转入科室审核人员、PATIENT-患者)
      rejectNoticeType: ['@pick(TEMPLATE,SMS)']; //审核不通过消息通知方式(TEMPLATE-模版消息,SMS-短信)
      rejectNoticeUser: [
        '@pick(ZCYS,ZCKSSHRY,ZCYYSZB,ZRYYSZB,ZRKSSHRY,PATIENT)'
      ]; //审核不通过消息通知对象(ZCYS-转出医师、ZCKSSHRY-转出科室审核人员、ZCYYSZB-转出医院双转办、ZRYYSZB-转入医院双转办、ZRKSSHRY-转入科室审核人员、PATIENT-患者)
    }; //流程3
    stepFour: {
      used: '@boolean'; //是否启用
      passedNoticeType: ['@pick(TEMPLATE,SMS)']; //审核通过消息通知方式(TEMPLATE-模版消息,SMS-短信)
      passedNoticeUser: [
        '@pick(ZCYS,ZCKSSHRY,ZCYYSZB,ZRYYSZB,ZRKSSHRY,PATIENT)'
      ]; //审核通过消息通知对象(ZCYS-转出医师、ZCKSSHRY-转出科室审核人员、ZCYYSZB-转出医院双转办、ZRYYSZB-转入医院双转办、ZRKSSHRY-转入科室审核人员、PATIENT-患者)
      rejectNoticeType: ['@pick(TEMPLATE,SMS)']; //审核不通过消息通知方式(TEMPLATE-模版消息,SMS-短信)
      rejectNoticeUser: [
        '@pick(ZCYS,ZCKSSHRY,ZCYYSZB,ZRYYSZB,ZRKSSHRY,PATIENT)'
      ]; //审核不通过消息通知对象(ZCYS-转出医师、ZCKSSHRY-转出科室审核人员、ZCYYSZB-转出医院双转办、ZRYYSZB-转入医院双转办、ZRKSSHRY-转入科室审核人员、PATIENT-患者)
    }; //流程4
    stepFive: {
      used: '@boolean'; //是否启用
      passedNoticeType: ['@pick(TEMPLATE,SMS)']; //审核通过消息通知方式(TEMPLATE-模版消息,SMS-短信)
      passedNoticeUser: [
        '@pick(ZCYS,ZCKSSHRY,ZCYYSZB,ZRYYSZB,ZRKSSHRY,PATIENT)'
      ]; //审核通过消息通知对象(ZCYS-转出医师、ZCKSSHRY-转出科室审核人员、ZCYYSZB-转出医院双转办、ZRYYSZB-转入医院双转办、ZRKSSHRY-转入科室审核人员、PATIENT-患者)
      rejectNoticeType: ['@pick(TEMPLATE,SMS)']; //审核不通过消息通知方式(TEMPLATE-模版消息,SMS-短信)
      rejectNoticeUser: [
        '@pick(ZCYS,ZCKSSHRY,ZCYYSZB,ZRYYSZB,ZRKSSHRY,PATIENT)'
      ]; //审核不通过消息通知对象(ZCYS-转出医师、ZCKSSHRY-转出科室审核人员、ZCYYSZB-转出医院双转办、ZRYYSZB-转入医院双转办、ZRKSSHRY-转入科室审核人员、PATIENT-患者)
    }; //流程5
    stepSix: {
      used: '@boolean'; //是否启用
      passedNoticeType: ['@pick(TEMPLATE,SMS)']; //审核通过消息通知方式(TEMPLATE-模版消息,SMS-短信)
      passedNoticeUser: [
        '@pick(ZCYS,ZCKSSHRY,ZCYYSZB,ZRYYSZB,ZRKSSHRY,PATIENT)'
      ]; //审核通过消息通知对象(ZCYS-转出医师、ZCKSSHRY-转出科室审核人员、ZCYYSZB-转出医院双转办、ZRYYSZB-转入医院双转办、ZRKSSHRY-转入科室审核人员、PATIENT-患者)
      rejectNoticeType: ['@pick(TEMPLATE,SMS)']; //审核不通过消息通知方式(TEMPLATE-模版消息,SMS-短信)
      rejectNoticeUser: [
        '@pick(ZCYS,ZCKSSHRY,ZCYYSZB,ZRYYSZB,ZRKSSHRY,PATIENT)'
      ]; //审核不通过消息通知对象(ZCYS-转出医师、ZCKSSHRY-转出科室审核人员、ZCYYSZB-转出医院双转办、ZRYYSZB-转入医院双转办、ZRKSSHRY-转入科室审核人员、PATIENT-患者)
    }; //流程6
  }; //流程事件控制JSON
  integratedConfig: {
    showAuditRecords: '@boolean'; //是否开启审核记录
    showInfo: ['@pick(OPERATOR,CREATE_TIME,TASK_NAME,REASON)']; //显示审核记录的内容（操作人-OPERATOR、操作时间-CREATE_TIME、操作内容-TASK_NAME、操作说明-REASON）
    openAddReg: '@boolean'; //是否开启加号
    opNoticeType: ['@pick(TEMPLATE,SMS)']; //门诊加号通知类型(TEMPLATE-模版消息,SMS-短信)
    opNoticeUser: ['@pick(ZCYS,ZCKSSHRY,ZCYYSZB,ZRYYSZB,ZRKSSHRY,PATIENT)']; //门诊加号通知对象(ZCYS-转出医师、ZCKSSHRY-转出科室审核人员、ZCYYSZB-转出医院双转办、ZRYYSZB-转入医院双转办、ZRKSSHRY-转入科室审核人员、PATIENT-患者)
    openInHospitalOrder: '@boolean'; //是否开启住院开单
    inNoticeType: ['@pick(TEMPLATE,SMS)']; //住院开单通知类型(TEMPLATE-模版消息,SMS-短信)
    inNoticeUser: [
      '@pick(ZCYS,ZCKSSHRY,ZCYYSZB,ZRYYSZB,ZRKSSHRY,PATIENT,ZRKSJSYS)'
    ]; //住院开单通知对象(ZCYS-转出医师、ZCKSSHRY-转出科室审核人员、ZCYYSZB-转出医院双转办、ZRYYSZB-转入医院双转办、ZRKSSHRY-转入科室审核人员、PATIENT-患者、ZRKSJSYS-转入科室接收医生)
  }; //集成功能配置信息-json格式
  templateContent: any; //模板内容JSON
  configStatus: ''; //配置状态
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //更新时间
};
