import { isLocal } from '@src/utils/common';
import { Spin } from 'antd';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router';
import useApi from './api';
import Step1 from './components/step1';
import Step2 from './components/step2';
import Step3 from './components/step3';
import Step4 from './components/step4';
import { devConfigData } from './constants';
import { transferSubmitDataToTableData } from './utils';

export default () => {
  const { hisId, id } = useParams<{ hisId: string; id: string }>();

  const [values, setValues] = useState<any>(
    isLocal()
      ? {
          hisId,
          ...devConfigData
        }
      : {
          hisId
        }
  );

  const isUpdate = useMemo(() => !!id && id !== 'add', [id]);
  const [step, setStep] = useState(isUpdate ? 0 : 1);

  const handleNext = useCallback(() => {
    setStep(prev => prev + 1);
  }, []);

  const handlePrev = useCallback(() => {
    setStep(prev => prev - 1);
  }, []);

  useEffect(() => {
    if (isUpdate) {
      useApi.转诊配置详情.request(id).then(res => {
        const resultData = { ...res.data };
        if (resultData?.templateContent?.desc) {
          resultData.templateContent.desc = transferSubmitDataToTableData(
            resultData.templateContent.desc
          );
        }
        setValues(res.data);
        setStep(1);
      });
    }
  }, [isUpdate, id]);

  return (
    <div>
      {step === 0 && (
        <div
          style={{
            height: 300,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
          <Spin />
        </div>
      )}
      {step === 1 && (
        <Step1
          isUpdate={isUpdate}
          onChange={setValues}
          onNext={handleNext}
          initialValues={values}
        />
      )}
      {step === 2 && (
        <Step2
          onChange={setValues}
          onNext={handleNext}
          onPrev={handlePrev}
          initialValues={values}
        />
      )}
      {step === 3 && (
        <Step3
          onChange={setValues}
          onNext={handleNext}
          onPrev={handlePrev}
          initialValues={values}
        />
      )}
      {step === 4 && (
        <Step4
          onChange={setValues}
          onPrev={handlePrev}
          initialValues={values}
        />
      )}
    </div>
  );
};
