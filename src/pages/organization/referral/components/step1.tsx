import {
  ProForm,
  ProFormCheckbox,
  ProFormDependency,
  ProFormRadio,
  ProFormText,
  ProFormTextArea
} from '@ant-design/pro-components';
import { CardLayout } from 'parsec-admin';
import {
  referralTypeOptions,
  referralDirectionOptions,
  toHospitalTypeOptions,
  showHideOptions,
  whenCanCancelOptions
} from '../constants';
import useApi from '../api';
import { Button } from 'antd';

export default ({
  onNext,
  onChange,
  isUpdate,
  initialValues
}: {
  isUpdate?: boolean;
  onChange?: (val?: any) => void;
  onNext?: () => void;
  initialValues?: any;
}) => {
  const {
    request: reqCheck,
    loading: checkLoading
  } = useApi.检验是否存在相同的转诊配置({
    needInit: false
  });

  const handleSubmit = async (values: any) => {
    if (!isUpdate) {
      await reqCheck({
        hisId: values.hisId,
        referralType: values.referralType,
        referralDirection: values.referralDirection,
        toHospitalType: values.toHospitalType
      });
    }
    onChange?.(prev => ({
      ...(prev || {}),
      ...values
    }));
    onNext?.();
  };

  return (
    <CardLayout title={'基础配置'}>
      <ProForm
        initialValues={initialValues}
        submitter={{
          render: props => {
            return (
              <Button
                type='primary'
                loading={checkLoading}
                onClick={() => {
                  props.submit?.();
                }}>
                下一步
              </Button>
            );
          }
        }}
        onFinish={handleSubmit}
        layout='horizontal'>
        <ProFormText name={'hisId'} hidden />
        <ProFormText
          width={500}
          formItemProps={{
            rules: [{ required: true, message: '请输入配置名称' }]
          }}
          name='configName'
          label='配置名称'
          placeholder='请输入配置名称'
        />
        <ProFormTextArea
          width={500}
          formItemProps={{
            rules: [{ required: true, message: '请输入配置说明' }]
          }}
          name='configInstruction'
          label='配置说明'
          placeholder='请输入配置说明'
        />
        <ProFormCheckbox.Group
          formItemProps={{
            rules: [{ required: true, message: '请选择转诊类别' }]
          }}
          name='referralType'
          label='转诊类别'
          options={referralTypeOptions}
        />
        <ProFormCheckbox.Group
          formItemProps={{
            rules: [{ required: true, message: '请选择转诊方向' }]
          }}
          name='referralDirection'
          label='转诊方向'
          options={referralDirectionOptions}
        />
        <ProFormCheckbox.Group
          formItemProps={{
            rules: [{ required: true, message: '请选择入院类别' }]
          }}
          name='toHospitalType'
          label='入院类别'
          options={toHospitalTypeOptions}
        />
        <ProForm.Group>
          <ProFormRadio.Group
            formItemProps={{
              rules: [{ required: true, message: '请选择是否显示建档二维码' }]
            }}
            name='addInfoShown'
            label='建档二维码'
            options={showHideOptions}
          />
          <ProFormDependency name={['addInfoShown']}>
            {({ addInfoShown }) => {
              if (addInfoShown === true) {
                return (
                  <ProFormText
                    width={500}
                    formItemProps={{
                      rules: [{ required: true, message: '请输入建档地址' }]
                    }}
                    required
                    name='addInfoUrl'
                    label='建档地址'
                    placeholder='请输入建档地址'
                  />
                );
              }
            }}
          </ProFormDependency>
        </ProForm.Group>
        {/*<ProForm.Group>*/}
        {/*  <ProFormRadio.Group*/}
        {/*    formItemProps={{*/}
        {/*      rules: [*/}
        {/*        { required: true, message: '请选择是否显示知情同意书模版' }*/}
        {/*      ]*/}
        {/*    }}*/}
        {/*    name='consentShown'*/}
        {/*    label='知情同意书模版'*/}
        {/*    options={showHideOptions}*/}
        {/*  />*/}
        {/*  <ProFormDependency name={['consentShown']}>*/}
        {/*    {({ consentShown }) => {*/}
        {/*      if (consentShown === true) {*/}
        {/*        return (*/}
        {/*          <ProForm.Item*/}
        {/*            name='consentUrl'*/}
        {/*            label='上传知情同意书'*/}
        {/*            rules={[{ required: true, message: '请上传知情同意书' }]}>*/}
        {/*            <UploadBtn arrValue={false} length={1} />*/}
        {/*          </ProForm.Item>*/}
        {/*        );*/}
        {/*      }*/}
        {/*    }}*/}
        {/*  </ProFormDependency>*/}
        {/*</ProForm.Group>*/}
        <ProFormRadio.Group
          formItemProps={{
            rules: [{ required: true, message: '请选择何时取消转诊' }]
          }}
          name='whenCanCancel'
          label='取消转诊'
          options={whenCanCancelOptions}
        />
      </ProForm>
    </CardLayout>
  );
};
