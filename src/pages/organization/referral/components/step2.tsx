import { ProForm, ProFormRadio, ProFormText } from '@ant-design/pro-components';
import { CardLayout } from 'parsec-admin';
import RadioSelect from './radioSelect';
import { Button } from 'antd';
import { useRef } from 'react';
import EditableTable from './editableTable';
import {
  chooseAllEnums,
  chooseOptions,
  descForms,
  guardianFormKeys,
  patientForm<PERSON>eys,
  referralFormKeys,
  transferFormKeys
} from '../constants';

type chooseAllType = 'patient' | 'guardian' | 'referral' | 'desc' | 'transfer';

export default ({
  onNext,
  onPrev,
  onChange,
  initialValues
}: {
  onChange?: (val?: any) => void;
  onNext?: () => void;
  onPrev?: () => void;
  initialValues?: any;
}) => {
  const formRef = useRef<any>();
  const handleChooseAll = (value: chooseAllEnums, type: chooseAllType) => {
    let keys: string[] = [];
    if (type === 'patient') {
      keys = patientFormKeys;
    }
    if (type === 'guardian') {
      keys = guardianFormKeys;
    }
    if (type === 'referral') {
      keys = referralFormKeys;
    }
    if (type === 'transfer') {
      keys = transferFormKeys;
    }
    const keyMaps = keys.reduce((prev, key) => {
      prev[key] = value;
      return prev;
    }, {} as any);
    formRef.current?.setFieldsValue(keyMaps);
  };

  const handleSubmit = async (values: any) => {
    onChange?.(prev => ({
      ...prev,
      templateContent: values
    }));
    onNext?.();
    return true;
  };

  return (
    <CardLayout title={'转诊申请单配置'}>
      <ProForm
        formRef={formRef}
        initialValues={initialValues?.templateContent}
        submitter={{
          render: props => {
            return [
              <Button
                type='primary'
                onClick={() => {
                  props.submit?.();
                }}>
                下一步
              </Button>,
              <Button onClick={onPrev}>上一步</Button>
            ];
          }
        }}
        onFinish={handleSubmit}
        layout='horizontal'>
        <ProFormText
          name={'patientName'}
          hidden
          initialValue={chooseAllEnums.All_REQUIRE}
        />
        <ProFormText
          name={'patientPhone'}
          hidden
          initialValue={chooseAllEnums.All_REQUIRE}
        />
        <RadioSelect type='patient' onChooseAll={handleChooseAll} />
        <RadioSelect type='guardian' onChooseAll={handleChooseAll} />
        <RadioSelect type='referral' onChooseAll={handleChooseAll} />
        <RadioSelect type='transfer' onChooseAll={handleChooseAll} />
        <ProForm.Group title='病情描述' />
        {descForms.map(i => (
          <ProFormRadio.Group
            formItemProps={{
              rules: [{ required: true, message: `请选择${i.label}` }]
            }}
            name={i.name}
            label={i.label}
            options={chooseOptions}
          />
        ))}
        <ProForm.Item name='desc'>
          <EditableTable />
        </ProForm.Item>
      </ProForm>
    </CardLayout>
  );
};
