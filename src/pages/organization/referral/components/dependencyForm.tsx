import { PlusOutlined } from '@ant-design/icons';
import {
  EditableProTable,
  ModalForm,
  ProForm
} from '@ant-design/pro-components';
import { Button } from 'antd';
import { useState } from 'react';

export default ({
  value,
  onChange
}: {
  value?: any;
  onChange?: (v: any) => void;
}) => {
  return (
    <ModalForm
      title={value ? '修改依赖项' : '新建依赖项'}
      trigger={
        <Button type='primary'>
          <PlusOutlined />
          新建依赖项
        </Button>
      }
      initialValues={{ dependency: value }}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true
      }}
      submitTimeout={2000}
      onFinish={async values => {
        onChange?.(values?.dependency);
        return true;
      }}>
      <ProForm.Item name='dependency'>
        <DependencyTable />
      </ProForm.Item>
    </ModalForm>
  );
};

const DependencyTable = ({
  value,
  onChange
}: {
  value?: any[];
  onChange?: (v: any[]) => void;
}) => {
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(
    () => value?.map(item => item.id) || []
  );

  const columns = [
    {
      title: '字段名',
      dataIndex: 'key'
    },
    {
      title: '取值',
      dataIndex: 'value'
    },
    {
      title: '操作',
      valueType: 'option',
      width: 250,
      render: () => {
        return null;
      }
    }
  ];
  return (
    <EditableProTable
      columns={columns}
      rowKey='id'
      value={value}
      onChange={onChange}
      recordCreatorProps={{
        newRecordType: 'dataSource',
        record: () => ({
          id: Date.now()
        })
      }}
      editable={{
        type: 'multiple',
        editableKeys,
        actionRender: (row, config, defaultDoms) => {
          return [defaultDoms.delete];
        },
        onValuesChange: (record, recordList) => {
          onChange?.(recordList);
        },
        onChange: setEditableRowKeys
      }}
    />
  );
};
