import {
  ProForm,
  ProFormCheckbox,
  ProFormRadio
} from '@ant-design/pro-components';
import { CardLayout } from 'parsec-admin';
import AuditForm from './auditForm';
import { useRef } from 'react';
import {
  enableTypeOptions,
  noticeTypeOptions,
  noticeUserOptions
} from '../constants';
import { Button } from 'antd';

export default ({
  onNext,
  onPrev,
  onChange,
  initialValues
}: {
  onChange?: (val?: any) => void;
  onNext?: () => void;
  onPrev?: () => void;
  initialValues?: any;
}) => {
  const formRef = useRef<any>();

  const handleSubmit = async (values: any) => {
    onChange?.(prev => ({
      ...prev,
      eventControl: {
        ...(prev?.eventControl || {}),
        ...values
      }
    }));
    onNext?.();
    return true;
  };

  return (
    <CardLayout title={'审核流程配置'}>
      <ProForm
        formRef={formRef}
        initialValues={initialValues?.eventControl}
        submitter={{
          render: props => {
            return [
              <Button
                type='primary'
                onClick={() => {
                  props.submit?.();
                }}>
                下一步
              </Button>,
              <Button onClick={onPrev}>上一步</Button>
            ];
          }
        }}
        onFinish={handleSubmit}
        layout='horizontal'>
        <ProForm.Group title={'流程1: 转出医师'} />
        <ProFormCheckbox.Group
          formItemProps={{
            rules: [{ required: true, message: '请选择消息通知方式' }]
          }}
          name={['stepOne', 'passedNoticeType']}
          label='消息通知方式'
          options={noticeTypeOptions}
        />
        <ProFormCheckbox.Group
          formItemProps={{
            rules: [{ required: true, message: '请选择消息通知对象' }]
          }}
          name={['stepOne', 'passedNoticeUser']}
          label='消息通知对象'
          options={noticeUserOptions}
        />
        <ProFormRadio.Group
          formItemProps={{
            rules: [{ required: true, message: '请选择是否可以重新发起转诊' }]
          }}
          name={['stepOne', 'initiateAgain']}
          label='重新发起转诊'
          options={enableTypeOptions}
        />
        <ProForm.Group title={'流程2: 转出科室审核'} />
        <AuditForm step='stepTwo' />
        <ProForm.Group title={'流程3: 转出医院审核'} />
        <AuditForm step='stepThree' />
        <ProForm.Group title={'流程4: 转入医院审核'} />
        <AuditForm step='stepFour' />
        <ProForm.Group title={'流程5: 转入科室审核'} />
        <AuditForm step='stepFive' />
        <ProForm.Group title={'流程6: 转入医师审核'} />
        <AuditForm step='stepSix' />
      </ProForm>
    </CardLayout>
  );
};
