/* eslint-disable jsx-a11y/anchor-is-valid */
import { EditableProTable, ProColumns } from '@ant-design/pro-components';
import React, { useState } from 'react';
import styled from 'styled-components';
import { componentValueEnums } from '../constants';
import DependencyForm from './dependencyForm';

import TagInput from './tagInput';

type DataSourceType = {
  id: React.Key;
  type?: string;
  label?: string;
  options?: string[];
  placeholder?: string;
  dependency?: { key: string; value?: string }[];
  rules?: string[];
};

export default ({
  value,
  onChange
}: {
  value?: DataSourceType[];
  onChange?: (v?: DataSourceType[]) => void;
}) => {
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '表单类型',
      dataIndex: 'type',
      valueType: 'select',
      valueEnum: componentValueEnums,
      width: 140
    },
    {
      title: '字段名称',
      dataIndex: 'label',
      width: 200
    },
    {
      title: '字段值',
      dataIndex: 'name',
      width: 200
    },
    {
      title: '规则',
      dataIndex: 'rules',
      valueType: 'checkbox',
      valueEnum: { required: '必填' }
    },
    {
      title: '可选项',
      dataIndex: 'options',
      renderFormItem: () => <TagInput arrayValue placeholder='新增可选项' />,
      render: (_, record) => {
        return record?.options?.join(',') || '';
      }
    },
    {
      title: '依赖项',
      dataIndex: 'dependency',
      renderFormItem: () => <DependencyForm />,
      render: (_, record) => {
        return (
          record?.dependency?.map(i => `${i.key}=${i.value}`).join(',') || ''
        );
      }
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      render: (text, record, _, action) => [
        <a
          key='editable'
          onClick={() => {
            action?.startEditable?.(record.id);
          }}>
          编辑
        </a>,
        <a
          key='delete'
          onClick={() => {
            onChange?.(value?.filter(item => item.id !== record.id));
          }}>
          删除
        </a>
      ]
    }
  ];

  return (
    <Wrap>
      <EditableProTable<DataSourceType>
        rowKey='id'
        maxLength={5}
        scroll={{
          x: 960
        }}
        recordCreatorProps={{
          record: () => ({ id: (Math.random() * 1000000).toFixed(0) })
        }}
        columns={columns}
        value={value}
        onChange={onChange}
        editable={{
          type: 'multiple',
          editableKeys,
          onSave: async (rowKey, data, row) => {
            console.log(rowKey, data, row);
          },
          onChange: setEditableRowKeys
        }}
      />
    </Wrap>
  );
};

const Wrap = styled.div`
  .ant-pro-table {
    > .ant-pro-card {
      > .ant-pro-card-body {
        padding: 0;
      }
    }
  }
`;
