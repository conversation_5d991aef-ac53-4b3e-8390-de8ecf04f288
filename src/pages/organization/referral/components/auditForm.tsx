import {
  ProForm,
  ProFormCheckbox,
  ProFormDependency,
  ProFormRadio,
  ProFormSelect
} from '@ant-design/pro-components';
import useApi from '@pages/authority/api';
import {
  enableTypeOptions,
  noticeTypeOptions,
  noticeUserOptions,
  rejectCallBackTypeOptions
} from '../constants';

export default ({ step }: { step: string }) => {
  return (
    <>
      <ProFormRadio.Group
        formItemProps={{
          rules: [{ required: true, message: '请选择是否可以流程启用' }]
        }}
        name={[step, 'used']}
        label='流程启用'
        options={enableTypeOptions}
      />
      <ProFormDependency name={[[step, 'used']]}>
        {val => {
          const used = val[step]?.used;
          if (used) {
            return (
              <>
                <ProForm.Group>
                  <ProFormSelect
                    formItemProps={{
                      rules: [{ required: true, message: '请选择审核角色' }]
                    }}
                    width={300}
                    request={async () => {
                      const res = await useApi.getRoleList
                        .request({ clientType: 2 })
                        .then(res =>
                          res.data?.recordList?.map(i => ({
                            label: i.roleName,
                            value: i.id + ''
                          }))
                        );
                      return res || [];
                    }}
                    label='审核角色'
                    name={[step, 'handleRoleId']}
                  />
                  <ProFormSelect
                    formItemProps={{
                      rules: [
                        { required: true, message: '请选择审核未通过处置' }
                      ]
                    }}
                    width={300}
                    label='审核未通过处置'
                    name={[step, 'rejectCallBackType']}
                    options={rejectCallBackTypeOptions}
                  />
                </ProForm.Group>
                <ProFormCheckbox.Group
                  formItemProps={{
                    rules: [
                      { required: true, message: '请选择审核通过消息通知方式' }
                    ]
                  }}
                  name={[step, 'passedNoticeType']}
                  label='审核通过消息通知方式'
                  options={noticeTypeOptions}
                />
                <ProFormCheckbox.Group
                  formItemProps={{
                    rules: [
                      { required: true, message: '请选择审核通过消息通知对象' }
                    ]
                  }}
                  name={[step, 'passedNoticeUser']}
                  label='审核通过消息通知对象'
                  options={noticeUserOptions}
                />
                <ProFormCheckbox.Group
                  formItemProps={{
                    rules: [
                      {
                        required: true,
                        message: '请选择审核不通过消息通知方式'
                      }
                    ]
                  }}
                  name={[step, 'rejectNoticeType']}
                  label='审核不通过消息通知方式'
                  options={noticeTypeOptions}
                />
                <ProFormCheckbox.Group
                  formItemProps={{
                    rules: [
                      {
                        required: true,
                        message: '请选择审核不通过消息通知对象'
                      }
                    ]
                  }}
                  name={[step, 'rejectNoticeUser']}
                  label='审核不通过消息通知对象'
                  options={noticeUserOptions}
                />
              </>
            );
          }
          return null;
        }}
      </ProFormDependency>
    </>
  );
};
