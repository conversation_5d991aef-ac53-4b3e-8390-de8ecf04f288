import {
  ProForm,
  ProFormCheckbox,
  ProFormDependency,
  ProFormRadio
} from '@ant-design/pro-components';
import { Button, message } from 'antd';
import { CardLayout } from 'parsec-admin';
import { useRef } from 'react';
import { useHistory } from 'react-router';
import useApi from '../api';
import {
  addNoticeUserOptions,
  enableTypeOptions,
  hospitalNoticeUserOptions,
  noticeTypeOptions,
  showInfoTypeOptions
} from '../constants';
import { transferTableDataToSubmitData } from '../utils';

export default ({
  onNext,
  onPrev,
  onChange,
  initialValues
}: {
  onChange?: (val?: any) => void;
  onNext?: () => void;
  onPrev?: () => void;
  initialValues?: any;
}) => {
  const formRef = useRef<any>();
  const history = useHistory();

  const handleSubmit = async (values: any) => {
    const val = { ...(initialValues || {}) };
    const isUpdate = val.id;
    val.integratedConfig = values;
    console.log('val', val);
    onChange?.(val);
    const submitValue = {
      ...val,
      templateContent: {
        ...(val?.templateContent || {}),
        desc: transferTableDataToSubmitData(val?.templateContent?.desc)
      }
    };
    await (isUpdate ? useApi.转诊配置修改 : useApi.转诊配置新增).request(
      submitValue
    );
    message.success(isUpdate ? '修改成功' : '新增成功');
    history.push(`/organization/config/${val.hisId}?tab=3`);
    return true;
  };

  return (
    <CardLayout title={'审核流程配置'}>
      <ProForm
        formRef={formRef}
        initialValues={initialValues?.integratedConfig}
        submitter={{
          render: props => {
            return [
              <Button
                type='primary'
                onClick={() => {
                  props.submit?.();
                }}>
                保存
              </Button>,
              <Button onClick={onPrev}>上一步</Button>
            ];
          }
        }}
        onFinish={handleSubmit}
        layout='horizontal'>
        <ProForm.Group title={'审核记录'} />
        <ProFormRadio.Group
          formItemProps={{
            rules: [{ required: true, message: '请选择是否开启' }]
          }}
          name='showAuditRecords'
          label='是否开启'
          options={enableTypeOptions}
        />
        <ProFormDependency name={['showAuditRecords']}>
          {({ showAuditRecords }) => {
            return showAuditRecords ? (
              <ProFormCheckbox.Group
                formItemProps={{
                  rules: [{ required: true, message: '请选择展示内容' }]
                }}
                name='showInfo'
                options={showInfoTypeOptions}
                label='展示内容'
              />
            ) : null;
          }}
        </ProFormDependency>

        <ProForm.Group title={'门诊加号'} />
        <ProFormRadio.Group
          formItemProps={{
            rules: [{ required: true, message: '请选择是否开启' }]
          }}
          name='openAddReg'
          label='是否开启'
          options={enableTypeOptions}
        />
        <ProFormDependency name={['openAddReg']}>
          {({ openAddReg }) => {
            return openAddReg ? (
              <>
                <ProFormCheckbox.Group
                  formItemProps={{
                    rules: [{ required: true, message: '请选择消息通知方式' }]
                  }}
                  name='opNoticeType'
                  options={noticeTypeOptions}
                  label='消息通知方式'
                />
                <ProFormCheckbox.Group
                  formItemProps={{
                    rules: [{ required: true, message: '请选择消息通知对象' }]
                  }}
                  name='opNoticeUser'
                  options={addNoticeUserOptions}
                  label='消息通知对象'
                />
              </>
            ) : null;
          }}
        </ProFormDependency>

        <ProForm.Group title={'住院开单'} />
        <ProFormRadio.Group
          formItemProps={{
            rules: [{ required: true, message: '请选择是否开启' }]
          }}
          name='openInHospitalOrder'
          label='是否开启'
          options={enableTypeOptions}
        />
        <ProFormDependency name={['openInHospitalOrder']}>
          {({ openInHospitalOrder }) => {
            return openInHospitalOrder ? (
              <>
                <ProFormCheckbox.Group
                  formItemProps={{
                    rules: [{ required: true, message: '请选择消息通知方式' }]
                  }}
                  name='inNoticeType'
                  options={noticeTypeOptions}
                  label='消息通知方式'
                />
                <ProFormCheckbox.Group
                  formItemProps={{
                    rules: [{ required: true, message: '请选择消息通知对象' }]
                  }}
                  name='inNoticeUser'
                  options={hospitalNoticeUserOptions}
                  label='消息通知对象'
                />
              </>
            ) : null;
          }}
        </ProFormDependency>
      </ProForm>
    </CardLayout>
  );
};
