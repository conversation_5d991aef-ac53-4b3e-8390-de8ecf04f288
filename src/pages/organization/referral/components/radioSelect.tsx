import { ProForm, ProFormRadio } from '@ant-design/pro-components';
import { Radio } from 'antd';
import { useMemo } from 'react';
import {
  chooseAllEnums,
  chooseAllOptions,
  chooseOptions,
  guardianForms,
  patientForms,
  referralForms,
  transferForms
} from '../constants';

type chooseAllType = 'patient' | 'guardian' | 'referral' | 'transfer';

export default ({
  type,
  onChooseAll
}: {
  type: chooseAllType;
  onChooseAll: (v: chooseAllEnums, type: chooseAllType) => void;
}) => {
  const info = useMemo(() => {
    if (type === 'patient') {
      return {
        title: '患者信息',
        options: patientForms
      };
    }
    if (type === 'guardian') {
      return {
        title: '监护人信息',
        options: guardianForms
      };
    }
    if (type === 'referral') {
      return {
        title: '转诊信息',
        options: referralForms
      };
    }
    if (type === 'transfer') {
      return {
        title: '转运信息',
        options: transferForms
      };
    }
  }, [type]);

  return (
    <ProForm.Group
      title={info?.title}
      extra={
        <Radio.Group
          onChange={e => onChooseAll(e.target.value, type)}
          options={chooseAllOptions}
        />
      }>
      {info?.options?.map(i => (
        <ProFormRadio.Group
          formItemProps={{
            rules: [{ required: true, message: `请选择${i.label}` }]
          }}
          name={i.name}
          label={i.label}
          options={chooseOptions}
        />
      ))}
    </ProForm.Group>
  );
};
