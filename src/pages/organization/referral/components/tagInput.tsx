import { PlusOutlined } from '@ant-design/icons';
import { Input, Space, Tag, Tooltip } from 'antd';
import { useMemo, useState } from 'react';

export default ({
  value,
  onChange,
  arrayValue,
  placeholder
}: {
  value?: string | string[];
  arrayValue?: boolean;
  placeholder?: string;
  onChange?: (v: string | string[]) => void;
}) => {
  const tags = useMemo(() => {
    if (!value) {
      return [];
    }
    if (Array.isArray(value)) {
      return value;
    }
    if (typeof value === 'string') {
      return value.split(',');
    }
    return [];
  }, [value]);
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [editInputIndex, setEditInputIndex] = useState(-1);
  const [editInputValue, setEditInputValue] = useState('');

  const handleClose = (removedTag: string) => {
    const newTags = tags.filter(tag => tag !== removedTag);
    onChange?.(arrayValue ? newTags : newTags.join(','));
  };

  const showInput = () => {
    setInputVisible(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputConfirm = () => {
    if (inputValue && tags.indexOf(inputValue) === -1) {
      onChange?.(
        arrayValue ? [...tags, inputValue] : [...tags, inputValue].join(',')
      );
    }

    setInputVisible(false);
    setInputValue('');
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditInputValue(e.target.value);
  };

  const handleEditInputConfirm = () => {
    const newTags = [...tags];
    newTags[editInputIndex] = editInputValue;
    onChange?.(arrayValue ? newTags : newTags?.join(','));
    setEditInputIndex(-1);
    setInputValue('');
  };

  return (
    <Space>
      {tags.map((tag, index) => {
        if (editInputIndex === index) {
          return (
            <Input
              width={100}
              key={tag}
              size='small'
              className='tag-input'
              value={editInputValue}
              onChange={handleEditInputChange}
              onBlur={handleEditInputConfirm}
              onPressEnter={handleEditInputConfirm}
            />
          );
        }
        const isLongTag: boolean = tag.length > 20;

        const tagElem = (
          <Tag
            className='edit-tag'
            key={tag}
            closable
            onClose={() => handleClose(tag)}>
            <span
              onDoubleClick={e => {
                if (index !== 0) {
                  setEditInputIndex(index);
                  setEditInputValue(tag);
                  e.preventDefault();
                }
              }}>
              {isLongTag ? `${tag.slice(0, 20)}...` : tag}
            </span>
          </Tag>
        );
        return isLongTag ? (
          <Tooltip title={tag} key={tag}>
            {tagElem}
          </Tooltip>
        ) : (
          tagElem
        );
      })}
      {inputVisible && (
        <Input
          width={100}
          type='text'
          size='small'
          className='tag-input'
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputConfirm}
          onPressEnter={handleInputConfirm}
        />
      )}
      {!inputVisible && (
        <Tag className='site-tag-plus' onClick={showInput}>
          <PlusOutlined /> {placeholder}
        </Tag>
      )}
    </Space>
  );
};
