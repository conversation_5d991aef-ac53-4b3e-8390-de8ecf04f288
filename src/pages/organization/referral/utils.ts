/** 对可编辑表单中的自定义数据进行格式化为提交数据 */
export const transferTableDataToSubmitData = (
  value?: EditTableItemType[]
): SubmitItemType[] => {
  if (!Array.isArray(value)) {
    return value as any;
  }
  return value.map(item => {
    const dependency = (item.dependency || []).reduce((acc, cur) => {
      acc[cur.key] = cur.value;
      return acc;
    }, {} as any);
    const rules = item.rules?.map(i => {
      return i === 'required' ? { required: true, message: '请输入' } : i;
    });
    return {
      ...item,
      dependency,
      rules
    };
  });
};

/** 对提交表单的数据进行格式化为可编辑表格数据 */
export const transferSubmitDataToTableData = (
  value?: SubmitItemType[]
): EditTableItemType[] => {
  if (!Array.isArray(value)) {
    return value as any;
  }
  return value.map(item => {
    const dependency = item.dependency
      ? Object.keys(item.dependency).map(key => ({
          id: getId(),
          key,
          value: item.dependency[key]
        }))
      : [];
    const rules = item.rules?.map(i => {
      return i?.required ? 'required' : i;
    });
    return {
      ...item,
      dependency,
      rules
    };
  });
};

export const getId = () => {
  return (Math.random() * 1000000).toFixed(0);
};

type EditTableItemType = {
  dependency: { key: string; value: string }[];
  id: string;
  index: 0;
  label: string;
  name: string;
  options: string[];
  rules: string[];
  type: string;
};

type SubmitItemType = {
  dependency: Record<string, string>;
  id: string;
  index: 0;
  label: string;
  name: string;
  options: string[];
  rules: any[];
  type: string;
};
