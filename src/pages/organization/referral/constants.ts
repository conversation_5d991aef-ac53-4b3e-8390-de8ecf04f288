//转诊类别（危急情况）可多选，多选时用于英文逗号隔开——（PZ-平诊,JZ-急诊,WZZZ-危重转诊,YQJJZY-院前急救转运
export const referralTypeOptions = [
  {
    label: '平诊',
    value: 'PZ'
  },
  {
    label: '急诊',
    value: 'JZ'
  },
  {
    label: '危重转诊',
    value: 'WZZZ'
  },
  {
    label: '院前急救转运',
    value: 'YQJJZY'
  }
];

export const referralDirectionOptions = [
  {
    label: '转出',
    value: 'REFERRAL_OUT'
  },
  {
    label: '转入',
    value: 'REFERRAL_IN'
  }
];

export const toHospitalTypeOptions = [
  {
    label: '门诊',
    value: 'OUTPATIENT'
  },
  {
    label: '住院',
    value: 'INHOSPITAL'
  }
];

export const showHideOptions = [
  {
    label: '显示',
    value: true
  },
  {
    label: '隐藏',
    value: false
  }
];

export const whenCanChangeOptions = [
  {
    label: '下一节点未审核时',
    value: 'NEXT_STAGE_NOT_ISSUE'
  },
  {
    label: '隐藏',
    value: 'HIDE'
  }
];

export const whenCanCancelOptions = [
  {
    label: '下一节点未审核时',
    value: 'NEXT_STAGE_NOT_ISSUE'
  },
  {
    label: '转诊成功前',
    value: 'BEFORE_FINISH_REFERRAL'
  },
  {
    label: '隐藏',
    value: 'HIDE'
  }
];

export enum chooseAllEnums {
  'All_REQUIRE' = 'All_REQUIRE',
  'All_OPTION' = 'All_OPTION',
  'ALL_HIDE' = 'ALL_HIDE'
}

export const chooseAllOptions = [
  {
    label: '全必填',
    value: chooseAllEnums.All_REQUIRE
  },
  {
    label: '全选填',
    value: chooseAllEnums.All_OPTION
  },
  {
    label: '全不填',
    value: chooseAllEnums.ALL_HIDE
  }
];

export const chooseOptions = [
  {
    label: '必填',
    value: chooseAllEnums.All_REQUIRE
  },
  {
    label: '选填',
    value: chooseAllEnums.All_OPTION
  },
  {
    label: '不填',
    value: chooseAllEnums.ALL_HIDE
  }
];

export const patientForms = [
  {
    name: 'patCardNo',
    label: '就诊卡号'
  },
  {
    name: 'patientIdType',
    label: '证件类别'
  },

  {
    name: 'patientIdNo',
    label: '证件号码'
  },
  {
    name: 'patientSex',
    label: '性别'
  },
  {
    name: 'patientBirthday',
    label: '出生日期'
  },
  {
    name: 'patientAge',
    label: '年龄'
  },
  // {
  //   name: '',
  //   label: '民族'
  // },
  {
    name: 'patientAddress',
    label: '居住地区'
  },
  {
    name: 'addressDetail',
    label: '详细地址'
  },
  {
    name: 'payModel',
    label: '支付方式'
  }
  // {
  //   name: 'treatmentType',
  //   label: '就诊类别'
  // }
];

export const patientFormKeys = patientForms.map(i => i.name);

export const guardianForms = [
  {
    name: 'parentRelation',
    label: '与患者关系'
  },
  {
    name: 'parentName',
    label: '姓名'
  },
  {
    name: 'parentIdType',
    label: '证件类别'
  },
  {
    name: 'parentIdNo',
    label: '证件号码'
  }
];

export const guardianFormKeys = guardianForms.map(i => i.name);

export const referralForms = [
  // {
  //   name: 'fromHisId',
  //   label: '转出医院'
  // },
  // {
  //   name: 'fromHisLevel',
  //   label: '转出医院等级'
  // },
  // {
  //   name: 'fromDeptId',
  //   label: '转出科室'
  // },
  // {
  //   name: 'referralType',
  //   label: '转诊类别'
  // },
  // {
  //   name: 'toHisId',
  //   label: '拟转入医院'
  // },
  // {
  //   name: 'toHospitalType',
  //   label: '入院类别'
  // },
  // {
  //   name: 'toDeptId',
  //   label: '拟转入科室'
  // },
  {
    name: 'appointDate',
    label: '到院日期'
  }
];

export const referralFormKeys = referralForms.map(i => i.name);

export const transferForms = [
  {
    name: 'transferAssessment',
    label: '转运评估'
  },
  {
    name: 'transferType',
    label: '转运方式'
  },
  {
    name: 'transferTool',
    label: '转运工具'
  },
  {
    name: 'estimateDate',
    label: '预计到院时间'
  },
  {
    name: 'inspectionReportUrl',
    label: '检验检查报告'
  },
  {
    name: 'consentUrl',
    label: '双向转诊知情同意书'
  },
  {
    name: 'remark',
    label: '备注'
  }
];

export const transferFormKeys = transferForms.map(i => i.name);

export const descForms = [
  {
    name: 'diagnosis',
    label: '主要诊断'
  },
  {
    name: 'resume',
    label: '主诉及简要病史（转运原因）'
  }
];

export const noticeTypeOptions = [
  {
    label: '模版消息',
    value: 'TEMPLATE'
  },
  {
    label: '短信消息',
    value: 'SMS'
  }
];

export const rejectCallBackTypeOptions = [
  {
    label: '终止转诊',
    value: 'INTERRUPT'
  },
  {
    label: '返回上一节点',
    value: 'RETURN_PREVIOUS_STAG'
  }
];

export const noticeUserOptions = [
  {
    label: '转出医师',
    value: 'ZCYS'
  },
  {
    label: '转出科室审核人员',
    value: 'ZCKSSHRY'
  },
  {
    label: '转出医院双转办',
    value: 'ZCYYSZB'
  },
  {
    label: '转入医院双转办',
    value: 'ZRYYSZB'
  },
  {
    label: '转入科室审核人员',
    value: 'ZRKSSHRY'
  },
  {
    label: '患者',
    value: 'PATIENT'
  }
];

export const enableTypeOptions = [
  {
    label: '启用',
    value: true
  },
  {
    label: '停用',
    value: false
  }
];

export const showInfoTypeOptions = [
  {
    label: '操作人',
    value: 'OPERATOR'
  },
  {
    label: '操作时间',
    value: 'CREATE_TIME'
  },
  {
    label: '操作内容',
    value: 'TASK_NAME'
  },
  {
    label: '操作说明',
    value: 'REASON'
  }
];

export const addNoticeUserOptions = [
  {
    label: '转出医师',
    value: 'ZCYS'
  },
  {
    label: '转出科室审核人员',
    value: 'ZCKSSHRY'
  },
  {
    label: '转出医院双转办',
    value: 'ZCYYSZB'
  },
  {
    label: '转入医院双转办',
    value: 'ZRYYSZB'
  },
  {
    label: '转入科室审核人员',
    value: 'ZRKSSHRY'
  },
  {
    label: '患者',
    value: 'PATIENT'
  }
];

export const hospitalNoticeUserOptions = [
  {
    label: '转出医师',
    value: 'ZCYS'
  },
  {
    label: '转出科室审核人员',
    value: 'ZCKSSHRY'
  },
  {
    label: '转出医院双转办',
    value: 'ZCYYSZB'
  },
  {
    label: '转入医院双转办',
    value: 'ZRYYSZB'
  },

  {
    label: '转入科室审核人员',
    value: 'ZRKSSHRY'
  },
  {
    label: '转入科室接收医生',
    value: 'ZRKSJSYS'
  },
  {
    label: '患者',
    value: 'PATIENT'
  }
];

export const componentValueEnums = {
  input: '文本输入框',
  radio: '单选框',
  switch: '开关',
  inputNumber: '数字输入框'
};

export const devConfigData = {
  configName: '平诊转出配置',
  configInstruction: '配置说明',
  referralType: ['PZ'],
  referralDirection: ['REFERRAL_OUT'],
  toHospitalType: ['INHOSPITAL'],
  addInfoShown: true,
  consentShown: true,
  whenCanChange: 'HIDE',
  whenCanCancel: 'HIDE',
  templateContent: {
    patientName: 'All_REQUIRE',
    patCardNo: 'All_REQUIRE',
    patientIdType: 'All_REQUIRE',
    patientIdNo: 'All_REQUIRE',
    patientSex: 'All_REQUIRE',
    patientBirthday: 'All_REQUIRE',
    patientAge: 'All_REQUIRE',
    patientPhone: 'All_REQUIRE',
    patientAddress: 'All_REQUIRE',
    addressDetail: 'All_REQUIRE',
    payModel: 'All_REQUIRE',
    treatmentType: 'All_REQUIRE',
    parentRelation: 'All_REQUIRE',
    parentName: 'All_REQUIRE',
    parentIdType: 'All_REQUIRE',
    parentIdNo: 'All_REQUIRE',
    fromHisId: 'All_REQUIRE',
    fromHisLevel: 'All_REQUIRE',
    fromDeptId: 'All_REQUIRE',
    referralType: 'All_REQUIRE',
    toHisId: 'All_REQUIRE',
    toHospitalType: 'All_REQUIRE',
    toDeptId: 'All_REQUIRE',
    appointDate: 'All_REQUIRE',
    transferAssessment: 'All_REQUIRE',
    transferType: 'All_REQUIRE',
    transferTool: 'All_REQUIRE',
    estimateDate: 'All_REQUIRE',
    consentUrl: 'All_REQUIRE',
    remark: 'All_REQUIRE',
    diagnosis: 'All_REQUIRE',
    resume: 'All_REQUIRE',
    desc: [
      {
        id: '541897',
        index: 0,
        type: 'input',
        label: '血压',
        name: 'blood',
        rules: ['required'],
        dependency: []
      }
    ]
  },
  eventControl: {
    stepOne: {
      passedNoticeType: ['TEMPLATE'],
      passedNoticeUser: ['ZCYS'],
      initiateAgain: true
    },
    stepTwo: {
      used: true,
      handleRoleId: '10020',
      rejectCallBackType: 'INTERRUPT',
      passedNoticeType: ['TEMPLATE'],
      passedNoticeUser: ['ZCYS'],
      rejectNoticeType: ['TEMPLATE'],
      rejectNoticeUser: ['ZCYS']
    },
    stepThree: {
      used: true,
      handleRoleId: '10020',
      rejectCallBackType: 'INTERRUPT',
      passedNoticeType: ['SMS'],
      passedNoticeUser: ['ZCKSSHRY'],
      rejectNoticeType: ['SMS'],
      rejectNoticeUser: ['ZCYS', 'ZCKSSHRY']
    },
    stepFour: {
      used: true,
      handleRoleId: '10020',
      rejectCallBackType: 'RETURN_PREVIOUS_STAG',
      passedNoticeType: ['SMS'],
      passedNoticeUser: ['ZCYS', 'ZCKSSHRY'],
      rejectNoticeType: ['TEMPLATE'],
      rejectNoticeUser: ['ZCYS', 'ZCKSSHRY']
    },
    stepFive: {
      used: true,
      handleRoleId: '10020',
      rejectCallBackType: 'INTERRUPT',
      passedNoticeType: ['TEMPLATE'],
      passedNoticeUser: ['ZCYS', 'ZCKSSHRY'],
      rejectNoticeType: ['SMS'],
      rejectNoticeUser: ['ZCYS', 'ZCKSSHRY']
    },
    stepSix: {
      used: true,
      handleRoleId: '10020',
      rejectCallBackType: 'INTERRUPT',
      passedNoticeType: ['SMS'],
      passedNoticeUser: ['ZCYS', 'ZCKSSHRY'],
      rejectNoticeType: ['TEMPLATE', 'SMS'],
      rejectNoticeUser: ['ZCYS', 'ZCKSSHRY']
    }
  },
  integratedConfig: {
    showAuditRecords: true,
    showInfo: ['OPERATOR', 'CREATE_TIME'],
    openAddReg: true,
    opNoticeType: ['TEMPLATE'],
    opNoticeUser: ['ZCYS', 'ZCKSSHRY'],
    openInHospitalOrder: true,
    inNoticeType: ['TEMPLATE'],
    inNoticeUser: ['ZCYS', 'ZCKSSHRY']
  },
  addInfoUrl: 'www.baidu.com',
  consentUrl:
    'https://ihoss.oss-cn-beijing.aliyuncs.com/PIC/2022/08/18/7c6e7c6e-7c6e-7c6e-7c6e-7c6e7c6e7c6e.jpeg'
};
