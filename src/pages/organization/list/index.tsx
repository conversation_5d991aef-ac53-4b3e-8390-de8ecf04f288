import React, { useMemo } from 'react';
import {
  actionConfirm,
  RouteComponentProps,
  LinkButton,
  useModal
} from 'parsec-admin';
import useApi from './api';
import MyTableList from '@components/myTableList';
import permisstion from '@utils/permisstion';
import { Switch, Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import moment from 'moment';
import env from '@src/configs/env';
import Qrcode from '@components/qrcode';

export default ({ history }: RouteComponentProps) => {
  // // 获取医院列表
  // const {
  //   data: { data }
  // } = useApi.获取医院列表({
  //   initValue: {
  //     data: []
  //   },
  //   params: {},
  //   needInit: true
  // });

  // const switchModalVisible = useModal({
  //   title: '编辑账号',
  //   onSubmit: values =>
  //     handleSubmit(() => {
  //       return useApi.修改医院.request({ ...values });
  //     }),
  //   items: [
  //     {
  //       name: 'id',
  //       render: false
  //     },
  //     {
  //       label: '账号',
  //       name: 'adminAccount',
  //       required: true
  //     },
  //     {
  //       label: '医院名称',
  //       name: 'hisId',
  //       render: () => (
  //         <ArrSelect
  //           options={(data || []).map(x => ({
  //             value: x.hisId,
  //             children: x.hospitalName
  //           }))}
  //         />
  //       ),
  //       required: true
  //     },
  //     {
  //       label: '姓名',
  //       name: 'name',
  //       required: true
  //     },
  //     // {
  //     //   label: '联系电话',
  //     //   name: 'phone',
  //     //   formItemProps: {
  //     //     rules: [
  //     //       {
  //     //         pattern: /^[1][3-9][0-9]{9}$/,
  //     //         required: true,
  //     //         message: '请输入正确的号码!'
  //     //       }
  //     //     ]
  //     //   }
  //     // },
  //     {
  //       label: '账号状态',
  //       name: 'status',
  //       render: () => (
  //         <Radio.Group>
  //           <Radio value={'1'}>开启</Radio>
  //           <Radio value={'2'}>关闭</Radio>
  //         </Radio.Group>
  //       ),
  //       required: true
  //     }
  //   ]
  // });

  const switchQrModalVisible = useModal(
    ({ hisId }: { hisId: number | string }) => ({
      title: '宣教二维码',
      onSubmit: () => {
        return Promise.resolve();
      },
      width: 280,
      items: [
        {
          name: 'id',
          render: (
            <Qrcode
              url={`https://${
                env.env === 'prod' ? '' : 't'
              }ihs.cqkqinfo.com/patients/health-learn-mobile-${hisId}/#/pages/announce/index?entry=2`}
              size={220}
            />
          )
        }
      ]
    })
  );

  return (
    <React.Fragment>
      <MyTableList
        tableTitle='医疗机构'
        killHisId={true}
        getList={({ params }) =>
          useApi.医疗机构列表.request({
            ...params
          })
        }
        action={
          permisstion.canAddOrgnization && (
            <Button
              type={'default'}
              icon={<PlusOutlined />}
              onClick={() => {
                history.push('/organization/add');
              }}>
              添加医院
            </Button>
          )
        }
        columns={useMemo(
          () => [
            {
              title: '医院ID',
              dataIndex: 'hisId',
              search: env.hisId !== '40009',
              width: 180
            },
            // {
            //   title: '医院ID',
            //   dataIndex: 'id',
            //   search: false,
            // },
            {
              title: '医院名称',
              dataIndex: 'hospitalName',
              search: true,
              width: 180
            },
            {
              title: '管理员账号',
              dataIndex: 'adminAccount',
              width: 180
            },
            {
              title: '用户名',
              dataIndex: 'adminName',
              width: 180
            },
            {
              title: '创建时间',
              dataIndex: 'createTime',
              width: 180,
              render: v => {
                return moment(v).format('YYYY-MM-DD HH:mm');
              }
            },
            {
              title: '状态',
              dataIndex: 'status',
              render: permisstion.canUpdateOrgnization
                ? (v, record: any) => {
                    return (
                      <Switch
                        checkedChildren='ON'
                        unCheckedChildren='OFF'
                        checked={record.status !== '2'}
                        onClick={() => {
                          actionConfirm(
                            () =>
                              useApi.修改医院.request({
                                hisId: record.hisId,
                                id: record.id,
                                status: record.status === '1' ? '2' : '1'
                              }),
                            record.status === '1' ? '关闭' : '开启'
                          );
                        }}
                      />
                    );
                  }
                : false,
              width: 180
            },
            {
              title: '操作',
              fixed: 'right',
              width: 180,
              render: permisstion.canUpdateOrgnization
                ? record => (
                    <div>
                      <LinkButton
                        onClick={() => {
                          history.push(`/organization/config/${record.hisId}`);
                        }}
                        style={{ marginRight: 20 }}>
                        {env.healthLearn ? '编辑' : '配置'}
                      </LinkButton>
                      {permisstion.canInHospitalQr && (
                        <LinkButton
                          style={{ marginRight: 20 }}
                          onClick={() => {
                            switchQrModalVisible({ hisId: record.hisId });
                          }}>
                          宣教二维码
                        </LinkButton>
                      )}
                      <LinkButton
                        style={{ color: 'red' }}
                        onClick={() => {
                          actionConfirm(
                            () =>
                              useApi.修改医院.request({
                                id: record.id,
                                hisId: record.hisId,
                                status: '0'
                              }),
                            '删除'
                          );
                        }}>
                        删除
                      </LinkButton>
                    </div>
                  )
                : false
            }
          ],
          [history, switchQrModalVisible]
        )}
      />
    </React.Fragment>
  );
};
