import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiRequestParams,
  ListApiResponseData,
  ApiResponse
} from '@src/configs/apis';
import { HosptitalGetBy, HosptitalGetByList } from '@src/pages/authority/d';

export interface MenuItem {
  childMenu: MenuItem[];
  code: string;
  id: number;
  name: string;
  operatePurview: string;
  parentId: number;
  sort: number;
  type: number;
  url: string;
  userId: number;
}

export interface AddHospParams {
  hisId: string;
  name: string;
  contactNumber: string;
  adminAccount: string;
  adminName: string;
  moIds: string;
}

export default {
  医疗机构列表: createApiHooks(
    (data: ListApiRequestParams & { hisId?: string }) =>
      request.post<
        ListApiResponseData<{
          adminAccount: string;
          adminName: string;
          certFirstDate: string;
          certFirstTime: string;
          certUpdateDate: string;
          certUpdateTime: string;
          contactNumber: string;
          createTime: string;
          grading: string;
          hisId: string;
          honorImages: string;
          hospitalAddress: string;
          hospitalIntroduction: string;
          hospitalLevel: string;
          hospitalName: string;
          id: number;
          instCode: string;
          instInfoSafeEnsure: string;
          instRegisterNumber: string;
          level: string;
          licence: string;
          licenseEndDate: string;
          licenseEndTime: string;
          licenseStartDate: string;
          licenseStartTime: string;
          name: string;
          serviceScope: string;
          status: string;
          updateTime: string;
        }>
      >('/mch/his/hospitalInfo/page', data)
  ),
  修改医院: createApiHooks(
    (data: {
      id: number;
      hisId?: string;
      status?: string;
      activityBanner?: string;
      notice?: string;
      hospitalLogo?: string;
      hospitalBanner?: string;
    }) =>
      request.post<ApiResponse<Record<string, unknown>>>(
        '/mch/his/hospitalInfo/update',
        data,
        {
          headers: { 'Content-Type': 'application/json' }
        }
      )
  ),
  获取医院列表: createApiHooks((params: HosptitalGetBy) =>
    request.post<HosptitalGetByList>('/mch/his/hospitalInfo/getBy', params)
  ),
  重置密码: createApiHooks(
    (params: { account: string; hisId: string; id: number }) =>
      request.post('/mch/user/doctorAccount/password/reset', params)
  ),
  查询所有菜单权限: createApiHooks((params: { clientType?: string }) =>
    request.get<{ data: MenuItem[] }>('/mch/his/role/getAllMenuOperate', {
      params
    })
  ),
  添加医疗机构: createApiHooks((data: AddHospParams) =>
    request.post('/mch/his/hospitalInfo/add', data)
  )
};
