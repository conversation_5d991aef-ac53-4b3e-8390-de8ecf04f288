import React, { useState, useEffect } from 'react';
import { Checkbox, Col, Row } from 'antd';
import { MenuItem } from './api';

interface Iprops {
  data: MenuItem[];
  onChange: (item: number[]) => void;
}

export default (props: Iprops) => {
  const { data, onChange } = props;
  const [checkedList, setCheckedList] = useState<number[]>([]);

  // 获取每个元素控制的子元素
  const getAllSubValues = (dataList: MenuItem[] | null): number[] => {
    if (!dataList) {
      return [];
    }
    let res = [] as number[];
    dataList.forEach(item => {
      res.push(item.id);
      if (item.childMenu) {
        res = [...res, ...getAllSubValues(item.childMenu)];
      }
    });
    return res;
  };

  // 根据Id值获取某一项
  const findItemById = (dataList: MenuItem[] | null, id: number) => {
    if (!id || !dataList) {
      return null;
    }
    let res = null as any;
    dataList.some(item => {
      if (item.id === id) {
        res = item;
        return true;
      }
      if (item.childMenu && item.childMenu.length > 0) {
        const resItem = findItemById(item.childMenu, id);
        console.log(resItem, item.childMenu, id);
        if (resItem) {
          res = resItem;
          return true;
        }
      }
      return false;
    });
    return res;
  };

  // 递归调用
  const setResult = (dataList: MenuItem[], parentId: number, res: number[]) => {
    if (!parentId) {
      return;
    }
    dataList.forEach(subItem => {
      if (subItem.id === parentId) {
        res.push(subItem.id);
        const parentItem = findItemById(data, parentId);
        if (parentItem) {
          getAllParentValues(data, parentItem, res);
        }
      } else if (subItem.childMenu && subItem.childMenu.length > 0) {
        setResult(subItem.childMenu, parentId, res);
      }
    });
  };

  // 获取每个元素的所有父元素
  const getAllParentValues = (
    topDataList: MenuItem[] | null,
    item: MenuItem,
    res: number[]
  ): void => {
    if (!topDataList || !item.parentId) {
      return;
    }
    const parentId = item.parentId;
    setResult(topDataList, parentId, res);
  };

  const handleItemChange = (
    dataList: MenuItem[] | null,
    id: number,
    item: MenuItem
  ) => {
    //获取自己id以及所有子元素id
    const allSubItems = [...getAllSubValues(dataList), id];
    const prevHasSelect = checkedList.includes(id);
    if (prevHasSelect) {
      setCheckedList(checkedList.filter(id => !allSubItems.includes(id)));
    } else {
      const res = [] as number[];
      getAllParentValues(data, item, res);
      setCheckedList(
        Array.from(new Set([...checkedList, ...allSubItems, ...res]))
      );
    }
  };

  //递归获取元素
  const newList = (dataList: MenuItem[], marginLeft: number) => {
    return dataList.map(item => {
      return (
        <Col
          style={{ marginBottom: marginLeft ? 0 : 15 }}
          key={item.id}
          xs={{ span: marginLeft ? 24 : 6 }}
          lg={{ span: marginLeft ? 24 : 4 }}
          xl={{ span: marginLeft ? 24 : 3 }}>
          <Checkbox
            onChange={() => handleItemChange(item.childMenu, item.id, item)}
            checked={checkedList.includes(item.id)}
            style={{ marginLeft }}>
            {item.name}
          </Checkbox>
          <Row>
            {item.childMenu && newList(item.childMenu, marginLeft + 15)}
          </Row>
        </Col>
      );
    });
  };

  useEffect(() => {
    onChange(checkedList);
  }, [onChange, checkedList]);

  return <Row>{newList(data, 0)}</Row>;
};
