import React, { useState, useRef } from 'react';
import { CardLayout } from 'parsec-admin';
import { message, Form, Row, Col, Input, Button } from 'antd';
import MyCheckbox from './checkBox';
import MySteps from './steps';
import { useHistory } from 'react-router-dom';
import useApi from './api';
import styled from 'styled-components';

export default () => {
  const history = useHistory();
  const [form] = Form.useForm();
  const [stage, setStage] = useState(0);

  const {
    data: { data: menuList },
    loading
  } = useApi.查询所有菜单权限({
    initValue: {
      data: []
    }
  });

  const rowLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 }
  };

  const tailLayout = {
    wrapperCol: { offset: 6, span: 18 }
  };

  const submitValue = useRef<any>({});
  const onFinish = (values: any) => {
    if (stage <= 1) {
      submitValue.current = { ...submitValue.current, ...values };
      setStage(prev => prev + 1);
      return;
    }
    if (submitValue.current.moIds) {
      useApi.添加医疗机构.request(submitValue.current).then(() => {
        message.success('添加成功');
        history.goBack();
      });
    }
  };

  const getTitle = function() {
    switch (stage) {
      case 0:
        return '医院信息';
      case 1:
        return '医院管理员账号';
      case 2:
        return '医院权限';
      default:
        return '医院权限';
    }
  };

  return (
    <Wrapper>
      <CardLayout>
        <MySteps current={stage} />
      </CardLayout>
      <CardLayout title={getTitle()} loading={loading}>
        <Form form={form} name='hosp-info' onFinish={onFinish}>
          {/* 医院设置 */}
          {stage === 0 && (
            <Row style={{ visibility: stage === 0 ? 'visible' : 'hidden' }}>
              <Col xs={{ span: 24 }} lg={{ span: 12 }} xl={{ span: 6 }}>
                <Form.Item
                  {...rowLayout}
                  label='医院名称'
                  name='name'
                  rules={[
                    {
                      required: true,
                      message: '请输入医院名称!'
                    }
                  ]}>
                  <Input placeholder='请输入医院名称' />
                </Form.Item>
              </Col>
              <Col xs={{ span: 24 }} lg={{ span: 12 }} xl={{ span: 6 }}>
                <Form.Item
                  {...rowLayout}
                  label='医院ID'
                  name='hisId'
                  rules={[
                    {
                      required: true,
                      message: '请输入医院ID!'
                    },
                    {
                      pattern: /^\d+$/,
                      message: '请输入数字类型的医院ID!'
                    }
                  ]}>
                  <Input placeholder='请输入医院ID' />
                </Form.Item>
              </Col>
              <Col
                xs={{ span: 24 }}
                lg={{ span: 12 }}
                xl={{ span: 6, push: 6 }}>
                <Form.Item {...tailLayout}>
                  <Button
                    style={{ marginRight: 20 }}
                    onClick={() => history.goBack()}>
                    取消
                  </Button>
                  <Button type='primary' htmlType='submit'>
                    下一步
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          )}
          {/* 医院管理员设置 */}
          {stage === 1 && (
            <Row>
              <Col
                xs={{ span: 24 }}
                lg={{ span: 12 }}
                xl={{ span: 6 }}
                order={1}>
                <Form.Item
                  {...rowLayout}
                  label='登录账号'
                  className='account'
                  name='adminAccount'
                  rules={[
                    {
                      required: true,
                      message: '请输入登录账号!'
                    }
                  ]}>
                  <Input placeholder='请输入登录账号' />
                </Form.Item>
              </Col>
              <Col
                xs={{ span: 24 }}
                lg={{ span: 12 }}
                xl={{ span: 6 }}
                order={3}>
                <Form.Item
                  {...rowLayout}
                  label='用户名'
                  name='adminName'
                  rules={[
                    {
                      required: true,
                      message: '请输入用户名!'
                    }
                  ]}>
                  <Input placeholder='请输入用户名' />
                </Form.Item>
              </Col>
              <Col
                xs={{ span: 24 }}
                lg={{ span: 12 }}
                xl={{ span: 6 }}
                order={4}>
                <Form.Item
                  {...rowLayout}
                  label='联系电话'
                  name='phone'
                  rules={[
                    {
                      pattern: /^[1][3-9][0-9]{9}$/,
                      required: true,
                      message: '请输入正确的号码!'
                    }
                  ]}>
                  <Input placeholder='请输入联系电话' />
                </Form.Item>
              </Col>
              <Col
                xs={{ span: 24 }}
                lg={{ span: 12 }}
                xl={{ span: 6 }}
                order={5}>
                <Form.Item {...tailLayout}>
                  <Button
                    style={{ marginRight: 20 }}
                    onClick={() => setStage(prev => prev - 1)}>
                    上一步
                  </Button>
                  <Button type='primary' htmlType='submit'>
                    下一步
                  </Button>
                </Form.Item>
              </Col>
              <Col
                xs={{ span: 24, order: 2 }}
                lg={{ span: 12, order: 2 }}
                xl={{ span: 6, order: 6 }}>
                <div className='tip'>
                  *账号初始密码为Yy@123456，用户第一次登录成功后/重置密码后需要修改该初始密码
                </div>
              </Col>
            </Row>
          )}
          {/* 医院权限设置 */}
          {stage === 2 && (
            <>
              <MyCheckbox
                data={menuList}
                onChange={(value: number[]) => {
                  submitValue.current.moIds = value.join(',');
                }}
              />
              <Row justify='end'>
                <Col span={6}>
                  <Form.Item {...tailLayout}>
                    <Button
                      style={{ marginRight: 20 }}
                      onClick={() => setStage(prev => prev - 1)}>
                      上一步
                    </Button>
                    <Button type='primary' htmlType='submit'>
                      确定
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </>
          )}
        </Form>
      </CardLayout>
    </Wrapper>
  );
};

const Wrapper = styled.div`
  margin: -30px 1px 0;
  .tip {
    padding-left: 25%;
  }
`;
