import React from 'react';
import styled from 'styled-components';
import classNames from 'classnames';

interface Iprops {
  current: number;
}

export default (props: Iprops) => {
  const { current } = props;
  const config = [
    {
      title: '1.医院信息',
      url: require('./images/hosp1.png'),
      selectUrl: require('./images/hosp2.png')
    },
    {
      title: '2.医院管理员账号',
      url: require('./images/admin1.png'),
      selectUrl: require('./images/admin2.png')
    },
    {
      title: '3.医院权限',
      url: require('./images/authority1.png'),
      selectUrl: require('./images/authority2.png')
    }
  ];
  return (
    <Wrapper>
      {config.map((item, index) => {
        return (
          <div
            key={item.title}
            className={classNames('step-item', {
              last: index === config.length - 1
            })}>
            <img src={index <= current ? item.selectUrl : item.url} alt='' />
            <div className='word'>
              <h6
                className={classNames({
                  select: index <= current
                })}>
                {item.title}
              </h6>
              {index <= config.length - 2 && (
                <img
                  className='arrow'
                  src={
                    index < current
                      ? require('./images/arrow1.png')
                      : require('./images/arrow2.png')
                  }
                  alt=''
                />
              )}
            </div>
          </div>
        );
      })}
    </Wrapper>
  );
};

const Wrapper = styled.div`
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > .step-item.last {
    flex: none;
  }
  > .step-item {
    display: flex;
    flex: 1;
    align-items: center;
    transition: all 0.5s linear;
    > img {
      width: 56px;
    }
    > .word {
      flex: 1;
      margin: 0 11px;
      > .arrow {
        width: calc(100% - 150px);
      }
      > h6 {
        font-size: 16px;
        margin: 0 10px;
        color: #666;
        display: inline-block;
      }
      > h6.select {
        color: #2780d9;
      }
    }
  }
`;
