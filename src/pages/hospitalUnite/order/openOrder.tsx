import React, { useMemo, useEffect, useState } from 'react';
import {
  CardLayout,
  actionConfirm,
  InputMoney,
  BaseTable,
  RouteComponentProps
} from 'parsec-admin';
import styled from 'styled-components';
import useApi from './api';
import { Form, Button, Row, Col, Input } from 'antd';
import { useParams } from 'react-router-dom';
export default ({ history }: RouteComponentProps) => {
  const { id } = useParams<any>();
  const [orderId, setOrderId] = useState(undefined as any);

  useEffect(() => {
    if (id) {
      setOrderId(id);
    }
  }, [id]);
  const { data, loading: detailLoading } = useApi.getOrderDetail({
    params: { id: orderId || '' },
    needInit: !!orderId,
    initValue: { data: {} as any }
  });

  const [form] = Form.useForm();

  // 检查检验项目
  const columns = [
    {
      title: '收费项目名称',
      dataIndex: 'itemName'
    },
    {
      title: '英文名称',
      dataIndex: 'enName'
    },
    {
      title: '收费编码',
      dataIndex: 'currentCode'
    },
    {
      title: '价格(元）',
      dataIndex: 'price',
      render: (val: any, { nodeId }: any) => (
        <Form.Item
          rules={[{ required: true, message: '请输入价格' }]}
          name={`${nodeId}-price`}>
          <InputMoney />
        </Form.Item>
      )
    },
    {
      title: 'HIS单号',
      dataIndex: 'hisOrderNo',
      render: (val: any, { nodeId }: any) => (
        <Form.Item
          rules={[{ required: true, message: '请输入HIS单号' }]}
          name={`${nodeId}-hisOrderNo`}>
          <Input />
        </Form.Item>
      )
    }
  ];
  // 标本项目
  const columns1 = [
    {
      title: '名称',
      dataIndex: 'itemName'
    },
    {
      title: 'HIS单号',
      dataIndex: 'hisOrderNo',
      width: '50%',
      render: (val: any, { nodeId }: any) => (
        <Form.Item
          rules={[{ required: true, message: '请输入HIS单号' }]}
          name={`${nodeId}-hisOrderNo`}>
          <Input />
        </Form.Item>
      )
    }
  ];

  const TestList = useMemo(() => {
    return (data?.items || []).filter((x: any) => x.type === 'TEST');
  }, [data]);
  console.log('TestList', TestList);
  const ExamList = useMemo(() => {
    return (data?.items || []).filter((x: any) => x.type === 'EXAM');
  }, [data]);
  console.log('ExamList', ExamList);
  const SpecList = useMemo(() => {
    return (data?.items || []).filter((x: any) => x.type === 'SPEC');
  }, [data]);
  console.log('ExamList', ExamList);
  const allItems = useMemo(
    () =>
      [...TestList, ...ExamList, ...SpecList]
        .map(({ chargeItems }) => chargeItems)
        .flat(),
    [ExamList, SpecList, TestList]
  );
  useEffect(() => {
    const initValues: any = {};
    console.log(allItems);
    allItems.forEach(({ nodeId, price, qty = 1, hisOrderNo }) => {
      initValues[`${nodeId}-price`] = price * qty;
      initValues[`${nodeId}-hisOrderNo`] = hisOrderNo;
    });
    form.setFieldsValue(initValues);
  }, [ExamList, SpecList, TestList, allItems, form]);
  return (
    <Wrapper edit={false} form={form}>
      <CardLayout>
        <Row>
          <Col sm={24} xl={12}>
            订单编号：{data?.orderNo}
          </Col>
          <Col sm={24} xl={12}>
            <Row justify='end'>
              <Button
                type='primary'
                onClick={() => {
                  form.validateFields().then(values =>
                    actionConfirm(
                      () =>
                        useApi.hisOrder
                          .request({
                            id: id as any,
                            hisNoItems: allItems.map(({ qty, id, nodeId }) => {
                              console.log(allItems);
                              return {
                                subOrderId: id,
                                hisOrderNo: values[`${nodeId}-hisOrderNo`],
                                price: values[`${nodeId}-price`],
                                qty: qty
                              };
                            })
                          })
                          .then(() => {
                            history.goBack();
                          }),
                      '确认出单'
                    )
                  );
                }}>
                确认出单
              </Button>
            </Row>
          </Col>
        </Row>
      </CardLayout>
      {TestList.length > 0 && (
        <CardLayout title={'检查项目'} loading={detailLoading}>
          {(TestList || []).map((x: any) => {
            return (
              <BaseTable
                title={() => x.itemName}
                pagination={false}
                bordered={true}
                columns={columns}
                dataSource={x?.chargeItems || []}
                key={x?.nodeId}
              />
            );
          })}
        </CardLayout>
      )}
      {ExamList.length > 0 && (
        <CardLayout title={'检验项目'} loading={detailLoading}>
          {(ExamList || []).map((x: any) => {
            return (
              <BaseTable
                title={() => x.itemName}
                pagination={false}
                bordered={true}
                columns={columns}
                dataSource={x?.chargeItems || []}
                key={x?.nodeId}
              />
            );
          })}
        </CardLayout>
      )}
      {SpecList.length > 0 && (
        <CardLayout title={'标本项目'} loading={detailLoading}>
          {(SpecList || []).map((x: any) => {
            return (
              <Row key={x?.nodeId} style={{ marginBottom: 15 }}>
                <Col sm={24} xl={12}>
                  <BaseTable
                    title={() => x.itemName}
                    pagination={false}
                    bordered={true}
                    columns={columns1}
                    dataSource={x?.chargeItems || []}
                  />
                </Col>
              </Row>
            );
          })}
        </CardLayout>
      )}
    </Wrapper>
  );
};

const Wrapper = styled(Form)<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
  .ant-card-body {
    .ant-table-wrapper {
      margin-bottom: 20px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .ant-table {
    > .ant-table-container {
      > .ant-table-content {
        > table {
          > tbody {
            > tr {
              > td {
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
`;
