//订单列表
export interface OrderList {
  id: number; //订单id
  sourceId?: string; //互联网医院过来的开单ID
  orderNo?: string; //订单NO，互联网医院过来的开单
  type?: string; //订单类型：TEST检验，EXAM检查
  hospitalId?: number; //来源医院ID
  hospitalName?: string; // 开单医院名称
  executeHospitalId?: number; //执行检验检查医院ID
  executeHospitalName?: string; // 检查医院名称
  source?: string; //来自标准版还是独立部署
  totalItemPrice?: number; //检查或者检验项目总价=sum(项目price*qty)，不包括额外的挂号费
  state?: string; //见备注
  registration?: number; //下级医院挂号费,单位是分
  registerNo?: string; //下级医院的挂号单号
  user?: {
    //根据互联网医院信息来存储
    id: number; //互联网医院的用户ID
    openId?: string; //互联网医院的openId
    hisId?: string; //用户在HIS中的ID，在创建后，应该回写到此处
    userName?: string; //
    hcNo?: string; //医保卡号
    idNo?: string; //身份证号
    ecardNo?: string; //电子健康卡NO
  };
  created?: string; //创建时间，对应INI状态
}
//订单结果
export interface OrderResult {
  id: number; //订单ID
  subOrderId?: number; //检查检验子项ID
  hisTestNo?: string; //HIS检查结果编号
  source?: string; //结果来源：HIS, USER用户自己上传
  type?: string; //TEST检验，EXAM检查
  testName?: string; // 检查检验名称
  deptName?: string; //科室名称
  deptCode?: string; //科室代码
  doctorCode?: string; //医生代码
  doctorName?: string; //医生姓名
  exeDeptCode?: string; //执行科室代码
  exeDeptName?: string; //执行科室名
  reporter?: string; //报告人
  auditor?: string; //审核人
  sentTime?: string; //送检时间
  reportTime?: string; //报告时间
  resultUrls?: string; // 用户自己上传的结果图片
  examResults?: Array<{
    //检查结果
    checkPart?: string; //检查部位
    checkMethod?: string; //检查方法
    checkSituation?: string; //检查所见
    option?: string; //诊断意见
    advice?: string; //医嘱项目
  }>;
  testResults?: Array<{
    //检验结果
    itemName?: string; //项目名称
    result?: string; //结果
    refRange?: string; //参考范围
    unit?: string; //单位
    abnormal?: string; //结果异常提示，0：正常，1：偏高，2：偏低
  }>;
  created?: string; //创建时间
  updatedAt?: string; //修改时间
}
export interface ExportParams {
  state: string;
  orderNo?: string;
  hospitalId?: number;
  executeHospitalId?: number;
  dateFrom?: string;
  dateTo?: string;
}
