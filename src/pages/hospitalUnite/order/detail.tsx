import React, { useEffect, useState } from 'react';
import {
  CardLayout,
  FormDescriptions,
  RouteComponentProps,
  Form,
  getPrice,
  actionConfirm
} from 'parsec-admin';
import styled from 'styled-components';
import useApi, { patientTypes, types, visitTypes } from './api';
import {
  Table,
  Button,
  Space,
  Row,
  Col,
  Input,
  Affix,
  Modal,
  message
} from 'antd';
import { useParams } from 'react-router-dom';
import { cash, age } from '@utils/tools';
import moment from 'moment';
import AllEnumHooks from '../allEnumHooks';
import mainDiagnosisFormat from '@src/utils/mainDiagnosisFormat';
const editSate = false;

export default ({ history }: RouteComponentProps) => {
  const { id = '0' } = useParams<any>();
  const [orderId, setOrderId] = useState(undefined as any);
  const [orderNo, setOrderNo] = useState('');
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [refundModalVisiable, setRefundModalVisiable] = useState(false);
  const [hisOrderModalVisiable, setHisOrderModalVisiable] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );
  const [selectedRows, setSelectedRows] = useState<Array<number>>([]);

  const {
    data: { OrderState = {}, OrderItemState = {}, PatRelation = {} }
  } = AllEnumHooks();

  useEffect(() => {
    if (id) {
      setOrderId(id);
    }
  }, [id]);

  const {
    request: requestDetail,
    data,
    loading: detailLoading
  } = useApi.getOrderDetail({
    params: { id: orderId || '' },
    needInit: !!orderId && orderId !== 'openOrder',
    initValue: { data: {} as any }
  });

  return (
    <Wrapper edit={false}>
      <Affix offsetTop={0} style={{ paddingTop: 24 }}>
        <CardLayout>
          <Row>
            <Col md={12}>
              <Input
                style={{ width: 200 }}
                placeholder='输入单号查询订单'
                onChange={e => {
                  setOrderNo(e.target.value);
                }}
                onPressEnter={e => {
                  if (orderNo) {
                    setLoading(true);
                    useApi.getOrderDetailByOrderNo
                      .request({
                        orderNo
                      })
                      .then(response => {
                        history.replace('/hospitalUnite/order/' + response.id);
                      })
                      .finally(() => setLoading(false));
                  }
                }}
              />
            </Col>
            <Col md={12}>
              <Row justify='end'>
                <Space size={22}>
                  {/* 订单状态：待付款、已取消（超时自动取消+患者主动取消）、已付款（展示“确定出单”和“退款”按钮）、申请退款中（展示“确定出单”和“退款”按钮）、已退款； */}
                  {(data.state === 'PAID' || data.state === 'APPLYREFUND') && (
                    <Button
                      type='primary'
                      onClick={() => {
                        setHisOrderModalVisiable(true);
                        setSelectedRows([]);
                      }}>
                      确认出单
                    </Button>
                  )}
                  {(data.state === 'PAID' || data.state === 'APPLYREFUND') && (
                    <Button
                      type='primary'
                      onClick={() => {
                        setRefundModalVisiable(true);
                        setSelectedRowKeys([]);
                      }}>
                      退款
                    </Button>
                  )}
                </Space>
              </Row>
            </Col>
          </Row>
        </CardLayout>
      </Affix>
      <CardLayout title={'订单信息'} loading={detailLoading || loading}>
        <FormDescriptions
          data={data}
          edit={false}
          form={form}
          loading={detailLoading || loading}
          items={[
            {
              label: '订单编号',
              name: 'orderNo'
            },
            {
              label: '订单类型',
              name: 'type',
              render: val => types[val]
            },
            {
              label: '订单状态',
              name: 'state',
              render: val => OrderState[val]
            },
            {
              label: '来源医院',
              name: 'fromHospital',
              render: v => <span>{v?.hospitalName}</span>
            },
            {
              label: '检查医院',
              name: 'executeHospital',
              render: v => <span>{v?.hospitalName}</span>
            },
            {
              label: '创建时间',
              name: 'createdAt',
              render: val => (
                <span>{moment(val).format('YYYY-MM-DD HH:mm:ss')}</span>
              )
            },
            {
              label: '支付时间',
              name: 'paidAt',
              render: val => (
                <span>{moment(val).format('YYYY-MM-DD HH:mm:ss')}</span>
              )
            },
            {
              label: '支付总额',
              name: 'payPrice',
              render: v => `￥${getPrice(v)}`
            },
            {
              label: '支付流水号',
              name: 'payStreamNo'
            },
            {
              label: '项目金额',
              name: 'totalItemPrice',
              render: v => `￥${getPrice(v)}`
            },
            {
              label: '挂号费',
              name: 'registration',
              render: v => `￥${getPrice(v)}`
            }
          ]}
        />
      </CardLayout>
      {data?.refundList && data?.refundList.length !== 0 && (
        <CardLayout title={'退款信息'} loading={detailLoading || loading}>
          <Table
            pagination={false}
            bordered={true}
            columns={[
              {
                title: '退款项目',
                dataIndex: 'content',
                align: 'center'
              },
              {
                title: '退款金额',
                dataIndex: 'refundFee',
                align: 'center',
                render: (val: any) => `¥${cash(val)}`
              },
              {
                title: '退款流水号',
                dataIndex: 'refundSerialNo',
                align: 'center'
              },
              {
                title: '退款时间',
                dataIndex: 'createdAt',
                align: 'center',
                render: val => (
                  <span>{moment(val).format('YYYY-MM-DD HH:mm:ss')}</span>
                )
              }
            ]}
            dataSource={data?.refundList}
            key={'id'}
          />
        </CardLayout>
      )}
      <CardLayout title={'就诊人信息'} loading={detailLoading || loading}>
        <FormDescriptions
          data={data?.user}
          edit={editSate}
          form={form}
          loading={detailLoading || loading}
          items={[
            {
              label: '姓名',
              name: 'userName'
            },
            {
              label: '性别',
              name: 'gender',
              render: v => (v === 'M' ? '男' : v === 'F' ? '女' : v)
            },
            {
              label: '年龄',
              name: 'birthDay',
              render: val => age(val) + '岁'
            },
            {
              label: '体重',
              name: 'weight',
              render: v => v + 'kg'
            },
            {
              label: '出生日期',
              name: 'birthDay'
            },
            {
              label: '地址信息',
              name: 'address'
            },
            {
              label: '证件类型',
              name: 'unionId',
              render: () => `身份证`
            },
            {
              label: '证件号码',
              name: 'idNo'
            },
            {
              label: '医保卡号',
              name: 'hcNo'
            },
            {
              label: '联系电话',
              name: 'mobile',
              span: 3
            },
            {
              label: '监护人姓名',
              name: 'guardianName'
            },
            {
              label: '证件类型',
              name: 'unionId',
              render: () => `身份证`
            },
            {
              label: '证件号码',
              name: 'guardianIdNo'
            },
            {
              label: '与就诊人关系',
              name: 'relation',
              render: v => PatRelation[v]
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'病历信息'} loading={detailLoading || loading}>
        <FormDescriptions
          data={data?.medicalRecord}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '姓名',
              name: 'patientName'
            },
            {
              label: '性别',
              name: 'patientSex'
            },
            {
              label: '年龄',
              name: 'patientAge'
            },
            {
              label: '体重',
              name: 'patientWeight',
              render: v => v + 'kg'
            },
            {
              label: '就诊时间',
              name: 'visitDate',
              render: v => (v ? moment(v).format('YYYY-MM-DD') : '')
            },
            {
              label: '就诊科室',
              name: 'deptName'
            },
            {
              label: '患者类型',
              name: 'patientType',
              render: v => patientTypes[v]
            },
            {
              label: '就诊类型',
              name: 'visitType',
              render: v => visitTypes[v],
              span: 2
            },
            {
              label: '主诉',
              name: 'chiefComplaint',
              span: 3
            },
            {
              label: '现病史',
              name: 'medicalHistory',
              span: 3
            },
            {
              label: '既往史',
              name: 'anamnesis',
              span: 3
            },
            {
              label: '体检',
              name: 'examination',
              span: 3
            },
            {
              label: '主要诊断',
              name: 'mainDiagnosis',
              span: 3,
              render: v => {
                return mainDiagnosisFormat(v).map((item, index) => {
                  return (
                    <div className=''>
                      {index + 1}、{item}
                    </div>
                  );
                });
              }
            },
            {
              label: '其它诊断',
              name: 'otherDiagnosis',
              span: 3
            },
            {
              label: '建议',
              name: 'recommend',
              span: 3
            }
          ]}
        />
      </CardLayout>
      {data && data.examItems && data.examItems.length > 0 && (
        <CardLayout title={'检查申请单'} loading={detailLoading || loading}>
          {(data.examItems || []).map((x: any) => {
            return (
              <Table
                title={() => <span>项目名称：{x.itemName}</span>}
                pagination={false}
                bordered={true}
                columns={[
                  {
                    title: '检验项',
                    dataIndex: 'itemName',
                    align: 'center'
                  },
                  {
                    title: '数量',
                    dataIndex: 'qty',
                    align: 'center'
                  },
                  {
                    title: '单价',
                    dataIndex: 'price',
                    align: 'center',
                    render: (val: any) => `¥${cash(val)}`
                  },
                  {
                    title: '项目金额',
                    dataIndex: 'price',
                    align: 'center',
                    render: (value, _row, index) => {
                      return {
                        children: `¥${cash(x?.price || 0)}`,
                        props: {
                          rowSpan:
                            index === 0 ? (x?.chargeItems || []).length : 0
                        }
                      };
                    }
                  },
                  {
                    title: '标本',
                    align: 'center',
                    render: (value, _row, index) => {
                      return {
                        children: `¥${cash(x?.price || 0)}`,
                        props: {
                          rowSpan:
                            index === 0 ? (x?.chargeItems || []).length : 0
                        }
                      };
                    }
                  },
                  {
                    title: '订单状态',
                    align: 'center',
                    render: (value, _row, index) => {
                      return {
                        children: OrderItemState[x?.state],
                        props: {
                          rowSpan:
                            index === 0 ? (x?.chargeItems || []).length : 0
                        }
                      };
                    }
                  },
                  {
                    title: '注意事项',
                    align: 'center',
                    render: (value, _row, index) => {
                      return {
                        children: x?.attention,
                        props: {
                          rowSpan:
                            index === 0 ? (x?.chargeItems || []).length : 0
                        }
                      };
                    }
                  }
                ]}
                dataSource={x?.chargeItems || []}
                key={x?.nodeId}
              />
            );
          })}
        </CardLayout>
      )}
      {data && data.testItems && data.testItems.length > 0 && (
        <CardLayout title={'检验申请单'} loading={detailLoading || loading}>
          {(data.testItems || []).map((x: any) => {
            return (
              <Table
                title={() => <span>项目名称：{x.itemName}</span>}
                pagination={false}
                bordered={true}
                columns={[
                  {
                    title: '检验项',
                    dataIndex: 'itemName',
                    align: 'center'
                  },
                  {
                    title: '数量',
                    dataIndex: 'qty',
                    align: 'center'
                  },
                  {
                    title: '单价',
                    dataIndex: 'price',
                    align: 'center',
                    render: (val: any) => `¥${cash(val)}`
                  },
                  {
                    title: '项目金额',
                    dataIndex: 'price',
                    align: 'center',
                    render: (value, _row, index) => {
                      return {
                        children: `¥${cash(x?.price || 0)}`,
                        props: {
                          rowSpan:
                            index === 0 ? (x?.chargeItems || []).length : 0
                        }
                      };
                    }
                  },
                  {
                    title: '标本',
                    align: 'center',
                    render: (value, _row, index) => {
                      return {
                        children: (
                          <div>
                            {(x?.specItems || []).map((y: any, i: number) => {
                              return <div key={i}>{y.itemName}</div>;
                            })}
                          </div>
                        ),
                        props: {
                          rowSpan:
                            index === 0 ? (x?.chargeItems || []).length : 0
                        }
                      };
                    }
                  },
                  {
                    title: '订单状态',
                    align: 'center',
                    render: (value, _row, index) => {
                      return {
                        children: OrderItemState[x?.state],
                        props: {
                          rowSpan:
                            index === 0 ? (x?.chargeItems || []).length : 0
                        }
                      };
                    }
                  },
                  {
                    title: '注意事项',
                    align: 'center',
                    render: (value, _row, index) => {
                      return {
                        children: x?.attention,
                        props: {
                          rowSpan:
                            index === 0 ? (x?.chargeItems || []).length : 0
                        }
                      };
                    }
                  }
                ]}
                dataSource={x?.chargeItems || []}
                key={x?.id}
              />
            );
          })}
          {(data.otherItems || []).map((x: any) => {
            return (
              <Table
                title={() => <span>{x.itemName}</span>}
                pagination={false}
                bordered={true}
                columns={[
                  {
                    title: '名称',
                    dataIndex: 'itemName',
                    align: 'center'
                  },
                  {
                    title: '数量',
                    dataIndex: 'qty',
                    align: 'center'
                  },
                  {
                    title: '单价',
                    dataIndex: 'price',
                    align: 'center',
                    render: (val: any) => `¥${cash(val)}`
                  },
                  {
                    title: '金额',
                    dataIndex: 'price',
                    align: 'center',
                    render: (v, record) => {
                      return `¥${cash(
                        (record.price || 0) * (record.qty || 0)
                      )}`;
                    }
                  },
                  {
                    title: '订单状态',
                    dataIndex: 'state',
                    align: 'center',
                    render: v => OrderItemState[v]
                  },
                  {
                    title: '注意事项',
                    dataIndex: 'attention',
                    align: 'center'
                  }
                ]}
                dataSource={x?.chargeItems || []}
                key={x?.id}
              />
            );
          })}
        </CardLayout>
      )}
      {refundModalVisiable && (
        <Modal
          title='请选择需要退款的项目'
          visible={true}
          width={800}
          onOk={() => {
            actionConfirm(() => {
              return useApi.refund
                .request({
                  id: Number(id),
                  subOrderIds: selectedRowKeys
                })
                .then(() => {
                  message.success('退款成功');
                  setRefundModalVisiable(false);
                  requestDetail({ id: id || '' });
                });
            }, '确认退款');
          }}
          onCancel={() => setRefundModalVisiable(false)}>
          <Table
            pagination={false}
            bordered={true}
            rowSelection={{
              onChange: selectedRowKeys => {
                setSelectedRowKeys(selectedRowKeys);
              },
              getCheckboxProps: record => ({
                disabled: record.state === 'REFUND' //已退款的数据不能再被选中
              })
            }}
            rowKey={'id'}
            columns={[
              {
                title: '序号',
                dataIndex: 'id',
                align: 'center',
                render: (_value, _row, index) => {
                  return index + 1;
                }
              },
              {
                title: '项目类型',
                dataIndex: 'type',
                align: 'center',
                render: value =>
                  value === 'TEST'
                    ? '检验项目'
                    : value === 'OTHER'
                    ? '其他'
                    : '检查项目'
              },
              {
                title: '项目名称',
                dataIndex: 'itemName',
                align: 'center'
              },
              {
                title: '项目金额',
                dataIndex: 'price',
                align: 'center',
                render: (val: any) => `¥${cash(val)}`
              },
              {
                title: '订单状态',
                dataIndex: 'state',
                align: 'center',
                render: value => OrderItemState[value]
              },
              {
                title: '退款原因',
                dataIndex: 'refundReason',
                align: 'center'
              }
            ]}
            dataSource={(data?.testItems || [])
              .concat(data?.examItems || [])
              .concat((data?.otherItems || [])[0]?.chargeItems || [])}
          />
        </Modal>
      )}
      {hisOrderModalVisiable && (
        <Modal
          title='请选择需要出单的项目'
          visible={true}
          width={800}
          onOk={() => {
            actionConfirm(() => {
              return useApi.createHisOrder
                .request({
                  id: Number(id),
                  subOrderIds: selectedRows
                })
                .then(() => {
                  message.success('开单成功');
                  setHisOrderModalVisiable(false);
                  requestDetail({ id: id || '' });
                });
            }, '确认开单');
          }}
          onCancel={() => setHisOrderModalVisiable(false)}>
          <Table
            pagination={false}
            bordered={true}
            rowSelection={{
              onChange: (selectedRowKeys, selectedRows) => {
                setSelectedRows(
                  (selectedRows || []).map(x => {
                    return x.id;
                  })
                );
              },
              getCheckboxProps: record => ({
                disabled: record.hisState === 1 //已出单的数据不能再被选中
              })
            }}
            rowKey={'id'}
            columns={[
              {
                title: '序号',
                dataIndex: 'id',
                align: 'center',
                render: (_value, _row, index) => {
                  return index + 1;
                }
              },
              {
                title: '项目类型',
                dataIndex: 'type',
                align: 'center',
                render: value =>
                  value === 'TEST'
                    ? '检验项目'
                    : value === 'OTHER'
                    ? '其他'
                    : '检查项目'
              },
              {
                title: '项目名称',
                dataIndex: 'itemName',
                align: 'center'
              },
              {
                title: '项目金额',
                dataIndex: 'price',
                align: 'center',
                render: (val: any) => `¥${cash(val)}`
              },
              {
                title: '订单状态',
                dataIndex: 'state',
                align: 'center',
                render: value => OrderItemState[value]
              },
              {
                title: '是否出单',
                dataIndex: 'hisState',
                align: 'center',
                render: value => (value === 1 ? '已出单' : '未出单')
              }
            ]}
            dataSource={(data?.testItems || [])
              .concat(data?.examItems || [])
              .concat((data?.otherItems || [])[0]?.chargeItems || [])}
          />
        </Modal>
      )}
    </Wrapper>
  );
};

const Wrapper = styled.div<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
  .ant-card-body {
    .ant-table-wrapper {
      margin-bottom: 20px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .ant-table {
    > .ant-table-container {
      > .ant-table-content {
        > table {
          > tbody {
            > tr {
              > td {
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
  .ant-card {
    margin: 0 24px 24px !important;
  }
  .ant-card-body {
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 6px 16px 0 rgba(0, 0, 0, 0), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  }
`;
