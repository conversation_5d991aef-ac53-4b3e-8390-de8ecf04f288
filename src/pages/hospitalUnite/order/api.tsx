import createApiHooks from 'create-api-hooks';
import { ListApiRequestParams, ListApiResponseData } from 'parsec-admin';
import { OrderResult, OrderList, ExportParams } from './d';
import { request } from 'parsec-admin';
//来源类型
export const resultType: any = {
  0: '正常',
  1: '偏高',
  2: '偏低'
};
//来源类型
export const source: any = {
  STANDARD: '标准版',
  INDEPENDENCE: '独立部署'
};
//类型
export const types: any = {
  TEST: '检验',
  EXAM: '检查'
};
//状态
export const orderStatus: any = {
  INIT: '创建成功',
  USERSYNCHRONIZING: '待审核方案',
  WAITINGPAY: '待付款',
  PAID: '已付款',
  CREATED: '开单成功',
  DONE: '检查完成',
  REPORTED: '已报告',
  REFUND: '已退款',
  APPLYREFUND: '申请退款',
  CANCELLED: '已取消'
};
//退款状态
export const funStatus: any = {
  WAITING_CHECKIN: '待登记',
  WAITING_PAY: '待用户缴费',
  PAID: '用户已缴费',
  WAITINGTEST: '待检查',
  FINISHED: '已完成',
  CANCELLED: '已取消',
  APPLYREFUND: '申请退款',
  REFUNDING: '退款中',
  REFUND: '退款完成'
};
export const accountStatusObj: any = {
  '': '全部',
  '0': '禁用',
  '1': '启用'
};
//患者类型
export const patientTypes: any = {
  1: '普病患者',
  2: '特病患者'
};
//就诊类型
export const visitTypes: any = {
  1: '在线问诊（初诊）',
  2: '复诊续方',
  3: '在线复诊'
};
//开单参数类型
export interface HisNoItemsType {
  subOrderId: number;
  hisOrderNo: string;
  price: number;
  qty: number;
}

export default {
  list: createApiHooks((params: ListApiRequestParams) =>
    request.get<ListApiResponseData<OrderList>>('/mch/transform/orders', {
      params
    })
  ),
  exportlist: createApiHooks((params: ListApiRequestParams) =>
    request.get<ListApiResponseData<OrderList>>(
      '/mch/transform/orders?isExport=1',
      {
        params
      }
    )
  ),
  getOrderDetail: createApiHooks((params: { id: string }) =>
    request.get<any>(`/mch/transform/order/${params.id}`)
  ),
  getOrderDetailByOrderNo: createApiHooks((params: { orderNo: string }) =>
    request.get<any>(`/mch/transform/order/detail/${params.orderNo}`)
  ),
  getOrderResult: createApiHooks((params: { id: string }) =>
    request.get<OrderResult>(`/mch/transform/order/result/${params.id}`)
  ),
  sureOrder: createApiHooks((params: { id: string }) =>
    request.put(`/mch/transform/order/done`, params)
  ),
  hisOrder: createApiHooks(
    (params: {
      id: string;
      hisNoItems: Array<{
        subOrderId: string;
        hisOrderNo: string;
        price: number;
      }>;
    }) => request.put(`/mch/transform/order/his-order`, params)
  ),
  cancleOrder: createApiHooks((params: { id: string; cancelReason: string }) =>
    request.put(`/mch/transform/order/cancel`, params)
  ),
  actionStatus: createApiHooks(
    (params: {
      id: number;
      subOrderId: number;
      refundMoney: number;
      refundReason: string;
    }) => request.put('/mch/transform/order/refund', params)
  ),
  refundAll: createApiHooks((params: { id: number }) =>
    request.post('/mch/transform/order/refund-all', params)
  ),
  payNotie: createApiHooks((params: { id: string }) =>
    request.put('/mch/transform/order/pay-notification', params)
  ),
  cancelOrder: createApiHooks((params: { id: string }) =>
    request.delete<any>(`/ihis/transform/order/${params.id}`)
  ),
  exportOrder: createApiHooks((params: ExportParams) =>
    request.get<Blob>('/mch/transform/order/export', {
      responseType: 'blob',
      params
    })
  ),
  // 确认部分退款
  refund: createApiHooks(
    (params: {
      id: number;
      subOrderIds: (string | number)[];
      refundReason?: string;
    }) => request.put('/mch/transform/order/refund', params)
  ),
  // 在医院HIS开单
  createHisOrder: createApiHooks(
    (params: { id: number; subOrderIds: Array<number> }) =>
      request.put('/mch/transform/order/his-order', params)
  )
};
