import React, { useMemo, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>ePicker,
  LinkButton,
  ArrSelect,
  RouteComponentProps
} from 'parsec-admin';
import useApi from './api';
import useApis from '../hospital/api';
import { saveAs } from 'file-saver';
import OldTableList from '@components/oldTableList';
import { hidePhone, cash } from '@utils/tools';
import { Button } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import AllEnumHooks from '../allEnumHooks';
import moment from 'moment';
export default ({ history }: RouteComponentProps) => {
  const [queryParams, setQueryParams] = useState({} as any);
  const { request: handleExport, loading: exportLoading } = useApi.exportOrder({
    needInit: false
  });
  const {
    data: { OrderState = {} }
  } = AllEnumHooks();
  // 获取医联体医院列表
  const {
    data: { list: treeItems }
  } = useApis.getHospital({
    params: {
      page: 1,
      limit: 999
    },
    needInit: true,
    initValue: []
  });
  return (
    <OldTableList
      action={
        <Button
          type={'default'}
          loading={exportLoading}
          icon={<ExportOutlined />}
          onClick={() =>
            handleExport({ ...queryParams, isExport: 1 }).then(data =>
              saveAs(
                data,
                `订单列表 ${moment().format('YYYY-MM-DD HH时mm分ss秒')}.xls`
              )
            )
          }>
          导出
        </Button>
      }
      getList={({ pagination: { current }, params }) => {
        setQueryParams({
          ...params,
          page: 1,
          limit: 999
        });
        return useApi.list.request({
          page: current,
          limit: 10,
          ...params
        });
      }}
      tableTitle={'订单列表'}
      columns={useMemo(
        () => [
          {
            title: '订单编号',
            dataIndex: 'orderNo',
            width: 100,
            fixed: 'left',
            search: true
          },
          {
            title: '状态',
            dataIndex: 'state',
            width: 100,
            render: val => OrderState[val],
            //待付款、已取消、已付款、申请退款中、已退款
            search: (
              <ArrSelect
                options={[
                  {
                    children: '待付款',
                    value: 'WAITINGPAY'
                  },
                  {
                    children: '已取消',
                    value: 'CANCELLED'
                  },
                  {
                    children: '已付款',
                    value: 'PAID'
                  },
                  {
                    children: '申请退款中',
                    value: 'APPLYREFUND'
                  },
                  {
                    children: '退款中',
                    value: 'REFUNDING'
                  },
                  {
                    children: '已退款',
                    value: 'REFUND'
                  }
                ]}
              />
            )
          },
          {
            title: '用户姓名',
            dataIndex: 'userName',
            width: 100,
            render: (_, record: any) => record?.user?.userName
          },
          {
            title: '用户电话',
            dataIndex: 'phone',
            width: 150,
            render: (_, record: any) => hidePhone(record?.user?.mobile)
          },
          {
            title: '总金额',
            width: 100,
            dataIndex: 'totalItemPrice',
            render: val => cash(val)
          },
          {
            title: '挂号费',
            dataIndex: 'registration',
            width: 100,
            render: val => cash(val)
          },
          {
            title: '来源医院',
            dataIndex: 'hospitalId',
            width: 100,
            render: (_, record: any) => record?.fromHospital.hospitalName,
            search: (
              <ArrSelect
                optionFilterProp='children'
                options={(treeItems || []).map(x => ({
                  value: x.id,
                  children: x.hospitalName
                }))}
              />
            )
          },
          {
            title: '执行医院',
            dataIndex: 'executeHospitalId',
            width: 100,
            render: (_, record: any) => record?.executeHospital?.hospitalName,
            search: (
              <ArrSelect
                optionFilterProp='children'
                options={(treeItems || []).map(x => ({
                  value: x.id,
                  children: x.hospitalName
                }))}
              />
            )
          },
          {
            title: '创建时间',
            dataIndex: 'createdAt',
            width: 150,
            render: v => moment(v).format('YYYY-MM-DD'),
            search: <DayRangePicker placeholder={['开始时间', '结束时间']} />,
            searchIndex: ['dateFrom', 'dateTo']
          },
          {
            title: '操作',
            fixed: 'right',
            width: 80,
            render: record => (
              <LinkButton
                onClick={() =>
                  history.push('/hospitalUnite/order/' + record.id)
                }>
                详情
              </LinkButton>
            )
          }
        ],
        [OrderState, history, treeItems]
      )}
    />
  );
};
