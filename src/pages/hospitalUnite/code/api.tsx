import createApiHooks from 'create-api-hooks';
import { ListApiRequestParams, ListApiResponseData } from 'parsec-admin';
import { request } from 'parsec-admin';
import { TreeObj, UserList } from './d';
export const treeLevel: any = {
  '1': '分类',
  '2': '子类',
  '3': '部位',
  '4': '检查项'
};
export const treeLevel1: any = {
  '1': '诊疗项目',
  '2': '收费项目',
  '3': '标本项目'
};
export const treeType: any = {
  '': '全部',
  TEST: '检验',
  EXAM: '检查',
  SPEC: '标本'
};
export const treeStatus: any = {
  '': '全部',
  ON: '已上线',
  OFF: '未上线'
};
export default {
  treeItems: createApiHooks(
    (params: { itemName: string; type: string; level: number }) =>
      request.get<
        {
          id: number; //项目ID
          itemName: string; //检验项目
          enName: string; //英文名称
          itemCode: string;
          type: string; // 检验，检查
          level: number; //层级
        }[]
      >('/mch/transform/test-items', {
        params
      })
  ),
  treelist: createApiHooks((params: ListApiRequestParams) =>
    request.get<ListApiResponseData<TreeObj>>(
      '/mch/transform/test-item/tree-items',
      {
        params
      }
    )
  ),
  treeStatus: createApiHooks(
    (params: { standardNodeId: string; state: string }) =>
      request.put('/mch/transform/test-item/tree-item/onoff', params)
  ),
  delTreeNode: createApiHooks((params: { standardNodeId: string }) =>
    request.delete(
      `/mch/transform/test-item/tree-item/${params.standardNodeId}`
    )
  ),
  delRecord: createApiHooks((params: { id: string }) =>
    request.delete(`/mch/transform/test-item/${params.id}`)
  ),
  addSpecNode: createApiHooks(
    (params: {
      itemName: string;
      enName?: string;
      itemCode?: string;
      indication?: string;
      significance?: string;
      type: string;
      attention?: string;
      inputCode?: string;
    }) =>
      request.post(`/mch/transform/test-item/spec`, params, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
  ),
  addTreeNode: createApiHooks(
    (params: {
      itemName: string;
      enName?: string;
      itemCode?: string;
      parentId: string;
      indication?: string;
      significance?: string;
      type: string;
      level: string;
      attention?: string;
      inputCode?: string;
    }) =>
      request.post(`/mch/transform/test-item`, params, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
  ),
  addlinkTreeNode: createApiHooks(
    (params: { parentId: string; standardId: string }) =>
      request.post(`/mch/transform/test-item/tree-child`, params, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
  ),
  editTreeNode: createApiHooks(
    (params: {
      standardId: string;
      itemName?: string;
      enName?: string;
      itemCode?: string;
      indication?: string;
      significance?: string;
      attention?: string;
      inputCode?: string;
    }) => request.put(`/mch/transform/test-item`, params)
  ),
  list: createApiHooks((params: ListApiRequestParams) =>
    request.get<ListApiResponseData<UserList>>('/mch/transform/orders', {
      params
    })
  ),
  批量导入: createApiHooks((params: { file: File }) => {
    const formData = new FormData();
    formData.append('file', params.file);
    return request.post('/mch/transform/test-item/import', formData);
  })
};
