import React, { useMemo } from 'react';
import { LinkButton, actionConfirm } from 'parsec-admin';
import useApi, { treeType } from './api';
import OldTableList from '@components/oldTableList';
export default () => {
  return (
    <OldTableList
      name='4'
      getList={({ pagination: { current }, params }) => {
        return useApi.treelist.request({
          page: current,
          limit: 10,
          ...{ ...params, unused: 1 }
        });
      }}
      tableTitle={false}
      rowKey={({ inputCode }: any) => inputCode}
      columns={useMemo(
        () => [
          {
            title: '项目名称',
            dataIndex: 'itemName',
            width: 100,
            fixed: 'left',
            search: true
          },
          {
            title: '项目编码',
            dataIndex: 'itemCode',
            width: 100,
            search: true
          },
          {
            title: '项目类型',
            dataIndex: 'type',
            width: 100,
            render: val => treeType[val]
          },
          {
            title: '操作',
            fixed: 'right',
            width: 120,
            render: (record: any) => {
              return (
                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.delRecord.request({
                          id: record.id
                        }),
                      '删除'
                    );
                  }}>
                  删除
                </LinkButton>
                // <ActionsWrap max={99}>
                //   <LinkButton
                //     onClick={() => {
                //       switchModalVisible({ ...record });
                //     }}>
                //     编辑
                //   </LinkButton>
                // </ActionsWrap>
              );
            }
          }
        ],
        []
      )}
    />
  );
};
