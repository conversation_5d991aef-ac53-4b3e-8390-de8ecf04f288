import React, { useMemo, useState, useRef, useCallback } from 'react';
import {
  useModal,
  LinkButton,
  ArrSelect,
  ActionsWrap,
  actionConfirm,
  Form,
  handleSubmit
} from 'parsec-admin';
import { useForceUpdate } from 'parsec-hooks';
import useApi, { treeStatus, treeLevel1 } from './api';
import OldTableList from '@components/oldTableList';
import { Button, Input, AutoComplete, message } from 'antd';
import { ImportOutlined } from '@ant-design/icons';
export default () => {
  const [form] = Form.useForm();
  const [itemName, setItemName] = useState('');
  const level = form.getFieldValue('level');
  const currentIdRef = useRef<number>();
  const [treeLevelObj, setTreeLevelObj] = useState(treeLevel1);
  const { loading: loadingImport, request: requestImport } = useApi.批量导入({
    needInit: false
  });
  const { data: treeItems } = useApi.treeItems({
    params: {
      itemName,
      type: level === 3 ? 'SPEC' : 'TEST',
      level: level === 3 ? 2 : level
    },
    needInit: !!level,
    initValue: []
  });
  const { forceUpdate } = useForceUpdate();
  const timerRef = useRef<any>();
  const changeLevel = useCallback(
    (record, b?: boolean) => {
      const levelData = Object.keys(treeLevelObj)
        .filter(key => Number(key) > Number(record.level))
        .reduce((obj: any, keys) => {
          obj[keys] = treeLevelObj[keys];
          return obj;
        }, {});
      setTreeLevelObj(levelData);
      const newlevel = b
        ? 3
        : Number(record.level) >= 4
        ? 4
        : Number(record.level) + 1;
      form.setFieldsValue({
        level: newlevel
      });
    },
    [form, treeLevelObj]
  );
  const handleSearch = (value: string) => {
    if (value) {
      console.log(value);
      clearTimeout(timerRef.current);
      timerRef.current = setTimeout(() => setItemName(value), 200);
    }
  };
  const onSelect = (value: string) => {
    const finedValue: any = treeItems.find(({ id }) => id === +value) || {
      id: undefined,
      itemCode: undefined,
      enName: undefined,
      indication: undefined,
      significance: undefined,
      attention: undefined,
      inputCode: undefined
    };
    console.log(treeItems);
    console.log(value);
    currentIdRef.current = finedValue.id;

    const obj = { ...finedValue, standardId: finedValue.id };

    delete obj.id;

    form.setFieldsValue(obj);
    forceUpdate();
  };
  const switchModalVisible = useModal<any>(
    ({ standardId, id }) => ({
      title: `${
        standardId ? (id ? '编辑检验项目' : '新增检验子项目') : '新增检验项目'
      }`,
      form,
      myFormProps: {
        formProps: {
          initialValues: { level: 1 }
        }
      },
      onCancel: () => {
        setTreeLevelObj(treeLevel1);
        currentIdRef.current = undefined;
      },
      onSubmit: values =>
        handleSubmit(async () => {
          if (values.id) {
            //编辑
            await useApi.editTreeNode.request({ ...values });
          } else {
            //新增
            if (values.parentId === 1 || !currentIdRef.current) {
              //顶级新增 || 无关联的新增
              const newvalues = { ...values };
              if (level === 3) {
                newvalues.level = 2;
              }
              await useApi.addTreeNode.request({
                type: level === 3 ? 'SPEC' : 'TEST',
                ...newvalues
              });
            } else {
              const newvalues = { ...values };
              if (level === 3) {
                newvalues.level = 2;
              }
              //否则用addTreeNode
              await useApi.addlinkTreeNode.request({
                ...newvalues,
                standardId: currentIdRef.current
              });
            }
          }

          setTreeLevelObj(treeLevel1);
        }),
      items: [
        {
          name: 'id',
          render: false
        },
        {
          name: 'parentId',
          render: false
        },
        {
          name: 'standardId',
          render: false
        },
        {
          label: '项目类型',
          name: 'level',
          render: () => {
            return (
              <ArrSelect
                allowClear={false}
                onChange={() => {
                  forceUpdate();
                  console.log(treeItems);
                  form.resetFields([
                    'itemName',
                    'itemCode',
                    'enName',
                    'indication',
                    'significance',
                    'attention',
                    'standardId'
                  ]);
                  currentIdRef.current = undefined;
                }}
                disabled={id}
                options={treeLevelObj}
              />
            );
          },
          required: true
        },
        {
          label: '项目名称',
          name: 'itemName',
          render: (v, { level, id, parentId }) =>
            (!id && parentId === 2) || id ? (
              <Input />
            ) : (
              <AutoComplete
                onClear={() => setItemName('')}
                allowClear={true}
                options={[
                  ...treeItems.map(({ id, itemName }) => ({
                    value: id,
                    label: itemName
                  }))
                ]}
                onChange={onSelect}
                onSearch={handleSearch}
                placeholder='请输入项目名称'
              />
            ),
          required: true
        },
        {
          label: '英文名称',
          name: 'enName',
          render: () => (
            <Input
              disabled={!!currentIdRef.current}
              placeholder='请输入英文名称'
            />
          )
        },
        {
          label: '项目编码',
          name: 'itemCode',
          required: true,
          render: () => (
            <Input
              disabled={!!currentIdRef.current}
              placeholder='请输入项目编码'
            />
          )
        },

        {
          label: '适应症',
          name: 'indication',
          render: () => (
            <Input
              disabled={!!currentIdRef.current}
              placeholder='请输入适应症'
            />
          )
        },
        {
          label: '化验意义',
          name: 'significance',
          render: () => (
            <Input
              disabled={!!currentIdRef.current}
              placeholder='请输入化验意义'
            />
          )
        },
        {
          label: '注意事项',
          name: 'attention',
          render: () => (
            <Input
              disabled={!!currentIdRef.current}
              placeholder='请输入注意事项'
            />
          )
        }
      ]
    }),
    [treeItems]
  );
  return (
    <OldTableList
      name='2'
      action={
        <>
          <Button
            type={'default'}
            onClick={() => {
              setTreeLevelObj({
                '1': '诊疗项目'
              });
              return switchModalVisible({
                parentId: 1
              });
            }}>
            新增顶级诊疗项目
          </Button>
          <Button
            type={'primary'}
            loading={loadingImport}
            icon={<ImportOutlined />}
            onClick={() => {
              const el = document.createElement('input');
              el.setAttribute('type', 'file');
              el.addEventListener('change', () => {
                const file = el.files?.[0];
                console.log(file);
                if (file) {
                  requestImport({ file })
                    .then(() => {
                      message.success('导入成功');
                    })
                    .catch(() => {
                      message.error('导入失败');
                    });
                }
              });
              el.click();
            }}>
            导入标准对码文件
          </Button>
        </>
      }
      getList={({ pagination: { current }, params }) => {
        return useApi.treelist.request({
          page: current,
          limit: 10,
          ...{ ...params, type: 'TEST' }
        });
      }}
      tableTitle={false}
      // rowKey={({ inputCode }: any) => inputCode}
      columns={useMemo(
        () => [
          {
            title: '项目名称',
            dataIndex: 'itemName',
            width: 100,
            fixed: 'left'
          },
          {
            title: '项目编码',
            dataIndex: 'itemCode',
            width: 100
          },
          {
            title: '项目类型',
            dataIndex: 'level',
            width: 100,
            render: (val, record: any) => {
              if (val === 2) {
                if (record.type === 'TEST') {
                  return '收费项目';
                } else {
                  return '标本项目';
                }
              } else {
                return treeLevel1[val];
              }
            }
          },
          {
            title: '状态',
            dataIndex: 'state',
            width: 100,
            render: (val, record: any) => record.level === 2 && treeStatus[val]
          },
          {
            title: '',
            fixed: 'right',
            width: 80,
            render: (record: any) => {
              return (
                <ActionsWrap max={99}>
                  {record.level === 2 && (
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.treeStatus.request({
                              standardNodeId: record.id,
                              state: record.state === 'ON' ? 'OFF' : 'ON'
                            }),
                          record.state === 'ON' ? '下线' : '上线'
                        );
                      }}>
                      {record.state === 'ON' ? '下线' : '上线'}
                    </LinkButton>
                  )}
                  {record.level === 1 && (
                    <LinkButton
                      onClick={() => {
                        currentIdRef.current = undefined;
                        changeLevel(record);
                        switchModalVisible({
                          parentId: record.id
                          // standardId: record.standardId
                        });
                      }}>
                      新增子项目
                    </LinkButton>
                  )}
                  {record.level === 1 && (
                    <LinkButton
                      onClick={() => {
                        currentIdRef.current = undefined;
                        changeLevel(record, true);
                        switchModalVisible({
                          parentId: record.id
                          // standardId: record.standardId
                        });
                      }}>
                      新增标本
                    </LinkButton>
                  )}
                </ActionsWrap>
              );
            }
          },
          {
            title: '操作',
            width: 60,
            render: (record: any) => {
              return (
                <ActionsWrap max={99}>
                  <LinkButton
                    onClick={() => {
                      currentIdRef.current = undefined;
                      switchModalVisible({ ...record });
                    }}>
                    编辑
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          useApi.delTreeNode.request({
                            standardNodeId: record.id
                          }),
                        '删除'
                      );
                    }}>
                    删除
                  </LinkButton>
                </ActionsWrap>
              );
            }
          }
        ],
        [changeLevel, switchModalVisible]
      )}
    />
  );
};
