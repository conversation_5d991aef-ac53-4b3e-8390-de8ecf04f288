import React from 'react';
import { CardLayout, useReloadTableList } from 'parsec-admin';
import { Tabs } from 'antd';
import Check from './check';
import Inspect from './inspect';
import Specimen from './specimen';
import Unlink from './unlink';
const { TabPane } = Tabs;
export default () => {
  const reloadTable = useReloadTableList();
  return (
    <CardLayout>
      <Tabs defaultActiveKey='1' onTabClick={reloadTable}>
        <TabPane tab='检查管理' key='1'>
          <Check />
        </TabPane>
        <TabPane tab='检验管理' key='2'>
          <Inspect />
        </TabPane>
        <TabPane tab='标本管理' key='3'>
          <Specimen />
        </TabPane>
        <TabPane tab='未关联项目' key='4'>
          <Unlink />
        </TabPane>
      </Tabs>
    </CardLayout>
  );
};
