import React, { useMemo, useState, useRef, useCallback } from 'react';
import {
  useModal,
  LinkButton,
  ArrSelect,
  ActionsWrap,
  actionConfirm,
  handleSubmit,
  Form
} from 'parsec-admin';
import { useForceUpdate } from 'parsec-hooks';
import useApi, { treeStatus, treeLevel } from './api';
import OldTableList from '@components/oldTableList';
import { Button, Input, AutoComplete } from 'antd';

export default () => {
  const [form] = Form.useForm();
  const [itemName, setItemName] = useState('' as any);
  const level = form.getFieldValue('level');
  const currentIdRef = useRef<number>();
  const [treeLevelObj, setTreeLevelObj] = useState(treeLevel);
  const { data: treeItems } = useApi.treeItems({
    params: {
      itemName,
      type: 'EXAM',
      level
    },
    needInit: !!itemName && !!level,
    initValue: []
  });
  const { forceUpdate } = useForceUpdate();
  const timerRef = useRef<any>();
  const changeLevel = useCallback(
    record => {
      const levelData = Object.keys(treeLevelObj)
        .filter(key => Number(key) > Number(record.level))
        .reduce((obj: any, keys) => {
          obj[keys] = treeLevelObj[keys];
          return obj;
        }, {});
      setTreeLevelObj(levelData);
      form.setFieldsValue({
        level: Number(record.level) >= 4 ? 4 : Number(record.level) + 1
      });
    },
    [form, treeLevelObj]
  );

  const handleSearch = (value: string) => {
    if (value) {
      clearTimeout(timerRef.current);
      timerRef.current = setTimeout(() => {
        setItemName(value);
      }, 200);
    }
  };
  const onSelect = (value: string) => {
    forceUpdate();
    form.resetFields([
      'itemCode',
      'enName',
      'indication',
      'significance',
      'attention',
      'inputCode',
      'standardId'
    ]);
    currentIdRef.current = undefined;
    const finedValue: any = treeItems.find(({ id }) => id === +value) || {
      id: undefined,
      itemCode: undefined,
      enName: undefined,
      indication: undefined,
      significance: undefined,
      attention: undefined,
      inputCode: undefined
    };
    console.log(treeItems);
    console.log(value);
    currentIdRef.current = finedValue.id;

    const obj = { ...finedValue, standardId: finedValue.id };

    delete obj.id;

    form.setFieldsValue(obj);
    forceUpdate();
  };

  const switchModalVisible = useModal<any>(({ standardId, id }) => ({
    title: `${
      standardId ? (id ? '编辑检查项目' : '新增检查子项目') : '新增检查项目'
    }`,
    form,
    myFormProps: {
      formProps: {
        initialValues: { level: 1 }
      }
    },
    onCancel: () => {
      setTreeLevelObj(treeLevel);
      currentIdRef.current = undefined;
    },
    onSubmit: values =>
      handleSubmit(async () => {
        if (values.id) {
          //编辑
          await useApi.editTreeNode.request({ ...values });
        } else {
          //新增
          if (values.parentId === 2 || !currentIdRef.current) {
            //顶级新增 || 无关联的新增
            await useApi.addTreeNode.request({
              type: 'EXAM',
              ...values
            });
          } else {
            //否则用addTreeNode
            await useApi.addlinkTreeNode.request({
              ...values,
              standardId: currentIdRef.current
            });
          }
        }

        setTreeLevelObj(treeLevel);
      }),
    items: [
      {
        name: 'id',
        render: false
      },
      {
        name: 'parentId',
        render: false
      },
      {
        name: 'standardId',
        render: false
      },
      {
        label: '项目类型',
        name: 'level',
        render: () => {
          return (
            <ArrSelect
              allowClear={false}
              onChange={() => {
                forceUpdate();
                form.resetFields([
                  'itemName',
                  'itemCode',
                  'enName',
                  'indication',
                  'significance',
                  'attention',
                  'inputCode',
                  'standardId'
                ]);
                currentIdRef.current = undefined;
              }}
              disabled={id}
              options={treeLevelObj}
            />
          );
        },
        required: true
      },
      {
        label: '项目名称',
        name: 'itemName',
        render: (v, { level, id, parentId }) =>
          (!id && parentId === 2) || id ? (
            <Input />
          ) : (
            <AutoComplete
              allowClear={true}
              options={[
                ...treeItems.map(({ id, itemName }) => ({
                  value: id,
                  label: itemName
                }))
              ]}
              onChange={onSelect}
              onSearch={handleSearch}
              placeholder='请输入项目名称'
            />
          ),
        required: true
      },
      {
        label: '英文名称',
        name: 'enName',
        render: () => (
          <Input
            disabled={!!currentIdRef.current}
            placeholder='请输入英文名称'
          />
        )
      },
      {
        label: '项目编码',
        name: 'itemCode',
        required: true,
        render: () => (
          <Input
            disabled={!!currentIdRef.current}
            placeholder='请输入项目编码'
          />
        )
      },

      {
        label: '适应症',
        name: 'indication',
        render: () => (
          <Input disabled={!!currentIdRef.current} placeholder='请输入适应症' />
        )
      },
      {
        label: '化验意义',
        name: 'significance',
        render: () => (
          <Input
            disabled={!!currentIdRef.current}
            placeholder='请输入化验意义'
          />
        )
      },
      {
        label: '注意事项',
        name: 'attention',
        render: () => (
          <Input
            disabled={!!currentIdRef.current}
            placeholder='请输入注意事项'
          />
        )
      },
      {
        label: '输入码',
        name: 'inputCode',
        render: () => (
          <Input disabled={!!currentIdRef.current} placeholder='请输入输入码' />
        )
      }
    ]
  }));
  return (
    <OldTableList
      action={
        <Button
          type={'default'}
          onClick={() => {
            setTreeLevelObj({
              '1': '分类'
            });
            currentIdRef.current = undefined;
            return switchModalVisible({
              parentId: 2
            });
          }}>
          新增顶级检查项目
        </Button>
      }
      getList={({ pagination: { current }, params }) => {
        return useApi.treelist.request({
          page: current,
          limit: 10,
          ...{ ...params, type: 'EXAM' }
        });
      }}
      tableTitle={false}
      // rowKey={({ inputCode }: any) => inputCode}
      columns={useMemo(
        () => [
          {
            title: '项目名称',
            dataIndex: 'itemName',
            width: 100,
            fixed: 'left'
          },
          {
            title: '项目编码',
            dataIndex: 'itemCode',
            width: 100
          },
          {
            title: '项目类型',
            dataIndex: 'level',
            width: 100,
            render: val => treeLevel[val]
          },
          {
            title: '状态',
            dataIndex: 'state',
            width: 100,
            render: (val, record: any) => record.level === 4 && treeStatus[val]
          },
          {
            title: '操作',
            fixed: 'right',
            width: 120,
            render: (record: any) => {
              if (record.level === 4) {
                return (
                  <ActionsWrap max={99}>
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.treeStatus.request({
                              standardNodeId: record.id,
                              state: record.state === 'ON' ? 'OFF' : 'ON'
                            }),
                          record.state === 'ON' ? '下线' : '上线'
                        );
                      }}>
                      {record.state === 'ON' ? '下线' : '上线'}
                    </LinkButton>
                    <LinkButton
                      onClick={() => {
                        currentIdRef.current = undefined;
                        switchModalVisible({ ...record });
                      }}>
                      编辑
                    </LinkButton>
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.delTreeNode.request({
                              standardNodeId: record.id
                            }),
                          '删除'
                        );
                      }}>
                      删除
                    </LinkButton>
                  </ActionsWrap>
                );
              } else {
                return (
                  <ActionsWrap max={99}>
                    <LinkButton
                      onClick={() => {
                        currentIdRef.current = undefined;
                        changeLevel(record);
                        switchModalVisible({
                          parentId: record.id
                        });
                      }}>
                      新增子项目
                    </LinkButton>
                    <LinkButton
                      onClick={() => {
                        currentIdRef.current = undefined;
                        switchModalVisible({ ...record });
                      }}>
                      编辑
                    </LinkButton>
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.delTreeNode.request({
                              standardNodeId: record.id
                            }),
                          '删除'
                        );
                      }}>
                      删除
                    </LinkButton>
                  </ActionsWrap>
                );
              }
            }
          }
        ],
        [changeLevel, switchModalVisible]
      )}
    />
  );
};
