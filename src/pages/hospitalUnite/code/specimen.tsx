import React, { useMemo } from 'react';
import {
  useModal,
  LinkButton,
  ActionsWrap,
  actionConfirm,
  Form,
  handleSubmit
} from 'parsec-admin';
import useApi from './api';
import OldTableList from '@components/oldTableList';
import { Button } from 'antd';
export default () => {
  const [form] = Form.useForm();

  const switchModalVisible = useModal<any>({
    title: `${form.getFieldValue('standardId') ? '编辑标本' : '新增标本'}`,
    form,
    myFormProps: {
      formProps: {
        initialValues: { level: 1 }
      }
    },
    onSubmit: values =>
      handleSubmit(() => {
        if (values.id) {
          values.standardId = values.id;
          return useApi.editTreeNode.request({ ...values });
        } else {
          return useApi.addSpecNode.request({
            type: 'SPEC',
            level: 2,
            ...values
          });
        }
      }),
    items: [
      {
        name: 'id',
        render: false
      },
      {
        name: 'parentId',
        render: false
      },
      {
        name: 'standardId',
        render: false
      },
      {
        label: '项目名称',
        name: 'itemName',
        required: true
      },
      {
        label: '英文名称',
        name: 'enName'
      },
      {
        label: '项目编码',
        name: 'itemCode',
        required: true
      },
      {
        label: '适应症',
        name: 'indication'
      },
      {
        label: '化验意义',
        name: 'significance'
      },
      {
        label: '注意事项',
        name: 'attention'
      }
    ]
  });
  return (
    <OldTableList
      name='3'
      getList={({ pagination: { current }, params }) => {
        return useApi.treelist.request({
          page: current,
          limit: 10,
          ...{ ...params, type: 'SPEC' }
        });
      }}
      action={
        <Button type={'default'} onClick={() => switchModalVisible()}>
          新增标本
        </Button>
      }
      tableTitle={false}
      rowKey={({ inputCode }: any) => inputCode}
      columns={useMemo(
        () => [
          {
            title: '项目名称',
            dataIndex: 'itemName',
            width: 100,
            fixed: 'left',
            search: true
          },
          {
            title: '项目编码',
            dataIndex: 'itemCode',
            width: 100,
            search: true
          },
          {
            title: '操作',
            fixed: 'right',
            width: 120,
            render: (record: any) => {
              return (
                <ActionsWrap max={99}>
                  <LinkButton
                    onClick={() => {
                      switchModalVisible({ ...record });
                    }}>
                    编辑
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          useApi.delRecord.request({
                            id: record.id
                          }),
                        '删除'
                      );
                    }}>
                    删除
                  </LinkButton>
                </ActionsWrap>
              );
            }
          }
        ],
        [switchModalVisible]
      )}
    />
  );
};
