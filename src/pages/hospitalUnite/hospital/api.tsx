import createApiHooks from 'create-api-hooks';
import {} from '@pages/hospitalUnite/code/d';
import { ListApiRequestParams, ListApiResponseData } from 'parsec-admin';
import { request } from 'parsec-admin';
import { HospitalList, SignParams, SignList, SignPutParams } from './d';
export const Status: any = {
  '': '全部',
  ON: '已上线',
  OFF: '未上线'
};
export const SignType: any = {
  '-1': '乙方向甲方流转',
  '0': '甲方乙方双向流转',
  '1': '甲方向乙方流转'
};
export const PayeeType: any = {
  EXECUTOR: '检查方收款',
  ORDERER: '开单方收款',
  FIXED: '固定医院收款'
};

export default {
  Hospitallist: createApiHooks((params: ListApiRequestParams) =>
    request.get<ListApiResponseData<HospitalList>>('/mch/transform/hospitals', {
      params
    })
  ),
  getHospital: createApiHooks((params: { page: number; limit: number }) =>
    request.get<ListApiResponseData<HospitalList>>('/mch/transform/hospitals', {
      params
    })
  ),
  saveHospital: createApiHooks(
    (params: { ids: string; registration: number }) =>
      request.post('/mch/transform/hospital', params)
  ),
  getPay: createApiHooks((params: { hostitalId: string }) =>
    request.get<{
      hospitalId?: string;
      merchantId?: string;
      channelId?: string;
      backUrl?: string;
      secretKey?: string;
      updatedAt?: string;
      createdAt?: string;
    }>('/mch/transform/hospital/payinfo', {
      params
    })
  ),
  savePay: createApiHooks(
    (params: {
      hospitalId: string;
      merchantId: string;
      channelId: string;
      backUrl: string;
      secretKey: string;
    }) => request.post('/mch/transform/hospital/payinfo', params)
  ),
  changeHospitalStatus: createApiHooks(
    (params: { id: string; state: string }) =>
      request.put('/mch/transform/hospital/state', params)
  ),
  delHospital: createApiHooks((params: { id: string }) =>
    request.delete(`/mch/transform/hospital/${params.id}`, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  ),
  HospitalDetail: createApiHooks(
    (params: { ids: string; operType: string; hisId: number }) =>
      request.post('/mch/user/doctorAccount/updateBatch', params)
  ),
  signlist: createApiHooks((params: ListApiRequestParams) =>
    request.get<ListApiResponseData<SignList>>('/mch/transform/agreements', {
      params
    })
  ),
  saveSign: createApiHooks((params: SignParams) =>
    request.post<SignParams & { id: string }>(
      '/mch/transform/agreement',
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  updateSign: createApiHooks((params: SignPutParams) =>
    request.put<
      {
        id?: string;
      } & SignPutParams
    >('/mch/transform/agreement', params)
  ),
  delSign: createApiHooks((params: { id: string }) =>
    request.delete(`/mch/transform/agreement/${params.id}`, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  )
};
