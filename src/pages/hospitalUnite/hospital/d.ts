// 签约Params
export interface SignParams {
  hospitalAId?: string;
  hospitalBId?: string;
  type?: string;
  agreedAt?: string;
  payeeType?: string;
  payee?: string;
}
export interface SignPutParams {
  id: string;
  type?: string;
  payeeType?: string;
  payee?: string;
}
// 医院列表
export interface HospitalList {
  id: string;
  hospitalName?: string;
  createdAt?: string;
  updatedAt?: string;
  seqNo?: string;
  lat?: string;
  lng?: number;
  city?: string;
  province?: string;
  distrcit?: string;
  address?: number;
  level?: string;
  state?: string;
}
// 签约列表
export interface SignList {
  id?: number;
  hospitalAName?: string;
  hospitalAId?: string;
  hospitalBName?: string;
  hospitalBId?: string;
  type?: string;
  createdAt?: string;
  agreedAt?: string;
  payeeType?: string;
  payee?: string;
  payeeName?: string;
}
//条件查询医院
export interface HosptitalGetBy {
  hisId?: number;
  name?: string;
  level?: string;
  licence?: string;
  serviceScope?: string;
  honorImages?: string;
  instCode?: string;
  instRegisterNumber?: string;
  instInfoSafeEnsure?: string;
  certFirstDate?: string;
  certUpdateDate?: string;
  licenseStartDate?: string;
  licenseEndDate?: string;
  hospitalName?: string;
  hospitalLevel?: string;
  hospitalAddress?: string;
  hospitalIntroduction?: string;
  contactNumber?: string;
  adminAccount?: string;
  adminName?: string;
}
// 查询医院返回数据
export interface HosptitalGetByList {
  code: number;
  data: Array<{
    adminAccount?: string;
    adminName?: string;
    certFirstDate?: string;
    certFirstTime?: string;
    certUpdateDate?: string;
    certUpdateTime?: string;
    contactNumber?: string;
    createTime?: string;
    grading?: string;
    hisId: number;
    honorImages?: string;
    hospitalAddress?: string;
    hospitalIntroduction?: string;
    hospitalLevel?: string;
    hospitalName?: string;
    id?: number;
    instCode?: string;
    instInfoSafeEnsure?: string;
    instRegisterNumber?: string;
    level?: string;
    licence?: string;
    licenseEndDate?: string;
    licenseEndTime?: string;
    licenseStartDate?: string;
    licenseStartTime?: string;
    name?: string;
    serviceScope?: string;
    status?: string;
    updateTime?: string;
  }>;
  msg: string;
}
