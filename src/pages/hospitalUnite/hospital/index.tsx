import React, { useState } from 'react';
import { CardLayout, RouteComponentProps } from 'parsec-admin';
import { Tabs } from 'antd';
import List from './list';
import Sign from './sign';

const { TabPane } = Tabs;
export default ({ history }: RouteComponentProps) => {
  const [curkey, setCurKey] = useState('1');
  const callback = (activeKey: string) => {
    setCurKey(activeKey);
  };

  return (
    <>
      <CardLayout>
        <Tabs defaultActiveKey='1' onChange={callback}>
          <TabPane tab='医院列表' key='1'>
            {curkey === '1' && <List />}
          </TabPane>
          <TabPane tab='医院签约' key='2'>
            {curkey === '2' && <Sign />}
          </TabPane>
        </Tabs>
      </CardLayout>
    </>
  );
};
