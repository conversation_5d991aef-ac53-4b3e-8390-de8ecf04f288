import React, { useMemo, useState, useRef } from 'react';
import {
  <PERSON><PERSON>angePicker,
  useModal,
  LinkButton,
  ArrSelect,
  ActionsWrap,
  actionConfirm,
  handleSubmit,
  Form,
  InputMoney,
  UploadImg,
  QQMap
} from 'parsec-admin';
import { useForceUpdate } from 'parsec-hooks';
import useApi, { Status } from './api';
import useApis from '../../authority/api';
import OldTableList from '@components/oldTableList';
import { Button, Switch, AutoComplete } from 'antd';
import moment from 'moment';
import env from '@src/configs/env';
export default () => {
  const [form] = Form.useForm();
  const [itemName, setItemName] = useState('' as any);
  const { forceUpdate } = useForceUpdate();
  const timerRef = useRef<any>();
  const hisId = env.hisId;

  //获取支付配置
  // const { request: getpayReguest, data: getpaydata } = useApi.getPay({
  //   needInit: false
  // });
  // 获取医联体医院列表
  const {
    data: { list: treeItems }
  } = useApi.getHospital({
    params: {
      page: 1,
      limit: 999
    },
    needInit: !!itemName,
    initValue: []
  });
  const handleSearch = (value: string) => {
    if (value) {
      console.log(value);
      clearTimeout(timerRef.current);
      timerRef.current = setTimeout(() => setItemName(value), 200);
    }
  };
  // 获取医院列表
  const {
    data: { data }
  } = useApis.getByHospit({
    initValue: {
      data: []
    },
    params: {},
    needInit: !!hisId
  });
  const [isEdit, setEdit] = useState<boolean>(true);
  const switchModalVisible = useModal(() => {
    return {
      title: isEdit ? `编辑医联体医院` : `新增医联体医院`,
      width: 1200,
      myFormProps: {
        submitButton: false,
        layout: {
          labelCol: {
            span: 6
          }
        }
      },
      onSubmit: values =>
        handleSubmit(() => {
          const newValues = { ...values };
          console.log(newValues.appLogoUrl);
          newValues.appLogoUrl =
            newValues.appLogoUrl instanceof Array
              ? newValues.appLogoUrl[0]
              : newValues.appLogoUrl;
          newValues.lat = newValues.gps.lat;
          newValues.lng = newValues.gps.lng;
          newValues.address = newValues.gps.address;
          delete newValues.gps;
          return useApi.saveHospital.request(
            values.isneed ? { ...newValues } : { ...newValues, registration: 0 }
          );
        }),
      form,
      items: [
        {
          label: '医院名称',
          name: 'id',
          render: () => (
            <ArrSelect
              disabled={isEdit}
              optionFilterProp='children'
              options={(data || []).map(x => ({
                value: x.hisId,
                children: x.hospitalName
              }))}
            />
          ),
          required: true
        },
        {
          label: '是否需要挂号费',
          name: 'isneed',
          formItemProps: {
            initialValue: false,
            valuePropName: 'checked'
          },
          render: <Switch onChange={forceUpdate} />
        },
        ({ isneed }) => ({
          label: '挂号金额',
          name: 'registration',
          render: isneed && ((v, { isneed }) => <InputMoney />),
          required: true
        }),
        {
          label: '医院logo',
          name: 'appLogoUrl',
          render: <UploadImg length={1} />,
          required: true
        },
        {
          label: '医院地址',
          name: 'gps',
          render: <QQMap />,
          required: true
        }
      ]
    };
  }, [data]);
  // const switchPayModalVisible = useModal({
  //   title: '编辑支付信息',
  //   onSubmit: values =>
  //     handleSubmit(() => {
  //       return useApi.savePay.request({ ...values });
  //     }),
  //   items: [
  //     {
  //       name: 'hospitalId',
  //       render: false
  //     },
  //     {
  //       label: '用户ID',
  //       name: 'merchantId',
  //       required: true
  //     },
  //     {
  //       label: '渠道ID',
  //       name: 'channelId',
  //       required: true
  //     },
  //     {
  //       label: '回调地址',
  //       name: 'backUrl',
  //       required: true
  //     },
  //     {
  //       label: '支付密钥',
  //       name: 'secretKey',
  //       required: true
  //     }
  //   ]
  // });
  return (
    <>
      <OldTableList
        action={
          <Button
            type={'default'}
            onClick={() => {
              setEdit(false);
              switchModalVisible();
            }}>
            添加医联体医院
          </Button>
        }
        getList={({ pagination: { current }, params }) => {
          return useApi.Hospitallist.request({
            page: current,
            limit: 10,
            ...params
          });
        }}
        tableTitle={false}
        columns={useMemo(
          () => [
            {
              title: '编号',
              dataIndex: 'id',
              width: 100,
              fixed: 'left'
            },
            {
              title: '医院名称',
              dataIndex: 'hospitalName',
              width: 180,
              search: (
                <AutoComplete
                  allowClear={true}
                  options={(treeItems || []).map(({ hospitalName }) => ({
                    value: `${hospitalName}`,
                    label: hospitalName
                  }))}
                  onSearch={handleSearch}
                  placeholder='请输入医院名称'
                />
              )
            },
            { title: '医院地址', dataIndex: 'address' },
            {
              title: '医院状态',
              dataIndex: 'state',
              width: 100,
              render: val => Status[val],
              search: <ArrSelect options={Status} />
            },
            {
              title: '创建时间',
              dataIndex: 'createdAt',
              width: 150,
              render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
              search: <DayRangePicker placeholder={['开始时间', '结束时间']} />,
              searchIndex: ['dateFrom', 'dateTo']
            },
            {
              title: '操作',
              fixed: 'right',
              width: 150,
              render: ({ lat, lng, address, ...record }: any) => (
                <ActionsWrap max={999}>
                  <LinkButton
                    onClick={() => {
                      setEdit(true);
                      switchModalVisible({
                        ...record,
                        gps: address
                          ? {
                              address,
                              lat,
                              lng
                            }
                          : undefined,
                        isEdit: 1,
                        isneed: record.registration > 0
                      });
                    }}>
                    编辑
                  </LinkButton>
                  {/*  {<LinkButton*/}
                  {/*  onClick={async () => {*/}
                  {/*    await getpayReguest({ hostitalId: record.id });*/}
                  {/*    await switchPayModalVisible(getpaydata);*/}
                  {/*  }}>*/}
                  {/*  支付配置*/}
                  {/*</LinkButton> }*/}
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          useApi.changeHospitalStatus.request({
                            id: record.id,
                            state: record.state === 'ON' ? 'OFF' : 'ON'
                          }),
                        record.state === 'ON' ? '下线' : '上线'
                      );
                    }}>
                    {record.state === 'ON' ? '下线' : '上线'}
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          useApi.delHospital.request({
                            id: record.id
                          }),
                        '删除'
                      );
                    }}>
                    删除
                  </LinkButton>
                </ActionsWrap>
              )
            }
          ],
          [switchModalVisible, treeItems]
        )}
      />
    </>
  );
};
