import React, { useMemo } from 'react';
import {
  DayRangePicker,
  useModal,
  LinkButton,
  ArrSelect,
  ActionsWrap,
  actionConfirm,
  Form,
  handleSubmit
} from 'parsec-admin';
import useApi, { SignType, PayeeType } from './api';
import OldTableList from '@components/oldTableList';
import { Button } from 'antd';
import moment from 'moment';

export default () => {
  // 获取医联体医院列表
  const {
    data: { list: treeItems }
  } = useApi.getHospital({
    params: {
      page: 1,
      limit: 999
    },
    needInit: true,
    initValue: []
  });

  const [form] = Form.useForm();
  const switchModalVisible = useModal({
    title: form.getFieldValue('id') ? '编辑医院签约' : '新增医院签约',
    form,
    onSubmit: values =>
      handleSubmit(() => {
        if (values.id) {
          return useApi.updateSign.request({ ...values });
        } else {
          delete values.id;
          return useApi.saveSign.request({ ...values });
        }
      }),
    myFormProps: {
      formProps: {
        initialValues: {
          status: 'OFF'
        }
      }
    },
    items: [
      {
        name: 'id',
        render: false
      },
      {
        label: '甲方医院',
        name: 'hospitalAId',
        render: () => (
          <ArrSelect
            disabled={!!form.getFieldValue('id')}
            optionFilterProp='children'
            options={(treeItems || []).map(x => ({
              value: x.id,
              children: x.hospitalName
            }))}
          />
        ),
        required: true
      },
      {
        label: '乙方医院',
        name: 'hospitalBId',
        render: () => (
          <ArrSelect
            disabled={!!form.getFieldValue('id')}
            optionFilterProp='children'
            options={(treeItems || []).map(x => ({
              value: x.id,
              children: x.hospitalName
            }))}
          />
        ),
        required: true
      },
      {
        label: '签约类型',
        name: 'type',
        render: () => <ArrSelect options={SignType} />,
        required: true
      },
      {
        label: '收款方式',
        name: 'payeeType',
        render: () => <ArrSelect options={PayeeType} />,
        required: true
      },
      ({ payeeType, hospitalAId, hospitalBId }) => ({
        label: '收款医院',
        name: 'payee',
        render:
          payeeType === 'FIXED' &&
          ((v, { hospitalAId, hospitalBId }) => (
            <ArrSelect
              optionFilterProp='children'
              options={(
                treeItems.filter(
                  item => item.id === hospitalAId || item.id === hospitalBId
                ) || []
              ).map(x => ({
                value: x.id,
                children: x.hospitalName
              }))}
            />
          )),
        required: true
      })
    ]
  });
  return (
    <OldTableList
      action={
        <Button type={'default'} onClick={() => switchModalVisible()}>
          新增签约
        </Button>
      }
      getList={({ pagination: { current }, params }) => {
        return useApi.signlist.request({
          page: current,
          limit: 10,
          ...params
        });
      }}
      tableTitle={false}
      columns={useMemo(
        () => [
          {
            title: '编号',
            dataIndex: 'id',
            width: 100,
            fixed: 'left'
          },
          {
            title: '甲方医院',
            dataIndex: 'hospitalAName',
            width: 100
          },
          {
            title: '乙方医院',
            dataIndex: 'hospitalBName',
            width: 100
          },
          {
            title: '选择医院',
            dataIndex: 'hospitalName',
            width: 100,
            render: false,
            search: (
              <ArrSelect
                optionFilterProp='children'
                options={(treeItems || []).map(x => ({
                  value: x.hospitalName,
                  children: x.hospitalName
                }))}
              />
            )
          },
          {
            title: '签约类型',
            dataIndex: 'type',
            width: 100,
            render: val => SignType[val],
            search: <ArrSelect options={SignType} />
          },
          {
            title: '收款方式',
            dataIndex: 'payeeType',
            width: 100,
            render: val => PayeeType[val],
            search: <ArrSelect options={PayeeType} />
          },
          {
            title: '创建时间',
            dataIndex: 'createdAt',
            width: 150,
            render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
            search: <DayRangePicker placeholder={['开始时间', '结束时间']} />,
            searchIndex: ['dateFrom', 'dateTo']
          },
          {
            title: '操作',
            fixed: 'right',
            width: 80,
            render: record => (
              <ActionsWrap max={999}>
                <LinkButton
                  onClick={() => {
                    switchModalVisible(record);
                  }}>
                  编辑
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.delSign.request({
                          id: record.id
                        }),
                      '删除'
                    ).then();
                  }}>
                  删除
                </LinkButton>
              </ActionsWrap>
            )
          }
        ],
        [switchModalVisible, treeItems]
      )}
    />
  );
};
