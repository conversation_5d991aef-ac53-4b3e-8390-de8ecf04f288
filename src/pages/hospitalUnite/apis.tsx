import { request } from 'parsec-admin';
import createApiHooks from 'create-api-hooks';

interface EnumItem {
  name: string;
  desc: string;
}
const ArrToObj = (arr: any[]) => {
  const obj: any = {};
  arr.forEach((item: any) => {
    obj[item.name] = item.desc;
  });
  return obj;
};
export default {
  allEnum: createApiHooks(() => {
    return request
      .get<{
        // 签约类型
        AgreementType: EnumItem[];
        // 来源
        HospitalSource: EnumItem[];
        // 收款方
        PayeeType: EnumItem[];
        // 订单结果来源
        ResultSource: EnumItem[];
        // 项目类型
        ItemType: EnumItem[];
        //医院，项目在线状态
        HospitalState: EnumItem[];
        //订单状态
        OrderState: EnumItem[];
      }>('/mch/transform/enum/table1')
      .then(({ data }) => {
        const newEmun: any = {};
        for (const key in data) {
          newEmun[key] = ArrToObj((data as any)[key]);
        }
        console.log(newEmun);
        return { data: newEmun as { [n: string]: any } };
      });
  })
};
