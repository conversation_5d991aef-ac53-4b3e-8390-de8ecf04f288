import createApiHooks from 'create-api-hooks';
import { ListApiRequestParams, ListApiResponseData } from 'parsec-admin';
import { request } from 'parsec-admin';
import { CodeList, HospitalList, InspectPriceList, CodeParams } from './d';
export const CodeState: any = {
  '': '全部',
  START: '未对码',
  FINISH: '已对码'
};
export const PriceState: any = {
  '': '全部',
  START: '未对价',
  FINISH: '已对价'
};
export const Status: any = {
  '': '全部',
  ON: '已上线',
  OFF: '未上线'
};
export const SignType: any = {
  '-1': '乙方向甲方流转',
  '0': '甲方乙方双向流转',
  '1': '甲方向乙方流转'
};
export const PayeeType: any = {
  EXECUTOR: '检查方收款',
  ORDERER: '开单方收款',
  FIXED: '固定医院收款'
};

export default {
  codeList: createApiHooks((params: ListApiRequestParams) =>
    request.get<ListApiResponseData<CodeList>>(
      '/mch/transform/hospital/test-items',
      {
        params
      }
    )
  ),
  getDetail: createApiHooks((params: { id: string }) =>
    request.get<{
      hospitalId?: string;
      merchantId?: string;
      channelId?: string;
      backUrl?: string;
      secretKey?: string;
      updatedAt?: string;
      createdAt?: string;
    }>(`/mch/transform/hospital/test-item/${params.id}`, {
      params
    })
  ),
  delCode: createApiHooks((params: { itemId: string }) =>
    request.delete(`/mch/transform/hospital/test-item/${params.itemId}`, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  ),
  addCode: createApiHooks((params: CodeParams) =>
    request.post('/mch/transform/hospital/test-item', params, {})
  ),
  saveCode: createApiHooks((
    params: CodeParams & { id: number } //id主键
  ) => request.put('/mch/transform/hospital/test-item', params)),
  Hospitallist: createApiHooks((params: ListApiRequestParams) =>
    request.get<ListApiResponseData<HospitalList>>('/mch/transform/hospitals', {
      params
    })
  ),
  saveHospital: createApiHooks(
    (params: { ids: string; registration: number }) =>
      request.post('/mch/transform/hospital', params)
  ),
  getPay: createApiHooks((params: { hostitalId: string }) =>
    request.get<{
      hospitalId?: string;
      merchantId?: string;
      channelId?: string;
      backUrl?: string;
      secretKey?: string;
      updatedAt?: string;
      createdAt?: string;
    }>('/mch/transform/hospital/payinfo', {
      params
    })
  ),
  savePay: createApiHooks(
    (params: {
      hospitalId: string;
      merchantId: string;
      channelId: string;
      backUrl: string;
      secretKey: string;
    }) => request.post('/mch/transform/hospital/payinfo', params)
  ),
  checkPriceList: createApiHooks((params: ListApiRequestParams) =>
    request.get<ListApiResponseData<CodeList>>(
      '/mch/transform/hospital/test-item/test-prices',
      {
        params
      }
    )
  ),
  inspectPriceList: createApiHooks((params: ListApiRequestParams) =>
    request.get<ListApiResponseData<InspectPriceList>>(
      '/mch/transform/hospital/test-item/exam-prices',
      {
        params
      }
    )
  ),
  savePrice: createApiHooks(
    (params: { id: string; qty: number; price: number }) =>
      request.put('/mch/transform/hospital/test-item/item-price', params)
  ),
  initPrice: createApiHooks((params: { hospitalId: number; type: string }) =>
    request.post('/mch/transform/hospital/test-item/price', params)
  ),
  批量导入: createApiHooks((params: { file: File }) => {
    const formData = new FormData();
    formData.append('file', params.file);
    return request.post('/mch/transform/hospital/test-item/import', formData);
  }),
  exportVerCode: createApiHooks(() =>
    request
      .get<Blob>('/mch/transform/hospital/test-item/export', {
        responseType: 'blob'
      })
      .then(({ headers, data }) => ({ data: { data, headers } }))
  )
};
