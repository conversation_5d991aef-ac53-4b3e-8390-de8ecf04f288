import React from 'react';
import { CardLayout, useReloadTableList } from 'parsec-admin';
import { Tabs, Button, message } from 'antd';
import CheckCode from './checkCode';
import useApi from './api';
import { ExportOutlined, ImportOutlined } from '@ant-design/icons';
import { saveAs } from 'file-saver';
const { TabPane } = Tabs;
export default () => {
  const reloadTable = useReloadTableList();
  const { loading: loadingImport, request: requestImport } = useApi.批量导入({
    needInit: false
  });
  const {
    request: handleExport,
    loading: exportLoading
  } = useApi.exportVerCode({
    needInit: false
  });
  return (
    <>
      <CardLayout>
        <Tabs
          tabBarExtraContent={
            <>
              <Button
                type={'primary'}
                loading={loadingImport}
                icon={<ImportOutlined />}
                onClick={() => {
                  const el = document.createElement('input');
                  el.setAttribute('type', 'file');
                  el.addEventListener('change', () => {
                    const file = el.files?.[0];
                    console.log(file);
                    if (file) {
                      requestImport({ file })
                        .then(() => {
                          message.success('导入成功');
                        })
                        .catch(() => {
                          message.error('导入失败');
                        });
                    }
                  });
                  el.click();
                }}>
                导入对码文件
              </Button>
              <Button
                style={{ marginLeft: 10 }}
                type={'default'}
                loading={exportLoading}
                icon={<ExportOutlined />}
                onClick={() =>
                  handleExport().then(data => {
                    saveAs(data.data, '医院对码导出表.xlsx');
                  })
                }>
                导出对码文件
              </Button>
            </>
          }
          defaultActiveKey='1'
          onTabClick={(tab: string) => reloadTable('inspect' + tab)}>
          <TabPane tab='诊疗项目对码' key='1'>
            <CheckCode currentLevel='1' name='inspect1' type='TEST' />
          </TabPane>
          <TabPane tab='收费项目对码' key='2'>
            <CheckCode currentLevel='2' name='inspect2' type='TEST' />
          </TabPane>
          <TabPane tab='标本对码' key='3'>
            <CheckCode currentLevel='2' name='inspect3' type='SPEC' />
          </TabPane>
        </Tabs>
      </CardLayout>
    </>
  );
};
