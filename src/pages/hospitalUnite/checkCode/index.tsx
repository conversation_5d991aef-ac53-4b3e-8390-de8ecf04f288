import React from 'react';
import { CardLayout, useReloadTableList } from 'parsec-admin';
import { Tabs } from 'antd';
import CheckCodeIndex from './checkCodeIndex';
import CheckPrice from './checkPrice';
import InspectCodeIndex from './inspectCodeIndex';
import InspectPrice from './inspectPrice';
const { TabPane } = Tabs;
export default () => {
  const reloadTable = useReloadTableList();
  return (
    <>
      <CardLayout>
        <Tabs defaultActiveKey='1' onTabClick={reloadTable}>
          <TabPane tab='检查对码' key='1'>
            <CheckCodeIndex />
          </TabPane>
          <TabPane tab='检验对码' key='2'>
            <InspectCodeIndex />
          </TabPane>
          <TabPane tab='检查对价' key='3'>
            <InspectPrice />
          </TabPane>
          <TabPane tab='检验对价' key='4'>
            <CheckPrice />
          </TabPane>
        </Tabs>
      </CardLayout>
    </>
  );
};
