import React from 'react';
import { CardLayout, useReloadTableList } from 'parsec-admin';
import { Tabs } from 'antd';
import CheckCode from './checkCode';
const { TabPane } = Tabs;
export default () => {
  const reloadTable = useReloadTableList();
  return (
    <>
      <CardLayout>
        <Tabs defaultActiveKey='1' onTabClick={reloadTable}>
          <TabPane tab='分类对码' key='1'>
            <CheckCode currentLevel='1' type='EXAM' />
          </TabPane>
          <TabPane tab='子类对码' key='2'>
            <CheckCode currentLevel='2' type='EXAM' />
          </TabPane>
          <TabPane tab='部位对码' key='3'>
            <CheckCode currentLevel='3' type='EXAM' />
          </TabPane>
          <TabPane tab='检查项对码' key='4'>
            <CheckCode currentLevel='4' type='EXAM' />
          </TabPane>
        </Tabs>
      </CardLayout>
    </>
  );
};
