import React, { useCallback, useMemo } from 'react';
import {
  useModal,
  LinkButton,
  handleSubmit,
  InputMoney,
  // actionConfirm,
  FormDescriptions,
  Form
} from 'parsec-admin';
import useApi from './api';
import { cash } from '@utils/tools';
import OldTableList from '@components/oldTableList';
import {
  //  Button,
  InputNumber
} from 'antd';
import env from '@src/configs/env';
export default () => {
  const hisId = env.hisId;
  const [form] = Form.useForm();
  const switchModalVisible = useModal(
    useCallback(
      data => ({
        title: '检查项目对价',
        width: 800,
        form,
        onSubmit: (values: any) =>
          handleSubmit(() => {
            return useApi.savePrice.request({ ...values });
          }),
        children: (
          <>
            <FormDescriptions
              form={form}
              edit={false}
              data={data}
              items={[
                {
                  name: 'id',
                  hidden: true
                },
                {
                  label: '项目标准名称',
                  name: 'itemName',
                  render: v => <span>{v}</span>
                },
                {
                  label: '项目英文名称',
                  name: 'enName',
                  render: v => <span>{v}</span>
                },
                {
                  label: '项目标准编码',
                  name: 'itemCode',
                  render: v => <span>{v}</span>
                },
                {
                  label: '医院项目名称',
                  name: 'currentName',
                  render: v => <span>{v}</span>
                },
                {
                  label: '医院项目编码',
                  name: 'currentCode',
                  render: v => <span>{v}</span>
                },
                {
                  label: '项目单价',
                  name: 'oldprice',
                  span: 3,
                  render: (v: any) => <span>¥{cash(v)}</span>
                },
                {
                  label: '项目详细信息',
                  name: 'level1Name',
                  render: (
                    v,
                    { currentName, level1Name, level2Name, bodyPart }: any
                  ) => (
                    <span>{`${level1Name || ''} ${level2Name ||
                      ''} ${bodyPart || ''} ${currentName || ''}`}</span>
                  )
                }
              ]}
            />
            <Form
              layout={'modal'}
              form={form}
              formProps={{ labelCol: { span: 4 }, labelAlign: 'left' }}
              items={[
                {
                  label: '单价',
                  name: 'price',
                  render: () => <InputMoney />,
                  required: true
                },
                {
                  label: '数量',
                  name: 'qty',
                  render: () => <InputNumber />,
                  required: true
                }
              ]}
            />
          </>
        )
      }),
      [form]
    )
  );

  return (
    <OldTableList
      name='3'
      // action={
      //   <>
      //     <Button
      //       type={'default'}
      //       onClick={() => {
      //         actionConfirm(
      //           () =>
      //             useApi.initPrice.request({
      //               hospitalId: hisId,
      //               type: 'EXAM'
      //             }),
      //           '对价初始化'
      //         );
      //       }}>
      //       对价初始化
      //     </Button>
      //     <Button type={'primary'} onClick={() => switchModalVisible()}>
      //       导入对价表
      //     </Button>
      //   </>
      // }
      getList={({ pagination: { current }, params }) => {
        return useApi.inspectPriceList.request({
          page: current,
          limit: 10,
          ...{ ...params, hospitalId: hisId }
        });
      }}
      tableTitle={false}
      columns={useMemo(
        () => [
          {
            title: '项目标准名称',
            dataIndex: 'itemName',
            width: 100,
            fixed: 'left'
          },
          {
            title: '项目标准编码',
            dataIndex: 'itemCode',
            width: 100
          },
          {
            title: '医院项目名称',
            dataIndex: 'currentName',
            width: 100,
            search: true
          },
          {
            title: '医院项目编码',
            dataIndex: 'currentCode',
            width: 100,
            search: true
          },
          {
            title: '医院项目描述',
            dataIndex: 'parentName',
            width: 100
          },
          {
            title: '单价',
            dataIndex: 'price',
            width: 100,
            render: val => cash(val)
          },
          {
            title: '数量',
            dataIndex: 'qty',
            width: 100
          },
          {
            title: '操作',
            fixed: 'right',
            width: 120,
            render: record => (
              <LinkButton
                onClick={() => {
                  switchModalVisible({ ...record, oldprice: record.price });
                }}>
                对价
              </LinkButton>
            )
          }
        ],
        [switchModalVisible]
      )}
    />
  );
};
