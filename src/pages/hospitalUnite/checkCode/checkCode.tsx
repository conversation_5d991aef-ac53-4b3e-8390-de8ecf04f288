import React, { useMemo } from 'react';
import {
  useModal,
  LinkButton,
  ArrSelect,
  ActionsWrap,
  actionConfirm,
  handleSubmit,
  getPrice
} from 'parsec-admin';
import useApi, { CodeState } from './api';
import OldTableList from '@components/oldTableList';
import { InputNumber, Input, Switch } from 'antd';
import env from '@src/configs/env';

const { TextArea } = Input;
interface Props {
  currentLevel: string;
  type: string;
  name?: string;
}
export default ({ currentLevel, type, name = currentLevel }: Props) => {
  const hisId = env.hisId;

  const switchModalVisible = useModal({
    title: '编辑检验项目',
    onSubmit: values => {
      if (
        (currentLevel === '4' && type === 'EXAM') ||
        (currentLevel === '2' && type === 'TEST')
      ) {
        values.chargeMode = values.chargeMode ? 1 : 0;
      }
      return handleSubmit(() => {
        if (values.id) {
          return useApi.saveCode.request({ ...values });
        } else {
          return useApi.addCode.request({ ...values, hospitalId: hisId });
        }
      });
    },
    items: [
      {
        name: 'id',
        render: false
      },
      {
        name: 'standardId',
        render: false
      },
      {
        label: '项目标准名称',
        name: 'itemName',
        render: () => <Input disabled />
      },
      {
        label: '项目英文名称',
        name: 'enName',
        render: () => <Input disabled />
      },
      {
        label: '项目标准编码',
        name: 'itemCode',
        render: () => <Input disabled />
      },
      {
        label: '医院项目名称',
        name: 'currentName',
        required: true
      },
      {
        label: '医院项目编码',
        name: 'currentCode'
      },
      // {
      //   label: '医院部门编码',
      //   name: 'depCode'
      // },
      {
        label: '单次收费',
        name: 'chargeMode',
        render:
          (currentLevel === '4' && type === 'EXAM') ||
          (currentLevel === '2' && type === 'TEST') ? (
            <Switch />
          ) : (
            false
          ),
        formItemProps: {
          valuePropName: 'checked'
        }
      },
      {
        label: '分类',
        name: 'label'
      },
      {
        label: '排序',
        name: 'seq',
        render: () => <InputNumber />
      },
      {
        label: '备注',
        name: 'registration',
        render: () => <TextArea rows={4} />
      }
    ]
  });
  return (
    <OldTableList
      name={name}
      getList={({ pagination: { current }, params }) => {
        return useApi.codeList.request({
          page: current,
          limit: 10,
          ...{ ...params, hospitalId: hisId, currentLevel, type }
        });
      }}
      tableTitle={false}
      columns={useMemo(
        () => [
          {
            title: '项目标准名称',
            dataIndex: 'itemName',
            width: 100,
            fixed: 'left'
          },
          {
            title: '状态',
            dataIndex: 'state',
            width: 100,
            search: <ArrSelect options={CodeState} />,
            render: false
          },
          {
            title: '项目标准编码',
            dataIndex: 'itemCode',
            width: 100
          },
          {
            title: '医院项目名称',
            dataIndex: 'currentName',
            width: 100,
            search: true
          },
          {
            title: '医院项目编码',
            dataIndex: 'currentCode',
            width: 100,
            search: true
          },
          {
            title: '单次收费',
            dataIndex: 'chargeMode',
            width: 100,
            render:
              (currentLevel === '4' && type === 'EXAM') ||
              (currentLevel === '2' && type === 'TEST')
                ? v => {
                    return v === 1 ? '是' : v === 0 ? '否' : '';
                  }
                : false
          },
          {
            title: '单价',
            dataIndex: 'price',
            width: 100,
            render: v => getPrice(v)
          },
          {
            title: '操作',
            fixed: 'right',
            width: 120,
            render: record => {
              if (record.state === 'START') {
                return (
                  <LinkButton
                    onClick={() => {
                      switchModalVisible(record);
                    }}>
                    对码
                  </LinkButton>
                );
              } else {
                return (
                  <ActionsWrap max={999}>
                    <LinkButton
                      onClick={() => {
                        if (
                          (currentLevel === '4' && type === 'EXAM') ||
                          (currentLevel === '2' && type === 'TEST')
                        ) {
                          switchModalVisible({
                            ...record,
                            chargeMode: record.chargeMode
                          });
                        } else {
                          switchModalVisible(record);
                        }
                      }}>
                      编辑
                    </LinkButton>
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.delCode.request({
                              itemId: record.id
                            }),
                          '删除'
                        );
                      }}>
                      删除
                    </LinkButton>
                  </ActionsWrap>
                );
              }
            }
          }
        ],
        [currentLevel, switchModalVisible, type]
      )}
    />
  );
};
