// 签约Params
export interface SignParams {
  hospitalAId?: string;
  hospitalBId?: string;
  type?: string;
  agreedAt?: string;
  payeeType?: string;
  payee?: string;
}
export interface SignPutParams {
  id: string;
  type?: string;
  payeeType?: string;
  payee?: string;
}
// 对码列表
export interface CodeList {
  id: number;
  currentName?: string;
  currentCode?: string; //医院的对应的编码
  standardId?: number; //标准ID
  itemName?: string; //标准项目名称
  depCode?: string; //医院的部门编码，可能没有用，我们预留
  price?: number; //单价，单位为分
  qty?: number; //检验数量
  hospitalId?: number; //医院ID,医院在流转系统中的ID
  label?: string; //用来分类用的一个标签
  createdAt?: string;
  updatedAt?: string;
  seq?: number; //顺序编号
  remark?: string; //备注
  currentLevel?: number; //当前对应的标准码的层级
  state?: string;
  extention?: JSON; //extention 用来保存一个JSON，以扩展医院的额外存储需求
  status?: string;
}
export interface CheckPriceList {
  id: number;
  state?: string;
  itemName?: string;
  itemCode?: string;
  currentName?: string; //医院的收费项目名称
  currentCode?: string; //医院的收费项目编码
  parentName?: string; //诊疗项目名称，
  parentCode?: string; //诊疗项目编码
  price?: number; //单价，单位为分
  qty?: number; //检验数量
  hospitalId?: number; //医院ID,医院在流转系统中的ID
  label?: string; //用来分类用的一个标签
  createdAt?: string;
  updatedAt?: string;
}
export interface InspectPriceList {
  id: number;
  itemName?: string;
  itemCode?: string;
  state?: string;
  currentName?: string; //医院的收费项目名称
  currentCode?: string; //医院的收费项目编码
  level1Name?: string; //一级分类的名称
  level2Name?: string; //二级分类的名称
  bodyPart?: string; //身体部位的名称
  parentName?: string; //诊疗项目名称，
  parentCode?: string; //项目编码
  price?: number; //单价，单位为分
  qty?: number; //检验数量
  hospitalId?: number; //医院ID,医院在流转系统中的ID
  label?: string; //用来分类用的一个标签
  createdAt?: string;
  updatedAt?: string;
}
export interface CodeParams {
  currentName?: string; //这个检验项目在医院的名称
  currentCode?: string; //医院的对应的编码
  depCode?: string; //医院的部门编码，可能没有用，我们预留
  price?: number; //单价，单位为分，可能为空
  label?: string; //用来分类用的一个标签
  seq?: number; //顺序编号
  remark?: string; //备注
  type?: string;
  state?: string;
  extention?: JSON; //extention 用来保存一个JSON，以扩展医院的额外存储需求
}
// 医院列表
export interface HospitalList {
  id: string;
  hospitalName?: string;
  createdAt?: string;
  updatedAt?: string;
  seqNo?: string;
  lat?: string;
  lng?: number;
  city?: string;
  province?: string;
  distrcit?: string;
  address?: number;
  level?: string;
  state?: string;
}
// 签约列表
export interface SignList {
  id?: number;
  hospitalAName?: string;
  hospitalAId?: string;
  hospitalBName?: string;
  hospitalBId?: string;
  type?: string;
  createdAt?: string;
  agreedAt?: string;
  payeeType?: string;
  payee?: string;
  payeeName?: string;
}
//条件查询医院
export interface HosptitalGetBy {
  hisId?: number;
  name?: string;
  level?: string;
  licence?: string;
  serviceScope?: string;
  honorImages?: string;
  instCode?: string;
  instRegisterNumber?: string;
  instInfoSafeEnsure?: string;
  certFirstDate?: string;
  certUpdateDate?: string;
  licenseStartDate?: string;
  licenseEndDate?: string;
  hospitalName?: string;
  hospitalLevel?: string;
  hospitalAddress?: string;
  hospitalIntroduction?: string;
  contactNumber?: string;
  adminAccount?: string;
  adminName?: string;
}
// 查询医院返回数据
export interface HosptitalGetByList {
  code: number;
  data: Array<{
    adminAccount?: string;
    adminName?: string;
    certFirstDate?: string;
    certFirstTime?: string;
    certUpdateDate?: string;
    certUpdateTime?: string;
    contactNumber?: string;
    createTime?: string;
    grading?: string;
    hisId: number;
    honorImages?: string;
    hospitalAddress?: string;
    hospitalIntroduction?: string;
    hospitalLevel?: string;
    hospitalName?: string;
    id?: number;
    instCode?: string;
    instInfoSafeEnsure?: string;
    instRegisterNumber?: string;
    level?: string;
    licence?: string;
    licenseEndDate?: string;
    licenseEndTime?: string;
    licenseStartDate?: string;
    licenseStartTime?: string;
    name?: string;
    serviceScope?: string;
    status?: string;
    updateTime?: string;
  }>;
  msg: string;
}
