import createApiHooks from 'create-api-hooks';
import { ListApiResponseData, request } from 'parsec-admin';
import { ApiResponse, ListApiRequestParams } from '@apiHooks';
import { Organization } from '@src/store/organizationListStore';

export default {
  分页获取机构列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        institutionName?: string;
      }
    ) =>
      request.get<ListApiResponseData<Organization>>(
        '/mch/his/ls-main/getInstitutionList',
        {
          params
        }
      )
  ),

  修改状态: createApiHooks(data =>
    request.post<ApiResponse<null>>('/mch/his/ls-main/changeStatus', data, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  )
};
