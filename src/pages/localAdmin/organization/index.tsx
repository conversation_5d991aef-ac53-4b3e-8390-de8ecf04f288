import React from 'react';
import MyTableList from '@components/myTableList';
import { Switch } from 'antd';
import { handleSubmit } from 'parsec-admin';
import apis from './api';
import env from '@configs/env';
export default () => {
  return (
    <MyTableList
      tableTitle={'机构列表'}
      showExpand={false}
      exportExcelButton
      columns={[
        { title: '序号', render: (_, record, index) => index + 1 },
        { title: '机构名称', dataIndex: 'institutionName', search: true },
        { title: '医生数量', dataIndex: 'doctorNum' },
        { title: '添加时间', dataIndex: 'createTime' },
        {
          title: '状态',
          dataIndex: 'superviseStatus',
          excelRender: v => (v === 0 ? '停用' : '启用'),
          render: (v, record: any) => {
            return record?.targetHisId !== Number(env.hisId) ? (
              <Switch
                checkedChildren='开启'
                unCheckedChildren='关闭'
                checked={v}
                onChange={checked => {
                  handleSubmit(() => {
                    return apis.修改状态.request({
                      id: record?.id || '',
                      superviseStatus: checked ? 1 : 0,
                      hisId: record.hisId,
                      targetHisId: record.targetHisId
                    });
                  }, `${checked ? '开启' : '关闭'}`);
                }}
              />
            ) : v === 0 ? (
              '停用'
            ) : (
              '启用'
            );
          }
        }
      ]}
      getList={({ pagination, params }) => {
        return apis.分页获取机构列表.request(params) as any;
      }}
    />
  );
};
