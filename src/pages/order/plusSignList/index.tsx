import React, { useMemo } from 'react';
import {
  LinkButton,
  DayRangePicker,
  ArrSelect,
  RouteComponentProps,
  ActionsWrap
} from 'parsec-admin';
import useApi, { plusSignReStatus } from '../api';
import MyTableList from '@components/myTableList';
import dayjs from 'dayjs';
import permisstion from '@utils/permisstion';
const timeFlag = ['', '上午', '下午', '晚上'];

export default ({ history }: RouteComponentProps) => {
  return (
    <MyTableList
      tableTitle='加号登记'
      pageHeaderProps={false}
      getList={({ params }) =>
        useApi.加号登记列表.request({
          ...params
        })
      }
      columns={useMemo(
        () => [
          {
            title: '创建时间',
            width: 180,
            dataIndex: 'createdAt',
            searchIndex: ['createdAtBegin', 'createdAtEnd'],
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                disabledDate={current => {
                  return current && current.valueOf() > Date.now();
                }}
              />
            ),
            render: v => {
              return dayjs(v).format('YYYY-MM-DD');
            }
          },
          {
            title: '患者姓名',
            width: 180,
            search: true,
            dataIndex: 'patName'
          },
          {
            title: '订单状态',
            width: 120,
            dataIndex: 'regStatus',
            searchIndex: 'regStatus',
            search: <ArrSelect options={plusSignReStatus} />,
            render: v => {
              return plusSignReStatus[v];
            }
          },
          {
            title: '开单序号',
            dataIndex: 'credentialNum',
            search: true
          },
          {
            title: '院区',
            dataIndex: 'hospitalDistrict'
          },
          {
            title: '医生',
            dataIndex: 'deptName',
            render: (v, record: any) => {
              return `${record.doctorTitle}|${record.deptName}`;
            }
          },
          {
            title: '加号时间',
            dataIndex: 'deptName',
            searchIndex: ['addDateBegin', 'addDateEnd'],
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                disabledDate={current => {
                  return current && current.valueOf() > Date.now();
                }}
              />
            ),
            render: (v, record: any) => {
              return `${dayjs(record.additionalRegisterDate).format(
                'YYYY-MM-DD'
              )} ${timeFlag[record.timeFlag]}`;
            }
          },
          {
            title: '操作',
            fixed: 'right',
            width: 160,
            render: record => (
              <ActionsWrap>
                {permisstion.canRegDetail && (
                  <LinkButton
                    onClick={() => {
                      console.log(1);
                      history.push(
                        '/order/plusSignList/plusSignDetails/' + record.id
                      );
                    }}>
                    查看
                  </LinkButton>
                )}
              </ActionsWrap>
            )
          }
        ],
        [history]
      )}
    />
  );
};
