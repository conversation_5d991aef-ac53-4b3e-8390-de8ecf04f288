import React from 'react';
import styled from 'styled-components';
import useApi from '../api';
import { useParams } from 'react-router-dom';
import dayjs from 'dayjs';

const timeFlag = ['', '上午', '下午', '晚上'];
export default () => {
  const { id } = useParams<any>();

  const {
    data: { data }
  } = useApi.加号登记详情({
    params: id,
    needInit: true,
    initValue: { data: {} }
  });

  return (
    <Page>
      <div>
        <Title>就诊人信息</Title>
        <PlusSignDetails>
          <div>
            <div>姓名：{data?.patName}</div>
            <div>患者ID：{data?.patCardNo}</div>
          </div>
          <div>
            <div>
              性别：
              {data?.patSex === 'F'
                ? '女'
                : data?.patSex === 'M'
                ? '男'
                : data?.patSex}
            </div>
            <div>联系电话：{data?.patPhone}</div>
          </div>
          <div>
            <div>年龄：{data?.patAge}</div>
            <div>身份证号：{data?.patIdNo}</div>
          </div>
        </PlusSignDetails>
      </div>
      <hr />
      <div>
        <Title>加号信息</Title>
        <PlusSignDetails>
          <div>
            <div>开单序号：{data?.id}</div>
            <div>就诊医师：{data?.patCardNo}</div>
          </div>
          <div>
            <div>就诊院区：{data?.hospitalDistrict}</div>
            <div>
              加号时间：
              {`${dayjs(data?.additionalRegisterDate).format(
                'YYYY-MM-DD'
              )} ${data?.timeFlag && timeFlag[data.timeFlag]}`}
            </div>
          </div>
          <div>
            <div>就诊科室：{data?.deptName}</div>
          </div>
        </PlusSignDetails>
      </div>
    </Page>
  );
};

const Title = styled.div`
  font-size: 20px;
  font-weight: 600;
`;

const PlusSignDetails = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 15px 20px;
`;

const Page = styled.div`
  margin: 24px;
  padding: 50px;
  background: #fff;
  line-height: 60px;
`;
