import React, { useState, useEffect } from 'react';
import { CardLayout, FormDescriptions, Form, getPrice } from 'parsec-admin';
import styled from 'styled-components';
import useApi, { payMethodObj, platformSourceObj } from './api';
import { Steps, Table, Tabs, Empty } from 'antd';
import { useParams } from 'react-router-dom';
import moment from 'moment';
import { typeObj, refundStatus } from './listSmart';
import WaterMarkWrap from '@src/components/waterMarkWrap';
import permisstion from '@utils/permisstion';

const editSate = false;
const { Step } = Steps;

export default () => {
  const { id: orderId, uniqueCode } = useParams<{
    id: string;
    uniqueCode: string;
  }>();
  const columns = [
    {
      title: '退款发起人',
      dataIndex: 'operatorName',
      key: 'patientName'
    },
    {
      title: '退款时间',
      dataIndex: 'refundSuccessTime',
      key: 'refundSuccessTime'
    },
    {
      title: '退款金额',
      dataIndex: 'refundFee',
      key: 'refundFee',
      render: (v: any) => {
        return `${getPrice(v, 2, true)}`;
      }
    },
    {
      title: '退款状态',
      dataIndex: 'refundStatus',
      key: 'refundStatus',
      render: (v: any) => {
        return refundStatus[v];
      }
    },
    {
      title: '退款流水号',
      dataIndex: 'refundSerialNo',
      key: 'refundSerialNo'
    },
    {
      title: '退款原因',
      dataIndex: 'refundDesc',
      key: 'refundDesc'
    }
  ];
  const [form] = Form.useForm();
  const [tableData, setTableData] = useState([]);

  const {
    data: { data },
    loading: detailLoading
  } = useApi.缴费记录详情({
    params: { orderId: orderId || '', uniqueCode },
    needInit: !!orderId && !!uniqueCode,
    initValue: { data: {} }
  });

  useEffect(() => {
    const data1: any = [
      // {
      //   operatorName: '退款发起人',
      //   refundSuccessTime: '退款时间',
      //   refundFee: '退款金额',
      //   refundStatus: '退款状态',
      //   refundSerialNo: '退款流水号',
      //   refundDesc: '退款原因'
      // }
    ];

    if (data && data.refundOrders) {
      // data.refundOrders && data1.push(data.refundOrders);
      data1.push(...data.refundOrders);
      setTableData(data1);
    }
  }, [data]);
  console.log(data, 888);

  return (
    <WaterMarkWrap>
      <Wrapper edit={false}>
        <Tabs>
          <Tabs.TabPane tab='订单详情' key='detail'>
            {data?.refundOrders?.length !== 0 && (
              <CardLayout title={'退款信息'} loading={detailLoading}>
                <Steps
                  current={
                    data ? { U: 0, W: 1, S: 2, F: 2 }[data?.refundStatus] : 0
                  }>
                  <Step
                    title='退款发起'
                    icon={
                      <img
                        src={require('../../images/refundStep1.png')}
                        alt=''
                      />
                    }
                  />
                  <Step
                    title='退款受理'
                    icon={
                      <img
                        src={require('../../images/refundStep2.png')}
                        alt=''
                      />
                    }
                  />
                  <Step
                    title={data?.refundStatus === 'F' ? '退款失败' : '退款完成'}
                    icon={
                      <img
                        src={require('../../images/refundStep3.png')}
                        alt=''
                      />
                    }
                  />
                </Steps>
                <Table
                  className='tables'
                  style={{ marginTop: 30 }}
                  showHeader={true}
                  pagination={false}
                  bordered={true}
                  rowKey='refundSerialNo'
                  columns={columns}
                  dataSource={tableData}
                />
              </CardLayout>
            )}
            <CardLayout title={'就诊信息'} loading={detailLoading}>
              <FormDescriptions
                data={data}
                edit={editSate}
                form={form}
                loading={detailLoading}
                items={[
                  {
                    label: '姓名',
                    name: 'patientName'
                  },
                  {
                    label: '性别',
                    name: 'patientSex',
                    render: v => {
                      return v === 'F' ? '女' : '男';
                    }
                  },
                  {
                    label: '年龄',
                    name: 'patientAge',
                    render: (v: any) => {
                      return v ? `${v?.includes('岁') ? v : v + '岁'}` : '-';
                    }
                  },
                  {
                    label: '患者ID',
                    name: 'patCardNo'
                  },
                  {
                    label: '联系电话',
                    name: 'patientMobile'
                  },
                  {
                    label: '身份证号',
                    name: 'patientIdNo'
                  },
                  {
                    label: '就诊院区',
                    name: 'hisAreaName' // TODO
                  },
                  {
                    label: '就诊科室',
                    name: 'deptName'
                  },
                  {
                    label: '就诊医师',
                    name: 'doctorName',
                    render: (v, r) => {
                      return (r.doctorName || '') + ' ' + (r.doctorTitle || '');
                    }
                  },
                  {
                    label: '号源类型',
                    name: 'type', // TODO
                    render: (v: any) => {
                      return v ? typeObj[v] : '-';
                    }
                  },
                  {
                    label: '就诊时段',
                    name: 'visitBeginTime',
                    render: (v: any, r) => {
                      return v ? r.visitBeginTime + '~' + r.visitEndTime : '-';
                    }
                  },
                  {
                    label: '就诊位置',
                    name: 'visitPosition' // TODO
                  }
                ]}
              />
            </CardLayout>
            <CardLayout title={'订单信息'} loading={detailLoading}>
              <FormDescriptions
                data={data}
                edit={editSate}
                form={form}
                loading={detailLoading}
                items={[
                  {
                    label: '订单状态',
                    name: 'statusName'
                  },
                  {
                    label: '业务类型',
                    name: 'bizType'
                  },
                  {
                    label: '业务渠道',
                    name: 'platformSource',
                    render: (v: any) => {
                      return platformSourceObj[v] || '-';
                    }
                  },
                  {
                    label: '支付方式',
                    name: 'payType',
                    render: (v: any) => {
                      return payMethodObj[v] || '-';
                    }
                  },
                  {
                    label: '订单金额',
                    name: 'totalFee',
                    render: (v: any) => {
                      return `${getPrice(v, 2, true)}`;
                    }
                  },
                  {
                    label: '实际支付金额',
                    name: 'totalRealFee',
                    render: (v: any) => {
                      return `${getPrice(v, 2, true)}`;
                    }
                  },
                  {
                    label: '下单时间',
                    name: 'orderTime',
                    render: (v: any) => {
                      return v ? moment(v).format('YYYY-MM-DD HH:mm') : '-';
                    }
                  },
                  {
                    label: '业务订单号',
                    name: 'orderId'
                  },
                  {
                    label: '支付时间',
                    name: 'payedTime',
                    render: (v: any) => {
                      return v ? moment(v).format('YYYY-MM-DD HH:mm') : '-';
                    }
                  },
                  {
                    label: '平台单号',
                    name: 'payOrderId'
                  },
                  {
                    label: '医院单号',
                    name: 'hisOrderNo'
                  },
                  {
                    label: '机构来源',
                    name: 'cooperHospitalName'
                  }
                ]}
              />
            </CardLayout>
          </Tabs.TabPane>
          {permisstion.canLookHisParameters && (
            <Tabs.TabPane tab='HIS 交互信息' key='his'>
              <h3>请求HIS入参信息</h3>
              {data?.toHisParam ? (
                <pre className='pre_box'>{data?.toHisParam}</pre>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
              <h3>请求HIS出参信息</h3>
              {data?.toHisResult ? (
                <pre className='pre_box'>{data?.toHisResult}</pre>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Tabs.TabPane>
          )}
        </Tabs>
      </Wrapper>
    </WaterMarkWrap>
  );
};

const Wrapper = styled.div<{ edit: boolean }>`
  background-color: white;
  margin: 24px;
  padding: 24px;
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
  .ant-table {
    > .ant-table-container {
      > .ant-table-content {
        > table {
          tr {
            > td {
              text-align: left;
            }
            > th {
              text-align: left;
            }
          }
        }
      }
    }
  }
  .pre_box {
    padding: 12px;
    border: 1px solid #ddd;
    max-height: 300px;
    overflow: auto;
  }
`;
