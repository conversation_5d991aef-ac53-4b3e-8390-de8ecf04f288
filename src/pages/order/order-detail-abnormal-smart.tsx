import React, { useEffect, Fragment } from 'react';
import { CardLayout, FormDescriptions, DateShow, getPrice } from 'parsec-admin';
import useApi, { payChannelObj, payMethodObj } from './api';

import {
  orderStatusObj,
  abnormalType,
  handleStatus
} from './listAbnormalSmart/index';
import { orderTypeObj } from './listSmart/index';
import { useParams } from 'react-router-dom';

export default () => {
  const { id: abnormalOrderId } = useParams<any>();

  const {
    data: { data }
  } = useApi.异常订单详情({
    params: {
      abnormalOrderId
    },
    initValue: {
      data: { data: {} }
    }
  });
  useEffect(() => {
    console.log('data', data);
  }, [data]);
  return (
    <Fragment>
      <CardLayout title='基础信息'>
        <FormDescriptions
          data={data}
          items={[
            {
              name: 'payOrderId',
              label: '平台单号'
            },
            {
              name: 'orderTime',
              label: '下单时间',
              render: (v: any) => {
                return <DateShow>{v}</DateShow>;
              }
            },
            {
              name: 'uniqueCode',
              label: '业务类型',
              render: (v: any) => {
                return orderTypeObj[v];
              }
            },
            {
              name: 'payChannel',
              label: '支付渠道',
              render: (v: any) => {
                return payChannelObj[v];
              }
            },
            {
              name: 'payMethod',
              label: '支付方式',
              render: (v: any) => {
                return payMethodObj[v];
              }
            },
            {
              name: 'totalFee',
              label: '订单金额',
              render: (v: any) => {
                return `${getPrice(v, 2, true)}`;
              }
            },
            {
              name: 'payFee',
              label: '实际支付金额',
              render: (v: any) => {
                return `${getPrice(v, 2, true)}`;
              }
            },
            {
              name: 'orderStatus',
              label: '订单状态',
              render: (v: any) => {
                return orderStatusObj[v];
              }
            }
          ]}
        />
      </CardLayout>
      <CardLayout title='异常信息'>
        <FormDescriptions
          data={data}
          items={[
            {
              name: 'abnormalTime',
              label: '异常创建时间',
              render: (v: any) => {
                return <DateShow>{v}</DateShow>;
              }
            },
            {
              name: 'abnormalType',
              label: '异常类型',
              render: (v: any) => {
                return abnormalType[v];
              }
            },
            {
              name: 'handleStatus',
              label: '订单状态',
              render: (v: any) => {
                return handleStatus[v];
              }
            },
            {
              name: 'operatorRemark',
              label: '操作'
            }
          ]}
        />
      </CardLayout>
    </Fragment>
  );
};
