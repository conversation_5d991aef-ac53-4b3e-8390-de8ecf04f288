import MyTableList from '@components/myTableList';
import useApi from '@pages/order/api';
import React, { useMemo, useRef } from 'react';
import { DayRangePicker, getPrice } from 'parsec-admin';
import { ExportOutlined } from '@ant-design/icons';
import { saveAs } from 'file-saver';
import moment from 'moment/moment';
import { Button } from 'antd';

export default () => {
  const {
    request: handleExport,
    loading: exportLoading
  } = useApi.物流订单统计导出({
    needInit: false
  });

  const queryParams = useRef<Record<string, string>>();

  return (
    <MyTableList
      tableTitle='物流统计'
      exportExcelButton={false}
      getList={({ params }) => {
        queryParams.current = params;
        return useApi.物流订单统计.request({
          ...params
        });
      }}
      action={
        <React.Fragment>
          <Button
            loading={exportLoading}
            type={'default'}
            icon={<ExportOutlined />}
            onClick={() =>
              handleExport({
                ...{
                  searchStartTime: queryParams.current?.searchStartTime,
                  searchEndTime: queryParams.current?.searchEndTime
                },
                isExport: '1'
              }).then(data =>
                saveAs(
                  data as any,
                  `物流统计 ${moment().format('YYYY-MM-DD HH时mm分ss秒')}.xls`
                )
              )
            }>
            导出
          </Button>
        </React.Fragment>
      }
      columns={useMemo(
        () => [
          {
            title: '用户',
            width: 180,
            dataIndex: 'patientName'
          },
          {
            title: '处方号',
            width: 180,
            dataIndex: 'hospitalTradeno'
          },
          {
            title: '订单号',
            width: 180,
            dataIndex: 'paySerialNumber'
          },
          {
            title: '物流单号',
            width: 180,
            dataIndex: 'deliveryCode'
          },
          {
            title: '物流费（元）',
            width: 180,
            dataIndex: 'fee',
            render: v => `¥${getPrice(v)}`,
            excelRender: v => `¥${getPrice(v)}`
          },
          {
            title: '收货人信息',
            width: 180,
            dataIndex: 'phone',
            render: (_, record: any) =>
              record.patientName
                ? record.patientName + '|' + record.phone
                : '-',
            excelRender: (_, record: any) =>
              record.patientName ? record.patientName + '|' + record.phone : '-'
          },
          {
            title: '收货人地址',
            width: 180,
            dataIndex: 'address'
          },
          {
            title: '订单状态',
            width: 180,
            dataIndex: 'mixStatus',
            render: v =>
              ({
                PAID: '已支付',
                REFUND: '已退款',
                UN_PAY: '未支付',
                EXCEPTION_REFUND: '异常退款'
              }[v] || '-'),
            excelRender: v =>
              ({
                PAID: '已支付',
                REFUND: '已退款',
                UN_PAY: '未支付',
                EXCEPTION_REFUND: '异常退款'
              }[v] || '-')
          },
          {
            title: '物流订单创建时间',
            width: 180,
            dataIndex: 'createTime',
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                valueFormat={'YYYY-MM-DD'}
                disabledDate={current => {
                  return current && current.valueOf() > Date.now();
                }}
              />
            ),
            searchIndex: ['searchStartTime', 'searchEndTime']
          }
        ],
        []
      )}
    />
  );
};
