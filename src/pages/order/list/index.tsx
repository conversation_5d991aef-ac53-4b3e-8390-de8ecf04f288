import React, { useMemo } from 'react';
import {
  LinkButton,
  getPrice,
  DayRangePicker,
  ArrSelect,
  RouteComponentProps
} from 'parsec-admin';
import useApi, { payChannelObj, payMethodObj, orderTypeObj } from '../api';
import MyTableList from '@components/myTableList';

export default ({ history }: RouteComponentProps) => {
  return (
    <MyTableList
      tableTitle='处理记录'
      getList={({ params }) =>
        useApi.处理记录列表.request({
          ...params,
          businessType: '1',
          refundStatus: '1'
        })
      }
      columns={useMemo(
        () => [
          {
            title: '下单时间',
            width: 180,
            dataIndex: 'createTime'
          },
          {
            title: '平台单号',
            width: 180,
            dataIndex: 'orderId',
            search: true
          },
          {
            title: '就诊人姓名',
            width: 140,
            dataIndex: 'patientName'
          },
          {
            title: '业务类型',
            width: 120,
            dataIndex: 'typeName'
          },
          {
            title: '支付渠道',
            dataIndex: 'payChannel',
            search: <ArrSelect options={payChannelObj} />,
            render: false
          },
          {
            title: '支付方式',
            dataIndex: 'payMethod',
            search: <ArrSelect options={payMethodObj} />,
            render: false
          },
          {
            title: '业务类型',
            dataIndex: 'type',
            search: <ArrSelect options={orderTypeObj} />,
            render: false
          },
          {
            title: '交易单号',
            dataIndex: 'paySerialNumber',
            search: true,
            render: false
          },
          {
            title: '支付渠道',
            width: 120,
            dataIndex: 'payChannelName'
          },
          {
            title: '支付方式',
            width: 120,
            dataIndex: 'payMethodName'
          },
          {
            title: '订单金额(元)',
            width: 180,
            dataIndex: 'totalFee',
            render: v => `${getPrice(v, 2, true)}`
          },
          {
            title: '操作时间',
            width: 180,
            dataIndex: 'operatorTime',
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                disabledDate={current => {
                  return current && current.valueOf() > Date.now();
                }}
              />
            ),
            searchIndex: ['beginTime', 'endTime']
          },
          {
            title: '操作人',
            width: 120,
            dataIndex: 'operatorName',
            search: true
          },
          {
            title: '订单状态',
            width: 120,
            dataIndex: 'statusName'
          },
          {
            title: '操作',
            fixed: 'right',
            width: 100,
            render: record => (
              <LinkButton
                onClick={() => {
                  record.orderId &&
                    history.push('/order/list/' + record.orderId);
                }}>
                查看
              </LinkButton>
            )
          }
        ],
        [history]
      )}
    />
  );
};
