import React, { useState, useEffect, useMemo } from 'react';
import { CardLayout, FormDescriptions, Form, getPrice } from 'parsec-admin';
import styled from 'styled-components';
import useApi, { invoiceStatusObj } from './api';
import { Rate, Steps, Table } from 'antd';
import { useParams } from 'react-router-dom';
import WaterMarkWrap from '@src/components/waterMarkWrap';

const editSate = false;
const { Step } = Steps;

export default ({ isLocal = false }: { isLocal?: boolean }) => {
  const { id: orderId, hisId, targetHisId } = useParams<any>();
  const columns = [
    {
      title: '退款发起人',
      dataIndex: 'operatorName',
      key: 'operatorName'
    },
    {
      title: '退款时间',
      dataIndex: 'refundSuccessTime',
      key: 'refundSuccessTime'
    },
    {
      title: '退款金额',
      dataIndex: 'amount',
      key: 'amount'
    },
    {
      title: '退款状态',
      dataIndex: 'refundStatusName',
      key: 'refundStatusName'
    },
    {
      title: '退款单号',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: '退款原因',
      dataIndex: 'refundDesc',
      key: 'refundDesc'
    }
  ];
  const [form] = Form.useForm();
  const [tableData, setTableData] = useState([]);

  const {
    data: { data: detailData },
    loading
  } = useApi.问诊记录详情({
    params: { orderId: orderId || '' },
    needInit: !!orderId && !isLocal,
    initValue: { data: {} }
  });
  const {
    data: { data: localDetailData },
    loading: localLoading
  } = useApi.本地问诊记录详情({
    params: { orderId: orderId || '', hisId, targetHisId },
    needInit: !!orderId && isLocal && targetHisId && hisId,
    initValue: { data: {} }
  });
  const detailLoading = useMemo(() => (isLocal ? localLoading : loading), [
    isLocal,
    loading,
    localLoading
  ]);

  console.log(detailLoading, 2222222222);

  const data = useMemo(() => (isLocal ? localDetailData : detailData), [
    detailData,
    isLocal,
    localDetailData
  ]);
  useEffect(() => {
    const data1: any = [
      {
        operatorName: '退款发起人',
        refundSuccessTime: '退款时间',
        amount: '退款金额',
        refundStatusName: '退款状态',
        id: '退款单号',
        refundDesc: '退款原因'
      }
    ];

    if (data) {
      data.refundOrderInfo && data1.push(data.refundOrderInfo);
      setTableData(data1);
    }
  }, [data]);

  return (
    <WaterMarkWrap>
      <Wrapper edit={false}>
        {data?.refundOrderInfo && (
          <CardLayout title={'退款信息'} loading={detailLoading}>
            <Steps
              current={
                { U: 0, W: 1, S: 2, F: 2 }[data?.refundOrderInfo.refundStatus]
              }>
              <Step
                title='退款发起'
                icon={
                  <img src={require('../../images/refundStep1.png')} alt='' />
                }
              />
              <Step
                title='退款受理'
                icon={
                  <img src={require('../../images/refundStep2.png')} alt='' />
                }
              />
              <Step
                title={
                  data?.refundOrderInfo.refundStatus === 'F'
                    ? '退款失败'
                    : '退款完成'
                }
                icon={
                  <img src={require('../../images/refundStep3.png')} alt='' />
                }
              />
            </Steps>
            <Table
              style={{ marginTop: 30 }}
              showHeader={false}
              pagination={false}
              bordered={true}
              columns={columns}
              dataSource={tableData}
            />
          </CardLayout>
        )}
        <CardLayout title={'就诊信息'} loading={detailLoading}>
          <FormDescriptions
            data={{
              ...(data?.patientInfo || {}),
              createTime: data?.createTime
            }}
            edit={editSate}
            form={form}
            loading={detailLoading}
            items={[
              {
                label: '姓名',
                name: 'name'
              },
              {
                label: '性别',
                name: 'patientSex'
              },
              {
                label: '年龄',
                name: 'patientAge'
              },
              {
                label: '体重',
                name: 'weight',
                render: v => v + ' kg'
              },
              {
                label: '联系电话',
                name: 'mobile'
              },
              {
                label: '身份证号',
                name: 'idNo'
              },
              {
                label: '患者ID',
                name: 'patCardNo'
              },
              {
                label: '地址',
                name: 'address'
              },
              {
                label: '问诊时间',
                name: 'createTime'
              },
              {
                label: '问诊科室',
                name: 'inquiryDept'
              },
              {
                label: '问诊医生',
                name: 'inquiryDoctor'
              },
              {
                label: '问诊目的',
                name: 'inquiryPurpose'
              }
            ]}
          />
        </CardLayout>
        <CardLayout title={'订单信息'} loading={detailLoading}>
          <FormDescriptions
            data={data}
            edit={editSate}
            form={form}
            loading={detailLoading}
            items={[
              {
                label: '订单状态',
                name: 'statusName'
              },
              {
                label: '业务类型',
                name: 'businessTypeStr'
              },
              {
                label: '办理渠道',
                name: 'payChannelStr'
              },
              {
                label: '支付方式',
                name: 'payMethodStr'
              },
              {
                label: '订单金额',
                name: 'orderTotalFee'
              },
              {
                label: '下单时间',
                name: 'createDate'
              },
              {
                label: '平台单号',
                name: 'orderIdStr'
              },
              {
                label: '支付时间',
                name: 'payDate'
              },
              {
                label: '交易单号',
                name: 'paySerialNumber'
              },
              {
                label: '医院单号',
                name: 'hospitalTradeno'
              },
              {
                label: '发票状态',
                children: data?.invoice?.state
                  ? invoiceStatusObj[data.invoice.state]
                  : '-'
              },
              {
                label: '费用明细',
                name: 'ownPayAmt',
                render: () => {
                  return `医保统筹：${getPrice(
                    data?.fundPay,
                    2,
                    true
                  )} 元  医保个账：${getPrice(
                    data?.psnAcctPay,
                    2,
                    true
                  )} 元 自费：${getPrice(data?.ownPayAmt, 2, true)} 元`;
                }
              }
            ]}
          />
        </CardLayout>
        <CardLayout title={'评价信息'} loading={detailLoading}>
          <FormDescriptions
            data={data?.appraisalInfo}
            edit={editSate}
            form={form}
            loading={detailLoading}
            items={[
              {
                label: '评价时间',
                name: 'createDate'
              },
              {
                label: '评价星级',
                name: 'score',
                render: v => {
                  return (
                    <Rate
                      disabled
                      defaultValue={v as number}
                      style={{
                        position: 'relative',
                        top: '-10px'
                      }}
                    />
                  );
                }
              },
              {
                label: '评价详情',
                name: 'appraisal'
              }
            ]}
          />
        </CardLayout>
      </Wrapper>
    </WaterMarkWrap>
  );
};

const Wrapper = styled.div<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
  .ant-table {
    > .ant-table-container {
      > .ant-table-content {
        > table {
          > tbody {
            > tr {
              > td {
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
`;
