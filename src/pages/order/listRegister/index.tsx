import { useMemo } from 'react';
import { DayRangePicker } from 'parsec-admin';
import useApi from '../api';
import MyTableList from '@components/myTableList';
import moment from 'moment';
import dayjs from 'dayjs';
import WaterMarkWrap from '@src/components/waterMarkWrap';
export default () => {
  const now = useMemo(() => moment(), []);
  return (
    <WaterMarkWrap>
      <MyTableList
        tableTitle='
      第三方挂号列表'
        getList={({ params }) => {
          const p = {
            ...params
            // uniqueCode: params.uniqueCode || '10'
            // businessType: '1'
            // refundStatus: '1'
          };
          return useApi.第三方挂号列表.request(p);
        }}
        columns={useMemo(
          () => [
            {
              title: '患者姓名',
              width: 180,
              dataIndex: 'patientName',
              searchIndex: 'queryInfo',
              search: true
            },
            {
              title: '证件号',
              width: 180,
              dataIndex: 'patientIdNo'
            },
            {
              title: '手机号',
              width: 180,
              dataIndex: 'patientMobile',
              searchIndex: 'phone',
              search: true
            },
            {
              title: '挂号科室',
              width: 180,
              dataIndex: 'deptName'
            },
            {
              title: '挂号医生',
              width: 180,
              dataIndex: 'doctorName'
            },
            {
              title: '支付时间',
              dataIndex: 'payedTime',
              width: 180,
              searchIndex: ['payedStartTime', 'payedEndTime'],
              search: (
                <DayRangePicker
                  valueFormat={'YYYY-MM-DD'}
                  disabledDate={current => {
                    return current && current.valueOf() > now.valueOf();
                  }}
                />
              ),
              render: v => {
                return v ? dayjs(v).format('YYYY-MM-DD HH:mm:ss') : '-';
              }
            },
            {
              title: '预约时间',
              dataIndex: 'visitDate',
              width: 180,
              searchIndex: ['startDate', 'endDate'],
              search: <DayRangePicker valueFormat={'YYYY-MM-DD'} />,
              render: v => {
                return v ? dayjs(v).format('YYYY-MM-DD HH:mm:ss') : '-';
              }
            }
          ],
          [now]
        )}
      />
    </WaterMarkWrap>
  );
};
