import React, { useEffect, useMemo, useRef } from 'react';
import {
  LinkButton,
  getPrice,
  DayRangePicker,
  ArrSelect,
  ActionsWrap,
  RouteComponentProps
} from 'parsec-admin';
import useApi, { payChannelObj, payMethodObj } from '../api';
import MyTableList from '@components/myTableList';
import ConfigStore from '@src/store/ConfigStore';
import { Button } from 'antd';

export enum PayChannel {
  weixin = '微信',
  zfb = '支付宝',
  app = 'app',
  offline = 'offline'
}

export default ({ history }: RouteComponentProps) => {
  const searchRef = useRef<any>();
  const { getCheckEnum, checkEnum } = ConfigStore.useContainer();
  useEffect(() => {
    if (!checkEnum) {
      getCheckEnum();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  console.log(checkEnum);
  return (
    <MyTableList
      tableTitle='检验检查列表'
      action={
        <Button onClick={() => useApi.exportCheckList(searchRef.current)}>
          导出EXCEL
        </Button>
      }
      getList={({ params }) => {
        searchRef.current = { ...params };
        return useApi.查询检验检查列表.request(params);
      }}
      columns={useMemo(
        () => [
          {
            title: '查询日期',
            width: 180,
            dataIndex: 'createTime',
            // search: <ArrSelect options={status} />,
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                disabledDate={current => {
                  return current && current.valueOf() > Date.now();
                }}
              />
            ),
            searchIndex: ['beginTime', 'endTime'],
            render: false
          },
          {
            title: '办理渠道',
            width: 180,
            searchIndex: 'payChannelStr',
            search: <ArrSelect options={payChannelObj} />,
            render: v => (v ? PayChannel[v] || '-' : '-')
          },
          {
            title: '下单时间',
            width: 180,
            dataIndex: 'createTime'
          },
          {
            title: '类型 | 平台单号',
            width: 210,
            dataIndex: 'type',
            render: (v, record: any) => {
              return `${v === 'TEST' ? '检验' : '检查'}|${record?.orderId ||
                '-'}`;
            }
          },
          {
            title: '就诊人 | ID',
            width: 140,
            dataIndex: 'patientName',
            render: (v, record: any) => {
              return `${v}|${record?.patientId || '- '}`;
            }
          },
          {
            title: '开单医师 | 开单科室',
            width: 190,
            dataIndex: 'deptName',
            render: (v, record: any) => {
              return `${record?.doctorName || '-'}|${v || '- '}`;
            }
          },
          {
            title: '支付方式',
            width: 120,
            dataIndex: 'payMethodStr',
            search: <ArrSelect options={payMethodObj} />
          },
          {
            title: '业务类型',
            width: 180,
            dataIndex: 'type',
            search: <ArrSelect options={[]} />,
            render: (v, record: any) => {
              return `${v === 'TEST' ? '检验' : '检查'}`;
            }
          },
          {
            title: '订单金额(元)',
            width: 180,
            dataIndex: 'totalFee',
            render: v => `${getPrice(v, 2, true)}`
          },
          {
            title: '订单状态',
            width: 120,
            dataIndex: 'status',
            search: (
              <ArrSelect
                options={(checkEnum?.mainStatus || []).map(item => {
                  return { label: item.desc, value: item.name };
                })}
              />
            ),
            render: v => {
              if (v && checkEnum?.mainStatus?.length) {
                return (
                  checkEnum.mainStatus?.find(item => item.name === v)?.desc ||
                  '-'
                );
              }
              return '-';
            }
          },
          {
            title: '平台单号',
            width: 120,
            dataIndex: 'orderId',
            search: true
          },
          {
            title: '患者姓名',
            searchIndex: 'patientName',
            search: true
          },
          {
            title: '就诊卡号',
            width: 120,
            dataIndex: 'patCardNo',
            search: true
          },
          {
            title: '支付流水号',
            width: 120,
            dataIndex: 'statusName',
            search: true
          },
          {
            title: '退款流水号',
            width: 120,
            dataIndex: 'statusName',
            search: true
          },

          {
            title: '操作',
            fixed: 'right',
            width: 120,
            render: record => (
              <ActionsWrap>
                <LinkButton
                  onClick={() => {
                    record.id &&
                      history.push('/order/OrderDetails/' + record.id);
                  }}>
                  查看
                </LinkButton>
                {['ERROR', 'PAYED', 'PART_REFUND', 'FAIL'].includes(
                  record?.status
                ) && (
                  <LinkButton
                    onClick={() => {
                      history.push('/order/RefundDetails/' + record.id);
                    }}>
                    退费
                  </LinkButton>
                )}
              </ActionsWrap>
            )
          }
        ],
        [checkEnum, history]
      )}
    />
  );
};
