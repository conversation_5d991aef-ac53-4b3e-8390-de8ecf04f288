import React, { useEffect } from 'react';
import {
  CardLayout,
  FormDescriptions,
  Form,
  getPrice,
  useModal,
  actionConfirm,
  CheckboxGroup
} from 'parsec-admin';
import styled from 'styled-components';
import useApi, { RecipeListItem } from '../api';
import { Button, Input, Space, Table } from 'antd';
import { useParams } from 'react-router-dom';
import { ColumnsType } from 'antd/lib/table';
import dayjs from 'dayjs';
import getSex from '@utils/getSex';
import ConfigStore from '@src/store/ConfigStore';
import { AES } from '@kqinfo/ui';
import TextArea from 'antd/lib/input/TextArea';
import env from '@src/configs/env';
const editSate = false;
enum UniqueCode {
  '检验' = 17,
  '检查'
}
enum OrderStatus {
  U = '待支付',
  S = '支付成功',
  F = '支付失败',
  C = '已取消'
}
enum RefundStatus {
  U = '初始状态',
  S = '退款中',
  F = '退款成功',
  C = '退款失败'
}
enum RefundStatusDetail {
  INIT = '初始状态',
  REFUNDINGS = '退款中',
  SUCCESS = '退款成功',
  FAILUE = '退款失败'
}
export default ({ isRefund = false }: { isRefund?: boolean }) => {
  const { id } = useParams<any>();

  const [form] = Form.useForm();

  const {
    data: datas,
    loading: detailLoading,
    request: queryDetail
  } = useApi.查询检验检查订单详情({
    params: { id }
  });
  const {
    getCheckEnum,
    checkEnum,
    getSingleCheckEnum,
    singleCheckEnum
  } = ConfigStore.useContainer();
  useEffect(() => {
    if (!checkEnum) {
      getCheckEnum();
    }
    if (!singleCheckEnum) {
      getSingleCheckEnum();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // 退款信息表格
  const refundColumn: ColumnsType<RecipeListItem> = [
    {
      title: '退款发起人',
      width: 200,
      align: 'center',
      dataIndex: 'patientName'
    },
    {
      title: '退款时间',
      width: 200,
      align: 'center',
      dataIndex: 'refundSuccessTime'
    },
    {
      title: '退款金额',
      width: 200,
      align: 'center',
      dataIndex: 'refundFee',
      render: v => `${getPrice(v, 2, true)}`
    },
    {
      title: '退款状态',
      width: 200,
      align: 'center',
      dataIndex: 'refundStatus',
      render: v => (v ? RefundStatus[v] || '-' : '-')
    },
    {
      title: '退款项目 ',
      width: 100,
      align: 'center',
      dataIndex: 'administrationName'
    },
    {
      title: '退款单号',
      width: 100,
      align: 'center',
      dataIndex: 'refundSerialNo'
    },
    {
      title: '退款原因',
      width: 100,
      align: 'center',
      dataIndex: 'refundDesc'
    }
  ];
  // 处置信息表格
  const switchPrizeModalVisible = useModal(
    ({ id }) => ({
      title: '操作退款',
      onSubmit: async values => {
        actionConfirm(
          () =>
            useApi.退费详情
              .request({
                password: AES.Encrypt(values.password),
                mainId: datas?.data?.id,
                itemIdList: values.hisTypeList,
                reason: values.name
              })
              .finally(() => {
                queryDetail();
                return Promise.resolve();
              }),
          '退费'
        );
      },
      width: 800,
      form,
      items: [
        {
          label: '退款项目',
          required: true,
          name: 'hisTypeList',
          render: (
            <CheckboxGroup
              options={(datas?.data?.itemList || [])?.map(item => ({
                value: item.id,
                label: item.itemName
              }))}
              onChange={(values: any) => {
                values?.forEach(id => {
                  const data = datas?.data?.itemList?.find(
                    item => item.id === id
                  );
                  form.setFieldValue('no', getPrice(data.unitPrice));
                });
              }}
            />
          )
        },
        {
          label: '请输入密码',
          name: 'password',
          required: true,
          formItemProps: {},
          render: () => <Input type='passWord' />
        },
        {
          label: '退款原因',
          name: 'name',
          required: true,
          render: () => {
            return <TextArea />;
          }
        }
      ]
    }),
    []
  );
  const columns: ColumnsType<RecipeListItem> = [
    {
      title: '项目类型',
      width: 400,
      align: 'center',
      dataIndex: 'itemClass'
    },
    {
      title: '项目名称',
      width: 300,
      align: 'center',
      dataIndex: 'itemName'
    },
    {
      title: '金额',
      width: 100,
      align: 'center',
      dataIndex: 'unitPrice',
      render: v => `${getPrice(v, 2, true)}`
    },
    {
      title: '状态',
      width: 100,
      align: 'center',
      dataIndex: 'status',
      render: v => {
        if (v && singleCheckEnum?.detailStatus?.length) {
          return (
            singleCheckEnum.detailStatus?.find(item => item.name === v)?.desc ||
            '-'
          );
        }
        return '-';
      }
    }
  ];
  const projectColumns: ColumnsType<RecipeListItem> = [
    {
      title: '项目名称',
      align: 'center',
      dataIndex: 'itemName'
    },
    {
      title: '项目编码',
      align: 'center',
      dataIndex: 'itemNum'
    },
    {
      title: '项目类型',
      align: 'center',
      dataIndex: 'itemClass'
    },
    {
      title: '项目类别',
      align: 'center',
      dataIndex: 'type'
    },
    {
      title: '单价/元',
      align: 'center',
      dataIndex: 'unitPrice',
      render: v => `${getPrice(v, 2, true)}`
    },
    {
      title: '项目状态',
      width: 100,
      align: 'center',
      dataIndex: 'status',
      render: v => {
        if (v && singleCheckEnum?.detailStatus?.length) {
          return (
            singleCheckEnum.detailStatus?.find(item => item.name === v)?.desc ||
            '-'
          );
        }
        return '-';
      }
    },
    {
      title: '开单数量',
      align: 'center',
      dataIndex: 'num'
    },
    {
      title: '合计/元',
      align: 'center',
      dataIndex: 'total',
      render: (_, record: any) => {
        const total = (record.unitPrice || 0) * record.num;
        return `${getPrice(total, 2, true)}`;
      }
    }
  ];

  const refundRecordColumns: ColumnsType<RecipeListItem> = [
    {
      title: '退费状态',
      dataIndex: 'refundStatus',
      render: v => {
        return RefundStatusDetail[v] || '-';
      }
    },
    {
      title: '退款发起人',
      dataIndex: 'patientName'
    },
    {
      title: '退费时间',
      dataIndex: 'refundSuccessTime'
    },
    // {
    //   title: '退款类型',
    //   dataIndex: 'type'
    // },
    {
      title: '退款项目',
      dataIndex: 'refundItems'
    },
    {
      title: '支付金额',
      dataIndex: 'payFee',
      render: v => `${getPrice(v, 2, true)}`
    },
    {
      title: '退费金额',
      dataIndex: 'refundFee',
      render: v => `${getPrice(v, 2, true)}`
    },
    {
      title: '退款流水号',
      dataIndex: 'refundSerialNo'
    },
    {
      title: '退款原因',
      dataIndex: 'refundDesc'
    },
    {
      title: '备注',
      dataIndex: 'remark'
    }
  ];

  return (
    <Wrapper edit={false}>
      {isRefund && env.hisId !== '6602' && (
        <CardLayout title={'退款信息'} loading={detailLoading}>
          <Table
            dataSource={datas?.data?.refundInfoList as any}
            columns={refundColumn}
            bordered
            pagination={false}
          />
        </CardLayout>
      )}
      {/* 两江中 */}
      {['6602'].includes(env.hisId) && (
        <CardLayout title={'项目信息'} loading={detailLoading}>
          <Table
            dataSource={datas?.data?.itemList || []}
            columns={projectColumns}
            bordered
            pagination={false}
            summary={(pageData: any) => {
              let totalNum = 0;
              let totalPrice = 0;
              pageData.forEach(({ num, unitPrice }) => {
                totalNum += num;
                totalPrice += num * (unitPrice || 0);
              });
              return (
                <>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={6}>
                      <div style={{ textAlign: 'right' }}>合计</div>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={1}>
                      <div style={{ textAlign: 'center' }}>{totalNum}</div>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={2}>
                      <div style={{ textAlign: 'center' }}>
                        {getPrice(totalPrice, 2, true)}
                      </div>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </>
              );
            }}
          />
        </CardLayout>
      )}
      <CardLayout title={'就诊信息'} loading={detailLoading}>
        <FormDescriptions
          data={datas?.data}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '姓名',
              name: 'patientName'
            },
            {
              label: '性别',
              name: 'patientSex',
              render: v => (v ? getSex(v) : '-')
            },
            {
              label: '年龄',
              name: 'patientAge',
              render: v => {
                return v && v?.includes('岁') ? v : v + '岁';
              }
            },
            {
              label: '就诊卡号',
              name: 'patCardNo'
            },
            {
              label: '联系电话',
              name: 'telephone'
            },
            {
              label: '就诊科室',
              name: 'deptName'
            },
            {
              label: '就诊医生',
              name: 'doctorName'
            },
            {
              label: '就诊时间',
              name: 'times',
              render: v => dayjs(v).format('YYYY-MM-DD HH:mm:ss')
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'订单信息'} loading={detailLoading}>
        <FormDescriptions
          data={{ ...datas?.data, ...datas?.data?.orderInfo }}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '订单状态',
              name: 'orderStatus',
              render: v => (v ? OrderStatus[v] || '-' : '-')
            },
            {
              label: '订单类型',
              name: 'uniqueCode',
              render: v => (v ? UniqueCode[v] || '-' : '-')
            },
            {
              label: '支付方式',
              name: 'payMethodStr'
            },
            {
              label: '定单金额',
              name: 'totalFee',
              render: v => `${getPrice(v, 2, true)}`
            },
            {
              label: '下单时间',
              name: 'createTime'
            },
            {
              label: '支付时间',
              name: 'payTime'
            },
            {
              label: '交易单号',
              name: 'paySerialNumber'
            },
            {
              label: '医院单号',
              name: 'hospitalOrderNo'
            }
          ]}
        />
      </CardLayout>
      {env.hisId !== '6602' && (
        <CardLayout title={'处置信息'} loading={detailLoading}>
          <Table
            dataSource={datas?.data?.itemList || []}
            columns={columns}
            bordered
            pagination={false}
          />
        </CardLayout>
      )}
      {/* 两江中 */}
      {['6602'].includes(env.hisId) && (
        <CardLayout title={'退款记录'} loading={detailLoading}>
          <Table
            dataSource={datas?.data?.refundInfoList || []}
            columns={refundRecordColumns}
            bordered
            pagination={false}
          />
        </CardLayout>
      )}
      {isRefund &&
        ['ERROR', 'PAYED', 'PART_REFUND', 'FAIL'].includes(
          datas?.data?.status
        ) && (
          <Space size={25}>
            <div></div>
            <Button type={'primary'} onClick={() => switchPrizeModalVisible()}>
              退款
            </Button>
          </Space>
        )}
    </Wrapper>
  );
};
const Wrapper = styled.div<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
  .btns {
    margin-top: 30px;
  }
  .record-title {
    text-align: right;
    margin-right: 5px;
  }
  .bold-price {
    font-size: 20px;
    font-weight: bold;
  }
  .ant-table {
    > .ant-table-container {
      > .ant-table-content {
        > table {
          > tbody {
            > tr {
              > td {
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
`;
