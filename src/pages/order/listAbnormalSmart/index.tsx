import React, { useMemo, useRef } from 'react';
import {
  LinkButton,
  getPrice,
  ArrSelect,
  RouteComponentProps,
  ActionsWrap,
  DateShow,
  useReloadTableList,
  actionConfirm,
  DayPicker
} from 'parsec-admin';
import useApi, { payChannelObj, payMethodObj } from '../api';

import MyTableList from '@components/myTableList';
import { message, Modal } from 'antd';
import { useRefundModal, orderTypeObj } from '../listSmart';
import { useSessionStorage } from 'react-use';
import usePaymodeWrapper from '@src/customHooks/usePaymodeWrapper';
import moment from 'moment';

// export const orderTypeObj: any = {
//   appointment_register: '预约挂号',
//   current_register: '当天挂号',
//   inpatient: '住院预交',
//   outpatient: '门诊缴费'
// };
export const typeObj: any = {
  1: '预约挂号',
  2: '当班挂号'
};
export const handleStatus: any = {
  U: '未处理',
  P: '处理中',
  S: '处理成功'
};
export const abnormalType: any = {
  N: '医院通知失败',
  M: '线上医保支付异常'
};

export const orderStatusObj: any = {
  U: '下单未支付',
  S: '支付成功',
  C: '取消支付',
  R: '已退款'
};

export const orderStatusObj2: any = {
  S: '成功',
  F: '缴费失败、付款完成',
  H: '医院确认超时或出现故障'
  // Z: '调用医院支付接口异常，重发次数超限'
};

export const refundStatus: any = {
  U: '初始状态',
  W: '退款中',
  S: '退款成功',
  C: '退款失败'
};

export default ({ history }: RouteComponentProps) => {
  const [loginInfo] = useSessionStorage<any>('doctor');
  const switchRefundModal = useRefundModal();
  const paramsRef = useRef<any>();
  const now = useMemo(() => moment(), []);
  const reloadTableList = useReloadTableList();

  const { payChannels, payMethods } = usePaymodeWrapper();

  return (
    <MyTableList
      tableTitle='异常订单'
      getList={({ params }) => {
        const p = {
          ...params
          // uniqueCode: params.uniqueCode || '10'
          // businessType: '1'
          // refundStatus: '1'
        };
        if (!p?.abnormalTime) {
          p.abnormalTime = now.format('YYYY-MM-DD');
        }
        paramsRef.current = p;
        return useApi.异常订单列表.request(p);
      }}
      columns={useMemo(
        () => [
          {
            title: '异常时间',
            searchIndex: 'abnormalTime',
            search: (
              <DayPicker
                defaultValue={now as any}
                valueFormat={'YYYY-MM-DD'}
                disabledDate={current => {
                  return current && current.valueOf() > now.valueOf();
                }}
              />
            ),
            render: false
          },
          {
            title: '业务类型',
            dataIndex: 'uniqueCode',
            search: (
              <ArrSelect
                options={orderTypeObj}
                // defaultValue={10}
                // allowClear={false}
              />
            ),
            render: false
          },
          {
            title: '下单时间',
            width: 180,
            dataIndex: 'orderTime'
          },
          {
            title: '交易流水号',
            width: 180,
            search: true,
            dataIndex: 'agtOrdNum'
          },
          {
            title: '平台单号',
            width: 180,
            search: true,
            dataIndex: 'payOrderId'
          },
          // {
          //   title: '支付流水号',
          //   width: 250,
          //   dataIndex: 'paySerialNumber',
          //   search: true
          // },
          // {
          //   title: '就诊人姓名',
          //   width: 140,
          //   search: true,
          //   dataIndex: 'patientName'
          // },
          {
            title: '业务类型',
            width: 120,
            dataIndex: 'uniqueCode',
            render: v => {
              return orderTypeObj[v] || '-';
            }
          },
          {
            title: '支付渠道',
            dataIndex: 'payChannel',
            search: (
              <ArrSelect
                options={
                  payChannels && Object.keys(payChannels)?.length > 0
                    ? payChannels
                    : payChannelObj
                }
              />
            ),
            render: false
          },
          {
            title: '支付方式',
            dataIndex: 'payMethod',
            search: (
              <ArrSelect
                options={
                  payMethods && Object.keys(payMethods)?.length > 0
                    ? payMethods
                    : payMethodObj
                }
              />
            ),
            render: false
          },

          // {
          //   title: '交易单号',r
          //   dataIndex: 'paySerialNumber',
          //   search: true,
          //   render: false
          // },
          // {
          //   title: '支付渠道',
          //   width: 120,
          //   dataIndex: 'payChannelName'
          // },
          {
            title: '支付渠道/支付方式',
            width: 160,
            dataIndex: 'payMethodName',
            render: (v, r: any) => {
              return `${r['payChannelName']}|${r['payMethodName']}`;
            }
          },
          {
            title: '订单金额(元)',
            width: 180,
            dataIndex: 'totalFee',
            render: v => `${getPrice(v, 2, true)}`
          },
          {
            title: '实际支付金额',
            width: 180,
            dataIndex: 'payFee',
            render: v => `${getPrice(v, 2, true)}`
          },
          {
            title: '退款流水号',
            dataIndex: 'refundSerialNo',
            render: false
          },
          // {
          //   title: '操作人',
          //   width: 120,
          //   dataIndex: 'operatorName',
          //   search: true
          // },
          {
            title: '业务状态',
            width: 120,
            search: <ArrSelect options={orderStatusObj2} />,
            dataIndex: 'orderStatus',
            render: v => {
              return orderStatusObj2[v] || '-';
            }
          },
          {
            title: '异常创建时间',
            width: 200,
            dataIndex: 'abnormalTime',
            render: v => {
              return <DateShow>{v}</DateShow>;
            }
          },
          {
            title: '异常类型',
            width: 120,
            dataIndex: 'abnormalType',
            search: <ArrSelect options={abnormalType} />,
            render: v => {
              return abnormalType[v] || '-';
            }
          },
          {
            title: '处理状态',
            width: 120,
            dataIndex: 'handleStatus',
            search: <ArrSelect options={handleStatus} />,
            render: v => {
              return handleStatus[v] || '-';
            }
          },
          {
            title: '处理时间',
            width: 200,
            dataIndex: 'handleTime',
            render: v => {
              return <DateShow>{v}</DateShow>;
            }
          },
          {
            title: '经办人',
            width: 120,
            dataIndex: 'operatorName'
          },
          {
            title: '操作',
            fixed: 'right',
            width: 150,
            render: record => {
              return (
                <ActionsWrap>
                  <LinkButton
                    onClick={() => {
                      record.orderId &&
                        history.push('/order/OrderAbnormalSmart/' + record.id);
                    }}>
                    查看
                  </LinkButton>
                  {record.canRefund && (
                    <LinkButton
                      onClick={() => {
                        // 退费金额默认=订单金额-已退款金额
                        switchRefundModal({
                          refundFee: record.canRefundFee / 100,
                          uniqueCode: record.uniqueCode,
                          orderId: record.payOrderId
                        });
                      }}>
                      退费
                    </LinkButton>
                  )}
                  {record?.canHandle && (
                    <LinkButton
                      onClick={() => {
                        Modal.confirm({
                          title: '提示',
                          content: '确定要手动处理吗？',
                          onOk: () => {
                            useApi.异常订单列表处理
                              .request({
                                abnormalId: record.id,
                                uniqueCode: record.uniqueCode,
                                operatorId: loginInfo.id,
                                operatorName: loginInfo.name
                              })
                              .then(res => {
                                if (res.data?.handleResult === '3') {
                                  Modal.confirm({
                                    title: '提示',
                                    okText: '了解',
                                    onOk: () => {
                                      reloadTableList();
                                    },
                                    content:
                                      '已发送提醒消息给患者，将由患者申请退费'
                                  });
                                  return;
                                }
                                if (res.data?.handleResult === '0') {
                                  message.success('手动处理成功');
                                  reloadTableList();
                                  return;
                                }
                                if (res.data?.handleResult === '1') {
                                  Modal.confirm({
                                    title: '提示',
                                    okText: '直接退费',
                                    onOk: () => {
                                      switchRefundModal({
                                        refundFee: record.payFee / 100,
                                        uniqueCode: record.uniqueCode,
                                        orderId: record.payOrderId
                                      });
                                    },
                                    content: '医院返回失败，可直接选择退费'
                                  });
                                  return;
                                }
                                if (res.data?.handleResult === '2') {
                                  let isFirst = true;
                                  type resType = typeof res;
                                  const func = (res: resType) => {
                                    if (res.data?.handleResult === '0') {
                                      message.success('手动处理成功');
                                      reloadTableList();
                                      return;
                                    }

                                    const redo = () => {
                                      actionConfirm(
                                        () =>
                                          useApi.异常订单列表处理重发
                                            .request({
                                              abnormalId: record.id,
                                              uniqueCode: record.uniqueCode,
                                              operatorId: loginInfo.id,
                                              operateType: 'AS',
                                              operatorName: loginInfo.name
                                            })
                                            .then(res => {
                                              func(res);
                                            }),
                                        '重发'
                                      );
                                    };
                                    if (res.data?.handleResult === '3') {
                                      Modal.confirm({
                                        title: '提示',
                                        okText: '了解',
                                        onOk: () => {
                                          reloadTableList();
                                        },
                                        content:
                                          '已发送提醒消息给患者，将由患者申请退费'
                                      });
                                      return;
                                    }
                                    if (res.data?.handleResult === '1') {
                                      Modal.confirm({
                                        title: '提示',
                                        okText: '直接退费',
                                        onOk: () => {
                                          switchRefundModal({
                                            refundFee: record.payFee / 100,
                                            uniqueCode: record.uniqueCode,
                                            orderId: record.payOrderId
                                          });
                                        },
                                        content: '医院返回失败，可直接选择退费'
                                      });
                                      return;
                                    }
                                    if (res.data?.handleResult === '2') {
                                      if (isFirst) {
                                        isFirst = false;
                                        Modal.confirm({
                                          title: '提示',
                                          okText: '重发',
                                          onOk: () => {
                                            redo();
                                          },
                                          content: '查询异常可进行重发操作。'
                                        });
                                      } else {
                                        Modal.confirm({
                                          title: '提示',
                                          okText: '重发',
                                          onOk: () => {
                                            redo();
                                          },
                                          content: (
                                            <div>
                                              <div>
                                                医院未明确返回状态，可以再次重发。
                                              </div>
                                              <div
                                                style={{
                                                  color: '#009DD9'
                                                }}>
                                                重发3次依然无明确状态返回，请联系HIS协助处理
                                              </div>
                                            </div>
                                          )
                                        });
                                      }
                                    }
                                  };
                                  func(res);
                                }
                              });
                          }
                        });
                      }}>
                      手动处理
                    </LinkButton>
                  )}
                </ActionsWrap>
              );
            }
          }
        ],
        [
          now,
          payChannels,
          payMethods,
          history,
          switchRefundModal,
          loginInfo.id,
          loginInfo.name,
          reloadTableList
        ]
      )}
    />
  );
};
