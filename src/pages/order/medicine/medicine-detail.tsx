import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  CardLayout,
  FormDescriptions,
  Form,
  getPrice,
  ActionsWrap,
  useModal,
  handleSubmit,
  LinkButton
} from 'parsec-admin';
import styled from 'styled-components';
import useApi, { CaseInfo, RecipeListItem, TrackInfoListItem } from '../api';
import {
  Input,
  Table,
  Button,
  Row,
  Col,
  message,
  Modal,
  Timeline,
  Space,
  Cascader,
  Radio
} from 'antd';
import {
  CaretDownOutlined,
  CaretUpOutlined,
  ConsoleSqlOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useParams, useHistory } from 'react-router-dom';
import { ColumnsType } from 'antd/lib/table';
import { usePromise } from 'parsec-hooks';
import getdiagnosisDictCodeFormat from '@src/utils/getdiagnosisDictCodeFormat';
import {
  AES,
  hideLoading,
  showLoading,
  Space as KqSpace,
  TransferChange
} from '@kqinfo/ui';
import dayjs from 'dayjs';
import addressOptions from '@src/utils/address-options';
const editSate = false;

export const DeliveryEnum = {
  '10': '新配送单',
  '30': '医院已发药',
  '40': '配送中',
  '50': '患者签收',
  '51': '系统自动签收',
  '99': '已取消',
  '100': '异常'
};

const TimeLineWrapper = ({
  trackInfoList
}: {
  trackInfoList: TrackInfoListItem[];
}) => {
  const [sort, setSort] = useState<'DESC' | 'ASC'>('DESC');

  const [innerTrackInfo, setInnerTrackInfo] = useState(trackInfoList);

  const returnWeekDay = (v: number) => {
    switch (v) {
      case 0:
        return '星期天';
      case 1:
        return '星期一';
      case 2:
        return '星期二';
      case 3:
        return '星期三';
      case 4:
        return '星期四';
      case 5:
        return '星期五';
      case 6:
        return '星期六';
    }
  };

  useEffect(() => {
    setInnerTrackInfo(innerTrackInfo.reverse());
  }, [innerTrackInfo, sort]);

  return (
    <React.Fragment>
      <KqSpace style={{ width: '100%', marginBottom: '10px' }} justify={'end'}>
        <LinkButton
          onClick={() => {
            setSort(sort === 'DESC' ? 'ASC' : 'DESC');
          }}>
          时间
          {sort === 'ASC' ? <CaretUpOutlined /> : <CaretDownOutlined />}
        </LinkButton>
      </KqSpace>

      <Timeline mode={'left'}>
        {innerTrackInfo?.map((item, index) => {
          return (
            <Timeline.Item
              key={index}
              label={item.opTime + returnWeekDay(dayjs(item.opTime)?.day())}>
              {item.opDesc}
            </Timeline.Item>
          );
        })}
      </Timeline>
    </React.Fragment>
  );
};

export default ({ isLocal = false }: { isLocal?: boolean }) => {
  const history = useHistory();
  const { id, targetHisId, hisId } = useParams<any>();

  const [form] = Form.useForm();
  const { data: PcaCode = [] } = usePromise(
    useCallback(
      () =>
        import('china-division/dist/pca-code.json').then(
          ({ default: data }) => data
        ),
      []
    )
  );
  // RecipeListItem
  const [tableData, setTableData] = useState<
    {
      hospitalTradeno: string;
      tableList: RecipeListItem[];
      lastData: {
        drugName: string;
        drugSpec: string;
        key: 'init';
      };
    }[]
  >([
    {
      hospitalTradeno: '',
      tableList: [
        {
          drugName: '合计金额（元）',
          drugSpec: '￥0.00',
          key: 'init'
        }
      ],
      lastData: {
        drugName: '合计金额（元）',
        drugSpec: '￥0.00',
        key: 'init'
      }
    }
  ]);

  // const { data: administrationList } = useApi.给药途径列表({
  //   initValue: [],
  //   needInit: true
  // });
  // const { data: rateInfo } = useApi.使用频次列表({
  //   initValue: [],
  //   needInit: true
  // });

  // 处置信息表格
  const columns: ColumnsType<RecipeListItem> = [
    {
      title: '药品名称',
      width: 400,
      align: 'center',
      dataIndex: 'drugName'
    },
    {
      title: '规格',
      width: 100,
      align: 'center',
      dataIndex: 'drugSpec'
      // render: (value: any, record: RecipeListItem, index: number) => {
      //   const obj = {
      //     children: record.packageSpec + '/' + record.packageUnits,
      //     props: {}
      //   } as any;
      //   if (index === tableData.length - 1) {
      //     obj.props.colSpan = 6;
      //     obj.children = <span className='bold-price'>{value}</span>;
      //   }
      //   return obj;
      // }
    },
    {
      title: '数量',
      width: 100,
      align: 'center',
      dataIndex: 'amount',
      render: (value: any, record: RecipeListItem, index: number) => {
        const obj = {
          children: value,
          props: {}
        } as any;
        // if (index === tableData.length - 1) {
        //   obj.props.colSpan = 0;
        // }
        return obj;
      }
    },
    {
      title: '途径',
      width: 100,
      align: 'center',
      dataIndex: 'administrationName'
      // render: x => {
      //   const find = administrationList?.find(res => res.value === x);
      //   return find?.text || x;
      // }
    },
    {
      title: '用量',
      width: 100,
      align: 'center',
      dataIndex: 'dosage',
      render: (value: any, record: RecipeListItem, index: number) => {
        const obj = {
          children:
            record?.dosageMode === 'MEASURE_UNIT'
              ? '每次' + record?.totalDosage + record?.extraData?.measureUnit
              : '每次' + record.dosage + '' + record.dosageUnit,
          props: {}
        } as any;
        // if (index === tableData.length - 1) {
        //   obj.props.colSpan = 0;
        // }
        return obj;
      }
    },
    {
      title: '频次',
      width: 100,
      align: 'center',
      dataIndex: 'frequencyName',
      render: (value: any, record: RecipeListItem, index: number) => {
        return record.frequencyName || record.frequency;
      }
    },
    {
      title: '金额（元）',
      width: 100,
      align: 'center',
      dataIndex: 'price',
      render: (value: any, record: RecipeListItem, index: number) => {
        const obj = {
          children: value,
          props: {}
        } as any;
        // if (index === tableData.length - 1) {
        //   obj.props.colSpan = 0;
        // }
        return obj;
      }
    }
  ];

  // 操作日志
  const columns1: ColumnsType<any> = [
    {
      title: '操作时间',
      align: 'center',
      width: 200,
      dataIndex: 'updateTime'
    },
    {
      title: '操作人',
      width: 100,
      align: 'center',
      dataIndex: 'operatorName'
    },
    {
      title: '操作类型',
      width: 100,
      align: 'center',
      dataIndex: 'type',
      render: v => {
        return '变更配送方式';
      }
    },
    {
      title: '操作内容',
      align: 'left',
      dataIndex: 'content'
    }
  ];

  const recordItems = [
    {
      title: '*主       诉：',
      dataIndex: 'chiefComplaint'
    },
    {
      title: '*现 病 史：',
      dataIndex: 'medicalHistory'
    },
    {
      title: '*既 往 史：',
      dataIndex: 'anamnesis'
    },
    {
      title: '*体       检：',
      dataIndex: 'examination'
    },
    {
      title: '*主要诊断：',
      dataIndex: 'mainDiagnosis'
    },
    {
      title: '其他诊断:',
      dataIndex: 'otherDiagnosis'
    },
    {
      title: '建   议：',
      dataIndex: 'recommend'
    }
  ] as {
    title: string;
    dataIndex: keyof CaseInfo;
  }[];

  const {
    data: { data: detailData },
    request: requestDetail,
    loading
  } = useApi.处方药品详情new({
    params: {
      id
    },
    needInit: !!id && !isLocal,
    initValue: {
      data: {}
    }
  });
  const {
    data: { data: localDetailData },
    loading: localLoading
  } = useApi.本地处方药品详情new({
    params: {
      id,
      hisId,
      targetHisId
    },
    needInit: !!id && isLocal && hisId && targetHisId,
    initValue: {
      data: {}
    }
  });
  const detailLoading = useMemo(() => (isLocal ? localLoading : loading), [
    isLocal,
    loading,
    localLoading
  ]);
  const data = useMemo(() => (isLocal ? localDetailData : detailData), [
    detailData,
    isLocal,
    localDetailData
  ]);

  const [showRefound, setShowRefound] = useState(false);
  const takeMethod = {
    HOSPITAL: '1',
    SELF: '1',
    POST: '2'
  };

  const switchRefundModal = useModal(
    ({ id, isDelivery }: { id: string; isDelivery: boolean }) => ({
      title: '退费提示',
      onSubmit: ({ ...params }: any) => {
        params.id = id;
        params.password = AES.Encrypt(params.password);

        return handleSubmit(
          () =>
            (isDelivery
              ? useApi.处方药品物流退款.request(params)
              : useApi.处方药品退款2.request(params)
            ).finally(() => {
              requestDetail();
            }),
          `退费`
        );
      },
      items: [
        {
          name: 'orderId',
          render: false
        },
        {
          label: '退费原因',
          name: 'refundReason',
          required: true,
          render: () => (
            <Input.TextArea
              placeholder='请输入退费原因 (最多40字)'
              maxLength={40}
            />
          )
        },
        {
          label: '密码',
          name: 'password',
          required: true,
          render: () => (
            <Input type='password' placeholder='请输入密码' maxLength={40} />
          )
        }
      ]
    }),
    []
  );
  const [deliveryType, setDeliveryType] = useState<any>('2');
  const switchDeliveryModal = useModal(
    ({
      id,
      mainOrderId,
      type,
      receiver,
      phone,
      addressOption,
      address
    }: {
      id: string;
      mainOrderId: string;
      type: string;
      receiver: string;
      phone: string;
      addressOption: string;
      address: string;
    }) => ({
      title: (
        <div style={{ width: 600 }}>
          变更配送方式
          <div
            style={{
              width: 550,
              fontWeight: 400,
              fontSize: 12,
              color: 'red'
            }}>
            温馨提示：变更配送方式并不会改变订单金额，系统更不会自动退款、补收费用，请谨慎操作。
          </div>
        </div>
      ),

      onSubmit: ({ ...params }: any) => {
        console.log('params', params);
        if (params.addressOption) {
          params.province = params.addressOption[0];
          params.city = params.addressOption[1];
          params.district = params.addressOption[2];
          delete params.addressOption;
        }

        return handleSubmit(() =>
          useApi.更改配送信息.request(params).then(() => {
            requestDetail();
          })
        );
      },
      myFormProps: {
        initialValues: {
          mainOrderId,
          id,
          type,
          receiver,
          phone,
          addressOption,
          address
        }
      } as any,

      items: [
        {
          name: 'mainOrderId',
          render: false
        },
        {
          name: 'id',
          render: false
        },
        {
          label: '配送方式',
          name: 'type',
          required: true,
          render: () => (
            <Radio.Group
              onChange={v => {
                setDeliveryType(v.target.value);
              }}>
              <Radio value={'2'}>快递</Radio>
              <Radio value={'1'}>自提</Radio>
            </Radio.Group>
          )
        },
        {
          label: '收件人',
          name: 'receiver',
          required: true,
          hidden: deliveryType !== '2',
          render: () => <Input placeholder='请输入收件人' />
        },
        {
          label: '手机号',
          name: 'phone',
          hidden: deliveryType !== '2',
          formItemProps: {
            rules: [
              {
                pattern: /^1[3456789]\d{9}$/,
                message: '手机号码格式不正确'
              }
            ]
          },
          render: () => <Input placeholder='请输入联系电话' />
        },
        {
          label: '收件地址',
          name: 'addressOption',
          required: true,
          hidden: deliveryType !== '2',
          render: () => (
            <Cascader
              placeholder='请选择所在地区'
              fieldNames={{
                label: 'name',
                value: 'name',
                children: 'children'
              }}
              style={{ width: '100%' }}
              changeOnSelect
              options={PcaCode as any}
            />
          )
        },
        {
          label: '详细地址',
          name: 'address',
          required: true,
          hidden: deliveryType !== '2',
          render: () => <Input placeholder='请输入街道、门牌' maxLength={40} />
        }
      ].filter(i => !i.hidden)
    }),
    [deliveryType]
  );

  const switchTimeline = useModal(
    ({ trackInfoList }: { trackInfoList: TrackInfoListItem[] }) => ({
      title: <span>地点和跟踪记录</span>,
      children: <TimeLineWrapper trackInfoList={trackInfoList} />
    }),
    []
  );

  useEffect(() => {
    if (data && data?.prescriptionList) {
      const arr: any[] = [];
      data.prescriptionList.forEach(item => {
        const recipeList = item?.recipeList?.[0] ?? {};
        const lastData = {
          drugName: '合计金额（元）',
          drugSpec: `￥${(recipeList.totalFee / 100).toFixed(2)}`,
          key: `￥${recipeList.prescriptionId}`
        };
        //不加key 控制台有警告。。。。。
        const newData = [...recipeList.drugList].map(subItem => {
          const newItem: any = {
            ...subItem
          };
          newItem.key = newItem.drugName;
          if (newItem.price) {
            newItem.price =
              '￥' +
              ((newItem.amount || 1) * (newItem.price as number)).toFixed(2);
          }
          return newItem;
        });
        arr.push({
          hospitalTradeno: recipeList?.hospitalTradeno,
          tableList: [...newData],
          lastData
        });
      });
      setTableData(arr);
    }
    // if (data && data.recipeList && data.recipeList[0]) {
    //   const lastData = {
    //     drugName: '合计金额（元）',
    //     drugSpec: `￥${(data.recipeList[0].totalFee / 100).toFixed(2)}`,
    //     key: `￥${data.recipeList[0].prescriptionId}`
    //   };
    //   //不加key 控制台有警告。。。。。
    //   const newData = [...data.recipeList[0].drugList].map(item => {
    //     item.key = item.drugName;
    //     if (item.price) {
    //       item.price =
    //         '￥' + ((item.amount || 1) * (item.price as number)).toFixed(2);
    //     }
    //     return item;
    //   });
    //   setTableData([...newData, lastData]);
    // }
  }, [data]);

  const [formRefund] = Form.useForm();

  async function handleRefund() {
    const refundReason = formRefund.getFieldValue('refundReason');
    const submitValue = {} as any;
    if (data) {
      submitValue.id = data?.id;
      submitValue.hisId = data?.hisId;
      submitValue.refundReason = refundReason;

      try {
        await useApi.处方药品退款.request(submitValue);
        message.success('保存成功');
        requestDetail();
        setShowRefound(false);
      } catch (err) {
        console.log(err);
      }
    }
  }

  function handleRefoundConfirm() {
    Modal.confirm({
      title: '确定需要为该订单退款吗？',
      icon: <ExclamationCircleOutlined />,
      content: '请仔细核实该处方订单信息，以便准确操作退款',
      onOk() {
        handleRefund();
      }
    });
  }

  return (
    <Wrapper edit={false}>
      <CardLayout title={'就诊信息'} loading={detailLoading}>
        <FormDescriptions
          data={{
            ...(data?.inquiryInfo || {}),
            ...data,
            ...(data?.registrationInfo || {})
          }}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '姓名',
              name: 'patientName'
            },
            {
              label: '性别',
              name: 'patientSex'
            },
            {
              label: '年龄',
              name: 'patientAge',
              render: x => {
                return x + ' 岁';
              }
            },
            {
              label: '体重',
              name: 'patientWeight',
              render: x => {
                return x + ' kg';
              }
            },
            {
              label: '患者ID',
              name: 'patientId'
            },
            {
              label: '联系电话',
              name: 'patientTel'
            },
            {
              label: '身份证号',
              name: 'patientIdNo'
            },
            {
              label: '地址',
              name: 'patientAddress'
            },
            {
              label: '就诊科室',
              name: 'deptName'
            },
            {
              label: '就诊医生',
              name: 'doctorName'
            },
            {
              label: '就诊时间',
              name: 'times',
              render: (x: any = '') => {
                // 20210116182030
                return `${x.substring(0, 4)}-${x.substring(4, 6)}-${x.substring(
                  6,
                  8
                )} ${x.substring(8, 10)}:${x.substring(10, 12)}:${x.substring(
                  12,
                  14
                )}`;
              }
            },
            {
              label: '问诊目的',
              name: 'purpose'
            }
          ]}
        />
      </CardLayout>
      {data?.caseInfo && (
        <CardLayout title={'病历史信息'} loading={detailLoading}>
          <Row>
            {recordItems.map(record => (
              <Col xl={12} md={24} key={record.title}>
                <Row>
                  <Col span={4}>
                    <div className='record-title'>{record.title}</div>
                  </Col>
                  <Col span={20}>
                    {record.dataIndex === 'mainDiagnosis' ? (
                      <div>
                        {getdiagnosisDictCodeFormat(
                          data.caseInfo[record.dataIndex]
                        )
                          .map((item, index) => {
                            return `${index + 1}.${item}`;
                          })
                          .join(' ')}
                      </div>
                    ) : (
                      <div>{data.caseInfo[record.dataIndex]}</div>
                    )}
                  </Col>
                </Row>
              </Col>
            ))}
          </Row>
        </CardLayout>
      )}
      <CardLayout title={'处置信息'} loading={detailLoading}>
        {tableData.map(subTable => {
          return (
            <Space direction='vertical' size={10} style={{ width: '100%' }}>
              <Table
                dataSource={subTable.tableList}
                columns={columns}
                title={() => <h3>处方单号：{subTable.hospitalTradeno}</h3>}
                bordered
                pagination={false}
                summary={() => (
                  <Table.Summary fixed>
                    <Table.Summary.Row>
                      <Table.Summary.Cell index={0}>
                        {subTable.lastData.drugName}
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={2} colSpan={6}>
                        {subTable.lastData.drugSpec}
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                  </Table.Summary>
                )}
              />
            </Space>
          );
        })}
      </CardLayout>
      <CardLayout title={'订单信息'} loading={detailLoading}>
        <FormDescriptions
          data={data?.prescriptionMainOrder ?? {}} // prescriptionMainOrder
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '订单状态',
              name: 'statusName'
            },
            // {
            //   label: '业务类型',
            //   name: 'bizeType'
            // },
            // {
            //   label: '处方类型',
            //   name: 'prescriptionType', //处方类别（1西药，3中药饮片）
            //   render: (
            //     <span>
            //       {data?.prescriptionType === 1
            //         ? '西医处方'
            //         : data?.prescriptionType === 3
            //         ? '中医处方'
            //         : '-'}
            //     </span>
            //   )
            // },
            {
              label: '开单时间',
              name: 'prescDate'
            },
            {
              label: '平台订单',
              name: 'id'
            },
            {
              label: '医院单号',
              name: 'hospitalTradeno',
              render: x => {
                return data?.recipeList?.[0]?.hospitalTradeno || x;
              }
            },
            {
              label: '订单金额',
              name: 'totalFee',
              render: (v: any) => `￥${getPrice(v)}`
            },
            {
              label: '支付时间',
              name: 'payDate'
            },
            {
              label: '交易单号',
              name: 'paySerialNumber'
            },
            // {
            //   label: '业务渠道',
            //   name: 'payChannelStr'
            // },
            // {
            //   label: '支付方式',
            //   name: 'payMethodStr'
            // },
            {
              label: '费用明细',
              name: 'ownPayAmt',
              render: () => {
                return `医保统筹：${getPrice(
                  data?.recipeList?.reduce((p, i) => p + i.fundPay, 0),
                  2,
                  true
                )} 元  医保个账：${getPrice(
                  data?.recipeList?.reduce((p, i) => p + i.psnAcctPay, 0),
                  2,
                  true
                )} 元 自费：${getPrice(
                  data?.recipeList?.reduce((p, i) => p + i.ownPayAmt, 0),
                  2,
                  true
                )} 元`;
              }
            }
          ]}
        />
      </CardLayout>
      {data?.prescriptionMainOrder?.takeMethod !== 'HOSPITAL' && (
        <CardLayout title={'配送信息'} loading={detailLoading}>
          <FormDescriptions
            data={data?.deliveryOrder || {}}
            edit={editSate}
            form={form}
            loading={detailLoading}
            items={[
              {
                label: '收货人',
                name: 'receiver'
              },
              {
                label: '联系电话',
                name: 'phone'
              },
              {
                label: '收获地址',
                name: 'address'
              },
              {
                label: '配送公司',
                name: 'deliveryCompany'
              },
              // {
              //   label: '配送方式',
              //   name: 'id'
              // },
              {
                label: '金额',
                name: 'fee',
                render: (v: any) => `￥${getPrice(v)}`
              },
              {
                label: '订单状态',
                name: 'status',
                render: (v: any) => DeliveryEnum[String(v)]
              },
              {
                label: '支付时间',
                name: 'createTime'
              },
              {
                label: '交易单号',
                name: 'paySerialNumber'
              },
              {
                label: '运单号',
                name: 'deliveryCode'
              },
              {
                name: 'deliveryCode',
                render: v => {
                  return v ? (
                    <LinkButton
                      onClick={() => {
                        showLoading();
                        useApi.查询物流信息
                          .request({
                            // prescriptionId: id
                            mainOrderId: id
                          })
                          .then(res => {
                            hideLoading();
                            switchTimeline({
                              trackInfoList: res?.data?.trackInfoList || []
                            });
                          })
                          .catch(() => {
                            hideLoading();
                          });
                      }}>
                      查看物流详情
                    </LinkButton>
                  ) : (
                    false
                  );
                }
              }
            ]}
          />
        </CardLayout>
      )}
      <CardLayout title={'操作日志'} loading={detailLoading}>
        <Table
          dataSource={data?.drugDeliveryAddressLogList || []}
          columns={columns1}
          bordered
          pagination={false}
        />
      </CardLayout>
      {!isLocal && (
        <CardLayout title={'操作'} loading={detailLoading}>
          {!showRefound && (
            <ActionsWrap max={3}>
              <Button
                type='primary'
                style={{
                  marginRight: 20
                }}
                onClick={() => {
                  history.push(
                    '/operate/consult/list/' + data?.inquiryInfo?.orderId
                  );
                }}>
                进入咨询详情
              </Button>
              {(data?.prescriptionMainOrder?.status === 'WAIT_DELIVERY' ||
                data?.prescriptionMainOrder?.status === 'PAYED') && (
                <Button
                  type='primary'
                  style={{
                    marginRight: 20
                  }}
                  onClick={() => {
                    console.log(
                      'type--->',
                      takeMethod[data?.prescriptionMainOrder?.takeMethod]
                    );
                    setDeliveryType(
                      takeMethod[data?.prescriptionMainOrder?.takeMethod]
                    );
                    switchDeliveryModal({
                      ...data?.deliveryOrder,
                      mainOrderId: data?.prescriptionMainOrder?.id,
                      id: data?.deliveryOrder?.id,
                      type: takeMethod[data?.prescriptionMainOrder?.takeMethod],
                      addressOption: [
                        data?.deliveryOrder?.province,
                        data?.deliveryOrder?.city,
                        data?.deliveryOrder?.district
                      ]
                    });
                  }}>
                  变更配送方式
                </Button>
              )}
              {data?.canPresRefund && (
                <Button
                  onClick={() =>
                    switchRefundModal({
                      id,
                      isDelivery: false
                    })
                  }>
                  退款
                </Button>
              )}
              {data?.canDeliveryRefund && (
                <Button
                  onClick={() =>
                    switchRefundModal({
                      id,
                      isDelivery: true
                    })
                  }>
                  退快递费
                </Button>
              )}
            </ActionsWrap>
          )}
          {showRefound && (
            <CardLayout title={'退款原因'} loading={detailLoading}>
              <Form form={formRefund}>
                <Form.Item
                  name='refundReason'
                  rules={[
                    {
                      required: true,
                      message: '请输入退款原因'
                    }
                  ]}>
                  <Input.TextArea
                    placeholder='请输入退款原因'
                    autoSize={{
                      minRows: 4,
                      maxRows: 6
                    }}
                  />
                </Form.Item>
                <Form.Item>
                  <div>
                    <Button
                      style={{
                        marginRight: 20
                      }}
                      onClick={() => setShowRefound(false)}>
                      取消
                    </Button>
                    <Button type='primary' onClick={handleRefoundConfirm}>
                      确定
                    </Button>
                  </div>
                </Form.Item>
              </Form>
            </CardLayout>
          )}
        </CardLayout>
      )}
    </Wrapper>
  );
};

const Wrapper = styled.div<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
  .btns {
    margin-top: 30px;
  }
  .record-title {
    text-align: right;
    margin-right: 5px;
  }
  .bold-price {
    font-size: 20px;
    font-weight: bold;
  }
  .ant-table {
    > .ant-table-container {
      > .ant-table-content {
        > table {
          > tbody {
            > tr {
              > td {
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
`;
