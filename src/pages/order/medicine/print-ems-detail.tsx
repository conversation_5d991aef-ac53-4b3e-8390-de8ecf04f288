import { Col, Row, Spin } from 'antd';
import { useEffect, useMemo } from 'react';
import JsBarcode from 'jsbarcode';
import useApi, { MailDetail } from './api';
import './print-ems-detail.less';

/** 整体样式复用的病案复印平台的面单打印相关的功能 */
export default ({ id }: { id?: number }) => {
  const { data, loading } = useApi.快递面单数据({
    needInit: !!id,
    params: {
      id,
      deliveryCompany: 'EMS'
    }
  });

  const detailData = useMemo(() => data?.data || ({} as MailDetail), [data]);

  const receiverAddress = useMemo(
    () =>
      detailData.receiverProv
        ? `${detailData.receiverProv}${detailData.receiverCity}${detailData.receiverCounty}${detailData.receiverAddress}`
        : '',
    [detailData]
  );

  const senderAddress = useMemo(
    () =>
      detailData.senderProv
        ? `${detailData.senderProv}${detailData.senderCity}${detailData.senderCounty}${detailData.senderAddress}`
        : '',
    [detailData]
  );

  useEffect(() => {
    if (detailData.waybillNo) {
      JsBarcode('.barcode', detailData.waybillNo, {
        width: 2,
        height: 50,
        background: 'rgba(0,0,0,0)'
      });
    }
  }, [detailData]);

  return (
    <Spin spinning={loading}>
      <Row style={{ fontWeight: 'bold', fontSize: 18, textAlign: 'center' }}>
        <Col span={12}>100*180</Col>
        <Col span={12}>100*150</Col>
      </Row>
      <Row>
        <Col span={12}>
          <div id='T100x180' className='print-box'>
            <div className='page'>
              <div className='base_product_no'>
                <div>标准快递</div>
              </div>
              <div className='routeCode line-bottom'>
                <span>{detailData.routeCode}</span>
              </div>
              <div className='line-bottom flex'>
                <div className='receiver-msg'>
                  <div className='receiver'>
                    <div className='title line-bottom line-right'> 收 </div>
                    <div className='content line-bottom line-right'>
                      <span className='name'>{detailData.receiver}</span>
                      &nbsp;
                      <span className='mobile'>{detailData.receiverPhone}</span>
                      <br />
                      <span className='address'>
                        <span>{receiverAddress}</span>
                      </span>
                    </div>
                  </div>
                  <div className='sender'>
                    <div className='title line-right'> 寄 </div>
                    <div className='content line-right'>
                      <span className='name'>{detailData.sender}</span>
                      &nbsp;
                      <span className='mobile'>{detailData.senderPhone}</span>
                      <br />
                      <span className='address'>
                        <span>{senderAddress}</span>
                      </span>
                    </div>
                  </div>
                </div>
                <div className='service'>
                  <div className='title line-bottom'>服&nbsp;&nbsp;务</div>
                  <div className='content'> 件数:{detailData.cargoCount} </div>
                </div>
              </div>
              <div
                className='trackingNo line-bottom'
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}>
                <img
                  className='barcode'
                  alt='二维码'
                  // jsbarcode-height="50"
                  // jsbarcode
                />
              </div>
              <div className='cargos line-bottom'>{detailData.cargo}</div>
              <div className='supplement line-bottom'>
                <div className='supplement-left line-right'>
                  {' '}
                  快件送达收件人地址,经收件人或收件人允许的代收件人签字,视为送达{' '}
                </div>
                <div className='supplement-right'>
                  <div>收件人\代收人:</div>
                  <div>签收时间:&nbsp;&nbsp;年&nbsp;月&nbsp;日&nbsp;时</div>
                </div>
              </div>
              <div></div>
              <div
                className='trackingNo-small line-bottom'
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}>
                <img
                  className='barcode'
                  alt='二维码'
                  // jsbarcode-height="50"
                  // jsbarcode
                />
              </div>
              <div className='receiver-small line-bottom'>
                <div className='title'>收</div>
                <div className='content'>
                  <span className='name'>{detailData.receiver}</span>
                  &nbsp;
                  <span className='mobile'>{detailData.receiverPhone}</span>
                  <br />
                  <span className='address'>
                    <span>{receiverAddress}</span>
                  </span>
                </div>
              </div>
              <div className='sender-small line-bottom'>
                <div className='title'>寄</div>
                <div className='content'>
                  <span className='name'>{detailData.sender}</span>
                  &nbsp;
                  <span className='mobile'>{detailData.senderPhone}</span>
                  <br />
                  <span className='address'>
                    <span>{senderAddress}</span>
                  </span>
                </div>
              </div>
              <div className='pickup_notes line-bottom'>
                <div className='remark'>备注</div>
                <div>{detailData.cargo}</div>
              </div>
              <div className='footer'>
                <span>
                  <span>网址: www.ems.com.cn</span>&nbsp;&nbsp;&nbsp;
                  <span>客服电话: 11183</span>
                </span>
              </div>
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div id='T100x150' className='print-box'>
            <div className='page'>
              <div className='base_product_no line-bottom'>
                <div
                  className='line-left'
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}>
                  <img
                    className='barcode'
                    alt='二维码'
                    // jsbarcode
                    // jsbarcode-height="50"
                  />
                </div>
              </div>
              <div className='routeCode line-bottom'>
                <span>{detailData.routeCode}</span>
              </div>
              <div className='line-bottom flex'>
                <div className='receiver-msg'>
                  <div className='receiver'>
                    <div className='title line-bottom line-right'> 收 </div>
                    <div className='content line-bottom'>
                      <span className='name'>{detailData.receiver}</span>
                      &nbsp;
                      <span className='mobile'>{detailData.receiverPhone}</span>
                      <br />
                      <span className='address'>
                        <span>{detailData.receiverAddress}</span>
                      </span>
                    </div>
                  </div>
                  <div className='sender'>
                    <div className='title line-right'> 寄 </div>
                    <div className='content'>
                      <span className='name'>{detailData.sender}</span>
                      &nbsp;
                      <span className='mobile'>{detailData.senderPhone}</span>
                      <br />
                      <span className='address'>
                        <span>{senderAddress}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div className='cargos line-bottom'>{detailData.cargo}</div>
              <div className='supplement line-bottom'>
                <div className='supplement-left line-right'>
                  {' '}
                  快件送达收件人地址,经收件人或收件人允许的代收件人签字,视为送达{' '}
                </div>
                <div className='supplement-right'>
                  <div>收件人\代收人:</div>
                  <div>签收时间:&nbsp;&nbsp;年&nbsp;月&nbsp;日&nbsp;时</div>
                </div>
              </div>
              <div></div>
              <div
                className='trackingNo-small line-bottom'
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}>
                <img
                  className='barcode'
                  alt='二维码'
                  // jsbarcode-height="50"
                  // jsbarcode
                />
              </div>
              <div className='flex'>
                <div className='receiver-small line-bottom'>
                  <div className='title'>收</div>
                  <div className='content'>
                    <span className='name'>{detailData.receiver}</span>
                    &nbsp;
                    <span className='mobile'>{detailData.receiverPhone}</span>
                    <span className='address'>
                      <span>{detailData.receiverAddress}</span>
                    </span>
                  </div>
                </div>
                <div className='sender-small line-bottom'>
                  <div className='title'>寄</div>
                  <div className='content'>
                    <span className='name'>{detailData.sender}</span>
                    &nbsp;
                    <span className='mobile'>{detailData.senderPhone}</span>
                    <span className='address'>
                      <span>{senderAddress}</span>
                    </span>
                  </div>
                </div>
              </div>
              <div className='pickup_notes line-bottom'>
                <div className='remark'>备注</div>
                <div>{detailData.cargo}</div>
              </div>
              <div className='footer'>
                <span>
                  <span>网址: www.ems.com.cn</span>&nbsp;&nbsp;&nbsp;
                  <span>客服电话: 11183</span>
                </span>
              </div>
            </div>
          </div>
        </Col>
      </Row>
    </Spin>
  );
};
