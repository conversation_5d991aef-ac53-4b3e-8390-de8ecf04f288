.barcode {
  object-fit: contain;
}
.print-box {
  padding: 2mm;
}

.print-box .page {
  border: 1px solid black;
}

.print-box .page .line-bottom {
  border-bottom: 1px solid black;
}

.print-box .page .line-right {
  border-right: 1px solid black;
}

.print-box .page .flex {
  display: flex;
}

@media screen {
  #T100x180 .page {
    // background-image: url('./images/ems100x180.png');
    background-size: cover;
  }

  #T100x150 .page {
    // background-image: url('./images/ems100x150.png');
    background-size: cover;
  }
}

.page {
  font-family: '黑体', serif;
}

.page > .flex {
  display: flex;
}

#T100x180 {
  display: flex;
  justify-content: center;
}

#T100x180 .page {
  width: 96mm;
  height: 176mm;
}

#T100x180 .base_product_no {
  display: flex;
  justify-content: flex-end;
  padding-right: 2mm;
}

#T100x180 .base_product_no > div {
  width: 4em;
  height: 12mm;
  font-size: 12pt;
}

#T100x180 .routeCode {
  text-align: center;
}

#T100x180 .routeCode > span {
  height: 18mm;
  width: 96mm;
  font-size: 20pt;
}

#T100x180 .receiver-msg .receiver {
  display: flex;
  font-size: 8pt;
  font-weight: bold;
}

#T100x180 .receiver-msg .receiver .title {
  height: 16mm;
  width: 6mm;
  text-align: center;
}

#T100x180 .receiver-msg .receiver .content {
  height: 16mm;
  width: 68mm;
}

#T100x180 .receiver-msg .sender {
  display: flex;
}

#T100x180 .receiver-msg .sender .title {
  height: 16mm;
  width: 6mm;
  font-size: 8pt;
  text-align: center;
}

#T100x180 .receiver-msg .sender .content {
  height: 16mm;
  width: 68mm;
  font-size: 8pt;
}

#T100x180 .service {
  flex: 1;
}

#T100x180 .service .title {
  display: flex;
  justify-content: center;
}

#T100x180 .service .content {
  padding: 1mm;
}

#T100x180 .trackingNo {
}

#T100x180 .trackingNo img {
  height: 25mm;
  width: 92mm;
}

#T100x180 .cargos {
  height: 13mm;
  width: 96mm;
  font-size: 8pt;
  padding-left: 1mm;
}

#T100x180 .supplement {
  height: 12mm;
  width: 96mm;
  display: flex;
}

#T100x180 .supplement .supplement-left {
  width: 50mm;
  font-size: 6pt;
}

#T100x180 .supplement .supplement-right {
  width: 46mm;
  font-size: 6pt;
  padding-left: 1mm;
}

#T100x180 .center-line {
  height: 7mm;
  width: 96mm;
  font-size: 8pt;
}

#T100x180 .trackingNo-small {
  display: flex;
}

#T100x180 .trackingNo-small > .logo {
  height: 13mm;
  width: 43mm;
}

#T100x180 .trackingNo-small > img {
  height: 24mm;
  width: 95mm;
}

#T100x180 .receiver-small {
  display: flex;
}

#T100x180 .receiver-small .title {
  width: 10mm;
  text-align: center;
}

#T100x180 .receiver-small .content {
  height: 11mm;
  width: 86mm;
  font-size: 8pt;
}

#T100x180 .sender-small {
  display: flex;
}

#T100x180 .sender-small .title {
  width: 10mm;
  text-align: center;
}

#T100x180 .sender-small .content {
  height: 11mm;
  width: 86mm;
  font-size: 8pt;
}

#T100x180 .pickup_notes {
  display: flex;
  height: 16mm;
}

#T100x180 .pickup_notes .remark {
  padding: 0 1mm;
  width: 10mm;
}

#T100x180 .pickup_notes div {
  width: 96mm;
  font-size: 8pt;
}

#T100x180 .footer > span {
  height: 7mm;
  width: 68mm;
  font-size: 8pt;
  padding-left: 1mm;
}

#T100x150 {
  display: flex;
  justify-content: center;
}

#T100x150 .page {
  width: 96mm;
  height: 146mm;
}

#T100x150 .base_product_no {
  display: flex;
  justify-content: flex-end;
  padding-right: 2mm;
}

#T100x150 .base_product_no > div {
  width: 95mm;
  height: 25mm;
}

#T100x150 .base_product_no > div > img {
  height: 100%;
  width: 100%;
}

#T100x150 .routeCode {
  text-align: center;
}

#T100x150 .routeCode > span {
  height: 18mm;
  width: 96mm;
  font-size: 20pt;
}

#T100x150 .receiver-msg .receiver {
  display: flex;
  font-size: 8pt;
  font-weight: bold;
}

#T100x150 .receiver-msg .receiver .title {
  height: 16mm;
  width: 6mm;
  text-align: center;
}

#T100x150 .receiver-msg .receiver .content {
  height: 16mm;
  width: 90mm;
}

#T100x150 .receiver-msg .sender {
  display: flex;
}

#T100x150 .receiver-msg .sender .title {
  height: 16mm;
  width: 6mm;
  font-size: 8pt;
  text-align: center;
}

#T100x150 .receiver-msg .sender .content {
  height: 16mm;
  width: 68mm;
  font-size: 8pt;
}

#T100x150 .service {
  flex: 1;
}

#T100x150 .service .title {
  display: flex;
  justify-content: center;
}

#T100x150 .service .content {
  padding: 1mm;
}

#T100x150 .trackingNo {
  padding: 2mm;
}

#T100x150 .trackingNo img {
  height: 20mm;
  width: 96mm;
}

#T100x150 .cargos {
  height: 13mm;
  width: 96mm;
  font-size: 8pt;
  padding-left: 1mm;
}

#T100x150 .supplement {
  height: 12mm;
  width: 96mm;
  display: flex;
}

#T100x150 .supplement .supplement-left {
  width: 50mm;
  font-size: 6pt;
}

#T100x150 .supplement .supplement-right {
  width: 46mm;
  font-size: 6pt;
  padding-left: 1mm;
}

#T100x150 .center-line {
  height: 7mm;
  width: 96mm;
  font-size: 8pt;
}

#T100x150 .trackingNo-small {
  display: flex;
}

#T100x150 .trackingNo-small > .logo {
  height: 13mm;
  width: 43mm;
}

#T100x150 .trackingNo-small > img {
  height: 17mm;
  width: 95mm;
  margin: 1mm 0;
}

#T100x150 .receiver-small {
  display: flex;
  width: 50%;
}

#T100x150 .receiver-small .title {
  width: 10mm;
  text-align: center;
}

#T100x150 .receiver-small .content {
  height: 11mm;
  width: 86mm;
  font-size: 8pt;
}

#T100x150 .sender-small {
  display: flex;
  width: 50%;
}

#T100x150 .sender-small .title {
  width: 10mm;
  text-align: center;
}

#T100x150 .sender-small .content {
  height: 11mm;
  width: 86mm;
  font-size: 8pt;
}

#T100x150 .pickup_notes {
  display: flex;
  height: 16mm;
}

#T100x150 .pickup_notes .remark {
  padding: 0 1mm;
  width: 10mm;
}

#T100x150 .pickup_notes div {
  width: 96mm;
  font-size: 8pt;
}

#T100x150 .footer > span {
  height: 7mm;
  width: 68mm;
  font-size: 8pt;
  padding-left: 1mm;
}
