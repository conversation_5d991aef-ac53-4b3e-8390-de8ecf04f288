import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  LinkButton,
  DayRangePicker,
  ArrSelect,
  ActionsWrap,
  actionConfirm,
  useReloadTableList,
  getPrice
  // actionConfirm
} from 'parsec-admin';
import useApi, {
  // payChannelObj,
  // payMethodObj,
  // orderTypeObj,
  // orderStatusObjOnline,
  MedicineListItem
  // invoiceStatusObj
} from '../api';
import MyTableList from '@components/myTableList';
import permisstion from '@utils/permisstion';
import { Button, Modal, Space } from 'antd';
import Qrcode from '@components/qrcode';
import WaterMarkWrap from '@src/components/waterMarkWrap';
import env from '@src/configs/env';
import { isLocal as isLocalHost } from '@src/utils/common';
import { print } from '@src/utils/tools';
import PrintEmsDetail from './print-ems-detail';
import { useHistory } from 'react-router';
import Organization from '@components/organization';

const statusObj = [
  {
    label: '待支付',
    value: 'UNPAY'
  },
  {
    label: '部分付款',
    value: 'PART_PAYED'
  },
  {
    label: '处方已付款未取药',
    value: 'PAYED'
  },
  {
    label: '待发货',
    value: 'WAIT_DELIVERY'
  },
  {
    label: '发货中',
    value: 'IN_DELIVERY'
  },
  {
    label: '已完成',
    value: 'FINISH'
  },
  {
    label: '付款超时',
    value: 'TIMEOUT'
  },
  {
    label: '已取消',
    value: 'CANCEL'
  },
  {
    label: '异常订单',
    value: 'EXCEPTION'
  },
  {
    label: '支付失败',
    value: 'FAIL'
  },
  {
    label: '已退款',
    value: 'REFUND'
  }
];

const businessSourceEnum = {
  PRESCRIPTION: '医嘱开方',
  QUICK_BUY_DRUG: '便捷购药'
};

export default ({ isLocal = false }: { isLocal?: boolean }) => {
  const paramsRef = useRef<any>();
  const history = useHistory();
  const [printModalShow, setPrintModalShow] = useState(false);
  const [showUrl, setShowUrl] = useState<string>('');
  const [selectRecord, setSelectRecord] = useState<MedicineListItem>();
  const reloadTableList = useReloadTableList();

  const { request: reqPrint, loading: printLoading } = useApi.更改处方打印状态({
    needInit: false
  });
  // const { data: statusData } = useApi.查询处方状态枚举({
  //   needInit: true
  // });
  // const statusObj = useMemo(() => {
  //   if (statusData.data?.prescriptionStatus) {
  //     return (statusData.data?.prescriptionStatus || []).map((item: any) => {
  //       return {
  //         label: item.desc,
  //         value: item.name
  //       };
  //     }, {});
  //   } else {
  //     return [];
  //   }
  // }, [statusData.data?.prescriptionStatus]);
  const [targetHisId, setTargetHisId] = useState<any>(env.hisId);
  const handlePrint = useCallback(
    (domId: string) => {
      Modal.confirm({
        title: '提示',
        content: '请确认是否打印EMS单号',
        onOk() {
          reqPrint({ id: selectRecord?.id }).then(() => {
            reloadTableList();
            const dom: HTMLElement | null = document.querySelector(domId);
            const type = domId === '#T100x180' ? 'ems1' : 'ems2';
            if (dom) {
              print(dom, 'EMS邮寄', type);
            }
          });
        }
      });
    },
    [selectRecord, reqPrint, reloadTableList]
  );
  return (
    <>
      <WaterMarkWrap>
        <MyTableList
          tableTitle='处方药品'
          scroll={{ x: 2000 }}
          pageHeaderProps={false}
          showHeaderExtra={isLocal}
          params={{ targetHisId }}
          getList={({ params }) => {
            const p = {
              ...params
              // refundStatus: '1'
            };
            paramsRef.current = p;
            return isLocal
              ? useApi.本地处方药品列表.request(p)
              : useApi.处方药品列表new.request(p);
          }}
          action={
            <>
              {isLocal && (
                <Organization
                  value={targetHisId}
                  headerExtraText={'处方'}
                  onChange={setTargetHisId}
                />
              )}
              <Button
                onClick={() => {
                  Modal.confirm({
                    title: '确认导出?',
                    content: '当前导出信息，请勿非法传阅',
                    onOk: () => {
                      isLocal
                        ? useApi.export本地处方订单列表(paramsRef.current)
                        : useApi.export处方订单列表(paramsRef.current);
                    }
                  });
                }}>
                导出EXCEL
              </Button>
            </>
          }
          columns={useMemo(
            () =>
              [
                {
                  title: '下单时间',
                  width: 180,
                  dataIndex: 'createTime'
                },
                // {
                //   title: '平台单号',
                //   width: 180,
                //   dataIndex: 'psOrdNum'
                // },
                {
                  title: '开单科室',
                  width: 120,
                  dataIndex: 'deptName'
                },
                {
                  title: '开单医生',
                  width: 120,
                  dataIndex: 'doctorName'
                },
                {
                  title: '就诊人姓名',
                  width: 180,
                  dataIndex: 'patientName'
                },
                // {
                //   title: '业务类型',
                //   width: 120,
                //   dataIndex: 'bizeType'
                // },
                // {
                //   title: '处方类型',
                //   width: 120,
                //   dataIndex: 'prescriptionType', //处方类别（1西药，3中药饮片）
                //   render: v =>
                //     v === 1 ? '西医处方' : v === 3 ? '中医处方' : ''
                // },
                {
                  title: '平台来源',
                  dataIndex: 'institutionName',
                  width: 150,
                  hidden: !isLocal
                },
                // {
                //   title: '支付渠道',
                //   width: 120,
                //   dataIndex: 'payChannelStr'
                // },
                // {
                //   title: '支付方式',
                //   width: 120,
                //   dataIndex: 'payMethodStr'
                // },
                {
                  title: '订单金额（元）',
                  width: 180,
                  dataIndex: 'totalFee',
                  render: v => '￥' + (v / 100).toFixed(2)
                },
                {
                  title: '自费金额(元)',
                  width: 120,
                  dataIndex: 'ownPayAmt',
                  render: v => `￥${getPrice(v, 2, true)}`
                },
                {
                  title: '医保金额(元)',
                  width: 120,
                  dataIndex: 'medicalInsuranceAmount',
                  render: v => `￥${getPrice(v, 2, true)}`
                },
                {
                  title: '处方类型',
                  width: 120,
                  dataIndex: 'businessSourceEnum',
                  render: v => (v ? businessSourceEnum[v] : '-'),
                  search: <ArrSelect options={businessSourceEnum} />
                },
                {
                  title: '订单状态',
                  width: 180,
                  dataIndex: 'status',
                  searchIndex: 'status',
                  search: <ArrSelect options={statusObj} />,
                  render: v => {
                    return (
                      <Space>
                        {statusObj.find(item => item.value === v)?.label}
                      </Space>
                    );
                  }
                },
                // {
                //   title: '处方状态',
                //   width: 180,
                //   dataIndex: 'statusName'
                //   // searchIndex: 'status',
                //   // search: <ArrSelect options={statusObj} />
                // },
                // {
                //   title: '发票状态',
                //   width: 180,
                //   dataIndex: 'invoice',
                //   render: v => (v?.state ? invoiceStatusObj[v.state] : '-')
                // },
                {
                  title: '查询时间',
                  dataIndex: 'operatorTime',
                  search: (
                    <DayRangePicker
                      placeholder={['开始时间', '结束时间']}
                      valueFormat={'YYYY-MM-DD HH:mm:ss'}
                      disabledDate={current => {
                        return current && current.valueOf() > Date.now();
                      }}
                    />
                  ),
                  searchIndex: ['orderStartTime', 'orderEndTime'],
                  render: false
                },
                {
                  title: isLocal ? '办理渠道' : '支付渠道',
                  dataIndex: 'payChannel',
                  // search: <ArrSelect options={payChannelObj} />,
                  render: false
                },
                {
                  title: '支付方式',
                  dataIndex: 'payMethod',
                  // search: !isLocal && <ArrSelect options={payMethodObj} />,
                  render: false
                },
                {
                  title: '业务类型',
                  dataIndex: 'businessType',
                  // search: !isLocal && <ArrSelect options={orderTypeObj} />,
                  render: false
                },
                // {
                //   title: '订单状态',
                //   dataIndex: 'status',
                //   // searchIndex: 'orderStatus',
                //   // search: !isLocal && (
                //   //   <ArrSelect options={orderStatusObjOnline} />
                //   // ),
                //   render: false
                // },
                {
                  title: '平台单号',
                  dataIndex: 'psOrdNum',
                  search: !isLocal && true,
                  render: false
                },
                {
                  title: '开单科室',
                  dataIndex: 'deptName',
                  search: !isLocal && true,
                  render: false
                },
                {
                  title: '开单医生',
                  dataIndex: 'doctorName',
                  search: !isLocal && true,
                  render: false
                },
                {
                  title: '就诊人姓名',
                  dataIndex: 'patientName',
                  search: !isLocal && true,
                  render: false
                },
                {
                  title: '交易单号',
                  dataIndex: 'tradeNo',
                  // search: !isLocal && true,
                  render: false
                },
                // {
                //   title: '退款流水号',
                //   dataIndex: 'refundOrderNo',
                //   search: true,
                //   render: false
                // },
                // {
                //   title: '收货人',
                //   dataIndex: 'contactName',
                //   search: true,
                //   render: false
                // },
                // {
                //   title: '联系电话',
                //   dataIndex: 'contactPhone',
                //   search: true,
                //   render: false
                // },
                // {
                //   title: '运单号',
                //   dataIndex: 'billNo',
                //   search: true,
                //   render: false
                // },
                {
                  title: '操作',
                  fixed: 'right',
                  width: 180,
                  render: record => {
                    /** 只有宏仁医院有这个功能，处于邮寄中 */
                    let canSend =
                      ['WAIT_DELIVERY'].includes(record.status) &&
                      env.hisId === '8900';
                    let canPrint =
                      ['IN_DELIVERY'].includes(record.status) &&
                      env.hisId === '8900';

                    /** 用于监管上报的派药 */
                    let canSendDrug =
                      ['PAYED'].includes(record.status) &&
                      ['10001', '10002'].includes(env.hisId);
                    // 调试代码
                    if (isLocalHost()) {
                      canSend = true;
                      canPrint = true;
                      canSendDrug = true;
                    }
                    return isLocal ? (
                      <LinkButton
                        onClick={() => {
                          record.id &&
                            history.push(
                              `/orderLocal/medicineDetail/${record.id}/${record?.hisId}/${record.targetHisId}`
                            );
                        }}>
                        查看
                      </LinkButton>
                    ) : (
                      <ActionsWrap>
                        {permisstion.canPrescDetail && (
                          <Space>
                            <LinkButton
                              onClick={() => {
                                record.id &&
                                  history.push(
                                    '/order/medicineDetail/' + record.id
                                  );
                              }}>
                              查看
                            </LinkButton>
                            {canSend && (
                              <LinkButton
                                onClick={() => {
                                  actionConfirm(
                                    () =>
                                      useApi.处方单发起邮寄.request({
                                        id: record?.id
                                      }),
                                    '发货'
                                  );
                                }}>
                                发货
                              </LinkButton>
                            )}
                            {canPrint && (
                              <LinkButton
                                onClick={() => {
                                  setPrintModalShow(true);
                                  setSelectRecord(record);
                                }}>
                                {record.printTag === 1 ? '补打' : '打印'}
                              </LinkButton>
                            )}
                            {canSendDrug && (
                              <LinkButton
                                onClick={() => {
                                  setShowUrl(
                                    `https://ihs.cqkqinfo.com/patients/p${env.hisId}/#/pages/supervision/prescription?prescriptionId=${record.id}`
                                  );
                                }}>
                                派药
                              </LinkButton>
                            )}
                          </Space>
                        )}
                      </ActionsWrap>
                    );
                  }
                }
              ].filter(item => !item?.hidden) as any[],
            [history, isLocal]
          )}
        />
      </WaterMarkWrap>
      <Modal
        title='打印EMS快递面单'
        width={900}
        visible={printModalShow && !!selectRecord?.id}
        footer={
          <Space>
            <Button
              loading={printLoading}
              type='primary'
              onClick={() => handlePrint('#T100x180')}>
              打印100*180
            </Button>
            <Button
              loading={printLoading}
              type='primary'
              onClick={() => handlePrint('#T100x150')}>
              打印100*150
            </Button>
          </Space>
        }
        destroyOnClose
        onCancel={() => setPrintModalShow(false)}>
        <PrintEmsDetail id={selectRecord?.id} />
      </Modal>
      <Modal
        title='请扫描二维码'
        destroyOnClose
        bodyStyle={{ display: 'flex', justifyContent: 'center' }}
        visible={!!showUrl}
        onOk={() => setShowUrl('')}
        onCancel={() => setShowUrl('')}>
        <Qrcode url={showUrl} size={220} />
      </Modal>
    </>
  );
};
