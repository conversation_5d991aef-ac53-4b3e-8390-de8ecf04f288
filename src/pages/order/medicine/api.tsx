import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ApiResponse } from '@src/configs/apis';

export default {
  快递面单数据: createApiHooks(
    (params: { id?: number; deliveryCompany?: 'EMS' | 'SF' | 'JD' }) =>
      request.get<ApiResponse<MailDetail>>(
        '/mch/prescription/prescription/express-print-data',
        { params }
      )
  )
};

export type MailDetail = {
  sender: '@cname'; //寄件人
  senderPhone: '187@integer(*********,99999999)'; //寄件人手机号
  senderProv: '@province'; //寄件人所在省份/直辖市
  senderCity: '@city'; //寄件人所在市
  senderCounty: '@county'; //寄件人所在区县
  senderAddress: '@ctitle()街道@integer(100,999)号'; //寄件人详细地址
  waybillNo: '11@integer(100000,9999999)'; //运单号
  baseProductNo: '1'; //基础产品代码，1：标准快递 2：快递包裹 3：代收/到付（标准快递）
  cargo: '药品'; //附件信息
  routeCode: string;
  cargoCount: '@integer(1,5)'; //件数
  receiver: '@cname'; //收件人
  receiverPhone: '187@integer(*********,99999999)'; //收件人手机号
  receiverProv: '@province'; //收件人所在省份/直辖市
  receiverCity: '@city'; //收件人所在市
  receiverCounty: '@county'; //收件人所在区县
  receiverAddress: '@ctitle()街道@integer(100,999)号'; //收件人详细地址
};
