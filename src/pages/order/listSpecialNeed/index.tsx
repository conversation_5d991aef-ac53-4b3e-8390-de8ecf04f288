import { useMemo, useState } from 'react';
import {
  LinkButton,
  ArrSelect,
  ActionsWrap,
  DayRangePicker,
  actionConfirm
} from 'parsec-admin';
import useApi from '../api';
import { saveAs } from 'file-saver';
import { Button } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import MyTableList from '@components/myTableList';
import moment from 'moment';
import dayjs from 'dayjs';
import permisstion from '@utils/permisstion';
export default () => {
  const {
    data: { data: deptList }
  } = useApi.特需科室列表({
    needInit: true
  });
  console.log('deptList', deptList);
  const now = useMemo(() => moment(), []);
  const [queryParams, setQueryParams] = useState({} as any);
  const { request: handleExport, loading: exportLoading } = useApi.导出特需订单(
    {
      needInit: false
    }
  );
  return (
    <MyTableList
      tableTitle='
      会员预约列表'
      action={
        <Button
          type={'default'}
          loading={exportLoading}
          icon={<ExportOutlined />}
          onClick={() =>
            handleExport({ ...queryParams }).then(data =>
              saveAs(
                data,
                `订单列表 ${moment().format('YYYY-MM-DD HH时mm分ss秒')}.xls`
              )
            )
          }>
          导出
        </Button>
      }
      getList={({ params }) => {
        const p = {
          ...params
          // uniqueCode: params.uniqueCode || '10'
          // businessType: '1'
          // refundStatus: '1'
        };
        setQueryParams({
          ...p,
          page: 1,
          limit: 999
        });
        return useApi.特需订单列表.request(p);
      }}
      columns={useMemo(
        () => [
          {
            title: '患者姓名',
            width: 180,
            dataIndex: 'patientName',
            searchIndex: 'name',
            search: true
          },
          {
            title: '证件号',
            width: 180,
            dataIndex: 'patientIdNo'
          },
          {
            title: '手机号',
            width: 180,
            dataIndex: 'patientMobile',
            searchIndex: 'phone',
            search: true
          },
          {
            title: '挂号科室',
            width: 180,
            dataIndex: 'deptName',
            searchIndex: 'deptId',
            search: (
              <ArrSelect
                options={(deptList || []).map((item: any) => {
                  return {
                    value: item.no,
                    children: item.name
                  };
                })}
              />
            )
          },
          {
            title: '挂号医生',
            width: 180,
            dataIndex: 'doctorName'
          },
          {
            title: '挂号时间',
            dataIndex: 'createTime',
            width: 180,
            searchIndex: ['regDateStart', 'regDateEnd'],
            search: (
              <DayRangePicker
                valueFormat={'YYYY-MM-DD'}
                disabledDate={current => {
                  return current && current.valueOf() > now.valueOf();
                }}
              />
            ),
            render: v => {
              return v ? dayjs(v).format('YYYY-MM-DD HH:mm:ss') : '-';
            }
          },
          {
            title: '预约时间',
            dataIndex: 'visitDate',
            width: 180,
            searchIndex: ['appointmentDateStart', 'appointmentDateEnd'],
            search: <DayRangePicker valueFormat={'YYYY-MM-DD'} />,
            render: v => {
              return v ? dayjs(v).format('YYYY-MM-DD HH:mm:ss') : '-';
            }
          },
          {
            title: '操作',
            fixed: 'right',
            width: 150,
            render: record =>
              permisstion.canEditVipReg ? (
                <ActionsWrap>
                  {!record.isHandle && (
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () => useApi.标记特需记录.request({ id: record.id }),
                          '处理'
                        );
                      }}>
                      处理
                    </LinkButton>
                  )}
                </ActionsWrap>
              ) : (
                <></>
              )
          }
        ],
        [deptList, now]
      )}
    />
  );
};
