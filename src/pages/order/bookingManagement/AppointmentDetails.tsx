import React, { useEffect } from 'react';
import { CardLayout, FormDescriptions, Form } from 'parsec-admin';
import styled from 'styled-components';
import useApi from '../api';
import { Steps } from 'antd';
import { useParams } from 'react-router-dom';
import ConfigStore from '@src/store/ConfigStore';
const editSate = false;
const { Step } = Steps;
export default () => {
  const { id } = useParams<any>();
  const { data: datas, loading: detailLoading } = useApi.查询住院预约详情({
    params: { id }
  });
  const {
    hospitalizationEnum,
    getHospitalizationEnum
  } = ConfigStore.useContainer();
  useEffect(() => {
    if (!hospitalizationEnum) {
      getHospitalizationEnum();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const [form] = Form.useForm();

  return (
    <Wrapper edit={false}>
      <CardLayout title={'预约阶段'} loading={detailLoading}>
        <Steps
          current={
            datas?.data?.admissionTime
              ? 4
              : datas?.data?.confirmTime
              ? 3
              : datas?.data?.applyTime
              ? 2
              : datas?.data?.patName && datas?.data?.applyAdmissionTime
              ? 1
              : 0
          }>
          <Step title='医生开单' />
          <Step title='患者登记' />
          <Step title='入院确认' />
          <Step title='预约成功' />
        </Steps>
        <div className='space'>
          <div className='content'>已开单</div>
          <div className='content1'>已登记</div>
          <div className='content1'>已确认</div>
          <div className='content2'>入院办理</div>
        </div>
        <div className='space'>
          <div className='content3'>
            {datas?.data?.patName && datas?.data?.applyAdmissionTime
              ? datas?.data?.patName + datas?.data?.applyAdmissionTime
              : ''}
          </div>
          <div className='content4'>
            {datas?.data?.applyTime && '登记日期' + datas?.data?.applyTime}
          </div>
          <div className='content4'>
            {datas?.data?.confirmTime && '确认日期' + datas?.data?.confirmTime}
          </div>
          <div className='content4'>
            {datas?.data?.admissionTime &&
              '办理时间' + datas?.data?.admissionTime}
          </div>
        </div>
      </CardLayout>
      <CardLayout title={'患者信息'} loading={detailLoading}>
        <FormDescriptions
          data={datas?.data}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '姓名',
              name: 'patName'
            },
            {
              label: '性别',
              name: 'patSex',
              render: v => {
                if (v && hospitalizationEnum?.PatSexEnum?.length) {
                  return (
                    hospitalizationEnum.PatSexEnum?.find(
                      item => item.name === v
                    )?.desc || '-'
                  );
                }
                return '-';
              }
            },
            {
              label: '年龄',
              name: 'patientAge'
            },
            {
              label: '国籍',
              name: 'patCountryName'
            },
            {
              label: '职业',
              name: 'patOccupationName'
            },
            {
              label: '学历',
              name: 'patEducationalName'
            },
            {
              label: '婚姻状况',
              name: 'patMarriageName'
            },
            {
              label: '就诊卡号',
              name: 'patPhone'
            },
            {
              label: '联系电话',
              name: 'patPhone'
            },
            {
              label: '就诊科室',
              name: 'admissionDeptName'
            },
            {
              label: '就诊医生',
              name: 'doctorName'
            },
            {
              label: '就诊时间',
              name: 'applyAdmissionTime'
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'联系人信息'} loading={detailLoading}>
        <FormDescriptions
          data={datas?.data?.patient}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '联系人',
              name: 'contactName'
            },
            {
              label: '与患者关系',
              name: 'contactRelation',
              render: v => {
                if (v && hospitalizationEnum?.ContactRelationEnum?.length) {
                  return (
                    hospitalizationEnum.ContactRelationEnum?.find(
                      item => item.code === Number(v)
                    )?.desc || '-'
                  );
                }
                return '-';
              }
            },
            {
              label: '联系电话',
              name: 'contactPhone'
            },
            {
              label: '证件类型',
              name: 'contactIdType',
              render: v => {
                if (v && hospitalizationEnum?.IdTypeEnum?.length) {
                  return (
                    hospitalizationEnum.IdTypeEnum?.find(
                      item => item.code === Number(v)
                    )?.desc || '-'
                  );
                }
                return '-';
              }
            },
            {
              label: '证件号码',
              name: 'contactIdNo'
            },
            {
              label: '联系地址',
              name: 'patLiveAddress'
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'预约信息'} loading={detailLoading}>
        <FormDescriptions
          data={datas?.data}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '主要诊断',
              name: 'mainDiagnosis',
              render: v => {
                if (v?.length) {
                  return v.map(item => item?.name)?.join(',');
                }
                return '-';
              }
            },
            {
              label: '入院病况',
              name: 'admissionCondition'
            },
            {
              label: '入院方式',
              name: 'admissionMethod'
            },
            {
              label: '院      区',
              name: 'admissionDistrictName'
            },
            {
              label: '入院科室',
              name: 'admissionDeptName'
            },
            {
              label: '入院日期',
              name: 'applyAdmissionTime'
            },
            {
              label: '办理地点',
              name: 'patientWeight'
            },
            {
              label: '开单医生',
              name: 'doctorName'
            },
            {
              label: '开单日期',
              name: 'createTime'
            },
            {
              label: '预约状态',
              name: 'state',
              render: v => {
                if (v && hospitalizationEnum?.AdmissionApplyStateEnum?.length) {
                  return (
                    hospitalizationEnum.AdmissionApplyStateEnum?.find(
                      item => item.name === v
                    )?.desc || '-'
                  );
                }
                return '-';
              }
            },
            {
              label: '备注',
              name: 'doctorRemark'
            }
          ]}
        />
      </CardLayout>
    </Wrapper>
  );
};
const Wrapper = styled.div<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
  .btns {
    margin-top: 30px;
  }
  .space {
    display: flex;
  }
  .content {
    font-size: 15px;
    margin-top: 50px;
    margin-left: 40px;
    width: 100px;
  }
  .content1 {
    font-size: 15px;
    margin-top: 50px;
    margin-left: 285px;
    width: 100px;
  }
  .content2 {
    font-size: 15px;
    margin-top: 50px;
    margin-left: 265px;
    width: 130px;
  }
  .content3 {
    font-size: 12px;
    margin-top: 5px;
    margin-left: 30px;
    width: 200px;
  }
  .content4 {
    font-size: 12px;
    margin-top: 5px;
    margin-left: 140px;
    width: 200px;
  }
  .bold-price {
    font-size: 20px;
    font-weight: bold;
  }
  .ant-table {
    > .ant-table-container {
      > .ant-table-content {
        > table {
          > tbody {
            > tr {
              > td {
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
`;
