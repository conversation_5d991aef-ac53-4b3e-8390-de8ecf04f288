import React, { useEffect, useMemo, useRef } from 'react';
import {
  LinkButton,
  DayRangePicker,
  ArrSelect,
  RouteComponentProps
} from 'parsec-admin';
import useApi from '../api';
import MyTableList from '@components/myTableList';
import ConfigStore from '@src/store/ConfigStore';
import { Button } from 'antd';

export default ({ history }: RouteComponentProps) => {
  const searchRef = useRef<any>();
  const {
    hospitalizationEnum,
    getHospitalizationEnum
  } = ConfigStore.useContainer();
  useEffect(() => {
    if (!hospitalizationEnum) {
      getHospitalizationEnum();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <MyTableList
      tableTitle='住院预约列表'
      getList={({ params }) => {
        searchRef.current = { ...params };
        return useApi.分页查询住院预约列表.request(params as any);
      }}
      action={
        <Button
          onClick={() => useApi.exportHospitalizationList(searchRef.current)}>
          导出EXCEL
        </Button>
      }
      columns={
        useMemo(
          () => [
            {
              title: '就诊人',
              searchIndex: 'patName',
              search: true
            },
            {
              title: '就诊人 | ID',
              width: 180,
              dataIndex: 'patName',
              render: (v, record: any) => {
                return `${v}|${record?.patientId || '-'}`;
              }
            },
            {
              title: '联系电话',
              width: 180,
              dataIndex: 'patPhone'
            },
            {
              title: '主要诊断',
              width: 180,
              dataIndex: 'mainDiagnosis',
              render: v => {
                if (v?.length) {
                  return v.map(item => item?.name)?.join(',');
                }
                return '-';
              }
            },
            {
              title: '入院病情',
              width: 180,
              dataIndex: 'admissionCondition'
            },
            {
              title: '入院科室',
              width: 180,
              dataIndex: 'admissionDeptName'
            },

            {
              title: '入院时间',
              width: 140,
              dataIndex: 'applyAdmissionTime',
              search: (
                <DayRangePicker
                  placeholder={['开始时间', '结束时间']}
                  disabledDate={current => {
                    return current && current.valueOf() > Date.now();
                  }}
                />
              ),
              searchIndex: ['searchStartTime', 'searchEndTime']
            },
            {
              title: '状态',
              width: 120,
              dataIndex: 'state',
              search: (
                <ArrSelect
                  options={(
                    hospitalizationEnum?.AdmissionApplyStateEnum || []
                  ).map(item => {
                    return { label: item.desc, value: item.name };
                  })}
                />
              ),
              render: v => {
                if (v && hospitalizationEnum?.AdmissionApplyStateEnum?.length) {
                  return (
                    hospitalizationEnum.AdmissionApplyStateEnum?.find(
                      item => item.name === v
                    )?.desc || '-'
                  );
                }
                return '-';
              }
            },
            {
              title: '开单医师 | 开单科室',
              width: 180,
              dataIndex: 'doctorName',
              render: (v, record: any) => {
                return `${v}|${record.applyDeptName}`;
              }
            },
            {
              title: '开单时间  ',
              width: 180,
              dataIndex: 'createTime'
            },
            {
              title: '操作',
              fixed: 'right',
              width: 100,
              render: record => (
                <LinkButton
                  onClick={() => {
                    history.push('/order/AppointmentDetails/' + record.id);
                  }}>
                  查看
                </LinkButton>
              )
            }
          ],
          [history, hospitalizationEnum]
        ) as any
      }
    />
  );
};
