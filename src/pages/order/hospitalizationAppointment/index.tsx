import React, { useMemo, useState } from 'react';
import {
  LinkButton,
  DayRangePicker,
  ArrSelect,
  ActionsWrap,
  actionConfirm,
  useModal,
  FormDescriptions,
  handleSubmit
} from 'parsec-admin';
import useApi from '../api';
import MyTableList from '@components/myTableList';
import { Button, Input, Modal, Spin } from 'antd';
import moment from 'moment';
import getSex from '@utils/getSex';
import styled from 'styled-components';
import permisstion from '@utils/permisstion';
import env from '@configs/env';
import dayjs from 'dayjs';
const status = {
  SUCCESS: '成功',
  CANCEL: '取消',
  WAITE: '待审核'
};
const switchIdType = type => {
  switch (type) {
    case '1':
      return '身份证';
    case '2':
      return '港澳居民身份证';
    case '3':
      return '台湾居民身份证';
    case '4':
      return '护照';
    default:
      return '-';
  }
};
export default () => {
  const {
    request,
    loading,
    data: { data },
    error
  } = useApi.查询住院预约记录详情({
    needInit: false,
    initValue: { data: {} }
  });
  const [visible, setVisible] = useState(false);
  const detailData: { [key: string]: any } = useMemo(() => {
    if (error) {
      return {};
    }
    if (Object.keys(data || {})?.length) {
      return { ...data, ...data?.patientInfo };
    }
    return {};
  }, [data, error]);
  const {
    request: successRequest,
    loading: successLoading
  } = useApi.住院预约记录同意({
    needInit: false
  });
  const {
    request: cancelRequest,
    loading: cancelLoading
  } = useApi.住院预约记录取消({
    needInit: false
  });
  const cancelVisible = useModal(({ id }) => {
    return {
      title: '取消预约',
      onSubmit: values =>
        handleSubmit(() =>
          cancelRequest({
            ...values,
            id,
            cancelDate: moment().format('YYYY-MM-DD HH:mm:ss')
          }).then(() => {
            setVisible(false);
          })
        ),
      items: [
        {
          label: '',
          name: 'cancelReason',
          render: <Input.TextArea placeholder={'请填写取消原因'} />,
          formItemProps: {
            required: true,
            requiredMark: false,
            wrapperCol: {
              offset: 1
            }
          }
        }
      ]
    };
  });
  const success = id => {
    actionConfirm(
      () =>
        successRequest({
          id
        }),
      '确认预约'
    );
  };
  return (
    <>
      <MyTableList
        tableTitle='住院预约记录'
        getList={({ params }) => {
          delete params.sort;
          return useApi.查询住院预约记录列表.request({
            ...params,
            platformId: env.hisId + '01'
          });
        }}
        showExpand={false}
        columns={[
          {
            title: '序号',
            width: 80,
            render: (_, v, index) => {
              return index + 1;
            }
          },
          {
            title: '申请时间',
            width: 180,
            dataIndex: 'createTime'
          },
          {
            title: '预约入院日期',
            width: 180,
            dataIndex: 'regDate',
            render: v => dayjs(v).format('YYYY-MM-DD')
          },
          {
            title: '预约科室',
            width: 180,
            dataIndex: 'deptName'
          },
          {
            title: '就诊人姓名',
            width: 140,
            dataIndex: 'patName'
          },
          {
            title: '患者ID',
            width: 120,
            dataIndex: 'patCardNo'
          },
          {
            title: '联系电话',
            width: 180,
            dataIndex: 'phone'
          },

          {
            title: '预约状态',
            width: 120,
            dataIndex: 'status',
            search: <ArrSelect options={status} />,
            render: v => status[v] || '-'
          },
          {
            title: '预约时间',
            width: 180,
            hidden: true,
            search: (
              <DayRangePicker
                valueFormat={'YYYY-MM-DD HH:mm:ss'}
                placeholder={['开始时间', '结束时间']}
              />
            ),
            searchIndex: ['startDate', 'endDate']
          },
          {
            title: '姓名',
            search: <Input placeholder={'请输入姓名或就诊卡号'} />,
            searchIndex: 'queryInfo',
            hidden: true
          },
          {
            title: '操作',
            fixed: 'right',
            width: 150,
            render: record => (
              <ActionsWrap>
                {permisstion.hospitalizationAppointmentCheck && (
                  <LinkButton
                    onClick={() => {
                      request({ id: record.id, platformId: env.hisId + '01' });
                      setVisible(true);
                    }}>
                    查看
                  </LinkButton>
                )}
                {record?.status === 'WAITE' && (
                  <ActionsWrap>
                    {permisstion.hospitalizationAppointmentCancel && (
                      <LinkButton
                        onClick={() => {
                          cancelVisible({ id: record.id });
                        }}>
                        取消预约
                      </LinkButton>
                    )}
                    {permisstion.hospitalizationAppointmentSuccess && (
                      <LinkButton
                        onClick={() => {
                          success(record.id);
                        }}>
                        确认预约
                      </LinkButton>
                    )}
                  </ActionsWrap>
                )}
              </ActionsWrap>
            )
          }
        ]}
      />
      <Modal
        title={'住院预约详情'}
        width={1200}
        visible={visible}
        footer={null}
        onCancel={() => setVisible(false)}>
        <Spin spinning={loading}>
          <h3>患者信息</h3>
          <FormDescriptions
            edit={false}
            data={detailData}
            items={[
              {
                label: '姓名',
                name: 'patientName'
              },
              {
                label: '性别',
                name: 'patientSex',
                render: v => getSex(v || '')
              },
              {
                label: '年龄',
                name: 'patientAge'
              },
              {
                label: '患者ID',
                name: 'patHisNo'
              },
              {
                label: '手机号码',
                name: 'patientMobile'
              },
              {
                label: '身份证号',
                name: 'idNo'
              },
              {
                label: '紧急联系人',
                name: 'emergencyContact'
              },
              {
                label: '联系人证件类别',
                name: 'documentType',
                render: v => switchIdType(v)
              },
              {
                label: '联系人证件号',
                name: 'certificateNo'
              },
              {
                label: '患者户籍地',
                name: 'residenceAddress'
              },
              {
                label: '联系人电话',
                name: 'phone'
              }
            ]}
          />
          <h3>预约信息</h3>
          <FormDescriptions
            edit={false}
            data={detailData}
            items={useMemo(() => {
              const { deptName } = detailData;
              let arr: {
                label: string;
                name?: string;
                render?: (v: any) => string;
              }[] = [
                {
                  label: '预约院区',
                  render: () => '沙区妇幼保健院'
                },
                {
                  label: '预约科室',
                  name: 'deptName'
                },
                {
                  label: '病情分级',
                  name: 'diseaseLevel',
                  render: v => (v === '0' ? '一般' : v === '1' ? '严重' : '')
                },
                {
                  label: '预约入院日期',
                  name: 'regDate',
                  render: v => dayjs(v).format('YYYY-MM-DD')
                },
                {
                  label: '确认入院日期',
                  name: 'regDate',
                  render: v => dayjs(v).format('YYYY-MM-DD')
                },
                {
                  label: '申请时间',
                  name: 'createTime'
                }
              ];
              if (deptName === '妇科病房') {
                arr = [
                  ...arr,
                  {
                    label: '症状',
                    name: 'symptom'
                  },
                  {
                    label: '辅助检查结果',
                    name: 'examResult'
                  }
                ];
              }
              if (deptName === '产科病房') {
                arr = [
                  ...arr,
                  {
                    label: '预产期',
                    name: 'expectedDate'
                  },
                  {
                    label: '是否我院产检',
                    name: 'examInOurHos',
                    render: v => (v === '0' ? '否' : v === '1' ? '是' : '-')
                  },
                  {
                    label: '高危因素',
                    name: 'isHighRisk',
                    render: v => (v === 'YES' ? '有' : v === 'NO' ? '无' : '-')
                  },
                  {
                    label: '高危因素详情',
                    name: 'highRisk'
                  }
                ];
              }
              return arr;
            }, [detailData])}
          />
          <ButtonBox>
            {useMemo(() => {
              const { id, status } = detailData;
              if (!id || status !== 'WAITE') return null;
              return (
                <>
                  <Button
                    loading={successLoading}
                    onClick={() => {
                      success(id);
                    }}
                    type={'primary'}>
                    确认预约
                  </Button>
                  <Button
                    onClick={() => {
                      cancelVisible({ id });
                    }}
                    loading={cancelLoading}>
                    取消预约
                  </Button>
                </>
              );
              // eslint-disable-next-line react-hooks/exhaustive-deps
            }, [cancelLoading, cancelVisible, detailData, successLoading])}
            <Button
              style={{ borderColor: 'black', color: 'black' }}
              onClick={() => setVisible(false)}>
              返回
            </Button>
          </ButtonBox>
        </Spin>
      </Modal>
    </>
  );
};
const ButtonBox = styled.div`
  display: flex;
  margin-top: 24px;
  > button {
    margin-right: 24px;
  }
`;
