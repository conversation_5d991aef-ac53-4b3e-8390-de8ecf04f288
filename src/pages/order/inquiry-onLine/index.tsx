import React, { useMemo, useRef, Fragment, useState } from 'react';
import {
  LinkButton,
  getPrice,
  DayRangePicker,
  ArrSelect,
  actionConfirm,
  ActionsWrap
} from 'parsec-admin';
import useApi, {
  payChannelObj,
  payMethodObj,
  orderTypeObj,
  orderStatusObjOnline,
  invoiceStatusObj,
  purposeTypeForListObj
} from '../api';
import MyTableList from '@components/myTableList';
import permisstion from '@utils/permisstion';
import { Button, Modal, DatePicker } from 'antd';
import { RangeValue } from 'rc-picker/lib/interface';
import moment from 'moment';
import Organization from '@components/organization';
import styled from 'styled-components';
import env from '@configs/env';

import WaterMarkWrap from '@src/components/waterMarkWrap';
import { useHistory } from 'react-router';
export default ({ isLocal = false }: { isLocal?: boolean }) => {
  const history = useHistory();
  const paramsRef = useRef<any>();
  const [isShowExport, setIsShowExport] = useState(false);
  const [exportDate, setExportDate] = useState<RangeValue<moment.Moment>>();
  const hisId = env.hisId;
  const [targetHisId, setTargetHisId] = useState<any>(env.hisId);

  return (
    <Fragment>
      <WaterMarkWrap>
        <MyTableList
          tableTitle='在线问诊'
          pageHeaderProps={false}
          params={{ targetHisId }}
          showHeaderExtra={isLocal}
          getList={({ params }) => {
            const p: any = {
              ...params,
              businessType: '1'
              // refundStatus: '1'
            };
            paramsRef.current = p;
            return isLocal
              ? useApi.本地在线问诊列表.request(p)
              : useApi.在线问诊列表.request(p);
          }}
          action={
            <>
              {isLocal ? (
                <Organization
                  value={targetHisId}
                  onChange={setTargetHisId}
                  headerExtraText={'数据'}
                />
              ) : (
                <Button
                  onClick={() => {
                    Modal.confirm({
                      title: '确认导出?',
                      content: '当前导出信息，请勿非法传阅',
                      onOk: () => {
                        setIsShowExport(true);
                      }
                    });
                  }}>
                  导出交易记录
                </Button>
              )}
              <Button
                onClick={() => {
                  Modal.confirm({
                    title: '确认导出?',
                    content: '当前导出信息，请勿非法传阅',
                    onOk: () => {
                      isLocal
                        ? useApi.export本地问诊订单列表(paramsRef.current)
                        : useApi.export问诊订单列表(paramsRef.current);
                    }
                  });
                }}>
                导出EXCEL
              </Button>
            </>
          }
          columns={useMemo(
            () =>
              [
                {
                  title: '下单时间',
                  width: 180,
                  dataIndex: 'createTime',
                  search: !isLocal && (
                    <DayRangePicker
                      placeholder={['开始时间', '结束时间']}
                      valueFormat={'YYYY-MM-DD HH:mm:ss'}
                      disabledDate={current => {
                        return current && current.valueOf() > Date.now();
                      }}
                    />
                  ),
                  searchIndex: ['beginTime', 'endTime']
                },
                {
                  title: '查询时间',
                  dataIndex: 'createTime',
                  search: (
                    <DayRangePicker
                      placeholder={['开始时间', '结束时间']}
                      valueFormat={'YYYY-MM-DD HH:mm:ss'}
                      disabledDate={current => {
                        return current && current.valueOf() > Date.now();
                      }}
                    />
                  ),
                  searchIndex: ['beginTime', 'endTime'],
                  render: false,
                  hidden: !isLocal
                },
                {
                  title: '患者姓名 | ID',
                  width: 180,
                  dataIndex: 'patientName',
                  render: (v, record: any) => {
                    return `${record.patientName}|${record.patCardNo}`;
                  }
                },
                {
                  title: '业务类型 | 平台单号',
                  width: 280,
                  dataIndex: 'typeName',
                  render: (v, record: any) => {
                    return `${v}|${record.orderId}`;
                  }
                },
                {
                  title: '问诊目的',
                  width: 160,
                  searchIndex: 'purposeType',
                  search: !isLocal && (
                    <ArrSelect options={purposeTypeForListObj} />
                  ),
                  dataIndex: 'purposeName'
                },
                {
                  title: '问诊医生 | 问诊科室',
                  width: 180,
                  dataIndex: 'doctorName',
                  render: (v, record: any) => {
                    return `${v}|${record.deptName}`;
                  }
                },
                {
                  title: '平台来源',
                  dataIndex: 'institutionName',
                  width: 150,
                  hidden: !isLocal
                },
                {
                  title: '支付渠道 | 支付方式',
                  width: 180,
                  dataIndex: 'payChannelName',
                  render: (v, record: any) => {
                    return `${v}|${record.payMethodName}`;
                  }
                },
                {
                  title: '订单状态',
                  width: 120,
                  dataIndex: 'statusName'
                },
                {
                  title: '发票状态',
                  width: 180,
                  dataIndex: 'invoice',
                  render: v => (v?.state ? invoiceStatusObj[v.state] : '-')
                },
                {
                  title: '金额(元)',
                  width: 120,
                  dataIndex: 'totalFee',
                  render: v => `￥${getPrice(v, 2, true)}`
                },
                {
                  title: '自费金额(元)',
                  width: 120,
                  dataIndex: 'ownPayAmt',
                  render: v => `￥${getPrice(v, 2, true)}`
                },
                {
                  title: '医保金额(元)',
                  width: 120,
                  dataIndex: 'medicalInsuranceAmount',
                  render: v => `￥${getPrice(v, 2, true)}`
                },
                {
                  title: isLocal ? '办理渠道' : '支付渠道',
                  dataIndex: 'payChannel',
                  search: <ArrSelect options={payChannelObj} />,
                  render: false
                },
                {
                  title: '支付方式',
                  dataIndex: 'payMethod',
                  search: !isLocal && <ArrSelect options={payMethodObj} />,
                  render: false
                },
                {
                  title: '业务类型',
                  dataIndex: 'type',
                  search: !isLocal && <ArrSelect options={orderTypeObj} />,
                  render: false
                },
                {
                  title: '订单状态',
                  dataIndex: 'status',
                  searchIndex: 'orderStatus',
                  search: !isLocal && (
                    <ArrSelect options={orderStatusObjOnline} />
                  ),
                  render: false
                },
                {
                  title: '平台单号',
                  dataIndex: 'orderId',
                  search: !isLocal,
                  render: false
                },
                {
                  title: '交易单号',
                  dataIndex: 'paySerialNumber',
                  search: !isLocal,
                  render: false
                },
                {
                  title: '问诊科室',
                  dataIndex: 'deptName',
                  search: !isLocal,
                  render: false
                },
                {
                  title: '问诊医生',
                  dataIndex: 'doctorName',
                  search: !isLocal,
                  render: false
                },
                {
                  title: '患者姓名',
                  dataIndex: 'patientName',
                  search: !isLocal,
                  render: false
                },
                {
                  title: '操作',
                  fixed: 'right',
                  width: 160,
                  render: record => (
                    <ActionsWrap>
                      <LinkButton
                        onClick={() => {
                          record.orderId &&
                            history.push(
                              isLocal
                                ? `/orderLocal/inquiryOnLine/${record.orderId}/${record.hisId}/${record.targetHisId}`
                                : `/order/inquiryOnLine/${record.orderId}`
                            );
                        }}>
                        查看
                      </LinkButton>
                      {record.status === 'S' &&
                        permisstion.canOrderRefund &&
                        // 合川宏仁隐藏退费
                        hisId !== '8900' &&
                        !isLocal && (
                          <LinkButton
                            onClick={() => {
                              if (hisId === '40075') {
                                actionConfirm(
                                  () =>
                                    useApi.退费
                                      .request({
                                        hisId: env.hisId,
                                        orderId: record.orderId
                                      })
                                      .finally(() => {
                                        return Promise.resolve();
                                      }),
                                  '',
                                  {
                                    template:
                                      '退费前，请核实HIS是否已取消该业务，确定要退费吗？'
                                  }
                                );

                                return;
                              }
                              actionConfirm(
                                () =>
                                  useApi.退费
                                    .request({
                                      hisId: env.hisId,
                                      orderId: record.orderId
                                    })
                                    .finally(() => {
                                      return Promise.resolve();
                                    }),
                                '退费'
                              );
                            }}>
                            退费
                          </LinkButton>
                        )}
                    </ActionsWrap>
                  )
                }
              ].filter(item => !item?.hidden) as any[],
            [hisId, history, isLocal]
          )}
        />
        <Modal
          title={
            <div>
              导出交易记录{' '}
              <span
                style={{
                  fontSize: '12px',
                  color: '#ccc'
                }}>
                净交易额=支付数据-退款数据
              </span>
            </div>
          }
          destroyOnClose={true}
          visible={!!isShowExport}
          footer={false}
          onCancel={() => {
            setIsShowExport(false);
          }}
          width={420}>
          <Form>
            <div className='formItem'>
              <div className='label'>
                <div>导出退款数据:</div>
              </div>
              <div className='value'>
                <DatePicker.RangePicker
                  value={exportDate as any}
                  onChange={v => {
                    setExportDate(v);
                  }}
                />
              </div>
            </div>

            <div className='formItem'>
              <div className='label'>
                <div>导出支付数据:</div>
              </div>
              <div className='value'>
                <Button
                  disabled={!exportDate || exportDate.length !== 2}
                  type='ghost'
                  onClick={() => {
                    if (!exportDate) {
                      return;
                    }
                    useApi.export支付数据({
                      hisId,
                      beginTime: exportDate[0]?.format('YYYY-MM-DD'),
                      endTime: exportDate[1]?.format('YYYY-MM-DD')
                    });
                  }}>
                  导出EXCEL
                </Button>
              </div>
            </div>
            <div className='formItem'>
              <div className='label'>
                <div>导出退款数据:</div>
              </div>
              <div className='value'>
                <Button
                  disabled={!exportDate || exportDate.length !== 2}
                  type='ghost'
                  onClick={() => {
                    if (!exportDate) {
                      return;
                    }
                    useApi.export退款数据({
                      hisId,
                      beginTime: exportDate[0]?.format('YYYY-MM-DD'),
                      endTime: exportDate[1]?.format('YYYY-MM-DD')
                    });
                  }}>
                  导出EXCEL
                </Button>
              </div>
            </div>
          </Form>
        </Modal>
      </WaterMarkWrap>
    </Fragment>
  );
};

export const Form = styled.div`
  > .formItem {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 10px 0;
    > .label {
      padding-right: 10px;
    }
    > .value {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }
`;
