import React, { useMemo, useRef } from 'react';
import {
  LinkButton,
  getPrice,
  DayRangePicker,
  ArrSelect,
  RouteComponentProps,
  ActionsWrap,
  useModal,
  handleSubmit
} from 'parsec-admin';
import env from '@src/configs/env';
import useApi, { payChannelObj, payMethodObj } from '../api';
import MyTableList from '@components/myTableList';
import { Button, Input, InputNumber, Modal } from 'antd';
import { useSessionStorage } from 'react-use';
import { AES } from '@kqinfo/ui';
import permisstion from '@utils/permisstion';
import usePaymodeWrapper from '@src/customHooks/usePaymodeWrapper';

import WaterMarkWrap from '@src/components/waterMarkWrap';
export const orderTypeObj: any = {
  10: '预约挂号',
  11: '门诊缴费',
  12: '住院服务',
  13: '当班挂号'
};
export const typeObj: any = {
  1: '预约挂号',
  2: '当班挂号'
};

export const orderStatusObj: any = {
  U: '下单未支付',
  S: '支付成功',
  C: '取消支付',
  R: '已退款'
};
export const refundStatus: any = {
  U: '初始状态',
  W: '退款中',
  S: '退款成功',
  C: '退款失败'
};

export const useRefundModal = () => {
  const [loginInfo] = useSessionStorage<any>('doctor');

  const switchRefundModal = useModal(
    ({ id }) => ({
      title: '退费提示',
      onSubmit: ({ ...params }: any) => {
        params.refundFee = Math.round(params.refundFee * 100); // 显示元 传分
        params.operatorId = loginInfo.id;
        params.operatorName = loginInfo.name;
        params.password = AES.Encrypt(params.password);

        return handleSubmit(() => useApi.缴费订单退款.request(params), `退费`);
      },
      items: [
        { name: 'orderId', render: false },
        { name: 'uniqueCode', render: false },
        {
          label: '退费金额',
          name: 'refundFee',
          formItemProps: {
            extra: '退费金额默认=订单金额-已退款金额'
          },
          required: true,
          render: () => (
            <InputNumber
              placeholder='请输入退费金额 (单位 元)'
              style={{
                width: '100%'
              }}
            />
          )
        },
        {
          label: '退费原因',
          name: 'reason',
          required: true,
          render: () => (
            <Input.TextArea
              placeholder='请输入退费原因 (最多40字)'
              maxLength={40}
            />
          )
        },
        {
          label: '密码',
          name: 'password',
          required: true,
          render: () => (
            <Input type='password' placeholder='请输入密码' maxLength={40} />
          )
        }
      ]
    }),
    []
  );

  return switchRefundModal;
};
export default ({ history }: RouteComponentProps) => {
  const { payChannels, payMethods } = usePaymodeWrapper();
  const paramsRef = useRef<any>();

  const switchRefundModal = useRefundModal();

  const canRefund = useMemo(() => {
    return (
      env.hisId === '40019' || env.hisId === '40026' || env.hisId === '40074'
    );
  }, []);

  return (
    <WaterMarkWrap>
      <MyTableList
        tableTitle='挂缴订单'
        action={
          <ActionsWrap>
            <Button
              onClick={() => {
                Modal.confirm({
                  title: '确认导出?',
                  content: '当前导出信息，请勿非法传阅',
                  onOk: () => {
                    useApi.export问诊订单列表(paramsRef.current);
                  }
                });
              }}>
              导出EXCEL
            </Button>
          </ActionsWrap>
        }
        getList={({ params }) => {
          const p = {
            ...params,
            // uniqueCode: params.uniqueCode || '10'
            businessType: '3'
            // refundStatus: '1'
          };
          paramsRef.current = p;
          return useApi.挂缴订单列表.request(p);
        }}
        columns={useMemo(
          () => [
            {
              title: '业务类型',
              dataIndex: 'uniqueCode',
              search: <ArrSelect options={orderTypeObj} allowClear={true} />,
              render: false
            },
            {
              title: '下单时间',
              width: 180,
              dataIndex: 'createTime'
            },
            {
              title: '平台单号',
              width: 180,
              dataIndex: 'orderId',
              search: true
            },
            {
              title: '支付流水号',
              width: 250,
              dataIndex: 'paySerialNumber',
              search: true
            },
            {
              title: '就诊人姓名',
              width: 140,
              search: true,
              dataIndex: 'patientName'
            },
            {
              title: '业务类型',
              width: 180,
              dataIndex: 'typeName',
              render: v => {
                return (
                  <div>
                    {v}
                    {/* <span
                      style={{
                        color: 'rgb(235 109 33)',
                        backgroundColor: 'rgb(243 210 130)',
                        fontSize: '12px',
                        padding: '2px 5px',
                        marginLeft: '5px',
                        borderRadius: '5px'
                      }}>
                      诊前开单
                    </span> */}
                  </div>
                );
              }
            },
            {
              title: '支付渠道',
              dataIndex: 'payChannel',
              search: (
                <ArrSelect
                  options={
                    payChannels && Object.keys(payChannels)?.length > 0
                      ? payChannels
                      : payChannelObj
                  }
                />
              ),
              render: false
            },
            {
              title: '支付方式',
              dataIndex: 'payMethod',
              search: (
                <ArrSelect
                  options={
                    payMethods && Object.keys(payMethods)?.length > 0
                      ? payMethods
                      : payMethodObj
                  }
                />
              ),
              render: false
            },

            // {
            //   title: '交易单号',r
            //   dataIndex: 'paySerialNumber',
            //   search: true,
            //   render: false
            // },
            // {
            //   title: '支付渠道',
            //   width: 120,
            //   dataIndex: 'payChannelName'
            // },
            {
              title: '支付渠道/支付方式',
              width: 160,
              dataIndex: 'payMethodName',
              render: (v, r: any) => {
                return `${r['payChannelName']}|${r['payMethodName']}`;
              }
            },
            {
              title: '订单金额(元)',
              width: 180,
              dataIndex: 'totalFee',
              render: v => `${getPrice(v, 2, true)}`
            },
            {
              title: '实际支付金额',
              width: 180,
              dataIndex: 'payFee',
              render: v => `${getPrice(v, 2, true)}`
            },
            {
              title: '查询时间',
              width: 180,
              render: false,
              dataIndex: 'operatorTime',
              search: (
                <DayRangePicker
                  placeholder={['开始时间', '结束时间']}
                  valueFormat={'YYYY-MM-DD HH:mm:ss'}
                  disabledDate={current => {
                    return current && current.valueOf() > Date.now();
                  }}
                />
              ),
              searchIndex: ['beginTime', 'endTime']
            },
            {
              title: '退款流水号',
              dataIndex: 'refundSerialNo',
              search: true,
              render: false
            },
            // {
            //   title: '操作人',
            //   width: 120,
            //   dataIndex: 'operatorName',
            //   search: true
            // },
            {
              title: '支付状态',
              width: 120,
              search: <ArrSelect options={orderStatusObj} />,
              searchIndex: 'orderStatus',
              dataIndex: 'statusName'
            },
            {
              title: '医院订单号',
              width: 120,
              dataIndex: 'hisOrderNo'
            },
            {
              title: '科室名称',
              width: 150,
              dataIndex: 'deptName',
              search: true,
              searchIndex: 'deptName',
              render: (v, r: any) => {
                return v === '-1' ? '' : v;
              }
            },
            {
              title: '医生名称',
              width: 150,
              dataIndex: 'doctorName',
              search: true,
              searchIndex: 'doctorName',
              render: (v, r: any) => {
                return v === '-1' ? '' : v;
              }
            },
            {
              title: '操作',
              fixed: 'right',
              width: 150,
              render: record => (
                <ActionsWrap>
                  {permisstion.canLookOrderSmartDetail && (
                    <LinkButton
                      onClick={() => {
                        record.orderId &&
                          history.push(
                            '/order/listSmart/' +
                              record.orderId +
                              '/' +
                              record.uniqueCode
                          );
                      }}>
                      查看
                    </LinkButton>
                  )}
                  {/* canRefund */}
                  {record.canRefund &&
                    (canRefund || permisstion.canLookOrderSmartrefund) && (
                      <LinkButton
                        onClick={() => {
                          // 退费金额默认=订单金额-已退款金额
                          switchRefundModal({
                            refundFee: record.canRefundFee / 100,
                            uniqueCode: record.uniqueCode,
                            orderId: record.orderId
                          });
                        }}>
                        退费
                      </LinkButton>
                    )}
                </ActionsWrap>
              )
            }
          ],
          [canRefund, history, payChannels, payMethods, switchRefundModal]
        )}
      />{' '}
    </WaterMarkWrap>
  );
};
