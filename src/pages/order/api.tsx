import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { administrationList, rateInfo } from './const';
import {
  ListApiRequestParams,
  ListApiResponseData,
  ApiResponse
} from '@src/configs/apis';
import QueryString from 'qs';
import env from '@src/configs/env';

export const payChannelObj: any =
  env.hisId === '40061'
    ? {
        weixin: '微信',
        app: '移动应用',
        wechat: '微信',
        zfb: '支付宝',
        alipay: '支付宝',
        offline: '线下',
        ybzf: '线上医保支付',
        sbk: '社保卡',
        ybdzpz: '医保电子凭证',
        yhk: '银行卡',
        sbk_weixin: '社保卡+微信',
        sbk_alipay: '社保卡+支付宝',
        sbk_yhk: '社保卡+银行卡',
        ybdzpz_weixin: '医保电子凭证+微信',
        ybdzpz_alipay: '医保电子凭证+支付宝',
        ybdzpz_yhk: '医保电子凭证+银行卡'
      }
    : {
        weixin: '微信',
        app: '移动应用',
        alipay: '支付宝',
        ybzf: '线上医保支付'
      };

export const plusSignReStatus: any = {
  '0': '已失效',
  '1': '已登记'
};
export const invoiceStatusObj = {
  INVOICING: '开票中',
  INVOICED: '开票成功',
  INVOICE_FAIL: '开票失败',
  RED_INVOICING: '冲红中',
  RED_INVOICED: '冲红成功',
  RED_INVOICE_FAIL: '冲红失败'
};
export const payMethodObj: any =
  env.hisId === '40061'
    ? {
        wap: 'wap支付',
        h5: 'H5支付',
        app: 'app支付',
        qr: 'qr扫码付',
        f2f: 'f2f扫码付',
        medicareOnline: '在线医保支付',
        medicareOffline: '线下医保支付',
        yhkOffline: '线下银行卡支付',
        mixOffline: '线下混合支付',
        prepaidCard: '预付卡支付',
        zero: '零元支付',
        credit: '信用卡支付',
        withhod: '代扣支付',
        mock: '模拟支付',
        mini: '立缴小程序'
      }
    : {
        wap: 'wap支付',
        h5: 'H5支付',
        app: 'app支付',
        qr: 'qr扫码付',
        medicareOnline: '在线医保支付',
        mini: '立缴小程序'
      };

export const platformSourceObj: any = {
  1: '凯桥微信公众号',
  2: '凯桥支付宝小程序',
  3: '凯桥微信小程序',
  4: '凯桥支付宝生活号',
  5: '微信公众号',
  6: '支付宝小程序',
  7: '微信小程序',
  8: '支付宝生活号',
  9: 'app',
  10: '医保',
  11: '自助机'
};

export const orderTypeObj: any = {
  1: '图文咨询',
  2: '电话咨询',
  3: '视频咨询'
};
export const orderPayStatusObj: any = {
  U: '待支付',
  S: '已支付',
  F: '失败',
  C: '已取消',
  R: '已退款',
  L: '锁定成功'
};
export const purposeTypeObj: any = {
  1: '健康咨询',
  2: '在线复诊',
  3: '报告解读',
  7: '复诊续方',
  12: '新冠咨询',
  13: '新冠找药'
};
export const Channel = {
  WECHAT_H5: '微信H5',
  WECHAT_APP: '微信小程序'
};
// U 待支付
// S 已支付
// C 已取消
// F 失败
export const orderStatusObjOnline: any = {
  U: '待支付',
  S: '已支付',
  C: '已取消',
  F: '失败',
  R: '已退款',
  L: '锁定成功'
};

// -1 未支付
// 0 未回复
// 1 进行中
// 2 已结束
// 3 已完成
// 4 申请中
// 5 已拒绝
// 40 已超时
// 50 已退款
export const orderStatusObj: any = {
  '-1': '未支付',
  '0': '未回复',
  '1': '进行中',
  '2': '已结束',
  '3': '已完成',
  '4': '申请中',
  '5': '已拒绝',
  '6': '异常结束',
  '7': '已过号',
  '40': '已超时',
  '50': '已退款',
  '-2': '已失效'
};

export const purposeTypeForListObj = {
  1: '健康咨询',
  2: '复诊',
  3: '报告解读',
  4: '加号',
  5: '预约手术',
  6: '预约检查',
  7: '在线开方',
  8: '其他',
  9: '免费报告解读',
  12: '新冠咨询',
  13: '新冠找药',
  31: '护理咨询'
};
export interface CaseInfo {
  chiefComplaint: string; //主诉
  medicalHistory: string; //现病史
  anamnesis: string; //既往史
  examination: string; //体检
  mainDiagnosis: string; //主要诊断
  otherDiagnosis: string; //其他诊断
  recommend: string; //建议
}

export interface RecipeListItem {
  key?: string;
  drugCode?: string; //药品代码
  administration?: string; //途径
  retailPrice?: string; //零售价
  firmId?: string; //公司id 号
  quantity?: string; //总量
  units?: string; //单位,指单个片剂的物理单位，比如片、粒、丸
  storage?: string; //药房编码
  inputCode?: string; //药品口称检索编码
  packageUnits?: string; //包装单位
  drugName?: string; //药品名称
  frequency?: string; //药品服用频次
  frequencyName?: string; //药品服用频次
  dosageUnit?: string; //用量单位，是每一个片剂的药量单位，比如mg，ml
  fydj?: string; //费用等级
  drugSpec?: string; //片剂规格，指单片药的规格
  hospitalCode?: string; //医院编码
  packageSpec?: string; //包装规格
  itemClass?: string; //类别
  price?: string | number; //单价
  amount?: number; //数量,每次开方的数量
  dosage?: string; //用量，指每次用量
  doctorExplain?: string; //医生说明
  totalDosage?: string;
  extraData?: {
    coefficient: '10';
    measureUnit: 'MG';
    packFactor: '10';
  };
  dosageMode?: string;
}

export interface MedicineListItem {
  id: number; //主键id
  createTime: string; //创建时间
  pControlResultNo: string; //电子处方平台流水号
  deptName: string; //科室名称
  doctorName: string; //医生名称
  patientName: string; //患者姓名
  bizeType: string; //业务类型
  payChannel: string; //支付渠道
  payMethod: string; //支付方法（wap支付， medicareOnline-在线医保支付）
  payMethodStr: string; //支付方法 字符串
  totalFee: number; //总金额
  totalFeeDouble: string; //总金额 小数
  status: string; //REG_UNPAY挂号未支付 APPLY申请中 AUDIT审核中 UNPAY处方未支付 PART_PAYED部分付款PAYED处方已付款未取药 FINISH处方已付款已取药(正常完成) UNAUDIT审核未通过 TIMEOUT付款超时 CANCEL取消 EXCEPTION异常订单 FAIL失败 REFUND退款 APPLY_REFUND申请退款)
  statusName: string; //订单状态
  auditStatus: string; //审核状态(WAIT待审核 UNAUDIT未通过待处理 PASSED已通过 FINISH医生双签 FAIL审核失败)
  auditName: string; //审核状态
  createDate: string; //创建时间字符串
  printTag?: 0 | 1; //1已打印，0未打印
}

export interface AdditionalRegisterType {
  id: number;
  hospitalDistrict: string; // '渝中院区';
  deptName: string; // '普内科全科';
  doctorName: string; // '陈鑫';
  doctorTitle: string; // '副主任医师';
  additionalRegisterDate: string; // '@date'; //加号日期
  timeFlag: number; // '1'; //时段,1：上午2：下午3：晚上
  patId: string; // '123'; //就诊人id
  patName: string; // '陈小桥'; //就诊人姓名
  patCardNo: string; // '00123456789'; //就诊人卡号
  patSex: string; // 'M'; //性别（F-女 M-男）
  patAge: number; //年龄
  patIdNo: string; // '500223199501032723'; //就诊人身份证号
  patPhone: string; // '17773928392'; //就诊人手机号,
  regStatus: string; // '0'; //登记状态 0已失效 1已登记
  createdAt: string; // '@date'; //创建时间
  updatedAt: string; // '@date'; //更新时间
}

// 处方药品详情
export interface MedicineDetailInfo {
  auditStatus: string; //审核状态(WAIT待审核 UNAUDIT未通过待处理 PASSED已通过 FINISH医生双签 FAIL审核失败)
  cancelReason: string; //取消原因
  caseId: number; //病历id
  deptName: string; //科室名称
  doctorName: string; //医生名称
  doctorTitle: string; //医生职称
  hisId: string; //医院id
  hisName: string; //医院名称
  id: number; //主键id
  inquiryId: number; //咨询问诊id
  inquiryOrderId: number; //咨询问诊id
  orderId: number; //处方的订单id
  patientAge: string; //患者年龄（申请慢病续方时刻的年龄）
  patientId: number; //就诊人id
  patientName: string; //患者姓名
  patientSex: string; //患者性别
  patientTel: string; //联系电话
  patientWeight: string; //患者体重
  payDate: string; //支付时间
  prescDate: string; //开单时间
  refundDate: string; //退款时间
  status: string; //REG_UNPAY挂号未支付 APPLY申请中 AUDIT审核中 UNPAY处方未支付 PART_PAYED部分付款PAYED处方已付款未取药 FINISH处方已付款已取药(正常完成) UNAUDIT审核未通过 TIMEOUT付款超时 CANCEL取消 EXCEPTION异常订单 FAIL失败 REFUND退款 APPLY_REFUND申请退款)
  totalFee: number; //总金额
  totalFeeDouble: string; //总金额 小数
  canPresRefund: boolean; //是否可以退处方
  canDeliveryRefund: boolean; //是否可以退快递费
  payChannel: string; //业务渠道
  payMethodStr: string; //支付方式
  prescriptionType: number; //处方类别（1西药，3中药饮片）
  hospitalTradeno: string; //医院单号
  controlResultNo: string; //平台订单
  paySerialNumber: string; //交易单号
  //病历信息
  caseInfo: CaseInfo;
  //处方单信息
  recipeList: {
    prescriptionId: string; //处方主表id
    totalFee: number; //总金额:单位分
    hospitalTradeno: string; //医院处方订单号
    status: string;
    ownPayAmt: number; // 自费
    psnAcctPay: number; // 医保个账
    fundPay: number; // 医保统筹
    drugList: RecipeListItem[];
  }[];
  inquiryInfo: any;
  registrationInfo: //就诊信息
  {
    patientName: string; //姓名
    patientSex: string; //性别
    patientAge: string; //年龄
    patientWeight: string; //体重
    patientId: string; //患者ID
    patientTel: string; //联系电话
    patientIdNo: string; //身份证号
    patientAddress: string; //地址
    deptName: string; //科室名称
    doctorName: string; //医生名称
    doctorTitle: string; //医生职称
    times: string; //就诊时间
    registerType: string; //就诊类型
  }[];
  deliveryOrder: {
    address: '重庆市渝北区康美街道';
    city: '市辖区';
    createTime: '2022-11-30 20:08:20';
    deliveryCfgId: 1547513275843440600;
    deliveryCode: null;
    deliveryCompany: '中国邮政';
    district: '渝北区';
    fee: number;
    hisStatus: 'HAS_NOTIFY';
    id: 49;
    orderId: 2326798745612801;
    payStatus: 1;
    phone: '15723241074';
    prescriptionId: ********;
    province: '重庆市';
    receiver: '韩桂林';
    refundReason: null;
    refundStatus: 0;
    status: number;
    thirdExtraData: null;
    updateTime: '2022-11-30 22:42:22';
  };
}

export interface MedicineDetailInfoNew extends MedicineDetailInfo {
  prescriptionMainOrder: {
    id: string; //合并处方主订单ID
    hisId: string; //医院id
    createTime: string; //创建时间
    updateTime: string; //修改时间
    mainOrderId: string; //主订单id
    accountId: string; //账户id
    patCardNo: string; //就诊人卡号
    paySerialNumber: string; //支付流水号
    status:
      | 'UNPAY'
      | 'PART_PAYED'
      | 'PAYED'
      | 'FINISH'
      | 'TIMEOUT'
      | 'CANCEL'
      | 'EXCEPTION'
      | 'WAIT_DELIVERY'
      | 'IN_DELIVERY'; //主订单状态：UNPAY("待支付"),PART_PAYED("部分付款"),PAYED("处方已付款未取药"),WAIT_DELIVERY("待发货"),IN_DELIVERY("发货中"),FINISH("已完成"),TIMEOUT("付款超时"),CANCEL("已取消"),EXCEPTION("异常订单"),FAIL("支付失败");
    totalFee: number; //总金额
    businessFee: number; //业务费用（处方费）
    deliveryDrugFee: number; //邮寄费用
    doctorName: string; //医生姓名
    doctorId: string; //医生编号
    inquiryId: string; //问诊id
    deptName: string; //科室名称
    patientId: string; //就诊人id
    patientName: string; //就诊人姓名
    patientAge: string; //就诊人年龄
    prescTime: string; //开单时间
    doctorTitle: string; //医生职称
    doctorImage: string; //医生头像
    takeMethod: 'SELF' | 'POST' | 'HOSPITAL'; //取药方式
    payTime: string; //支付时间
    payChannel: string; //支付渠道
    payMethod: 'mini' | 'h5'; //支付方式:mini立缴小程序，h5：H5支付
  };
  prescriptionList: [
    {
      diseaseTypeCode: string; //病种类型编码
      diseaseTypeName: string; //病种类型名称
      prescriptionType: 1 | 3 | 4; //处方类别（1西药，3中药饮片）
      auditStatus: string; //审核状态(WAIT待审核 UNAUDIT未通过待处理 PASSED已通过 FINISH医生双签 FAIL审核失败)
      cancelReason: string; //取消原因
      caseId: number; //病历id
      deptName: string; //科室名称
      doctorName: string; //医生名称
      doctorTitle: string; //医生职称
      hisId: number; //医院id
      hisName: string; //医院名称
      id: number; //主键id
      orderId: number; //处方的订单id
      patientAge: string; //患者年龄（申请慢病续方时刻的年龄）
      patientId: number; //患者ID
      patientName: string; //患者姓名
      patientSex: string; //患者性别
      patientTel: string; //联系电话
      patientWeight: string; //患者体重
      payDate: string; //支付时间
      prescDate: string; //开单时间
      refundDate: string; //退款时间
      status: string; //REG_UNPAY挂号未支付 APPLY申请中 AUDIT审核中 UNPAY处方未支付 PART_PAYED部分付款PAYED处方已付款未取药 FINISH处方已付款已取药(正常完成) UNAUDIT审核未通过 TIMEOUT付款超时 CANCEL取消 EXCEPTION异常订单 FAIL失败 REFUND退款 APPLY_REFUND申请退款)
      totalFee: number; //总金额
      totalFeeDouble: string; //总金额 小数
      payChannel: string; //业务渠道
      bizeType: string; //业务类型
      payMethodStr: string; //支付方式
      hospitalTradeno: string; //医院单号
      controlResultNo: string; //平台订单
      paySerialNumber: string; //交易单号
      ownPayAmt: ''; // 自费
      psnAcctPay: ''; // 医保个账
      fundPay: ''; // 医保统筹
      prescriptionId: string; //处方主表id
      mainOrderId: 2408930368058114; // 主订单id
      canPresRefund: boolean; // 是否可以退款
      recipeList: [
        {
          prescriptionId: 88;
          totalFee: 1;
          hospitalTradeno: '1682305098519252994';
          status: 'IN_DELIVERY';
          statusName: null;
          drugList: [
            {
              id: '@natural'; //处方单药品ID
              prescriptionId: '@natural'; //处方主表ID
              recipeId: '@natural'; //处方单ID
              hospitalCode: '@word(16)'; //医院编码
              storage: ''; //药房编码
              inputCode: '@word(16)'; //拼音输入码
              drugType: ''; //药品类别（西药01，中成药02，中草药03）
              drugCode: '@word(16)'; //药品代码
              drugName: '@cword(6)'; //药品名称
              drugSpec: ''; //药品规格
              firmName: '@cword(6)'; //厂家
              units: ''; //单位（制剂单位：片、支、粒、袋等）
              packageSpec: ''; //包装规格
              packageUnit: ''; //包装单位（瓶、箱、盒、罐、袋等）
              price: ''; //单价(元)
              amount: '@integer(0, 1000000)'; //数量（单价*数量=总价）
              dosageMode: "@pick('UNIT','MEASURE_UNIT')"; //处方药品用量模式, UNIT 最小单位(默认), MEASURE_UNIT：计量单位
              dosage: ''; //用量（描述药品单次用量）
              totalDosage: ''; //总用量(单次总用量, dosageMode=MEASURE_UNIT时使用)
              dosageUnit: ''; //用量单位（描述药品单次用量）
              medicationDays: ''; //用药天数
              administrationName: '@cword(6)'; //用药途径（口服、注射、滴眼等）
              administration: ''; //途径编码
              frequency: ''; //使用频次
              frequencyName: '@cword(6)'; //频次名称（如：每天三次）
              formulation: ''; //剂型
              fydj: ''; //费用等级
              doctorExplain: ''; //医生说明
              hospitalChargeCode: '@word(16)'; //医院收费项目编码
              hospitalChargeName: '@cword(6)'; //医院收费项目名称
              centreChargeCode: '@word(16)'; //医保收费项目编码
              medicareFeeitemName: '@cword(6)'; //医保收费项目名称
              priceitemCode: '@word(16)'; //物价项目编码
              itemClass: ''; //医保药物类别（01甲类，02乙类）
              rowNum: ''; //行数（？？？）
              quantity: '@integer(0, 1000000)'; //总量（？？？）
              chWeightPerDose: '@integer(0, 1000000)'; //中医-单药单付重量（g），单个药品在“单付药方”中的重量
              chWeight: '@integer(0, 1000000)'; //中医-单药全付重量（g），单个药品在“整个疗程”中的重量，数值同amount
              chRemark: ''; //中药饮片备注, (无、先煎、后下、包煎、另煎、冲服、烊化、打碎)
              totalPrice: '@integer(0, 1000000)'; //总价(四舍五入成分)
              extraData: ''; //额外返回数据
            }
          ];
        }
      ];
    }
  ];
  drugDeliveryAddressLogList?: Array<{
    id: string;
    updateTime?: string;
    operatorName?: string;
    content?: string;
    type?: string;
  }>;
}
interface OnlineOrderDetail {
  payChannelStr: string;
  deptName: string;
  patCardNo: string;
  leftPayTime: string;
  patientId: number;
  payTime: string;
  operatorAccount: string;
  type: string;
  unlockFlag: string;
  score: number;
  hisName: string;
  payFee: number;
  payMethod: string;
  paySerialNumber: string;
  id: number;
  patientInfo: PatientInfo;
  canPayFlag: string;
  operatorId: string;
  businessTypeStr: string;
  refundOrderInfo: RefundOrderInfo;
  appraisalInfo: AppraisalInfo;
  appraisalTime: string;
  hisId: string;
  deptId: string;
  orderIdStr: string;
  typeStr: string;
  sourceType: number;
  replyStatus: number;
  status: string;
  orderStatus: string;
  refundStatus: string;
  remark: string;
  operatorName: string;
  payMethodStr: string;
  autoCancelTimeout: number;
  doctorName: string;
  refundDesc: string;
  hospitalOrderInfo: any;
  invoice: {
    // INVOICING-开票中
    // INVOICED-开票成功
    // INVOICE_FAIL-开票失败
    // RED_INVOICING-冲红中
    // RED_INVOICED-冲红成功
    // RED_INVOICE_FAIL-冲红失败
    state:
      | 'INVOICING'
      | 'INVOICED'
      | 'INVOICE_FAIL'
      | 'RED_INVOICING'
      | 'RED_INVOICED'
      | 'RED_INVOICE_FAIL';
    invoiceUrl: string; //发票访问地址
    orderId: string; //对应的orderId
    id: string; //发票记录id
    company: 'NUONUO' | 'BOSSSOFT'; //开票公司
    createTime: string; //开票时间
  };
  doctorId: string;
  unlockDatetime: string;
  statusName: string;
  appraisal: string;
  createDate: string;
  patientName: string;
  autoCancelFlag: string;
  refundTime: string;
  sex: string;
  updateTime: string;
  canCancelFlag: string;
  userName: string;
  userId: number;
  extFields: string;
  createTime: string;
  orderTotalFee: string;
  totalFee: number;
  payChannel: string;
  businessType: string;
  payStatus: string;
  payDate: string;
  ownPayAmt: number; // 自费
  psnAcctPay: number; // 医保个账
  fundPay: number; // 医保统筹
}
export interface PatientInfo {
  id: number;
  name: string;
  hisId: string;
  hisName: string;
  userId: number;
  channelType: number;
  patientType: number;
  relationType: number;
  idType: number;
  idNo: string;
  sex: string;
  birthday: string;
  mobile: string;
  address: string;
  bindStatus: number;
  parentName: string;
  parentIdType: number;
  parentIdNo: number;
  patCardType: number;
  patCardNo: string;
  consumeType: number;
  isDefalut: boolean;
  isSelf: boolean;
  syncStatus: string;
  type: string;
  idImage: string;
  patInNo: string;
  bindMedicareCard: number;
  height: number;
  weight: number;
  married: string;
  smoking: string;
  patHisId: string;
  patHisNo: string;
  createTime: string;
  updateTime: string;
  accountId: number;
  patientId: number;
  patientName: string;
  patientMobile: string;
  patientSex: string;
  isDefault: boolean;
  patientImg: string;
  patientAge: string;
  platformId: number;
  patientAddress: string;
  openId: number;
  realName: string;
  birth: string;
  platformSource: string;
  age: string;
  inquiryTime: string;
  inquiryDept: string;
  inquiryDoctor: string;
  inquiryPurpose: string;
  userName: string;
}

export interface RefundOrderInfo {
  id: number;
  orderId: number;
  businessType: string;
  type: string;
  hisId: string;
  sourceType: number;
  payType: string;
  refundStatus: 'U' | 'W' | 'S' | 'F';
  refundFee: number;
  refundSerialNo: string;
  refundDesc: string;
  refundSuccessTime: string;
  refundResultDesc: string;
  extFields: string;
  createTime: string;
  updateTime: string;
  deptId: string;
  deptName: string;
  doctorId: string;
  doctorName: string;
  userId: number;
  operatorId: number;
  operatorName: string;
  amount: string;
  refundStatusName: string;
}

export interface AppraisalInfo {
  id: number;
  userId: number;
  name: string;
  mobile: string;
  orderId: number;
  hisId: string;
  hisName: string;
  deptId: string;
  deptName: string;
  doctorId: string;
  doctorName: string;
  score: number;
  appraisal: string;
  isShow: string;
  createTime: string;
  updateTime: string;
  appraisalLabel: string;
  hisScore: number;
  hisAppraisal: string;
  hisAppraisalLabel: string;
  replyAppraisal: string;
  createDate: string;
  inquiryId: number;
  inquiryType: number;
  patientId: number;
  patientName: string;
  idNumber: number;
  sex: string;
  age: number;
  orderIdStr: string;
  createTimeStr: string;
  nameStr: string;
}

export interface TrackInfoListItem {
  opTime: string; // 操作时间
  trackStatus: "@pick('PICKED', 'TRANSPORT', 'DELIVER', 'PENDING', 'RECEIVED')"; // 见枚举https://yapi.cqkqinfo.com/project/703/interface/api/45121返回的DeliveryStatusEnum
  opDesc: string; // 操作详述
  opOrgProvName: string; // 操作网点省名
  opOrgCity: string; // 操作网点城市
  opOrgName: string; // 操作网点名称
  operatorName: string; // 操作员
}

interface RecordParam {
  sort?: string;
  hisId?: string;
  businessType?: string;
  refundStatus?: string;
  beginTime?: string;
  endTime?: string;
}

interface RecordRes {
  orderId: string;
  businessType: string;
  type: string;
  typeName: string;
  status: string;
  statusName: string;
  patientId: number;
  patientName: string;
  createTime: string;
  totalFee: number;
  payChannel: string;
  payChannelName: string;
  payMethod: string;
  payMethodName: string;
  doctorId: string;
  doctorName: string;
  deptId: string;
  deptName: string;
  operatorId: number;
  operatorName: string;
  operatorTime: string;
  refundSerialNo: string;
  paySerialNumber: string;
}

export interface DeliveryListItem {
  hisId: 1; //医院ID
  patientName: '@cname'; //患者姓名
  createTime: '2021-04-14 10:07:09';
  updateTime: '2021-04-14 10:07:09';
  orderId: 1;
  hospitalTradeno: '@string(10)'; //医院处方号
  paySerialNumber: '@string(10)'; //订单号（支付流程号）
  prescriptionId: 1;
  receiver: '@cname';
  phone: '***********';
  address: string;
  fee: 2500;
  deliveryCompany: '中国邮政';
  deliveryCode: '202104020001';
  mixStatus: "@pick('PAID', 'REFUND', 'EXCEPTION_REFUND')"; //综合状态:已支付，已退款，异常退款
  status: 10; //物流状态：10新配送单，30医院已发药，50患者签收，40: 已收件（配送中）51系统自动签收， 99: 已取消（正常退款）   100:异常退款
  payStatus: 0;
  refundStatus: 0;
}
interface HospitalizationAppointment<D> {
  id: '@integer(0, 1000000)'; //主键
  hisId: string; //医院ID
  userId: string; //用户ID
  patCardNo: string; //就诊人就诊卡号
  patName: '@cname'; //就诊人姓名
  diseaseLevel: string; //病情分级
  deptName: '@cword(6)'; //预约科室
  deptId: string; //科室ID
  regDate: string; //预约入院日期
  'emergencyContact ': '@cname'; //紧急联系人
  relation: string; //与患者关系（用数据字典，relations）
  documentType: string; //紧急联系人证件类型
  certificateNo: string; //紧急联系人证件号
  residenceAddress: '@county@cword(2)街@integer(1,100)号@cword(4)小区'; //患者户籍地址
  phone: '1@integer(**********, **********)'; //联系电话
  symptom: string; //症状
  examResult: string; //辅助检查（检验检查）结果
  expectedDate: string; //预产期
  examInOurHos: '@integer(0, 1000000)'; //是否我院产检
  cancelDate: string; //取消日期
  cancelReason: string; //取消原因
  highRisk: string; //高危因素
  status: string; //预约状态（成功-SUCCESS；取消-CANCEL；待审核-WAITE）
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //修改时间
  patientInfo: D;
}
export default {
  电子处方售后审核: createApiHooks(
    (data: {
      prescriptionId: '@natural'; //处方ID
      result: "@pick('REJECT','PASS')"; //审核结果：通过，不通过
      verifyRemark: ''; //审核备注
    }) =>
      request.post<ApiResponse<any>>(
        '/mch/prescription/prescription-after-sales/verify',
        data,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
  ),
  问诊记录列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        sort?: string;
        hisId?: string;
        businessType?: string;
        refundStatus?: string;
        beginTime?: string;
        endTime?: string;
      }
    ) =>
      request.post<
        ListApiResponseData<{
          orderId: string;
          businessType: string;
          type: string;
          typeName: string;
          status: string;
          statusName: string;
          patientId: number;
          patientName: string;
          createTime: string;
          totalFee: number;
          payChannel: string;
          payChannelName: string;
          payMethod: string;
          payMethodName: string;
          doctorId: string;
          doctorName: string;
          deptId: string;
          deptName: string;
          operatorId: number;
          operatorName: string;
          operatorTime: string;
          refundSerialNo: string;
          paySerialNumber: string;
        }>
      >('/mch/order/order/page', data)
  ),
  处理记录列表: createApiHooks((data: ListApiRequestParams & RecordParam) =>
    request.post<ListApiResponseData<RecordRes>>(
      '/mch/order/order/page/refund-consult',
      data
    )
  ),
  挂缴订单列表: createApiHooks((data: ListApiRequestParams & RecordParam) =>
    request.post<ListApiResponseData<RecordRes>>(
      '/mch/order/order/page/wisdom',
      data
    )
  ),
  在线问诊列表: createApiHooks((data: ListApiRequestParams & RecordParam) =>
    request.post<ListApiResponseData<RecordRes>>(
      '/mch/order/order/page/consult',
      data
    )
  ),
  本地在线问诊列表: createApiHooks(
    (params: ListApiRequestParams & RecordParam) =>
      request.get<ListApiResponseData<RecordRes>>(
        '/mch/order/ls-main/inquiry-order',
        { params }
      )
  ),
  标记为已读: createApiHooks(
    (data: {
      id: number | string; // 更新记录Id
      status: number | string; // 处理状态 0=未处理 1=已处理
      handleRemark: string; // 处理备注
    }) => request.put<any>('/mch/customer/appoint', data)
  ),
  预约管理导出: createApiHooks((params: any) =>
    request.get<Blob>('/mch/customer/appoint/export', {
      responseType: 'blob',
      params
    })
  ),
  用户预约记录分页: createApiHooks(
    (
      params: ListApiRequestParams & {
        area: string;
        regStatus?: string; // 登记状态
        patName?: string; // 就诊人姓名
        pageNum?: number; // 当前页码
        numPerPage?: number; //  每页显示记录数
      }
    ) =>
      request.get<
        ListApiResponseData<{
          id: '@natural(1,100)'; // ID
          hisId: '@natural(1,100)'; // alue = "HIS ID
          userId: '@natural(1,100)'; // 用户id
          serviceType: "@pick('HOUSE_CARE')"; // 预约类型, HOUSE_CARE: 居家护理
          clientName: '@cname'; // 客户姓名
          phone: "1@string('number',10)"; // 联系电话
          remark: '@csentence'; // 表单备注内容
          area: '@city'; // 服务区域
          status: '@natural(0,1)'; // 状态 0=未处理 1=已处理
          handleRemark: '@csentence'; // 处理备注内容
          createTime: '@datetime'; // 创建时间
          updateTime: '@datetime'; // 更新时间
        }>
      >('/mch/customer/appoint/page', {
        params
      })
  ),
  预约类型分页: createApiHooks(
    (
      params: ListApiRequestParams & {
        serviceName?: string;
        status?: string; // 登记状态
        hisId?: string; // 就诊人姓名
      }
    ) =>
      request.get<
        ListApiResponseData<{
          id: string; // ID
          hisId?: string; // alue = "HIS ID
          createUser?: string; // 用户id
          serviceName?: string; // 预约类型, HOUSE_CARE: 居家护理
          status?: string; // 客户姓名
          updateTime?: string; // 联系电话
          createTime?: string; // 表单备注内容
        }>
      >('/mch/customer/service-type/page', {
        params
      })
  ),
  更新预约类型: createApiHooks(
    (data: {
      id: number | string; // 更新记录Id
      status: number | string; // 处理状态 0=未处理 1=已处理
      serviceName: string; // 处理备注
    }) =>
      request.put<any>('/mch/customer/service-type', data, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
  ),
  添加预约类型: createApiHooks(
    (data: { serviceName: string; status: string }) =>
      request.post<ApiResponse<string>>('/mch/customer/service-type', data, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
  ),
  异常订单列表处理: createApiHooks(
    (data: {
      abnormalId: string;
      uniqueCode: string;
      operatorId: string;
      operatorName: string;
    }) =>
      request.post<
        ApiResponse<{
          handleResult: '0' | '1' | '2' | '3'; // 处理结果。0 修改状态成功，1 查询失败可进行退费操作，2 查询异常可进行重发操作
        }>
      >('/intelligent/mch/intelligent/order/abnormal/check', data)
  ),
  异常订单列表处理重发: createApiHooks(
    (data: {
      abnormalId: string;
      uniqueCode: string;
      operateType: 'US' | 'AS';
      operatorId: string;
      operatorName: string;
    }) =>
      request.post<
        ApiResponse<{
          handleResult: '0' | '1' | '2' | '3'; // 处理结果。0 修改状态成功，1 查询失败可进行退费操作，2 查询异常可进行重发操作
        }>
      >('/intelligent/mch/intelligent/order/abnormal/handle', data)
  ),
  异常订单列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        sort?: string;
        hisId?: string;
        businessType?: string;
        refundStatus?: string;
        beginTime?: string;
        endTime?: string;
        abnormalTime?: string;
      }
    ) =>
      request.post<
        ListApiResponseData<{
          orderId: string;
          businessType: string;
          type: string;
          typeName: string;
          status: string;
          statusName: string;
          patientId: number;
          patientName: string;
          createTime: string;
          totalFee: number;
          payChannel: string;
          payChannelName: string;
          payMethod: string;
          payMethodName: string;
          doctorId: string;
          doctorName: string;
          deptId: string;
          deptName: string;
          operatorId: number;
          operatorName: string;
          operatorTime: string;
          refundSerialNo: string;
          paySerialNumber: string;
        }>
      >('/intelligent/mch/intelligent/order/abnormal', data)
  ),
  异常订单详情: createApiHooks((data: { abnormalOrderId: string }) =>
    request.get<
      ApiResponse<{
        orderId: string;
        businessType: string;
        type: string;
        typeName: string;
        status: string;
        statusName: string;
        patientId: number;
        patientName: string;
        createTime: string;
        totalFee: number;
        payChannel: string;
        payChannelName: string;
        payMethod: string;
        payMethodName: string;
        doctorId: string;
        doctorName: string;
        deptId: string;
        deptName: string;
        operatorId: number;
        operatorName: string;
        operatorTime: string;
        refundSerialNo: string;
        paySerialNumber: string;
      }>
    >(`/intelligent/mch/intelligent/order/abnormal/${data.abnormalOrderId}`)
  ),
  第三方挂号列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        queryInfo?: string;
        startDate?: string;
        endDate?: string;
        payedStartTime?: string;
        payedEndTime?: string;
        status?: string;
        payStatus?: string;
        tripartite?: string;
        payType?: string;
        refundStatus?: string;
      }
    ) =>
      request.get<
        ListApiResponseData<{
          id: string; // 记录id
          createTime: string; // 创建时间
          updateTime: string; // 更新时间
          hisId: string; // 医院id
          tripartite: string; // 对接三方
          patientId: string; // 就诊人id
          status: string; // 订单状态（U：初始预登记;L：锁号;S：预约成功;F：挂号失败;P：付款完成，调用医院支付接口中;A：付款&挂号成功;H：调用医院支付接口异常;Z：调用医院支付接口异常，重发次数超限;C：已取消;）
          payStatus: string; // 是否已支付（0-否 1-是）
          payType: string; // 支付方式
          payedTime: string; // 订单支付时间
          refundStatus: string; // 退款状态（0-没退款 1-有退款）
          deptId: string; // 科室id
          deptName: string; // 科室名称
          doctorId: string; // 医生id
          doctorName: string; // 医生名称
          scheduleId: string; // 号源id
          visitDate: string; // 就诊日期
          visitPeriod: string; // 上午-1;下午-2;晚上-3;全天-4;白天-5;中午-6;凌晨-7
          visitBeginTime: string; // 就诊区间 开始时间
          visitEndTime: string; // 就诊区间 结束时间
          hisOrderNo: string; // 医院订单no
          hisSerialNo: string; // 医院流水号
          serialNo: string; // 医院就诊序号
          hisRecepitNo: string; // 医院收据号
          totalFee: string; // 挂号总金额
          totalRealFee: string; // 实际支付金额
          patientType: string; // 就诊人类别（1-成人 2-儿童）
          patientName: string; // 就诊人名称
          patientIdType: string; // 就诊人证件id类型（1：二代身份证 2：港澳居民身份证 3：台湾居民身份证 4：护照 ）
          patientIdNo: string; // 就诊人证件号
          patientSex: string; // 就诊人性别
          patientAge: string; // 就诊人年龄
          patientMobile: string; // 就诊人电话号码
          patCardType: string; // 就诊卡类别
          patCardNo: string; // 就诊卡卡号
          patHisNo: string; // 患者在his的唯一id
          parentName: string; // 监护人姓名
          parentIdType: string; // 监护人证件类别
          parentIdNo: string; // 监护人证件id
          refundTag: string; // 退款原因标志(1：医院返回明确失败时，自动退款 2：患者主动取消时退款 3：人工发起的退款 4：医生停诊退款 5：超时未报到的退款 )
          cancelTime: string; // 订单取消时间
          cancelReason: string; // 取消原因
          payOrderId: string; // 支付订单号
          agtOrdNum: string; // 支付流水号
          selfFee: string; // 个人支付金额
        }>
      >('/mch/openapi/tripartite-register/', { params })
  ),
  特需订单列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        appointmentStatus?: string;
        appointmentDateStart?: string;
        appointmentDateEnd?: string;
        name?: string;
        regDateStart?: string;
        regDateEnd?: string;
        phone?: string;
        deptId?: string;
      }
    ) =>
      request.get<
        ListApiResponseData<{
          id: string;
          createTime: string;
          hisId: number;
          type: string;
          registerType: string;
          patientId: string;
          patientName: number;
          patientMobile: number;
          patientMobileCipher: string;
          patientSex: string;
          patientAge: string;
          parentIdNo: string;
          visitDate: string;
          visitBeginTime: string;
          visitEndTime: string;
          hisAreaName: string;
          deptName: string;
          doctorName: string;
          visitPosition: string;
          hisOrderNo: string;
          status: string;
          payChannel: string;
          payType: string;
          totalFee: string;
          isHandle: number;
        }>
      >('/intelligent/mch/register/specific/dept', { params })
  ),
  导出特需订单: createApiHooks(
    (
      params: ListApiRequestParams & {
        appointmentStatus?: string;
        appointmentDateStart?: string;
        appointmentDateEnd?: string;
        name?: string;
        regDateStart?: string;
        regDateEnd?: string;
        phone?: string;
        deptId?: string;
      }
    ) =>
      request.get<Blob>(
        '/intelligent/mch/register/specific/dept/exportDeptList',
        {
          responseType: 'blob',
          params
        }
      )
  ),
  特需科室列表: createApiHooks(() =>
    request.get<
      ApiResponse<
        Array<{
          hisId: number;
          hisDistrict: string;
          districtId: string;
          no: string;
          name: string;
          sortNo: string;
          employeeCount: string;
          initials: string;
          summary: string;
          skill: string;
          tel: string;
          img: string;
          pid: string;
          medicalDepartment: string;
          hospitalDeptNo: string;
          hospitalDeptName: string;
          standardDeptNo: string;
          standardDeptName: string;
          address: string;
          pathCode: string;
          status: string;
          createTime: string;
          updateTime: string;
          caty: string;
          catyName: string;
        }>
      >
    >('/intelligent/mch/register/specific/dept/mchGetDeptMainList')
  ),
  标记特需记录: createApiHooks((data: { id: string }) =>
    request.put<ApiResponse<any>>(
      `/intelligent/mch/register/specific/dept/updateRegisterOrder/${data?.id}`
    )
  ),
  问诊记录详情: createApiHooks((data: { orderId: string }) =>
    request.post<ApiResponse<OnlineOrderDetail>>('/mch/order/order/info', data)
  ),
  本地问诊记录详情: createApiHooks(
    (params: { orderId: string; hisId: string; targetHisId: string }) =>
      request.get<ApiResponse<OnlineOrderDetail>>(
        '/mch/order/ls-main/inquiry-order-detail',
        { params }
      )
  ),
  缴费记录详情: createApiHooks(
    (data: { orderId: string; uniqueCode: string }) =>
      request.post<
        ApiResponse<{
          agtOrdNum: null;
          bizName: 'appointment_register';
          bizType: '预约挂号缴费';
          deptName: '呼吸内科门诊';
          deptNo: null;
          doctorId: '164';
          doctorName: '刘朝元';
          doctorTitle: '主治医师';
          hisAreaName: '本院区';
          hisId: 40009;
          hisName: null;
          hisOrderNo: 'DHC210902175932000024';
          orderId: '1433368998233247745';
          orderTime: '2021-09-02T17:59:35';
          patCardNo: '**********';
          patientAge: string;
          patientId: '1216';
          patientIdNo: '500************401';
          patientIdType: 1;
          patientMobile: '132****8615';
          patientName: '测试患者';
          patientSex: 'F';
          payOrderId: 2109024000100014;
          payStatus: 0;
          payType: null;
          payedTime: null;
          platformId: 40009;
          platformSource: 7;
          refundOrders: {
            businessType: '2';
            createTime: '2021-09-06 02:49:10';
            deptId: '44';
            deptName: '眼科门诊';
            doctorId: '319';
            doctorName: '曹亚娜';
            extFields: null;
            hisId: 40009;
            id: 2109065000100001;
            operatorId: 0;
            operatorName: '系统退款';
            orderId: 2109064000100001;
            payType: 'weixin_wap';
            refundDesc: null;
            refundFee: 700;
            refundResultDesc: null;
            refundSerialNo: '50301509542021090612217324140';
            refundStatus: 'S';
            refundSuccessTime: '2021-09-06 10:49:12';
            sourceType: 3;
            type: null;
            updateTime: '2021-09-06 02:49:10';
            userId: 1818;
          }[];
          refundStatus: 'U' | 'W' | 'S' | 'F';
          registerType: 3;
          status: 'C';
          statusName: '未支付';
          totalFee: number;
          totalRealFee: 700;
          userId: 1814;
          visitBeginTime: '14:00';
          visitDate: '2021-09-08T00:00:00';
          visitEndTime: '14:30';
          visitPeriod: 2;
          toHisParam: string;
          toHisResult: string;
        }>
      >('/intelligent/mch/intelligent/order/detail', data)
  ),
  处方药品列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        sort?: string;
        hisId?: string;
        refundStatus?: string;
        beginTime?: string;
        beginTtargetHisIdime?: string;
        endTime?: string;
      }
    ) =>
      request.get<ListApiResponseData<MedicineListItem>>(
        '/mch/prescription/prescription/page',
        { params }
      )
  ),
  本地处方药品列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        sort?: string;
        hisId?: string;
        refundStatus?: string;
        beginTime?: string;
        targetHisId?: string;
        endTime?: string;
      }
    ) =>
      request.get<ListApiResponseData<MedicineListItem>>(
        '/mch/prescription/ls-main/prescription-order',
        { params }
      )
  ),
  更改处方打印状态: createApiHooks((params: { id?: number }) =>
    request.get<ApiResponse<null>>('/mch/prescription/prescription/printed', {
      params
    })
  ),
  查询处方状态枚举: createApiHooks(() =>
    request.get<
      ApiResponse<{
        prescriptionStatus: Array<{
          name: string;
          desc: string;
        }>;
      }>
    >('/common/prescription/enum/status')
  ),
  处方单发起邮寄: createApiHooks((params: { id?: number }) =>
    request.get<ApiResponse<null>>('/mch/prescription/prescription/shipping', {
      params
    })
  ),
  加号登记列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        createdAtBegin?: string; // 创建开始时间
        createdAtEnd?: string; // 创建结束时间
        addDateBegin?: string; // 加号登记开始时间
        addDateEnd?: string; // 加号登记结束时间
        id?: string; // 开单登记序号
        regStatus?: string; // 登记状态
        patName?: string; // 就诊人姓名
        pageNum?: number; // 当前页码
        numPerPage?: number; //  每页显示记录数
      }
    ) =>
      request.get<ListApiResponseData<AdditionalRegisterType>>(
        '/mch/inquiry/additionalRegister',
        { params }
      )
  ),

  加号登记详情: createApiHooks((id: string) =>
    request.get<ApiResponse<AdditionalRegisterType>>(
      `/mch/inquiry/additionalRegister/${id}`
    )
  ),
  处方药品详情: createApiHooks((params: { id?: string }) =>
    request.get<ApiResponse<MedicineDetailInfo>>(
      `/mch/prescription/prescription/prescription-detail/${params.id}`,
      { params }
    )
  ),
  本地处方药品详情: createApiHooks(
    (params: { id?: string; hisId: string; targetHisId?: string }) =>
      request.get<ApiResponse<MedicineDetailInfo>>(
        `/mch/prescription/ls-main/prescription-order-detail`,
        { params }
      )
  ),
  物流订单统计: createApiHooks(
    (params: {
      searchStartTime?: string; // 创建开始时间
      searchEndTime?: string; // 创建结束时间
      isExport?: string; // 登记状态
      pageNum?: number; // 当前页码
      numPerPage?: number; //  每页显示记录数
    }) =>
      request.get<ListApiResponseData<DeliveryListItem>>(
        `/mch/prescription/prescription/delivery/order`,
        {
          params
        }
      )
  ),
  物流订单统计导出: createApiHooks(
    (params: {
      searchStartTime?: string; // 创建开始时间
      searchEndTime?: string; // 创建结束时间
      isExport?: string; // 登记状态
      pageNum?: number; // 当前页码
      numPerPage?: number; //  每页显示记录数
    }) =>
      request.get<ListApiResponseData<DeliveryListItem>>(
        `/mch/prescription/prescription/delivery/order`,
        {
          params,
          responseType: 'blob'
        }
      )
  ),
  查询物流信息: createApiHooks((params: { mainOrderId?: string }) =>
    request.get<
      ApiResponse<{
        deliveryCode: '@word(2)-@natural(1000000, 9999999)'; // 运单号
        deliveryCompany: "@pick('EMS', 'SF')"; // 见枚举接口https://yapi.cqkqinfo.com/project/703/interface/api/45121返回的DeliveryEnum
        drugDeliveryOrder: {
          receiver: '@cname'; // 收货人
          phone: '@natural'; // 收货人电话
          province: '@cword'; // 收货人所在省/直辖市
          city: '@cword'; // 收货人所在市
          district: '@cword'; // 收货人所在区
          address: '@cword'; // 收货人所在详细地址
          status: '@pick(10, 30, 40, 50, 51, 99, 100)'; //快递状态 10新配送单，30医院已发药，40配送中，50患者签收，51系统自动签收，99已取消，100异常
        };
        lastTrackInfo: {
          // 最新物流信息
          opTime: '@datetime'; // 操作时间
          trackStatus: "@pick('PICKED', 'TRANSPORT', 'DELIVER', 'PENDING', 'RECEIVED')"; // 见枚举https://yapi.cqkqinfo.com/project/703/interface/api/45121返回的DeliveryStatusEnum
          opDesc: '@csentence'; // 操作详述
          opOrgProvName: '@cword'; // 操作网点省名
          opOrgCity: '@cword'; // 操作网点城市
          opOrgName: '@cword'; // 操作网点名称
          operatorName: '@cword'; // 操作员
        };
        trackInfoList: TrackInfoListItem[];
      }>
    >(`/mch/prescription/prescription/delivery/track-info`, { params })
  ),
  查询支付模式列表: createApiHooks((data: { hisId: string }) =>
    request.post<
      ApiResponse<{
        payMethods: { payMethod: string; name: string }[];
        payChannels: { payChannel: string; name: string }[];
      }>
    >('/intelligent/mch/intelligent/order/payMode/list', data, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  ),
  处方药品物流退款: createApiHooks(
    (data: {
      refundReason: string; //退款原因
      password: string; //用户登陆密码（(AES加密)）
      id: string; //处方id
    }) =>
      request.put<ApiResponse<any>>(
        '/mch/prescription/prescription/delivery/refund',
        data
      )
  ),
  处方药品退款2: createApiHooks(
    (data: {
      refundReason: string; //退款原因
      password: string; //用户登陆密码（(AES加密)）
      id: string; //处方id
    }) =>
      request.put<ApiResponse<any>>(
        '/mch/prescription/prescription/refund',
        data
      )
  ),
  缴费订单退款: createApiHooks(
    (data: {
      uniqueCode: string;
      orderId: string;
      refundFee: number;
      operatorId?: number;
      operatorName?: string;
      reason?: string;
      sourceType?: string;
    }) =>
      request.post<ApiResponse<any>>(
        '/intelligent/mch/intelligent/order/refund',
        data
      )
  ),
  处方药品退款: createApiHooks(
    (data: { id: string; hisId: string; refoundReason?: string }) =>
      request.put<ApiResponse<any>>(
        '/mch/prescription/prescription/refund',
        data
      )
  ),
  退费: createApiHooks((data: { hisId: string; orderId: string }) =>
    request.post<ApiResponse<Record<string, unknown>>>(
      '/mch/order/order/refund',
      data
    )
  ),
  派药: createApiHooks((data: { prescriptionId: string }) =>
    request.post<ApiResponse<Record<string, unknown>>>(
      '/mch/prescription/prescription/Q440',
      data
    )
  ),
  给药途径列表: createApiHooks(() => {
    return request
      .get<
        ApiResponse<
          {
            name: string;
            hisCode: string;
            platformCode: string;
          }[]
        >
      >(`/doctor/prescription/prescription/getDrugDeliveryRoute`)
      .then(res => {
        return {
          data: res?.data?.data?.map(item => ({
            text: item.name,
            value: item.hisCode
          }))
        };
      })
      .catch(res => {
        return {
          data: administrationList
        };
      });
  }),
  使用频次列表: createApiHooks(() => {
    return request
      .get<
        ApiResponse<
          {
            name: string;
            hisCode: string;
            platformCode: string;
          }[]
        >
      >(`/doctor/prescription/prescription/getFrequencyOfUse`)
      .then(res => {
        return {
          data: res?.data?.data?.map(item => ({
            text: item.name,
            value: item.hisCode
          }))
        };
      })
      .catch(res => {
        return {
          data: rateInfo
        };
      });
  }),
  export问诊订单列表: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${
      env.apiHost
    }/mch/order/order/page/export?${QueryString.stringify(p)}`;
    window.open(url);
  },
  export本地问诊订单列表: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${
      env.apiHost
    }/mch/order/ls-main/inquiry-order/export?${QueryString.stringify(p)}`;
    window.open(url);
  },
  export处方订单列表: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${
      env.apiHost
    }/mch/prescription/prescription/page/export?${QueryString.stringify(p)}`;
    window.open(url);
  },
  export本地处方订单列表: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${
      env.apiHost
    }/mch/prescription/ls-main/prescription-order/export?${QueryString.stringify(
      p
    )}`;
    window.open(url);
  },
  export支付数据: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${env.apiHost}/mch/order/export/pay?${QueryString.stringify(
      p
    )}`;
    window.open(url);
  },
  export退款数据: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${env.apiHost}/mch/order/export/refund?${QueryString.stringify(
      p
    )}`;
    window.open(url);
  },
  查询住院预约记录列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        queryInfo?: string;
        startDate?: string;
        endDate?: string;
        platformId: number | string;
      }
    ) =>
      request.get<ListApiResponseData<HospitalizationAppointment<any>>>(
        '/intelligent/mch/inpatient/reg/list',
        {
          params
        }
      )
  ),
  住院预约记录同意: createApiHooks((data: { id: string }) =>
    request.put<ApiResponse<any>>(
      '/intelligent/mch/inpatient/reg/confirm',
      data
    )
  ),
  住院预约记录取消: createApiHooks(
    (data: {
      id: string;
      cancelDate: string; //取消日期
      cancelReason: string; //取消原因
    }) =>
      request.put<ApiResponse<any>>(
        '/intelligent/mch/inpatient/reg/cancel',
        data
      )
  ),
  查询住院预约记录详情: createApiHooks(
    (params: { id: string | number; platformId: string }) =>
      request.get<
        ApiResponse<
          HospitalizationAppointment<{
            idNo: '5****************6'; //患者证件号
            idType: 1; //患者证件类型
            patCardNo: '**********'; //患者就诊卡号
            patHisNo: '**********'; //患者院内ID
            patientMobile: '151******93'; //患者联系电话
            patientName: '邓海军'; //患者姓名
            patientSex: 'M' | 'F'; //患者性别
            patientType: 0; //患者类型（0-成人；1-儿童）
            patientAge: '31岁'; //患者年龄
          }>
        >
      >(`/intelligent/mch/inpatient/reg/${params.id}`, { params })
  ),
  处方药品列表new: createApiHooks(
    (
      params: ListApiRequestParams & {
        sort?: string;
        hisId?: string;
        refundStatus?: string;
        beginTime?: string;
        beginTtargetHisIdime?: string;
        endTime?: string;
      }
    ) =>
      request.get<ListApiResponseData<MedicineListItem>>(
        '/mch/prescription/prescription/prescription-main-order/page',
        { params }
      )
  ),
  处方药品详情new: createApiHooks((params: { id?: string }) =>
    request.get<ApiResponse<MedicineDetailInfoNew>>(
      `/mch/prescription/prescription/prescription-main-order/${params.id}`,
      { params }
    )
  ),
  本地处方药品详情new: createApiHooks(
    (params: { id?: string; hisId: string; targetHisId?: string }) =>
      request.get<ApiResponse<MedicineDetailInfoNew>>(
        `/mch/prescription/ls-main/prescription-order-detail`,
        { params }
      )
  ),
  查询检验检查列表: createApiHooks(
    (params: {
      pageNum?: number; //当前页码
      numPerPage?: number; //分页大小
      patientName?: string; //就诊人名
      status?: string; // UNPAY/待缴费；PAYED/已缴费；ERROR / 缴费异常；INVALID / 已作废；TIMEOUT / 已超时；REFUND / 已退费
      dateFilterStart?: string; //查询开始时间
      dateFilterEnd?: string; //查询结束时间
      payChannel?: any; //办理渠道
      isExport?: 0 | 1; //是否导出，0-否，1-是
    }) =>
      request.get<ApiResponse<any>>('/mch/prescription/test-exam-main', {
        params
      })
  ),
  分页查询住院预约列表: createApiHooks(
    (params: {
      state?: number; //状态
      searchStartTime?: string | undefined; //开单时间（开始）
      searchEndTime?: string | undefined; //开单时间（截止）
      pageNum: number; //当前页码;
      numPerPage: number; //每页数量
      doctorName?: string; //开单医生名称
      deptName: string; //开单科室名称
      isExport?: 0 | 1; //是否导出，0否，1是，默认0
    }) =>
      request.get<ApiResponse<any>>('/mch/prescription/admission-apply', {
        params
      })
  ),

  查询检验检查订单详情: createApiHooks((params: { id: number }) =>
    request.get<ApiResponse<any>>(
      `/mch/prescription/test-exam-main/${params.id}`
    )
  ),
  查询住院预约详情: createApiHooks((params: { id: number }) =>
    request.get<ApiResponse<any>>(
      `/mch/prescription/admission-apply/${params.id}`
    )
  ),
  退费详情: createApiHooks(
    (params: {
      password: string;
      mainId: number;
      itemIdList: any[];
      reason: string;
    }) =>
      request.put<ApiResponse<any>>(
        `/mch/prescription/test-exam-main/refund`,
        params
      )
  ),
  exportHospitalizationList: (p: any) => {
    const url = `${
      env.apiHost
    }/mch/prescription/admission-apply?${QueryString.stringify({
      ...p,
      isExport: '1'
    })}`;
    window.open(url);
  },
  exportCheckList: (p: any) => {
    const url = `${
      env.apiHost
    }/mch/prescription/test-exam-main?${QueryString.stringify({
      ...p,
      isExport: '1'
    })}`;
    window.open(url);
  },

  更改配送信息: createApiHooks(
    (data: {
      mainOrderId: string;
      id: string;
      type: string;
      receiver: string;
      phone: string;
      province: string;
      city: string;
      district: string;
      address: string;
    }) =>
      request.post<ApiResponse<Record<string, unknown>>>(
        '/mch/prescription/prescription/prescription-main-order/changeDelivery',
        data,
        {
          headers: {
            Accept: 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/json; charset=UTF-8'
          }
        }
      )
  )
};
