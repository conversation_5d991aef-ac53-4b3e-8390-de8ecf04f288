import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiResponseData,
  ListApiRequestParams,
  ApiResponse1
} from '@apiHooks';
type Questionnaire = {
  id: '@natural'; //主键ID
  hisId: '@natural'; //机构ID
  title: '@ctitle'; //诊前问卷标题
  surveyKey: '@guid'; //问卷key, 问卷唯一标识
  surveyTitle: '@ctitle'; //问卷标题（关联的问卷的标题）
  validDateStart: '@Date'; //起点有效时间
  validDateEnd: '@Date'; //终点有效时间
  state: '@pick(0,1)'; //状态，0：停用，1：启用
  creatorName: '@cname'; //创建人
  creatorId: '@natural(10000,90000)'; //创建人id
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //修改时间
  depts: [
    //科室列表
    {
      deptId: '@natural(100000)'; //科室id，deptMain主键id
      deptName: '@cword(4)科'; //科室名称
      deptNo: '489302'; //科室编号
    }
  ];
  scenes: "@pick('REGISTER','OP_PAY')"; //REGISTER挂号，OP_PAY门诊缴费
};
type QuestionnaireEdit = {
  title: '@ctitle'; //诊前问卷标题
  surveyKey: '@guid'; //问卷key, 问卷唯一标识
  validDateStart: '@Date'; //起点有效时间
  validDateEnd: '@Date'; //终点有效时间
  state: '@pick(0,1)'; //状态，0：停用，1：启用
  deptIds: [1, 2, 3]; //科室id数组（主键id不是编号）
  scenes: "@pick('REGISTER','OP_PAY')"; //REGISTER挂号，OP_PAY门诊缴费
};
export default {
  分页查询诊前问卷详: createApiHooks(
    (
      params: ListApiRequestParams & {
        surveyKey?: string;
        deptId?: string;
        status?: string;
        searchStartTime?: string;
        searchEndTime?: string;
      }
    ) =>
      request.get<ListApiResponseData<Questionnaire>>(
        '/intelligent/mch/intelligent/pre-survey',
        {
          params
        }
      )
  ),
  新增: createApiHooks((data: QuestionnaireEdit) =>
    request.post<ApiResponse1<Questionnaire>>(
      `/intelligent/mch/intelligent/pre-survey`,
      data,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  编辑: createApiHooks((data: QuestionnaireEdit & { id: number }) =>
    request.put<ApiResponse1<Questionnaire>>(
      `/intelligent/mch/intelligent/pre-survey`,
      data,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  详情: createApiHooks((id: number | string) =>
    request.get<ApiResponse1<Questionnaire>>(
      `/intelligent/mch/intelligent/pre-survey/${id}`
    )
  ),
  诊前问卷状态切换: createApiHooks((id: number | string) =>
    request.put<ApiResponse1<any>>(
      `/intelligent/mch/intelligent/pre-survey/on-off/${id}`
    )
  ),
  调查问卷列表: createApiHooks((params: { hospitalId: string }) =>
    request.get<
      {
        id: number;
        publishKey: string; //问卷key
        publishUrl: string; //问卷地址
        title: string; //问卷题目
      }[]
    >(`/mch/survey/list`, { params })
  )
};
