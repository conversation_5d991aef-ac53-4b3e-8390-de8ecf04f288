import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  handleSubmit,
  LinkButton,
  TableList,
  useModal
} from 'parsec-admin';
import { Button, DatePicker, Radio, TreeSelect } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import useApi from '../api';
import React, { useMemo } from 'react';
import env from '@configs/env';
import { getTreeOptions } from '@pages/hospital/doctorDetailSmart/utils';
import useApi2 from '@pages/hospital/doctorSmart/api';
import moment from 'moment';
const { RangePicker } = DatePicker;
const scenes = {
  REGISTER: '挂号',
  OP_PAY: '门诊缴费'
};
const state = {
  0: '停用',
  1: '启用'
};

export default () => {
  const { data } = useApi.调查问卷列表({
    params: { hospitalId: env.hisId },
    needInit: !!env.hisId,
    initValue: []
  });
  const {
    data: {
      data: { recordList: deptListHisType }
    },
    loading: deptLoading
  } = useApi2.科室管理列表({
    params: { hisId: env.hisId, hisType: 2 },
    initValue: {
      data: {
        recordList: []
      }
    },
    needInit: !!env.hisId
  });
  const surveyList = useMemo(() => {
    if (data?.length) {
      return data.map(v => {
        return { label: v.title, value: v.publishKey };
      });
    }
    return [];
  }, [data]);
  const modalForm = useModal(({ id }) => {
    return {
      title: `${id ? '编辑' : '新增'}诊前问卷`,
      onSubmit: values => {
        const params = { ...values };
        if (params?.validDateStart?.length) {
          params.validDateStart = values.validDateStart[0].format('YYYY-MM-DD');
          params.validDateEnd = values.validDateStart[1].format('YYYY-MM-DD');
        }
        console.log(params);
        return handleSubmit(() =>
          id
            ? useApi.编辑.request({ ...params, id })
            : useApi.新增.request(params)
        );
      },
      items: [
        {
          label: '诊前问卷名称',
          name: 'title',
          required: true
        },
        {
          label: '适用科室',
          name: 'deptIds',
          required: true,
          render: (
            <TreeSelect
              showSearch={true}
              style={{ width: '100%' }}
              treeNodeFilterProp='label'
              placeholder='请选择所属科室'
              loading={deptLoading}
              treeCheckable={true}
              treeData={
                deptLoading ? undefined : getTreeOptions(deptListHisType)
              }
              showCheckedStrategy={'SHOW_CHILD'}
            />
          )
        },
        {
          label: '适用场景',
          name: 'scenes',
          render: <ArrSelect options={scenes} />,
          required: true
        },
        {
          label: '关联问卷',
          name: 'surveyKey',
          render: <ArrSelect options={surveyList} />,
          required: true
        },
        {
          label: '问卷状态',
          name: 'state',
          required: true,
          render: (
            <Radio.Group>
              <Radio value={0}>停用</Radio>
              <Radio value={1}>启用</Radio>
            </Radio.Group>
          )
        },
        {
          label: '有效时间',
          name: 'validDateStart',
          required: true,
          render: (
            <RangePicker placeholder={['请选择开始日期', '请选择结束时间']} />
          )
        }
      ]
    };
  });
  return (
    <TableList
      tableTitle={'诊前问卷列表'}
      showExpand={false}
      scroll={{ x: '120%' }}
      action={
        <Button icon={<PlusOutlined />} onClick={() => modalForm()}>
          添加
        </Button>
      }
      showTool={false}
      getList={({ pagination: { current = 1, pageSize = 10 }, params }) => {
        return useApi.分页查询诊前问卷详
          .request({
            ...params,
            pageNum: current,
            numPerPage: pageSize
          })
          .then(res => ({
            list: res?.data?.recordList ?? [],
            total: res?.data?.totalCount
          }));
      }}
      columns={[
        { title: 'ID', dataIndex: 'id', width: 80 },
        { title: '诊前问卷名称', dataIndex: 'title' },
        {
          title: '适用科室',
          dataIndex: 'depts',
          searchIndex: 'deptId',
          search: (
            <TreeSelect
              showSearch={true}
              style={{ width: '100%' }}
              treeNodeFilterProp='label'
              multiple={false}
              placeholder='请选择所属科室'
              loading={deptLoading}
              treeData={
                deptLoading ? undefined : getTreeOptions(deptListHisType)
              }
              showCheckedStrategy={'SHOW_CHILD'}
            />
          ),
          render: v => {
            if (v?.length) {
              return v.map(l => l.deptName)?.join(',');
            }
            return '-';
          }
        },
        {
          title: '适用场景',
          dataIndex: 'scenes',
          width: 100,
          render: v => scenes[v] || '-'
        },
        {
          title: '关联问卷',
          dataIndex: 'surveyTitle',
          searchIndex: 'surveyKey',
          search: <ArrSelect options={surveyList} />
        },
        {
          title: '有效期',
          render: (record: any) => {
            return record?.validDateStart && record?.validDateEnd
              ? record?.validDateStart + '至' + record?.validDateEnd
              : '-';
          }
        },
        {
          title: '状态',
          dataIndex: 'state',
          width: 80,
          search: <ArrSelect options={state} />,
          render: v => state[v] || '-'
        },
        { title: '创建日期', dataIndex: 'createTime' },
        {
          title: '操作',
          fixed: 'right',
          width: 110,
          render: record => {
            const { state, id } = record;
            const stateText = !state ? '启用' : '停用';
            return (
              <ActionsWrap>
                <LinkButton
                  onClick={() => {
                    modalForm({
                      ...record,
                      deptIds: (record.depts || []).map(l => l.deptId),
                      validDateStart: [
                        moment(record.validDateStart),
                        moment(record.validDateEnd)
                      ]
                    });
                  }}>
                  修改
                </LinkButton>
                <LinkButton
                  style={{ color: !state ? '#139f2b' : 'red' }}
                  onClick={() => {
                    actionConfirm(
                      () => useApi.诊前问卷状态切换.request(id),
                      stateText
                    );
                  }}>
                  {stateText}
                </LinkButton>
              </ActionsWrap>
            );
          }
        }
      ]}
    />
  );
};
