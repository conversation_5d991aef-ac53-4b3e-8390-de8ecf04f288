import React, { useState } from 'react';
import StyleComponent from './index.style';
import { Row, Col, Modal, Pagination, Spin, message } from 'antd';
import { comps, Comp } from '../Charts';
import useApi from '@pages/statistics/configure/api';

export default (props: {
  onSelect: (comp: Comp) => void;
  onClose: () => void;
  visible: boolean;
}) => {
  const [page, setPage] = useState(1);
  const [limit] = useState(8);
  const {
    data: { list, total },
    loading
  } = useApi.配置列表({
    initValue: {
      list: [],
      pageNum: 1
    },
    params: { page, limit },
    needInit: props.visible
  });
  return (
    <Modal
      confirmLoading={loading}
      width={800}
      title='选择图表'
      visible={props.visible}
      destroyOnClose={true}
      footer={false}
      maskClosable={false}
      onCancel={props.onClose}>
      <Spin spinning={loading}>
        <StyleComponent>
          <Row className='items' gutter={[16, 16]}>
            {list.map(item => {
              const comp = comps.find(
                x => x.chartId === item.chartId && x.id === item.id
              );
              return (
                <Col
                  span={6}
                  className='itemWrap'
                  key={item.id}
                  onClick={() => {
                    if (comp) {
                      props.onSelect(comp);
                    } else {
                      message.warning('图表未实现');
                    }
                  }}>
                  <div className='item'>
                    <div
                      className='img'
                      style={{
                        backgroundImage: comp
                          ? `url(${comp.thumbnail})`
                          : undefined
                      }}
                    />
                    <div className='name'>{item.chartName}</div>
                  </div>
                </Col>
              );
            })}
          </Row>
          <Row className='page'>
            <Pagination
              pageSize={limit}
              total={total}
              current={page}
              onChange={page => {
                setPage(page);
              }}
            />
          </Row>
        </StyleComponent>
      </Spin>
    </Modal>
  );
};
