import styled from 'styled-components';

export default styled.div`
  width: 100%;
  overflow: auto;
  > div {
    //height: calc(100vh - 80px);
    overflow: hidden;
    background: #fff;
    /* min-width: 1200px;*/
    > .btns {
      padding-top: 10px;
      > button {
        margin-left: 10px;
      }
    }
    > .react-grid-layout {
      user-select: none;
      > .react-grid-item {
        background: #fff;
        .anticon-close {
          position: absolute;
          right: 10px;
          top: 10px;
          cursor: pointer;
        }
        .board-item {
          overflow: auto;
          height: 100%;
          width: 100%;
          padding: 5px;
          > .loading {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
  }
`;
