import React, { useCallback, useEffect, useState } from 'react';
import { <PERSON><PERSON>, Row } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import RGL, { WidthProvider } from 'react-grid-layout';
import StyledComponent from './index.style';
import SelectCompModal from './SelectCompModal';
import { comps, Comp } from './Charts';
import permisstion from '@utils/permisstion';
import { v4 } from 'uuid';
import useApi, { LayoutComp } from './api';
import env from '@src/configs/env';

const ReactGridLayout = WidthProvider(RGL);

export default () => {
  const hisId = env.hisId;
  const [layout, setLayout] = useState<LayoutComp[]>([]);
  const { data, request } = useApi.读取配置({
    initValue: [],
    params: {
      hisId
    },
    needInit: !!hisId
  });
  useEffect(() => {
    setLayout(data || []);
  }, [data]);
  const handleLayoutChange = useCallback(
    (newLayout: LayoutComp[]) => {
      // 还原id
      newLayout.forEach(newItem => {
        const item = layout.find(x => x.i === newItem.i);
        if (item) {
          newItem.id = item.id;
          newItem.chartId = item.chartId;
        }
      });
      setLayout([...newLayout]);
    },
    [setLayout, layout]
  );
  const [viewMode, setViewMode] = useState<'view' | 'edit'>('view');
  const [isShowAddModal, setIsShowAddModal] = useState(false);
  const handleAdd = useCallback(
    (comp: Comp) => {
      // FIXME push第一个的时候有bug，大小变成了1
      layout.push({
        id: comp.id,
        chartId: comp.chartId,
        i: v4(),
        x: 0,
        y: 0,
        w: comp.defaultSizeW || 6,
        h: comp.defaultSizeH || 4
      });
      setLayout([...layout]);
      setIsShowAddModal(false);
    },
    [layout, setLayout]
  );
  const handleSave = useCallback(async () => {
    await useApi.保存配置.request({
      hisId,
      dashboard: JSON.stringify(layout)
    });
    await request();
    setViewMode('view');
  }, [setViewMode, hisId, layout, request]);
  const handleRmove = useCallback(
    (item: LayoutComp) => {
      const index = layout.findIndex(x => x.i === item.i);
      layout.splice(index, 1);
      setLayout([...layout]);
    },
    [layout, setLayout]
  );
  return (
    <StyledComponent>
      <div>
        {viewMode === 'view' && permisstion.canEditChartLayout && (
          <Row className='btns'>
            <Button onClick={() => setViewMode('edit')}>编辑</Button>
          </Row>
        )}
        {viewMode === 'edit' && (
          <Row className='btns'>
            <Button onClick={() => setIsShowAddModal(true)}>添加</Button>
            <Button onClick={handleSave}>保存</Button>
          </Row>
        )}
        {!env.smartFollowUp && (
          <ReactGridLayout
            layout={layout}
            onLayoutChange={handleLayoutChange}
            cols={30}
            rowHeight={50}
            isResizable={viewMode === 'edit'}
            isDraggable={viewMode === 'edit'}>
            {layout?.map(item => {
              const comp = comps.find(
                x => x.id === item.id && x.chartId === item.chartId
              );
              const Comp = comp?.comp; // jsx 组件要大写开头
              return (
                <div key={item.i} className='item'>
                  {Comp && <Comp id={comp?.id} chartId={comp?.chartId} />}
                  {viewMode === 'edit' && (
                    <CloseOutlined
                      onClick={() => {
                        handleRmove(item);
                      }}
                    />
                  )}
                </div>
              );
            })}
          </ReactGridLayout>
        )}

        <SelectCompModal
          visible={isShowAddModal}
          onClose={() => setIsShowAddModal(false)}
          onSelect={handleAdd}
        />
      </div>
    </StyledComponent>
  );
};
