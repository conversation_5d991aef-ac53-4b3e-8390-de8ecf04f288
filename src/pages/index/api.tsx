import { ApiResponse } from '@src/pages/login/apis';
import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';

import { Layout } from 'react-grid-layout';

export type LayoutComp = Layout & {
  id: number;
  chartId: number;
};

export default {
  保存配置: createApiHooks((data: { hisId: string; dashboard: string }) =>
    request.post<ApiResponse<Record<string, unknown>>>(
      '/mch/his/hospital-config/base-setting',
      {
        ...data
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  读取配置: createApiHooks((data: { hisId: string }) =>
    request
      .get<
        ApiResponse<{
          dashboard: string;
        }>
      >(`/mch/his/hospital-config/base-settings`, {
        params: data
      })
      .then(res => {
        let dashboard = [];
        try {
          dashboard = JSON.parse(res.data?.data?.dashboard || '');
        } catch (error) {}
        return {
          ...res,
          data: dashboard as LayoutComp[]
        };
      })
  )
};
