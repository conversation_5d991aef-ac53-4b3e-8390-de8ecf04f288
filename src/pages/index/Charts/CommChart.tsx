import React, { useCallback, useMemo, useState } from 'react';
import SelectDate from './SelectDate';
import StyledComponent from './CommChart.style';
import useApi from './api';
import echarts from 'echarts';
import Charts from '@components/charts';
import { ChartItem } from './d';

interface CommObjChartProps {
  id?: number;
  chartId?: number;
  type?: 'line' | 'bar';
  title: React.ReactNode;
}
export default (props: CommObjChartProps) => {
  const { title, id, type } = props;
  const [params, setParams] = useState<any>();

  const { data, loading } = useApi.公共统计接口({
    initValue: [],
    params: {
      configId: id as any,
      params
    },
    needInit: !!id && !!params
  });
  const mapOptions: echarts.EChartOption = useMemo(() => {
    const items = data as ChartItem[];
    // const items = (data[0] ? data : []) as ChartItem[]; //FIXME 数据没对齐，先用列表第一个
    return {
      color: [
        '#0984CE',
        '#47D99D',
        '#DEEB8E',
        '#C9D097',
        '#AEA9BA',
        '#6B3B9F',
        '#6551E2',
        '#273FC0',
        '#0984CE'
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
        }
        // formatter: (obj: any) => {
        //   const params = obj?.[0];
        //   if (!params) {
        //     return '';
        //   }
        //   const { marker, data, seriesName } = params;
        //   return `${seriesName}<br />${marker}${(data * 100).toFixed(2)}%`;
        // }
      },
      legend: {
        top: '3%',
        right: '3%'
      },
      grid: {
        top: '3%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: items[0]?.point.map(point => point.x) ?? [],
          axisTick: {
            alignWithLabel: true
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: function(value: number) {
              return value;
            }
          }
        }
      ],
      series: items.map(item => {
        return {
          name: item.title,
          type: type ?? 'line',
          areaStyle: {},
          smooth: true,
          barWidth: '60%',
          data: item.point?.map(point => point.y)
        };
      })
    };
  }, [data, type]);

  return (
    <StyledComponent>
      <div className='head'>
        <div className='title'>{title}</div>
        <div className='date'>
          <SelectDate
            onChange={useCallback(v => {
              setParams({
                beginTime: v[0].format('YYYY-MM-DD 00:00:00'),
                endTime: v[1].format('YYYY-MM-DD 23:59:59')
              });
            }, [])}
          />
        </div>
      </div>
      <div className='container'>
        <Charts
          loading={loading}
          option={mapOptions}
          style={{ width: '100%', height: '100%' }}
        />
      </div>
    </StyledComponent>
  );
};
