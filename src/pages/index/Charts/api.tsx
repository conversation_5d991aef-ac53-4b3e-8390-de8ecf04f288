import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ChartItem, ObjChatItem } from './d';
import moment from 'moment';
import env from '@configs/env';

/**
 * 获取common接口的默认参数值
 */
export const getInitParams = () => {
  const hisId = env.hisId;
  return {
    hisId,
    beginTime: moment('20000101').format('YYYY-MM-DD 00:00:00'),
    endTime: moment().format('YYYY-MM-DD 23:59:59')
  };
};

export default {
  获取图表配置详情: createApiHooks((data: { id: string }) =>
    request.get<any>(`/mch/his/statistics/config/${data.id}`)
  ),
  公共统计接口: createApiHooks((data: { configId: number; params?: any }) => {
    data.params = {
      ...getInitParams(),
      ...(data.params || {})
    };
    return request.post<Array<ChartItem | ObjChatItem>>(
      `/mch/his/statistics/common`,
      data,
      {
        headers: {
          Accept: 'application/json, text/javascript, */*; q=0.01',
          'Content-Type': 'application/json; charset=UTF-8'
        }
      }
    );
  })
};
