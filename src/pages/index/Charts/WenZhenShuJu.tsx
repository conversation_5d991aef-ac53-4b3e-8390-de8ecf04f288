import React from 'react';
import CommObjChart from './CommObjChart';
import { ChartComponent } from './index';
const Comp: ChartComponent = props => {
  return (
    <CommObjChart
      {...props}
      icon={require('./images/wzsj.png')}
      title='问诊数据'
      items={[
        { filedName: 'inquiry', title: '问诊量', unit: '次' },
        {
          filedName: 'replyRate',
          title: '回复率',
          unit: '%',
          handleFileNameValue: val => (val * 100).toFixed(2)
        },
        {
          filedName: 'praiseRate',
          title: '好评率',
          unit: '%',
          handleFileNameValue: val => (val * 100).toFixed(2)
        }
      ]}
    />
  );
};
export default Comp;
