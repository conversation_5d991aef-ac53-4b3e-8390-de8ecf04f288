import React, { useCallback, useMemo, useState } from 'react';
import SelectDate from './SelectDate';
import StyledComponent from './CommChart.style';
import useApi from './api';
import echarts from 'echarts';
import Charts from '@components/charts';
import styled from 'styled-components';
import { ChartItem } from './d';

interface CommObjChartProps {
  id?: number;
  chartId?: number;
}
export default (props: CommObjChartProps) => {
  const { id } = props;
  const [params, setParams] = useState<any>();

  const { data, loading } = useApi.公共统计接口({
    initValue: [],
    params: {
      configId: id as any,
      params
    },
    needInit: !!id && !!params
  });

  function JudeTypeIsChartItem(item: any): item is ChartItem {
    return Boolean(item?.point);
  }

  const mapOptions: echarts.EChartOption = useMemo(() => {
    const items = (data[0] ? data : []) as ChartItem[];
    return {
      color: ['#D5D5D5', '#A0DDD5', '#558AD1'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        top: 30,
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      legend: {
        top: 0,
        right: '3%',
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12
      },
      xAxis: [
        {
          type: 'category',
          data: items[0]?.point.map(point => point.x) ?? []
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: function(value: number) {
              return value;
            }
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: items.slice(0, 3).map(item => {
        return {
          name: item.title,
          type: 'line',
          smooth: true,
          data: item.point?.map(point => (point.y / 100).toFixed(2))
        };
      })
    };
  }, [data]);

  const hasListData = (index: number) => {
    const item = data?.[index];
    return JudeTypeIsChartItem(item) && item?.point.length > 0;
  };

  const typeList = useMemo(() => {
    const res: { x: string; y: number }[] = [];
    if (JudeTypeIsChartItem(data?.[4]) && data?.[4]?.point) {
      data.slice(4).forEach(item => {
        if (JudeTypeIsChartItem(item)) {
          item.point.forEach(subItem => {
            res.push(subItem);
          });
        }
      });
    }
    return res;
  }, [data]);

  const typePieOptions: echarts.EChartOption = {
    color: hasListData(4)
      ? ['#2773D1', '#674CD9', '#5BADD0', '#7DA6E7', '#7CCFD0', '#8772E1']
      : ['#ccc'],
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)'
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      icon: 'circle',
      itemWidth: 12,
      itemHeight: 12,
      right: 10,
      top: 20,
      bottom: 20,
      data: typeList.length > 0 ? typeList.map(item => item.x) : ['暂无数据']
    },
    series: [
      {
        name: '业务类型占比',
        type: 'pie',
        radius: '80%',
        left: '-30%',
        label: {
          show: true,
          position: 'inner',
          formatter: hasListData(4) ? '{c}' : ''
        },
        data:
          typeList.length > 0
            ? typeList.map(item => ({
                value: item.y,
                name: item.x
              }))
            : [{ value: 0, name: '暂无数据' }],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  const channelPieOptions: echarts.EChartOption = {
    color: hasListData(3)
      ? ['#2773D1', '#674CD9', '#5BADD0', '#7DA6E7', '#7CCFD0', '#8772E1']
      : ['#ccc'],
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)'
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      icon: 'circle',
      itemWidth: 12,
      itemHeight: 12,
      right: 10,
      top: 20,
      bottom: 20,
      data: JudeTypeIsChartItem(data?.[3])
        ? data?.[3]?.point.map(item => item.x)
        : ['暂无数据']
    },
    series: [
      {
        name: '支付渠道占比',
        type: 'pie',
        left: '-30%',
        radius: ['30%', '65%'],
        center: ['50%', '40%'],
        label: {
          show: true,
          formatter: '{b}: {c}'
        },
        data:
          JudeTypeIsChartItem(data?.[3]) && data?.[3].point.length > 0
            ? data?.[3]?.point.map(item => ({
                value: item.y,
                name: item.x
              }))
            : [{ value: 0, name: '暂无数据' }],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  return (
    <Wrap>
      <div>
        <StyledComponent hideBorder>
          <div className='head'>
            <div className='title'>随访反馈</div>
            <div className='date'>
              <SelectDate
                onChange={useCallback(v => {
                  console.log('TODO', v);
                  setParams({
                    beginTime: v[0].format('YYYY-MM-DD 00:00:00'),
                    endTime: v[1].format('YYYY-MM-DD 23:59:59')
                  });
                }, [])}
              />
            </div>
          </div>
          <div className='container'>
            <Charts
              loading={loading}
              option={mapOptions}
              style={{ width: '100%', height: '100%' }}
            />
          </div>
        </StyledComponent>
      </div>
      <div>
        <div>
          <StyledComponent hideBorder>
            <div className='head'>
              <div className='title'>业务类型占比</div>
            </div>
            <div className='container'>
              <Charts
                loading={loading}
                option={typePieOptions}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          </StyledComponent>
        </div>
        <div>
          <StyledComponent hideBorder>
            <div className='head'>
              <div className='title'>支付渠道占比</div>
            </div>
            <div className='container'>
              <Charts
                loading={loading}
                option={channelPieOptions}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          </StyledComponent>
        </div>
      </div>
    </Wrap>
  );
};

const Wrap = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background-color: #fff;
  justify-content: space-between;
  border: 1px solid #e2e2e2;
  > div:nth-child(1) {
    width: 60%;
    height: 100%;
  }
  > div:nth-child(2) {
    width: 38%;
    height: 100%;
    > div:nth-child(1) {
      width: 100%;
      height: 50%;
    }
    > div:nth-child(2) {
      width: 100%;
      height: 50%;
    }
  }
`;
