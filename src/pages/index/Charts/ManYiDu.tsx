import React, { useCallback, useMemo, useState } from 'react';
import SelectDate from './SelectDate';
import StyledComponent from './CommChart.style';
import useApi from './api';
import echarts from 'echarts';
import Charts from '@components/charts';
import { ChartItem } from './d';

interface CommObjChartProps {
  id?: number;
  chartId?: number;
}
export default (props: CommObjChartProps) => {
  const { id } = props;
  const [params, setParams] = useState<any>();

  const { data, loading } = useApi.公共统计接口({
    initValue: [],
    params: {
      configId: id as any,
      params
    },
    needInit: !!id && !!params
  });

  const mapOptions: echarts.EChartOption = useMemo(() => {
    const items = data as ChartItem[];
    // const items = (data[0] ? data : []) as ChartItem[]; //FIXME 数据没对齐，先用列表第一个
    return {
      color: ['#4F8BCF'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        top: '3%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: items[0]?.point.map(point => point.x) ?? [],
          axisTick: {
            alignWithLabel: true
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: function(value: number) {
              return value;
            }
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: items.map(item => {
        return {
          name: item.title,
          type: 'bar',
          areaStyle: {},
          smooth: true,
          barWidth: '30%',
          data: item.point?.map(point => point.y)
        };
      })
    };
  }, [data]);

  return (
    <StyledComponent>
      <div className='head'>
        <div className='title'>满意度</div>
        <div className='date'>
          <SelectDate
            onChange={useCallback(v => {
              setParams({
                beginTime: v[0].format('YYYY-MM-DD 00:00:00'),
                endTime: v[1].format('YYYY-MM-DD 23:59:59')
              });
            }, [])}
          />
        </div>
      </div>
      <div className='container'>
        <Charts
          loading={loading}
          option={mapOptions}
          style={{ width: '100%', height: '100%' }}
        />
      </div>
    </StyledComponent>
  );
};
