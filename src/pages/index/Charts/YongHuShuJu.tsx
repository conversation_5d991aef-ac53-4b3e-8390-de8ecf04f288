import React from 'react';
import CommObjChart from './CommObjChart';
import { ChartComponent } from './index';
const Comp: ChartComponent = props => {
  return (
    <CommObjChart
      {...props}
      icon={require('./images/yhsj.png')}
      title='用户数据'
      items={[
        { filedName: 'visitor', title: '访问人数', unit: '人' },
        { filedName: 'resiger', title: '注册人数', unit: '人' },
        { filedName: 'inquiryUser', title: '就诊人数', unit: '人' }
      ]}
    />
  );
};
export default Comp;
