import React from 'react';
import CommObjChart from './CommObjChart';
import { ChartComponent } from './index';
const Comp: ChartComponent = props => {
  return (
    <CommObjChart
      {...props}
      icon={require('./images/jysj.png')}
      title='业务数据'
      items={[
        {
          filedName: 'transactionAmount',
          title: '业务点击量',
          unit: '',
          handleFileNameValue: val => val * 3
        },
        { filedName: 'transactionAmount', title: '业务量', unit: '笔' },
        {
          filedName: 'transactionMoney',
          title: '业务金额',
          unit: '元',
          handleFileNameValue: val => (val / 100).toFixed(2)
        }
      ]}
    />
  );
};
export default Comp;
