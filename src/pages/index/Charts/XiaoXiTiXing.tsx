import React from 'react';
import CommObjChart from './CommObjChart';
import { ChartComponent } from './index';
const Comp: ChartComponent = props => {
  return (
    <CommObjChart
      {...props}
      icon={require('./images/jysj.png')}
      title='消息提醒'
      items={[
        {
          filedName: 'transactionMoney',
          title: '今日任务数',
          unit: '',
          handleFileNameValue: () => '14/167'
        },
        {
          filedName: 'transactionAmount',
          title: '异常任务',
          unit: '',
          handleFileNameValue: () => '10/17'
        }
      ]}
    />
  );
};
export default Comp;
