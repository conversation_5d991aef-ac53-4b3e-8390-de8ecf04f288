import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './YongHuShuJu';
import <PERSON><PERSON>hi<PERSON>huJu from './YiShiShuJu';
import <PERSON><PERSON><PERSON>ShuJu from './WenZhenShuJu';
import ChuFangShuJu from './ChuFangShuJu';
import <PERSON>aoYiShuJu from './JiaoYiShuJu';
import YongHuTongJi from './YongHuTongJi';
import ChuFangTongJi from './ChuFangTongJi';
import JiaoYiTongJi from './JiaoYiTongJi';
import <PERSON><PERSON>henTongJi from './WenZhenTongJi';
import OperationGuide from './OperationGuide';
// import JianKangXuanJiaoChart from './JianKangXuanJiaoChart';
// import JianKangXuanJiao from './JianKangXuanJiao';
// import KeShiSuiFangChart from './KeShiSuiFangChart';
// import KeShiSuiFang from './KeShiSuiFang';
// import ManYiDu from './ManYiDu';
// import SuiFangFan<PERSON>ui from './SuiFangFanKui';
// import SuiFangOperationGuide from './SuiFangOperationGuide';
// import SuiFangTongJi from './SuiFangTongJi';
// import XiaoXiTiXing from './XiaoXiTiXing';
// import XiaoXiTiXingCharts from './XiaoXiTiXingCharts';
// import YiHuanGouTong from './YiHuanGouTong';
// import YiHuanHuDong from './YiHuanHuDong';
import YuanJiSuiFang from './YuanJiSuiFang';
export type ChartComponent = React.FunctionComponent<{
  id?: number; // 没有id表示是预览模式
  chartId?: number; // 本地图表代码的id
}>;
export interface Comp {
  id: number;
  chartId: number;
  name: string;
  thumbnail: string;
  defaultSizeW?: number;
  defaultSizeH?: number;
  comp: ChartComponent;
}
export const comps: Comp[] = [
  {
    id: 43,
    chartId: 5,
    name: '用户数据',
    thumbnail: require('./images/thumbnail/yonghushuju.jpg'),
    defaultSizeW: 6,
    defaultSizeH: 4,
    comp: YonghuShuJu
  },
  {
    id: 66,
    chartId: 5,
    name: '院级随访',
    thumbnail: require('./images/thumbnail/yonghushuju.jpg'),
    defaultSizeW: 6,
    defaultSizeH: 4,
    comp: YuanJiSuiFang
  },
  {
    id: 41,
    chartId: 4,
    name: '医师数据',
    thumbnail: require('./images/thumbnail/yishishuju.jpg'),
    defaultSizeW: 6,
    defaultSizeH: 4,
    comp: YiShiShuJu
  },
  {
    id: 33,
    chartId: 2,
    name: '问诊数据',
    thumbnail: require('./images/thumbnail/wenzhenshuju.jpg'),
    defaultSizeW: 6,
    defaultSizeH: 4,
    comp: WenZhenShuJu
  },
  {
    id: 45,
    chartId: 7,
    name: '处方数据',
    thumbnail: require('./images/thumbnail/chufangshuju.jpg'),
    defaultSizeW: 6,
    defaultSizeH: 4,
    comp: ChuFangShuJu
  },
  {
    id: 47,
    chartId: 9,
    name: '交易数据',
    thumbnail: require('./images/thumbnail/jiaoyishuju.jpg'),
    defaultSizeW: 6,
    defaultSizeH: 4,
    comp: JiaoYiShuJu
  },
  {
    id: 44,
    chartId: 6,
    name: '用户统计',
    thumbnail: require('./images/thumbnail/yonghutongji.jpg'),
    defaultSizeW: 8,
    defaultSizeH: 9,
    comp: YongHuTongJi
  },
  {
    id: 46,
    chartId: 8,
    name: '处方统计',
    thumbnail: require('./images/thumbnail/chufangtongji.jpg'),
    defaultSizeW: 8,
    defaultSizeH: 9,
    comp: ChuFangTongJi
  },
  {
    id: 50,
    chartId: 11,
    name: '问诊统计',
    thumbnail: require('./images/thumbnail/wenzhentongji.jpg'),
    defaultSizeW: 8,
    defaultSizeH: 9,
    comp: WenZhenTongJi
  },
  {
    id: 48,
    chartId: 10,
    name: '交易统计',
    thumbnail: require('./images/thumbnail/jiaoyitongji.jpg'),
    defaultSizeW: 8,
    defaultSizeH: 9,
    comp: JiaoYiTongJi
  },
  {
    id: 59,
    chartId: 13,
    name: '操作指南',
    thumbnail: require('./images/thumbnail/caozuozhinan.jpg'),
    defaultSizeW: 8,
    defaultSizeH: 9,
    comp: OperationGuide
  }
];
