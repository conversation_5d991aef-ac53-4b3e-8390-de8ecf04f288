import React, { useCallback, useMemo, useState } from 'react';
import SelectDate from './SelectDate';
import StyledComponent from './CommChart.style';
import useApi from './api';
import echarts from 'echarts';
import Charts from '@components/charts';
import { ChartItem } from './d';

interface CommObjChartProps {
  id?: number;
  chartId?: number;
  type?: 'line' | 'bar';
}
export default (props: CommObjChartProps) => {
  const { id, type } = props;
  const [params, setParams] = useState<any>();

  const { data, loading } = useApi.公共统计接口({
    initValue: [],
    params: {
      configId: id as any,
      params
    },
    needInit: !!id && !!params
  });
  const mapOptions: echarts.EChartOption = useMemo(() => {
    const items = data as ChartItem[];
    // const items = (data[0] ? data : []) as ChartItem[]; //FIXME 数据没对齐，先用列表第一个
    return {
      color: ['#D2E4F5', '#9ADDD6', '#4F8BCF'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        top: 0,
        right: '3%',
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12
      },
      grid: {
        top: 30,
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: items[0]?.point.map(point => point.x) ?? [],
          axisTick: {
            alignWithLabel: true
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: function(value: number) {
              return value;
            }
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: items.map(item => {
        return {
          name: item.title,
          type: type ?? 'line',
          areaStyle: {},
          smooth: true,
          barWidth: '60%',
          data: item.point?.map(point => point.y)
        };
      })
    };
  }, [data, type]);

  return (
    <StyledComponent>
      <div className='head'>
        <div className='title'>随访统计</div>
        <div className='date'>
          <SelectDate
            hideToday
            onChange={useCallback(v => {
              setParams({
                beginTime: v[0].format('YYYY-MM-DD 00:00:00'),
                endTime: v[1].format('YYYY-MM-DD 23:59:59')
              });
            }, [])}
          />
        </div>
      </div>
      <div className='container'>
        <Charts
          loading={loading}
          option={mapOptions}
          style={{ width: '100%', height: '100%' }}
        />
      </div>
    </StyledComponent>
  );
};
