import React from 'react';
import CommObjChart from './CommObjChart';
import { ChartComponent } from './index';
const Comp: ChartComponent = props => {
  return (
    <CommObjChart
      {...props}
      icon={require('./images/jysj.png')}
      title='院级随访'
      items={[
        {
          filedName: 'transactionMoney',
          title: '今日任务数',
          unit: '',
          handleFileNameValue: () => '2/5'
        },
        {
          filedName: 'transactionAmount',
          title: '异常任务',
          unit: '',
          handleFileNameValue: () => '0/0'
        }
      ]}
    />
  );
};
export default Comp;
