import React from 'react';
import MyTableList from '@components/myTableList';
import useApi, { OperType } from '../apis';
import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  CheckboxGroup,
  LinkButton,
  useModal,
  handleSubmit,
  useTableRowSelect
} from 'parsec-admin';
import { Button } from 'antd';
import { hospitalLevel } from '@pages/hospital/info/apis';
import env from '@src/configs/env';

export default () => {
  const hisId = env.hisId;
  const showModal = useModal({
    onSubmit: values => {
      const { level, auditMethod, id } = values;
      return handleSubmit(() =>
        useApi[id ? '修改转诊医院' : '添加转诊医院'].request({
          ...values,
          hisId,
          auditMethod: auditMethod.length === 2 ? 3 : auditMethod[0],
          levelName: hospitalLevel.find(({ value }) => value === level)?.label
        })
      );
    },
    title: '添加 / 编辑医院',
    items: [
      {
        name: 'id',
        render: false
      },
      {
        label: '医院名称',
        name: 'name',
        required: true
      },
      {
        label: '医院等级',
        name: 'level',
        required: true,
        render: (
          <ArrSelect
            options={hospitalLevel.reverse().map(({ label, value }) => ({
              value,
              children: label
            }))}
          />
        )
      },
      {
        label: '审核方式',
        required: true,
        name: 'auditMethod',
        render: (
          <CheckboxGroup
            options={[
              { value: '1', label: '监管端' },
              { value: '2', label: '医生工作台' }
            ]}
          />
        )
      }
    ]
  });
  return (
    <MyTableList
      action={<Button onClick={() => showModal()}>添加医院</Button>}
      {...useTableRowSelect([
        {
          actionText: '批量停用',
          onClick: ids =>
            useApi.转诊医院批量修改状态.request({
              ids: ids.join(),
              hisId,
              operType: OperType.停用
            })
        },
        {
          actionText: '批量启用',
          onClick: ids =>
            useApi.转诊医院批量修改状态.request({
              ids: ids.join(),
              hisId,
              operType: OperType.启用
            })
        },
        {
          actionText: '批量删除',
          danger: true,
          onClick: ids =>
            useApi.转诊医院批量修改状态.request({
              ids: ids.join(),
              hisId,
              operType: OperType.删除
            })
        }
      ])}
      columns={[
        {
          title: '名称',
          dataIndex: 'name',
          search: true
        },
        {
          title: '等级',
          dataIndex: 'levelName'
        },
        {
          title: '状态',
          dataIndex: 'status',
          render: v => (v === 1 ? '启用' : v === 2 && '停用'),
          search: (
            <ArrSelect
              options={[
                { value: 1, children: '启用' },
                { value: 2, children: '停用' }
              ]}
            />
          )
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          render: record => {
            const actionText = record.status === 1 ? '停用' : '启用';
            return (
              <ActionsWrap>
                <LinkButton
                  onClick={() =>
                    actionConfirm(
                      () =>
                        useApi.转诊医院批量修改状态.request({
                          ids: record.id,
                          hisId,
                          operType:
                            record.status === 1 ? OperType.停用 : OperType.启用
                        }),
                      actionText
                    )
                  }>
                  {actionText}
                </LinkButton>
                <LinkButton
                  onClick={() =>
                    showModal({
                      ...record,
                      auditMethod:
                        record.auditMethod === '3'
                          ? ['1', '2']
                          : [record.auditMethod]
                    })
                  }>
                  编辑
                </LinkButton>
                <LinkButton
                  onClick={() =>
                    actionConfirm(
                      () =>
                        useApi.转诊医院批量修改状态.request({
                          ids: record.id,
                          hisId,
                          operType: OperType.删除
                        }),
                      '删除'
                    )
                  }>
                  删除
                </LinkButton>
              </ActionsWrap>
            );
          }
        }
      ]}
      getList={({ pagination: { current }, params }) => {
        return useApi.分页查询医院信息.request({
          page: current,
          limit: 10,
          ...{ ...params, hospitalId: hisId }
        });
      }}
    />
  );
};
