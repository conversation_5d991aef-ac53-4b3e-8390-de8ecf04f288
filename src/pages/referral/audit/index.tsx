import React, { useRef } from 'react';
import MyTableList from '@components/myTableList';
import useApi from '../apis';
import Tmpl from './tmpl';
import Html2Pdf from './html2pdf';
import { useSessionStorage } from 'react-use';
import { ArrSelect, LinkButton, DayRangePicker, useModal } from 'parsec-admin';
import { useHistory } from 'react-router-dom';
import env from '@src/configs/env';

export default () => {
  const hisId = env.hisId;
  const docTableRef = useRef<HTMLElement | null>(null);
  const switchModalVisible = useModal(
    ({ type, hospitalName, data }) => ({
      width: 940,
      children: (
        <div style={{ width: 880, margin: '0 auto' }}>
          <div ref={ref => (docTableRef.current = ref)}>
            <Tmpl type={type} hospitalName={hospitalName || ''} data={data} />
          </div>
        </div>
      ),
      okText: '打印',
      onSubmit: () =>
        new Promise((_, reject) => {
          Html2Pdf(docTableRef.current, '患者交接PDF');
          reject();
        })
    }),
    []
  );
  const {
    data: { data: hospital }
  } = useApi.列出医院ID和医院名称({
    params: { hisId },
    initValue: {
      data: []
    }
  });
  const {
    data: { data: dept }
  } = useApi.列出科室ID和科室名称({
    params: { hisId },
    initValue: {
      data: []
    }
  });
  const [doctor] = useSessionStorage<any>('doctor');
  const history = useHistory();
  return (
    <MyTableList
      searchFormProps={{
        initialValues: {
          state: '',
          applyType: '1'
        }
      }}
      columns={[
        {
          title: '患者姓名',
          dataIndex: 'patientName'
        },
        {
          title: '科室',
          dataIndex: 'deptName',
          searchIndex: 'deptId',
          search: (
            <ArrSelect
              options={
                dept?.map(({ name, id }) => ({
                  value: id,
                  children: name
                })) || []
              }
            />
          )
        },
        {
          title: '医生',
          dataIndex: 'doctorName'
        },
        {
          title: '转出医院',
          dataIndex: 'hisName'
        },
        {
          title: '转入医院',
          dataIndex: 'hospitalName',
          searchIndex: 'hospitalId',
          search: (
            <ArrSelect
              options={
                hospital?.map(({ name, id }) => ({
                  value: id,
                  children: name
                })) || []
              }
            />
          )
        },
        {
          title: '转入/转出',
          dataIndex: 'category',
          searchIndex: 'category',
          render: false,
          searchSort: 1,
          search: (
            <ArrSelect
              allowClear={false}
              options={[
                { value: '2', children: '转入' },
                { value: '1', children: '转出' }
              ]}
            />
          )
        },
        {
          title: '状态',
          dataIndex: 'status',
          searchIndex: 'state',
          render: v =>
            v === '1'
              ? '审核中'
              : v === '2'
              ? '转诊成功'
              : v === '3'
              ? '被驳回'
              : '取消转诊',
          searchSort: 1,
          search: (
            <ArrSelect
              allowClear={false}
              options={[
                { value: '', children: '全部' },
                { value: '1', children: '审核中' },
                { value: '2', children: '转诊成功' },
                { value: '3', children: '被驳回' },
                { value: '4', children: '取消转诊' }
              ]}
            />
          )
        },
        {
          title: '申请时间',
          dataIndex: 'createTime',
          searchIndex: ['startDate', 'endDate'],
          search: <DayRangePicker />
        },
        {
          title: '关键词',
          dataIndex: 'vagueName',
          search: true,
          render: false
        },
        {
          title: '操作',
          render: record => (
            <>
              <LinkButton
                style={{ marginRight: '10px' }}
                onClick={() => {
                  history.push(`/referral/audit/${record.id}`);
                }}>
                查看
              </LinkButton>
              {record.status === '2' && (
                <LinkButton
                  onClick={() =>
                    useApi.获取转诊申请详情
                      .request({ id: record.id, hisId: hisId })
                      .then(({ data }) =>
                        switchModalVisible({
                          type: data.fromHisName === '重庆医科大学附属儿童医院',
                          data
                        })
                      )
                  }>
                  转诊单
                </LinkButton>
              )}
            </>
          )
        }
      ]}
      getList={({ pagination: { current }, params }) => {
        return useApi.分页查询申请信息.request({
          page: current,
          limit: 10,
          category: 1,
          doctorId: doctor.id,
          doctorName: doctor.name,
          ...params
        });
      }}
    />
  );
};
