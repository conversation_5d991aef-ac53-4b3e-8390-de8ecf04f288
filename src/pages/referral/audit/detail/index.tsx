import React, { useMemo } from 'react';
import { CardLayout, FormDescriptions, FixedFormActions } from 'parsec-admin';
import { Steps, Space, Button, Upload, StepProps } from 'antd';
import useApi from '../../apis';
import { useParams } from 'react-router-dom';
import { useHistory } from 'react-router';
import moment from 'moment';
import env from '@src/configs/env';

const { Step } = Steps;

export default () => {
  const { id }: any = useParams<any>();
  const hisId = env.hisId;
  const {
    data: { data: { auditList } = {} },
    loading: loading1
  } = useApi.获取审核流程信息({
    params: { applyId: id, hisId },
    initValue: {
      data: { auditList: [] }
    }
  });
  const history = useHistory();
  const {
    data: { data: auditRecord },
    loading: loading2
  } = useApi.获取转诊申请详情({
    params: { id, hisId },
    initValue: {
      data: {}
    }
  });
  const current = +(auditRecord?.auditProcess || '1') - 1;

  const isFail = auditRecord.status === '3';

  const innerAuditList = useMemo(() => {
    const list: Array<StepProps & { key: string }> = [
      {
        key: '1',
        title: '转出医院科室审核',
        status: 'wait'
      },
      {
        key: '2',
        title: '转出医院双转办审核',
        status: 'wait'
      },
      {
        key: '3',
        title: '转入医院双转办审核',
        status: 'wait'
      },
      {
        key: '4',
        title: '转入医院科室审核',
        status: 'wait'
      },
      {
        key: '5',
        title: '转入医院科室医生接收',
        status: 'wait'
      },
      {
        key: '6',
        title: '完成',
        status: 'wait'
      }
    ];
    const current = parseInt(auditRecord.auditProcess) - 1;

    list.forEach((item, index) => {
      if (index < current) {
        item.status = 'finish';
      }
      if (index === current) {
        item.status = 'process';
        if (auditRecord.status === '3') {
          item.status = 'error';
          const e = auditList?.find(x => x.status === '3');
          if (e?.remarker) {
            item.description = e.remarker;
          }
        }
      }
      if (index > current) {
        item.status = 'wait';
      }
    });
    return list;
  }, [auditList, auditRecord]);

  return (
    <>
      <CardLayout title={'转诊阶段'} loading={loading2}>
        <Steps current={isFail ? current - 1 : current}>
          {innerAuditList.map((step, index) => {
            return <Step {...(step as any)} />;
          })}
        </Steps>
      </CardLayout>
      {auditRecord.status === '2' && (
        <CardLayout title={'患者追踪'} loading={loading1}>
          <FormDescriptions
            data={auditRecord}
            items={[
              { label: '转诊到位情况', name: 'referralFinallyStatus' },
              { label: '患者转归情况', name: 'referralPatientStatus' }
            ]}
          />
        </CardLayout>
      )}
      <CardLayout title={'患者信息'} loading={loading1}>
        <FormDescriptions
          data={auditRecord}
          items={[
            { label: '姓名', name: 'patientName' },
            { label: '年龄', name: 'patientAge' },
            { label: '生日', name: 'patientBirthday' },
            {
              label: '性别',
              name: 'patientSex',
              render: v => (v === 'M' ? '男' : '女')
            },
            { label: '籍贯', name: 'patientNative' },
            { label: '医保类型', name: 'patientInsureName' },
            { label: '患者就医类型', name: 'treatmentTypeName' },
            { label: '就诊卡号', name: 'patCardNo' },
            { label: '身份证号', name: 'patientIdNo' },
            { label: '联系方式', name: 'patientPhone' },
            { label: '家庭住址', name: 'patientAddress' }
          ]}
        />
      </CardLayout>
      <CardLayout title={'转诊医院信息'} loading={loading1}>
        <FormDescriptions
          data={auditRecord}
          items={[
            { label: '转出医院', name: 'hisName' },
            { label: '转出科室', name: 'deptName' },
            { label: '转入医院', name: 'hospitalName' },
            { label: '转入科室', name: 'hospitalDeptName' },
            {
              label: '接收医生',
              name: 'hospitalDoctorName',
              hidden: auditRecord.hospitalDoctorName ? false : true
            },
            {
              label: '申请时间',
              name: 'createDate'
            },
            {
              label: '就诊时间 ',
              name: 'appointDate',
              render: v => (v ? moment(v).format('YYYY-MM-DD') : '-'),
              hidden: auditRecord.hospitalDoctorName ? false : true
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'诊断详情'} loading={loading1}>
        <FormDescriptions
          column={2}
          data={auditRecord}
          items={[
            { label: '医生诊断', name: 'diagnosis' },
            { label: '现病史', name: 'mainPresentMedicalHistory' },
            { label: '既往史', name: 'mainPhysicalCharacteristics' },
            { label: '主要体征', name: 'mainPresentMedicalHistory' },
            {
              label: '检验检查报告',
              name: 'reports',
              render: (
                <div style={{ position: 'relative', top: -8 }}>
                  <Upload
                    fileList={
                      auditRecord?.reports
                        ? JSON.parse(auditRecord?.reports)
                        : []
                    }
                  />
                </div>
              )
            },
            { label: '已采取的治疗措施', name: 'takedMedicalTreatment' },
            { label: '转出原因', name: 'referralReason' },
            // {
            //   label: '知情同意书',
            //   name: 'consentUrl',
            //   render: v => v && <PreviewImg width={100} height={100} src={v} />
            // },
            { label: '备注', name: 'remark' }
          ]}
        />
      </CardLayout>
      <br />
      <br />
      <FixedFormActions>
        <Space>
          <Button onClick={() => history.goBack()}>返回</Button>
        </Space>
      </FixedFormActions>
    </>
  );
};
