import React, { useMemo } from 'react';
import { DetailsData } from '../../apis';
import './index.less';
import moment from 'moment';

export default ({ data }: { data: DetailsData; hospitalName: string }) => {
  const patientList = useMemo(
    () => [
      {
        label: '患者姓名',
        value: data?.patientName
      },
      {
        label: '患者性别',
        value: data?.patientSex === 'M' ? '男' : '女'
      },
      {
        label: '患者年龄',
        value: data?.patientAge
      },
      {
        label: '联系电话',
        value: data?.patientPhone
      },
      {
        label: '就诊卡号',
        value: data?.patCardNo
      },
      {
        label: '身份证号',
        value: data?.patientIdNo
      },
      {
        label: '家庭住址',
        value: data?.patientAddress
      }
    ],
    [data]
  );
  const parentList = useMemo(
    () => [
      {
        label: '姓名',
        value: data?.parentName
      },
      {
        label: '与患者关系',
        value: ['本人', '父亲', '母亲'][parseInt(data?.parentRelation)] || '-'
      },
      {
        label: '联系电话',
        value: data?.parentPhone
      },
      {
        label: '身份证号',
        value: data?.parentIdNo
      }
    ],
    [data]
  );
  const hospitalList = useMemo(
    () => [
      {
        label: '转出医院',
        value: data?.hisName
      },
      {
        label: '转出科室',
        value: data?.deptName
      },
      {
        label: '申请医生',
        value: data?.doctorName
      },
      {
        label: '转入医院',
        value: data?.hospitalName
      },
      {
        label: '转入科室',
        value: data?.hospitalDeptName
      },
      {
        label: '接收医生',
        value: data?.hospitalDoctorName
      },
      {
        label: '申请时间',
        value: data?.createTime
      },
      {
        label: '就诊时间',
        value: data?.appointDate
          ? moment(data?.appointDate).format('YYYY-MM-DD')
          : '暂无'
      }
    ],
    [data]
  );
  const diagnoseList = useMemo(() => {
    /** 检验检查报告名字 */
    let reports;
    if (data?.reports) {
      try {
        const list: any[] = JSON.parse(data?.reports);
        reports = list?.map((item: any) => item.name)?.join(',');
      } catch (err) {
        console.log(err);
      }
    }
    return [
      {
        label: '医生诊断',
        value: data?.diagnosis
      },
      {
        label: '现病史',
        value: data?.mainPresentMedicalHistory
      },
      {
        label: '既往史',
        value: data?.mainPassedMedicalHistory
      },
      {
        label: '主要体征',
        value: data?.mainPhysicalCharacteristics
      },
      {
        label: '已采取的治疗措施',
        value: data?.takedMedicalTreatment
      },
      {
        label: '转出原因',
        value: data?.referralReason
      },
      {
        label: '检验检查报告',
        value: reports || '暂无'
      }
    ];
  }, [data]);
  return (
    <div className={'pdf-warp'}>
      <div className={'header'}>
        <div className={'title'}>
          <span>
            转诊申请（
            {data?.hisName === '重庆医科大学附属口腔医院' ? '下转' : '上转'}）
          </span>
        </div>
        <div className={'sub-title'}>
          <span>{data?.hisName}</span>
          <span>申请时间：{moment(data?.createTime).format('YYYY-MM-DD')}</span>
        </div>
      </div>
      <div className={'body'}>
        <table className={'table'}>
          <tr>
            <td>患者信息</td>
            <td>
              {patientList?.map(item => (
                <div key={item.label}>
                  {item.label}: {item.value}
                </div>
              ))}
            </td>
          </tr>
          {data?.parentName && data?.parentName !== '无' && (
            <tr>
              <td>监护人信息</td>
              <td>
                {parentList?.map(item => (
                  <div key={item.label}>
                    {item.label}: {item.value}
                  </div>
                ))}
              </td>
            </tr>
          )}
          <tr>
            <td>转诊医院信息</td>
            <td>
              {hospitalList?.map(item => (
                <div key={item.label}>
                  {item.label}: {item.value}
                </div>
              ))}
            </td>
          </tr>
          <tr>
            <td>诊断详情</td>
            <td>
              {diagnoseList?.map(item => (
                <div key={item.label}>
                  {item.label}: {item.value}
                </div>
              ))}
            </td>
          </tr>
          <tr>
            <td>备注</td>
            <td>
              <div>{data?.remark}</div>
            </td>
          </tr>
          <tr>
            <td>审核记录</td>
            <td>
              {data?.referralAuditLogsList?.map(item => (
                <div key={item.id}>
                  <div>转诊阶段：{item.typeName}</div>
                  <div>
                    <span style={{ marginRight: 40 }}>
                      审核人：{item.hisDoctorName}
                    </span>
                    <span>时间：{item.auditTime}</span>
                  </div>
                  <div>结果：{item?.status === '1' ? '驳回' : '通过'}</div>
                </div>
              ))}
            </td>
          </tr>
        </table>
      </div>
    </div>
  );
};
