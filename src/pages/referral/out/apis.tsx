import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiResponseData,
  ListApiRequestParams,
  ApiResponse
} from '@apiHooks';
export interface ReferralApplyAudit {
  id: number;
  createTime: string;
  updateTime: string;
  applyId: number;
  type: string;
  typeName: string;
  hisId: number;
  hisName: string;
  deptId: number;
  deptName: string;
  doctorId: number;
  doctorName: string;
  status: string;
  auditTime: string;
  auditSecond: number;
  extenField: string;
  remarker: string;
}

export interface ReferralApplyExtradata {
  id: string;
  createTime: string;
  updateTime: string;
  applyId: number;
  temperature: string;
  pulse: string;
  breathing: string;
  bloodPressure: string;
  spo2: string;
  weight: string;
  leftPupil: string;
  rightPupil: string;
  consciousness: string;
  intravenousLine: string;
  localConditions: string;
  sjmzg: string;
  ccsd: string;
  pfqk: string;
  pfqkSm: string;
  zylg: string;
  zylgSm: string;
  dszcwxys: string;
  zkdysyz: string;
  zkdysyzSm: string;
  zkdyswyz: string;
  zkdyswyzSm: string;
  sfyjfyf: string;
  glyq: string;
  zypg: string;
  zyfs: string;
  zygj: string;
}

export interface ApiHeaders {
  id: number;
}

export interface DetailsData {
  id: '@natural'; //记录ID
  hisId: '@natural'; //上级医院id
  patientName: '@cname'; //病人姓名
  patCardNo: ''; //转出医院病人就诊卡号
  patientIdType: ''; //就诊人身份证类型
  patientIdNo: ''; //就诊人身份证号
  patHisId: ''; //转出医院病人id
  hospitalNo: ''; //转出医院住院号
  patientInsure: ''; //保险类型
  patientSex: string; //性别
  patientBirthday: '@date'; //就诊人生日
  patientAge: ''; //就诊人年龄
  patientNative: ''; //籍贯
  patientPhone: '1@integer(**********, **********)'; //联系电话
  patientAddress: '@county@cword(2)街@integer(1,100)号@cword(4)小区'; //居住地区
  addressDetail: '@county@cword(2)街@integer(1,100)号@cword(4)小区'; //详细地址
  payModel: ''; //支付方式
  patientCase: ''; //病人其他信息(医院返回的其他资料)
  treatmentType: ''; //就医类型 门诊或者住院
  parentRelation: ''; //监护人关系
  parentName: '@cname'; //监护人姓名
  parentIdType: ''; //监护人证件类型
  parentIdNo: ''; //监护人身份证
  parentPhone: '1@integer(**********, **********)'; //监护人手机号
  fromHisId: '@natural'; //转出医院id
  fromHisName: '@cword(6)'; //转出医院名称
  fromHisLevel: ''; //转出医院等级
  fromDeptId: ''; //转出科室Id
  fromDeptName: '@cword(6)'; //转出医院科室名称
  fromDoctorId: '@natural'; //转出医院医生主键Id
  fromDoctorName: '@cword(6)'; //转出医院医生名称
  toHisId: '@natural'; //转入医院id
  toHisName: '@cword(6)'; //转入医院名称
  toHisLevel: ''; //转入医院等级
  toDeptId: ''; //转入科室id
  toDeptName: '@cword(6)'; //转入科室名称
  toDistrictName: '@cword(6)'; //拟转入院区名字
  hospitalDoctorId: '@natural'; //分配的医生id
  hospitalDoctorName: '@cword(6)'; //分配的医生名称
  referralType: ''; //转诊类别（PZ-平诊,JZ-急诊,WZZZ-危重转诊,YQJJZY-院前急救转运）
  toHospitalType: ''; //入院类别（OUTPATIENT-门诊,INHOSPITAL-住院）
  estimateDate: '@datetime'; //预计到院时间
  appointDate: '@datetime'; //预约入院时间
  confimDate: '@datetime'; //确认入院时间
  diagnosis: ''; //诊断(多个诊断用;分割)
  resume: ''; //主诉及简要病史(转运原因)
  otherContent: ''; //其他需要说明的情况
  reports: ''; //报告json串
  configInfo: string; // 配置项
  consentUrl: 'https://httpbin.org/get?q=@word(8)'; //同意书url
  configId: '@natural'; //使用的配置ID
  referralFormId: '@natural'; //表单内容ID
  remark: ''; //备注
  applyUrl: 'https://httpbin.org/get?q=@word(8)'; //申请单url
  status: 'BEGIN'; //状态(BEGIN-医生发起,暂未提交审核；ZCYYKSSH-转出医院科室审核；ZCYYSH-转出医院审核；ZRYYKSSH-转入医院科室审核；ZRYYSH-转入医院审核；ZRYYYSJS-转入医院医生接受；SUCCESS-申请通过（完成）；CANCEL-申请取消；BACK-申请打回)
  mainTreatment: ''; //主要治疗经过
  applyDoctorId: '@natural'; //申请医生ID
  applyDoctorName: '@cword(6)'; //申请医生名称
  auditDoctorId: '@natural'; //审核医生ID
  auditDoctorName: '@cword(6)'; //审核医生名称
  auditProcess: string; //审核进度
  auditFailReason: ''; //审核失败原因
  auditProcessRoute: string[]; //审核路径，顶部的步骤图
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //更新时间
  referralFormInfo: {
    configId: '1560459219905851393';
    createTime: '2022-08-30 18:14:37';
    formContent: {
      [key: string]: string;
    };
    id: '1564557190433931265';
    referralApplyId: '1564557190354239490';
  }; //配置项值
}

export interface ConfigType {
  label: '血压';
  type: 'input';
  name: 'blood';
}

interface ListParam {
  hisId: string;
  fromHisId?: string;
  toHisId?: string;
  fromDeptId?: string;
  toDeptId?: string;
  patientInfo?: string;
}

export const statusOption = {
  // BEGIN: '医生发起,暂未提交审核',
  ZCYYKSSH: '转出医院科室审核',
  ZCYYSH: '转出医院审核',
  ZRYYKSSH: '转入医院科室审核',
  ZRYYSH: '转入医院审核',
  ZRYYYSJS: '转入医院医生审核',
  SUCCESS: '申请通过（完成）',
  CANCEL: '申请取消',
  BACK: '申请打回'
};

export interface auditRes {
  operatorId: '@natural'; //审核人id
  operator: '@natural'; //审核人
  reason: '@cname'; //审核说明
  taskName: ''; //任务名称
  status: string; //审核状态
  createTime: ''; //审核时间
}

export const toHosTypeOption = {
  OUTPATIENT: '门诊',
  INHOSPITAL: '住院'
};
export const referralTypeOption = {
  PZ: '平诊',
  JZ: '急诊',
  WZZZ: '危重转诊',
  YQJJZY: '院前急救转运'
};

export default {
  转诊记录列表查询: createApiHooks((params: ListApiRequestParams & ListParam) =>
    request.get<ListApiResponseData<DetailsData>>(
      '/mch/cooperate/referral-apply',
      { params }
    )
  ),
  转诊记录详情查询: createApiHooks((params: { id: string }) =>
    request.get<ApiResponse<DetailsData>>(
      `/mch/cooperate/referral-apply/${params.id}`
    )
  ),
  审核记录查询: createApiHooks(
    (params: { hisId: string; businessId: string }) =>
      request.get<ApiResponse<auditRes[]>>(
        '/mch/cooperate/referral-apply/check-records',
        { params }
      )
  ),
  所有医院列表: createApiHooks(() =>
    request.get<{
      data: { id: string; hisId: number; name: string; level: string }[];
    }>('/mch/cooperate/cooperate-hospital/auth')
  ),
  医院科室列表: createApiHooks(
    (params: { cpHospitalId: string | number; type?: 1 | 2 }) => {
      return request.get<{ data: { id: string; name: string }[] }>(
        '/mch/cooperate/cooperate-dept/hospital',
        { params }
      );
    }
  ),
  // 转诊订单统计-转入
  useStatistics: createApiHooks(params =>
    request.get<any>(`/mch/cooperate/referral-apply/statistics`, {
      params
    })
  )
};
