import {
  Actions<PERSON>rap,
  ArrSelect,
  <PERSON>RangePicker,
  LinkButton,
  useModal
} from 'parsec-admin';
import { TableListColumnProps } from 'parsec-admin/lib/templates/tableList';
import React, { useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router';
import useApi, {
  statusOption,
  toHosTypeOption,
  referralTypeOption
} from './apis';
import env from '@src/configs/env';
// import Tmpl from './tmpl';
import Html2Pdf from './html2pdf';
import moment from 'moment';

export function useTab1(type: string) {
  const hisId = env.hisId;
  const history = useHistory();
  const [curTab, setCurTab] = useState('0');

  const {
    data: { data: hospital }
  } = useApi.所有医院列表({
    params: { hisId }
  });
  const {
    data: { data: dept },
    request: reqDept
  } = useApi.医院科室列表({
    needInit: false
  });

  const docTableRef = useRef<HTMLElement | null>(null);
  // 交接单
  const switchModalVisible = useModal(
    ({ type, hospitalName, data }) => ({
      width: 940,
      children: (
        <div style={{ width: 880, margin: '0 auto' }}>
          <div ref={ref => (docTableRef.current = ref)}>
            {/* <Tmpl type={type} hospitalName={hospitalName || ''} data={data} /> */}
          </div>
        </div>
      ),
      okText: '打印',
      onSubmit: () =>
        new Promise((_, reject) => {
          Html2Pdf(docTableRef.current, '患者交接PDF');
          reject();
        })
    }),
    []
  );

  const [tab1, setTab1] = useState<TableListColumnProps<any>[]>();
  const [tab234, setTab234] = useState<TableListColumnProps<any>[]>();
  useEffect(() => {
    setTab1([
      {
        title: '患者姓名',
        dataIndex: 'patientName'
      },
      {
        title: '就诊类型',
        dataIndex: 'treatmentType'
      },
      {
        title: '就诊卡号',
        dataIndex: 'patCardNo'
      },
      {
        title: type === 'out' ? '转出科室' : '转入科室',
        dataIndex: type === 'out' ? 'fromDeptName' : 'toDeptName'
      },
      {
        title: '疾病名称',
        dataIndex: 'diagnosis'
      },
      {
        title: type === 'out' ? '拟转入医院' : '转出医院',
        dataIndex: type === 'out' ? 'toHisName' : 'fromHisName',
        searchIndex: type === 'out' ? 'toHisId' : 'fromHisId',
        search: (
          <ArrSelect
            onChange={val => {
              reqDept({ cpHospitalId: val as string });
            }}
            options={
              hospital?.map(({ name, id }) => ({
                value: id,
                children: name
              })) || []
            }
          />
        )
      },
      {
        title: type === 'out' ? '拟转入科室' : '转出科室',
        dataIndex: type === 'out' ? 'toDeptName' : 'fromDeptName',
        searchIndex: type === 'out' ? 'toDeptId' : 'fromDeptId',
        search: (
          <ArrSelect
            options={
              dept?.map(({ name, id }) => ({
                value: id,
                children: name
              })) || []
            }
          />
        )
      },
      {
        title: '申请转出时间',
        dataIndex: 'createTime',
        search: <DayRangePicker valueFormat='YYYY-MM-DD' />,
        searchIndex: ['startApplyDate', 'endApplyDate']
      },
      {
        title: '患者信息',
        search: true,
        searchIndex: 'patientInfo'
      },
      {
        title: '入院类别',
        dataIndex: 'toHospitalType',
        render: v => toHosTypeOption[v]
      },
      {
        title: '转诊类别',
        dataIndex: 'referralType',
        render: v => referralTypeOption[v]
      },
      {
        title: '状态',
        dataIndex: 'status',
        render: v => statusOption[v]
      },
      {
        title: '操作',
        width: 140,
        render: record => (
          <ActionsWrap max={999}>
            <LinkButton
              onClick={() => {
                history.push(`/referral/${type}/detail/${record.id}`);
              }}>
              查看
            </LinkButton>
            <LinkButton
              onClick={() => {
                // history.push(`/referral/audit/${record.id}`);
              }}>
              打印
            </LinkButton>
          </ActionsWrap>
        )
      }
    ]);
    setTab234([
      {
        title: '患者姓名',
        dataIndex: 'patientName'
      },
      {
        title: '就诊类型',
        dataIndex: 'treatmentType'
      },
      {
        title: '就诊卡号',
        dataIndex: 'patCardNo'
      },
      {
        title: '转出科室',
        dataIndex: 'fromDeptName'
      },
      {
        title: '疾病名称',
        dataIndex: 'diagnosis'
      },
      {
        title: '转诊状态',
        searchIndex: 'status',
        search: <ArrSelect options={statusOption} />
      },
      {
        title: '拟转入医院',
        dataIndex: 'toHisName',
        searchIndex: 'toHisId',
        search: (
          <ArrSelect
            options={
              hospital?.map(({ name, id }) => ({
                value: id,
                children: name
              })) || []
            }
          />
        )
      },
      {
        title: '拟转入科室',
        dataIndex: 'toDeptName',
        searchIndex: 'toDeptId',
        search: (
          <ArrSelect
            options={
              dept?.map(({ name, id }) => ({
                value: id,
                children: name
              })) || []
            }
          />
        )
      },
      {
        title: '申请转出时间',
        dataIndex: 'createTime',
        search: <DayRangePicker valueFormat='YYYY-MM-DD' />,
        searchIndex: ['startApplyDate', 'endApplyDate']
      },
      {
        title: '患者信息',
        search: true,
        searchIndex: 'patientInfo'
      },
      {
        title: '申请就诊类型',
        dataIndex: 'toHospitalType',
        render: v => toHosTypeOption[v]
      },
      {
        title: '紧急程度',
        dataIndex: 'emergency',
        render: (_, record) => {
          return record?.referralFormInfo?.emergency || '-';
        }
      },
      {
        title: '预约时间',
        dataIndex: 'estimateDate',
        render: v => (v ? moment(v).format('YYYY-MM-DD') : '-')
      },
      {
        title: '状态',
        dataIndex: 'status',
        render: v => statusOption[v]
      },
      {
        title: '操作',
        fixed: 'right',
        width: 220,
        dataIndex: 'opt',
        excelRender: false,
        render: (_, record) => (
          <ActionsWrap max={4}>
            <LinkButton
              onClick={() => {
                history.push(`/referral/${type}/detail/${record.id}`);
              }}>
              查看
            </LinkButton>
            <LinkButton
              onClick={() => {
                // switchModalVisible({
                //   type: 'jjd',
                //   data: {}
                // });
              }}>
              交接单
            </LinkButton>
            <LinkButton
              onClick={() => {
                window.open(record.consentUrl);
              }}>
              知情同意书
            </LinkButton>
            <LinkButton
              onClick={() => {
                console.log('todo..');
              }}>
              转诊单
            </LinkButton>
          </ActionsWrap>
        )
      }
    ]);
  }, [dept, history, hospital, reqDept, switchModalVisible, type]);
  return {
    tabColumns: [tab1, tab234][curTab === '0' ? 0 : 1],
    curTab,
    setCurTab
  };
}
