import React, { useMemo, useState } from 'react';
import MyTableList from '@components/myTableList';
import useApi from './apis';
// import { useSessionStorage } from 'react-use';
// import { handleSubmit } from 'parsec-admin';
// import { useHistory } from 'react-router-dom';
import env from '@src/configs/env';
import { Tabs, Form } from 'antd';
import { useTab1 } from './columns';
import styled from 'styled-components';
import { useConfig } from '@src/store/hisConfig';

const { TabPane } = Tabs;

export default (props: { type: string }) => {
  const { type } = props;
  const hisId = env.hisId;
  const { isICU } = useConfig();
  const { data: statistics, request: fetchStatistics } = useApi.useStatistics({
    params: {
      isFromOrTo: type === 'out' ? 'isFrom' : 'isTo'
    },
    initValue: {},
    needInit: false
  });
  const { tabColumns, setCurTab } = useTab1(type);
  // tab切换参数
  const [tabStatus, setTabStatus] = useState<string | undefined>(
    isICU ? undefined : 'ZCSH'
  );

  const [form] = Form.useForm();

  const onChange = (key: string) => {
    form.resetFields();
    setCurTab(key);
    setTabStatus(
      {
        0: 'ZCSH',
        1: 'ZRSH',
        2: 'OVER',
        3: undefined
      }[key]
    );
  };
  // const switchModalVisible = useModal(
  //   ({ type, hospitalName, data }) => ({
  //     width: 940,
  //     children: (
  //       <div style={{ width: 880, margin: '0 auto' }}>
  //         <div ref={ref => (docTableRef.current = ref)}>
  //           <Tmpl type={type} hospitalName={hospitalName || ''} data={data} />
  //         </div>
  //       </div>
  //     ),
  //     okText: '打印',
  //     onSubmit: () =>
  //       new Promise((_, reject) => {
  //         Html2Pdf(docTableRef.current, '患者交接PDF');
  //         reject();
  //       })
  //   }),
  //   []
  // );

  // const [doctor] = useSessionStorage<any>('doctor');
  // const history = useHistory();
  const table = useMemo(() => {
    return (
      <>
        <MyTableList
          tableTitle={type === 'out' ? '转出列表' : '转入列表'}
          columns={tabColumns || []}
          params={{ tabStatus, isFromOrTo: type === 'out' ? 'isFrom' : 'isTo' }}
          getList={({ params }) => {
            if (isICU && type === 'in') {
              fetchStatistics(params);
            }
            return useApi.转诊记录列表查询.request({
              // page: current,
              // limit: 10,
              hisId,
              ...params
            });
          }}
        />
        {isICU && type === 'in' && (
          <div
            style={{
              position: 'relative',
              marginTop: '-80px',
              marginLeft: '50px',
              color: 'red',
              fontWeight: 'bold',
              pointerEvents: 'none'
            }}>
            <span style={{ marginRight: '6px' }}>
              转诊单总计数量：{statistics?.data?.total || 0};
            </span>
            <span style={{ marginRight: '6px' }}>
              重医儿童医院参与数量：
              {statistics?.data?.cqChildrenNum || 0};
            </span>
            <span>
              成都妇女儿童中心医院参与数量：
              {statistics?.data?.cdChildrenNum || 0}
            </span>
          </div>
        )}
      </>
    );
  }, [hisId, tabColumns, tabStatus, type, statistics, isICU]);
  return (
    <Wrap>
      <Tabs
        className='tabs'
        defaultActiveKey={isICU ? '3' : '0'}
        onChange={onChange}
        destroyInactiveTabPane>
        {!isICU && (
          <>
            <TabPane tab='转出审核' key='0'>
              {table}
            </TabPane>
            <TabPane tab='转入审核' key='1'>
              {table}
            </TabPane>
            <TabPane tab='转诊结束' key='2'>
              {table}
            </TabPane>
          </>
        )}
        <TabPane tab='全部' key='3'>
          {table}
        </TabPane>
      </Tabs>
    </Wrap>
  );
};

const Wrap = styled.div`
  .red {
    color: red;
  }
  .tabs {
    margin: 25px 25px 0 25px;
    padding: 10px;
    padding-left: 20px;
    background: #fff;
  }
  .juidxb.juidxb {
    margin-top: 0;
  }
`;
