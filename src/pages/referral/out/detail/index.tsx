import React, { useEffect, useMemo, useState } from 'react';
import {
  CardLayout,
  FormDescriptions,
  FixedFormActions,
  PreviewImg
} from 'parsec-admin';
import { Steps, Button, StepProps } from 'antd';
import useApi, { auditRes, referralTypeOption, toHosTypeOption } from '../apis';
// import apis from '@src/pages/organization/config/api';
import { useParams } from 'react-router-dom';
import { useHistory } from 'react-router';
import env from '@src/configs/env';
import styled from 'styled-components';
import { getCascaderLabel } from '@utils/address-options';
import { getAddressOptions } from '@kqinfo/ui';
import { icuItems } from './icu-items';
import { useConfig } from '@src/store/hisConfig';

const { Step } = Steps;

export default () => {
  const { id }: any = useParams<any>();
  const { isICU } = useConfig();
  const hisId = env.hisId;
  //地址
  const [addressOptions, setAddressOptions] = useState<any[]>([]);
  useEffect(() => {
    getAddressOptions().then(options => {
      setAddressOptions(options);
    });
  }, []);
  const {
    data: { data: auditList },
    loading: loading1
  } = useApi.审核记录查询({
    params: { businessId: id, hisId }
  });
  const history = useHistory();
  const {
    data: { data: auditRecord },
    loading: loading2
  } = useApi.转诊记录详情查询({
    params: { id }
  });

  // // 查询配置的字段
  // useEffect(() => {
  //   if (auditRecord) {
  //     apis.标准字段列表.request(auditRecord.configId).then(res => {
  //       console.log(res);
  //     });
  //   }
  // }, [auditRecord]);

  // const current = +(auditRecord?.auditProcess || '1') - 1;
  const current = useMemo(() => {
    if (auditRecord) {
      const index = auditRecord?.auditProcessRoute.findIndex(
        item => item === auditRecord?.auditProcess
      );
      if (index > -1) {
        return index;
      }
      return auditRecord?.auditProcessRoute.length;
    }
    return 0;
  }, [auditRecord]);

  const isFail = ['CANCEL', 'BACK'].includes(auditRecord?.status || '-1');

  // 审核step
  const innerAuditList = useMemo(() => {
    if (auditRecord) {
      const lastStatus =
        {
          CANCEL: '已取消',
          BACK: '审核未通过'
        }[auditRecord.status] || '待审核';

      const listBt = (info?: auditRes, isLast?: boolean) => (
        <div className='stepBt'>
          <div>{isLast ? lastStatus : info?.reason}</div>
          <div className='info'>
            {info?.operator} {info?.createTime}
          </div>
        </div>
      );

      const list: Array<StepProps & {
        key: string;
      }> = auditRecord.auditProcessRoute.map((item, index) => {
        return {
          key: index.toString(),
          title: (
            <div className={current > index ? 'stepTitle' : ''}>{item}</div>
          ),
          status: 'wait'
        };
      });

      list.forEach((item, index) => {
        if (index < current) {
          item.status = 'finish';
          item.description = listBt(auditList?.[index], false);
        }
        if (index === current) {
          item.description = listBt(auditList?.[index], true);
          item.status = 'process';
          if (isFail) {
            item.status = 'error';
          }
        }
        if (index > current) {
          item.status = 'wait';
        }
      });
      return list;
    }
    return [];
  }, [auditList, auditRecord, current, isFail]);

  // 提取配置信息
  const configs = useMemo(() => {
    if (auditRecord?.configInfo) {
      const con =
        JSON.parse(auditRecord.configInfo)?.templateContent?.desc || [];
      return con;
    }
    return [];
  }, [auditRecord]);

  return (
    <Wrap>
      <CardLayout title={'转诊阶段'} loading={loading2}>
        <div className='flex'>
          <div className='stepLeft'>
            <div className='stepStatus'>阶段</div>
            <div className='stepStatusBt'>状态</div>
          </div>
          <Steps className='steps' current={isFail ? current - 1 : current}>
            {innerAuditList.map((step, index) => {
              return <Step {...(step as any)} />;
            })}
          </Steps>
        </div>
      </CardLayout>
      <CardLayout title={'患者信息'} loading={loading1}>
        <FormDescriptions
          data={auditRecord}
          items={[
            { label: '姓名', name: 'patientName' },
            { label: '就医类型', name: 'treatmentType' },
            // { label: '就医类型', name: 'treatmentTypeName' },
            { label: '就诊卡号', name: 'patCardNo' },
            { label: '医保类型', name: 'patientInsure' },
            // { label: '医保类型', name: 'patientInsureName' },
            { label: '身份证号', name: 'patientIdNo' },
            { label: '联系电话', name: 'patientPhone' },
            { label: '生日', name: 'patientBirthday' },
            { label: '年龄', name: 'patientAge' },
            {
              label: '性别',
              name: 'patientSex',
              render: v => (v === 'M' ? '男' : '女')
            },
            { label: '籍贯', name: 'patientNative' },
            {
              label: '家庭住址',
              name: 'patientAddress',
              render: (v: any) => {
                if (v?.length) {
                  return (
                    getCascaderLabel(v.split(','), addressOptions)?.join('-') ||
                    ''
                  );
                }
                return '';
              }
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'监护人信息'} loading={loading1}>
        <FormDescriptions
          data={auditRecord}
          items={[
            { label: '姓名', name: 'parentName' },
            { label: '身份证号', name: 'parentIdNo' },
            { label: '联系电话', name: 'parentPhone' },
            { label: '与患者关系', name: 'parentRelation' }
          ]}
        />
      </CardLayout>
      <CardLayout title={'转诊信息'} loading={loading1}>
        <FormDescriptions
          data={auditRecord}
          items={[
            { label: '转出医院', name: 'fromHisName' },
            { label: '医院等级', name: 'fromHisLevel' },
            { label: '转出科室', name: 'fromDeptName' },
            { label: '转入医院', name: 'toHisName' },
            { label: '拟转入科室', name: 'toDeptName' },
            {
              label: '转诊类别',
              name: 'referralType',
              render: v => referralTypeOption[(v as string) || '']
            },
            {
              label: '入院类别',
              name: 'toHospitalType',
              render: v => toHosTypeOption[(v as string) || '']
            },
            {
              label: '到院日期',
              name: 'estimateDate',
              render: (v: any) => v?.split(' ')[0]
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'病情信息'} loading={loading1}>
        {isICU ? (
          <FormDescriptions column={2} data={auditRecord} items={icuItems} />
        ) : (
          <FormDescriptions
            column={2}
            data={auditRecord?.referralFormInfo?.formContent}
            items={configs}
          />
        )}
      </CardLayout>
      <CardLayout title={'转运信息'} loading={loading1}>
        <FormDescriptions
          data={auditRecord}
          items={[
            { label: '转运评估', name: 'transferAssessment' },
            { label: '转运方式', name: 'transferType' }
          ]}
        />
      </CardLayout>
      <CardLayout title={'检验检查报告'} loading={loading1}>
        <FormDescriptions
          data={auditRecord}
          layout='vertical'
          items={[
            {
              label: '影像学检查，检查无明显异常',
              span: 24,
              name: 'inspectionReportUrl',
              render: v => (
                <div>
                  <PreviewImg
                    className='mr10'
                    width={136}
                    src={v as string}></PreviewImg>
                </div>
              )
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'双向转诊知情同意书'} loading={loading1}>
        <FormDescriptions
          data={auditRecord}
          items={[
            {
              label: '',
              name: 'consentUrl',
              render: v =>
                v ? (
                  <Button
                    type='link'
                    className='btn'
                    ghost
                    onClick={() => {
                      window.open(v as string);
                    }}>
                    查看
                  </Button>
                ) : (
                  '暂无'
                )
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'备注'} loading={loading1}>
        <FormDescriptions
          data={auditRecord}
          items={[
            {
              label: '',
              name: 'remark'
            }
          ]}
        />
      </CardLayout>
      <br />
      <br />
      <FixedFormActions>
        <div className='flex'>
          {/* <Button className='mr10' type='primary'>
            撤销审核
          </Button>
          <Button className='mr10' type='primary'>
            修改
          </Button>
          <Button className='mr10' type='primary'>
            取消转诊
          </Button> */}
          <Button onClick={() => history.goBack()}>返回</Button>
        </div>
      </FixedFormActions>
    </Wrap>
  );
};

const Wrap = styled.div`
  .flex {
    display: flex;
  }
  .between {
    display: flex;
    justify-content: space-between;
  }
  .around {
    display: flex;
    justify-content: space-around;
  }
  .vCenter {
    display: flex;
    justify-content: center;
  }
  .hCenter {
    display: flex;
    align-items: center;
  }
  .center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .mr10 {
    margin-right: 10px;
  }
  .steps {
    .stepBt {
      color: #2780d9;
      font-size: 16px;
    }
    .info {
      font-size: 14px;
    }
    .stepTitle {
      color: #2780d9;
    }
  }
  .stepLeft {
    width: 60px;
    padding-top: 6px;
    .stepStatusBt {
      margin-top: 20px;
    }
  }
  .btn {
    margin-top: -14px;
  }
`;
