import React from 'react';
import { Form, InputNumber, Radio, Space, Input } from 'antd';
import { RadioGroupProps } from 'antd/lib/radio/interface';

export const OtherRadio = ({
  values,
  value,
  onChange,
  children,
  otherName,
  ...props
}: {
  values: string[];
  children: React.ReactNode;
  otherName?: string;
} & RadioGroupProps) => {
  const last = values[values.length - 1];
  const filterValues = values.filter(v => v !== last);
  const showOther = value !== undefined && !filterValues.includes(value);
  return (
    <>
      <Radio.Group value={value} onChange={onChange} {...props}>
        {filterValues.map(v => (
          <Radio value={v}>{v}</Radio>
        ))}
        <Radio key={last} value={otherName ? last : showOther ? value : ''}>
          <Space>
            {last}
            {showOther &&
              (otherName ? (
                <Form.Item
                  name={otherName}
                  style={{ marginBottom: 0 }}
                  rules={[{ required: true, message: '请补充' }]}>
                  {children}
                </Form.Item>
              ) : (
                React.isValidElement(children) &&
                React.cloneElement(children, {
                  // @ts-ignore
                  onChange,
                  value
                })
              ))}
          </Space>
        </Radio>
      </Radio.Group>
    </>
  );
};

// 气管插管
export const QGCG = ({ value, onChange }: any) => {
  const showOther = value === '有';
  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Radio.Group value={value} onChange={onChange}>
        <Radio value={'无'}>{'无'}</Radio>
        <Radio value={'有'}>{'有'}</Radio>
      </Radio.Group>
      {showOther && (
        <div style={{ display: 'flex' }}>
          <Form.Item
            label={'ID'}
            name={'qgcgid'}
            style={{ marginBottom: 0, marginRight: '10px' }}
            rules={[{ required: true, message: '请补充' }]}>
            <Input addonAfter={'mm'}></Input>
          </Form.Item>
          <Form.Item
            label={'经口/鼻'}
            name={'qgcgjk'}
            style={{ marginBottom: 0, marginRight: '10px' }}
            rules={[{ required: true, message: '请补充' }]}>
            <Radio.Group defaultValue={'经口'}>
              <Radio value={'经口'}>经口</Radio>
              <Radio value={'经鼻'}>经鼻</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label={'插入深度'}
            name={'qgcgsd'}
            style={{ marginBottom: 0, marginRight: '10px' }}
            rules={[{ required: true, message: '请补充' }]}>
            <Input addonAfter={'cm'}></Input>
          </Form.Item>
        </div>
      )}
    </div>
  );
};

const Textarea = Input.TextArea;
export const icuItems: any[] = [
  {
    label: '主要诊断',
    name: 'diagnosis',
    span: 1
  },
  {
    label: '主诉及简要病史（转运原因）',
    span: 3,
    name: 'resume'
  },
  {
    required: true,
    label: '体温(℃)',
    name: 'tzt',
    formItemProps: {
      render: <InputNumber />
    }
  },
  {
    required: true,
    label: '脉搏(次/分)',
    name: 'tzp',
    formItemProps: {
      render: <InputNumber />
    }
  },
  {
    required: true,
    label: '呼吸(次/分)',
    name: 'tzr',
    formItemProps: {
      render: <InputNumber />
    }
  },
  {
    required: true,
    label: 'Sp0₂(%)',
    name: 'tzsp',
    formItemProps: {
      render: <InputNumber />
    }
  },
  {
    required: true,
    label: '血压(mmHg)',
    span: 2,
    name: 'tzbp'
  },
  {
    required: true,
    label: '意识',
    name: 'consciousness',
    span: 6,
    formItemProps: {
      initialValue: '清醒',
      render: (
        <Radio.Group>
          <Radio value={'清醒'}>清醒</Radio>
          <Radio value={'嗜睡'}>嗜睡</Radio>
          <Radio value={'意识模糊'}>意识模糊</Radio>
          <Radio value={'昏睡'}>昏睡</Radio>
          <Radio value={'昏迷'}>昏迷</Radio>
          <Radio value={'谵妄'}>谵妄</Radio>
        </Radio.Group>
      )
    }
  },
  {
    required: true,
    name: 'ktLeft',
    label: '瞳孔左',
    span: 1,
    formItemProps: {
      render: <InputNumber addonAfter={'cm'} />,
      style: {
        paddingRight: 0
      }
    }
  },
  {
    required: true,
    name: 'leftLightReflex',
    label: '瞳孔左反射',
    span: 2,
    formItemProps: {
      initialValue: '灵敏',
      render: (
        <Radio.Group>
          <Radio value={'灵敏'}>灵敏</Radio>
          <Radio value={'迟钝'}>迟钝</Radio>
          <Radio value={'消失'}>消失</Radio>
        </Radio.Group>
      )
    }
  },

  {
    required: true,
    name: 'ktRight',
    label: '瞳孔右',
    span: 1,
    formItemProps: {
      render: <InputNumber addonAfter={'cm'} />
    }
  },
  {
    required: true,
    name: 'rightLightReflex',
    label: '瞳孔右反射',
    span: 2,
    formItemProps: {
      initialValue: '灵敏',
      render: (
        <Radio.Group>
          <Radio value={'灵敏'}>灵敏</Radio>
          <Radio value={'迟钝'}>迟钝</Radio>
          <Radio value={'消失'}>消失</Radio>
        </Radio.Group>
      )
    }
  },
  {
    required: true,
    name: 'zg',
    label: '紫绀',
    span: 6,
    formItemProps: {
      render: (
        <OtherRadio values={['无', '有']} otherName={'zg'}>
          <Input />
        </OtherRadio>
      )
    }
  },
  {
    required: true,
    name: 'breathingFeat',
    label: '呼吸节律',
    span: 4,
    formItemProps: {
      render: <Textarea></Textarea>
    }
  },
  {
    required: true,
    name: 'saz',
    label: '三凹征',
    span: 2,
    formItemProps: {
      initialValue: '无',
      render: (
        <Radio.Group>
          <Radio value={'无'}>无</Radio>
          <Radio value={'有'}>有</Radio>
        </Radio.Group>
      )
    }
  },
  {
    required: true,
    name: 'lungFeat',
    label: '肺部特征',
    span: 24,
    formItemProps: {
      render: <Textarea></Textarea>
    }
  },
  {
    required: true,
    name: 'heartFeat',
    label: '心脏特征',
    span: 4,
    formItemProps: {
      render: <Textarea></Textarea>
    }
  },
  {
    required: true,
    name: 'crt',
    label: 'CRT',
    span: 2,
    formItemProps: {
      render: <InputNumber addonAfter={'秒'}></InputNumber>
    }
  },
  {
    required: true,
    name: 'limbCircular',
    label: '肢端循环',
    span: 6,
    formItemProps: {
      initialValue: '暖',
      render: (
        <Radio.Group>
          <Radio value={'暖'}>暖</Radio>
          <Radio value={'发凉'}>发凉</Radio>
          <Radio value={'花纹'}>花纹</Radio>
        </Radio.Group>
      )
    }
  },
  {
    required: true,
    name: 'nervousSysFeat',
    label: '神经系统特征',
    span: 24,
    formItemProps: {
      render: <Textarea></Textarea>
    }
  },
  {
    required: true,
    name: 'surgeryCase',
    label: '外科情况',
    span: 24,
    formItemProps: {
      render: <Textarea></Textarea>
    }
  },
  {
    required: true,
    name: 'other',
    label: '其他',
    span: 24,
    formItemProps: {
      render: <Textarea></Textarea>
    }
  },
  {
    required: true,
    name: 'yc',
    label: '皮肤完整性：压疮',
    span: 3,
    formItemProps: {
      initialValue: '无',
      render: (
        <OtherRadio values={['无', '有']} otherName={'yc'}>
          <Input />
        </OtherRadio>
      )
    }
  },
  {
    required: true,
    name: 'tzpg',
    label: '疼痛评估',
    span: 3,
    formItemProps: {
      initialValue: '无',
      render: (
        <OtherRadio values={['无', '有']} otherName={'tzpg'}>
          <Input />
        </OtherRadio>
      )
    }
  },
  {
    required: true,
    name: 'jbqjcs',
    label: '基本抢救措施',
    span: 6,
    formItemProps: {
      initialValue: '给氧',
      render: (
        <Radio.Group>
          <Radio value={'给氧'}>给氧</Radio>
          <Radio value={'心电监护'}>心电监护</Radio>
          <Radio value={'建立通道'}>建立通道</Radio>
        </Radio.Group>
      )
    }
  },
  {
    required: true,
    name: 'qgcg',
    label: '气管插管',
    span: 6,
    formItemProps: {
      initialValue: '无',
      render: <QGCG />
    },
    render: (v, record) => {
      if (v === '有') {
        return `ID: ${record.qgcgid}; 径口: ${record.qgcgjk}; 插入深度: ${record.qgcgsd};`;
      }
      return v;
    }
  },
  {
    required: true,
    name: 'qtqjcs',
    label: '其他抢救措施',
    span: 3,
    formItemProps: {
      initialValue: '无',
      render: (
        <OtherRadio values={['无', '有']} otherName={'qtqjcs'}>
          <Input />
        </OtherRadio>
      )
    }
  },
  {
    required: true,
    name: 'tsyyqk',
    label: '特殊用药情况',
    span: 3,
    formItemProps: {
      initialValue: '无',
      render: (
        <OtherRadio values={['无', '有']} otherName={'tsyyqk'}>
          <Input />
        </OtherRadio>
      )
    }
  }
];
