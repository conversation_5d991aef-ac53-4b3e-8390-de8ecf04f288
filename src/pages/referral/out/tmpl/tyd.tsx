import React from 'react';
export default () => <div></div>;
// import { DateShow } from 'parsec-admin';
// import { DetailsData } from '../apis';
// import './index.less';
// import { Space } from '@kqinfo/ui';

// export default ({ data }: { data: DetailsData }) => {
//   return (
//     <div className={'pdf-warp'}>
//       <div className={'header'}>
//         <div className={'subtitle'}>双向转诊知情同意书</div>
//       </div>
//       <div className={'body'}>
//         <table className={'table'}>
//           <tr className={'tr'}>
//             <td className={'td'}>姓名</td>
//             <td className={'td'} colSpan={2}>
//               {data?.patientName}
//             </td>
//             <td className={'td'}>性别</td>
//             <td className={'td'} colSpan={2}>
//               {data?.patientSex}
//             </td>
//             <td className={'td'}>年龄</td>
//             <td className={'td'} colSpan={2}>
//               {data?.patientAge}
//             </td>
//           </tr>
//           <tr className={'tr'}>
//             <td className={'td'}>身份证号</td>
//             <td className={'td'} colSpan={7}>
//               {data?.patientIdNo}
//             </td>
//           </tr>
//           <tr className={'tr'}>
//             <td className={'td'}>家庭地址</td>
//             <td className={'td'} colSpan={7}>
//               {data?.patientNative} {data?.patientAddress}
//             </td>
//           </tr>
//           <tr className={'tr'}>
//             <td className={'td'} style={{ whiteSpace: 'nowrap' }}>
//               转诊医院名称
//             </td>
//             <td className={'td'} colSpan={5}>
//               {data?.fromHisName}
//             </td>
//             <td className={'td'}>接诊医院名称</td>
//             <td className={'td'} colSpan={5}>
//               {data?.toHisName}
//             </td>
//           </tr>
//           <tr className={'tr'}>
//             <td className={'td'}>
//               <Space vertical>{[...'患者双向转诊告知内容']}</Space>
//             </td>
//             <td className={'td'} colSpan={7}>
//               <tr
//                 className={'tr'}
//                 style={{
//                   whiteSpace: 'break-spaces',
//                   textAlign: 'left',
//                   borderTop: 'none'
//                 }}>
//                 {`尊敬的患者及其亲属：
//         您好！根据患者目前的疾病状况及身体情况，医生认为，已符合我院双向转诊（下转）标准，现拟将患者转至与我院签约的相关协议医院继续进行治疗。
//         特告知如下：
//         1、我院负责与接诊医院联系、落实转诊患者基层协议医院继续就诊事宜。
//         2、为保障患者医疗安全及就诊绿色通道畅通，请务必在完善转诊手续后24小时内，通过接诊医院绿色通道完成就诊或住院。如超出时限，我院不能保证其能顺利完成转诊。
//         3、转诊需携带患者身份证或者医保卡、家属身份证、《双向转诊单》及疾病诊疗记录材料。
//         4、如患者病情需要，与我院完善相应转诊手续后，我院可提供救护车协助转运，有专职医务人员护送，必要时可对患者进行紧急诊治，但由患方承担相应费用。
//         5、在转诊途中，患者亲属需加强对患者病情的观察及护理，以保证患者安全。如患者病情发生变化，需立即就近就医进行诊治。
//         6、在接诊医院诊治期间，如患者病情出现反复，在完善相关手续后，可转回我院继续进行诊疗。
//         7、其他：如无必须写“无”`}
//               </tr>
//               <tr
//                 className={'tr'}
//                 style={{
//                   borderBottom: 'none',
//                   height: 100
//                 }}>
//                 主管医生签字：
//               </tr>
//             </td>
//           </tr>
//           <tr className={'tr'}>
//             <td className={'td'}>
//               <Space vertical>{[...'患方意见']}</Space>
//             </td>
//             <td
//               className={'td'}
//               colSpan={7}
//               style={{
//                 whiteSpace: 'break-spaces',
//                 textAlign: 'left',
//                 borderTop: 'none'
//               }}>
//               <Space vertical size={10} style={{ lineHeight: 1.5715 }}>
//                 {`转诊途中可能出现的各种并发症和可能发生的意外，经治医师已作了详细说明，我们已充分理解。经慎重考虑，我同意转诊治疗且愿意承担转诊途中可能出现的各种并发症及意外情况的风险，并同意医护人员在途中应对各种意外情况所做的所有诊疗行为。`}
//                 <Space vertical size={10}>
//                   <Space>
//                     <Space vertical flex={1} size={10}>
//                       <div>患方签字:</div>
//                       <div>与患者关系:</div>
//                     </Space>
//                     <Space vertical flex={1} size={10}>
//                       <div>身份证号:</div>
//                       <div>联系电话:</div>
//                     </Space>
//                   </Space>
//                   <Space size={20}>
//                     {[...'日期']}:{' '}
//                     <Space size={20}>{[...'年月日时分秒']}</Space>
//                   </Space>
//                 </Space>
//               </Space>
//             </td>
//           </tr>
//         </table>
//       </div>
//     </div>
//   );
// };
