import React from 'react';
export default () => <div></div>;
// import { DetailsData } from '../apis';
// import './index.less';
// import moment from 'moment';

// export default ({ data }: { data: DetailsData }) => {
//   const fromAuditTime = useMemo(() => {
//     const filteredList = data?.referralApplyAuditList?.filter(
//       item => item.status === '1' && item.type === '2'
//     );
//     if (filteredList?.length) {
//       return filteredList[filteredList.length - 1]?.auditTime;
//     }
//     return '';
//   }, [data]);

//   const toAuditTime = useMemo(() => {
//     const filteredList = data?.referralApplyAuditList?.filter(
//       item => item.status === '1' && item.type === '3'
//     );
//     if (filteredList?.length) {
//       return filteredList[filteredList.length - 1]?.auditTime;
//     }
//     return '';
//   }, [data]);

//   return (
//     <div className={'pdf-warp'}>
//       <div className={'header'}>
//         <div className={'subtitle'}>
//           重庆医科大学附属儿童医院双向转诊单
//           <div>（上转单）</div>
//         </div>
//       </div>
//       <div className={'body'}>
//         <table className={'table'}>
//           <tr className={'tr'}>
//             <td className={'td'}>姓名</td>
//             <td className={'td'}>{data?.patientName}</td>
//             <td className={'td'}>性别</td>
//             <td className={'td'}>{data?.patientSex}</td>
//             <td className={'td'}>年龄</td>
//             <td className={'td'}>{data?.patientAge}</td>
//             <td className={'td'}>家长联系电话</td>
//             <td className={'td'}>{data?.parentPhone}</td>
//           </tr>
//           <tr className={'tr'}>
//             <td className={'td'}>家庭地址</td>
//             <td className={'td'} colSpan={7}>
//               {data?.patientNative} {data?.patientAddress}
//             </td>
//           </tr>
//           <tr className={'tr'}>
//             <td className={'td'}>身份证号</td>
//             <td className={'td'} colSpan={5}>
//               {data?.patientIdNo}
//             </td>
//             <td className={'td'}>建议接诊专业</td>
//             <td className={'td'}>{data.toDeptName}</td>
//           </tr>
//           <tr className={'tr'}>
//             <td className={'td'} style={{ whiteSpace: 'nowrap' }}>
//               转出医院名称
//             </td>
//             <td className={'td'} colSpan={5}>
//               {data?.fromHisName}
//             </td>
//             <td className={'td'} style={{ whiteSpace: 'nowrap' }}>
//               医院等级
//             </td>
//             <td className={'td'}>{data?.fromHisLevel}</td>
//           </tr>
//           <tr className={'tr'}>
//             <td className={'td'}>转入医院名称</td>
//             <td className={'td'} colSpan={5}>
//               {data?.toHisName}
//             </td>
//             <td className={'td'}>申请就诊时间</td>
//             <td className={'td'}>
//               {data?.appointDate &&
//                 moment(data?.appointDate).format('YYYY-MM-DD')}
//             </td>
//           </tr>
//           <tr className={'tr'}>
//             <td className={'td value bold'} colSpan={8}>
//               <div>
//                 转诊医院意见:
//                 <span style={{ marginLeft: 20 }}>
//                   同 意
//                   <input type='checkbox' readOnly checked />
//                 </span>
//                 <span style={{ marginLeft: 25 }}>
//                   不同意
//                   <input type='checkbox' readOnly checked={false} />
//                 </span>
//               </div>
//               <div className={'info'}>
//                 <div className={'text-right'}>
//                   主管医生: {data?.fromDoctorName}
//                 </div>
//                 <div className={'text-right'}>
//                   联系电话:{data?.fromDoctorPhone}
//                 </div>
//                 <div className={'text-right'}>医院签章:</div>
//                 <div className={'text-right'}>
//                   日期:
//                   {fromAuditTime
//                     ? moment(fromAuditTime).format('YYYY-MM-DD')
//                     : ''}
//                 </div>
//               </div>
//             </td>
//           </tr>
//           <tr className={'tr'}>
//             <td className={'td value bold'} colSpan={8}>
//               <div>
//                 转入医院意见:
//                 <span style={{ marginLeft: 20 }}>
//                   同 意
//                   <input type='checkbox' readOnly checked />
//                 </span>
//                 <span style={{ marginLeft: 25 }}>
//                   不同意
//                   <input type='checkbox' readOnly checked={false} />
//                 </span>
//               </div>
//               <div className={'info'}>
//                 <div className={'text-right'}>
//                   主管医生: {data?.hospitalDoctorName}
//                 </div>
//                 <div className={'text-right'}>
//                   联系电话:{data?.hospitalDoctorPhone}
//                 </div>
//                 <div className={'text-right'}>
//                   日期:
//                   {toAuditTime ? moment(toAuditTime).format('YYYY-MM-DD') : ''}
//                 </div>
//               </div>
//             </td>
//           </tr>
//           <tr className={'tr'}>
//             <td className={'td value'} colSpan={8}>
//               <div>
//                 <span className={'bold'}>备注：</span>
//                 1、持本单到重庆医科大学附属儿童医院结账处办理入院。
//                 <p>
//                   2、重庆医科大学附属儿童医院渝中院区地址：重庆市渝中区中山二路136号；两江院区地址：重庆市两江新区金渝大道20号。
//                 </p>
//               </div>
//             </td>
//           </tr>
//         </table>
//       </div>
//     </div>
//   );
// };
