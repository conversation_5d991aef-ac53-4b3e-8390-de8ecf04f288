import React from 'react';
export default () => <div></div>;
// import { DetailsData } from '../apis';
// import Szx from './szx';
// import Xzs from './xzs';
// // import Zzd from './zzd';
// import Tyd from './tyd';
// import Jjd from './jjd';

// export interface TmplProps {
//   type: 'jjd' | 'szx' | 'xzs' | 'tyd';
//   data: DetailsData;
//   hospitalName?: string;
// }

// export default function Tmpl(props: TmplProps) {
//   switch (props.type) {
//     case 'szx':
//       return <Szx data={props.data} />;
//     case 'xzs':
//       return <Xzs data={props.data} />;
//     case 'tyd':
//       return <Tyd data={props.data} />;
//     case 'jjd':
//       return <Jjd data={props.data} />;
//   }
//   return <>PDF 数据异常</>;
// }
