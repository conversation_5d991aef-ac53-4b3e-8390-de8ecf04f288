import React from 'react';
export default () => <div></div>;
// import './index.less';
// import { DetailsData } from '../apis';
// import moment from 'moment';

// const Items = ({
//   value,
//   items,
//   other = '其他',
//   otherValue = value,
//   showOther = true
// }: {
//   value: any;
//   items: string[];
//   other?: string;
//   otherValue?: string;
//   showOther?: boolean;
// }) => {
//   return (
//     <>
//       {items.map((item, i) => (
//         <span style={{ marginLeft: i === 0 ? 10 : 20 }}>
//           {item}
//           <input type='checkbox' readOnly checked={value === item} />
//         </span>
//       ))}
//       {showOther && (
//         <span style={{ marginLeft: 20 }}>
//           {!items.includes('有') && other}
//           {!items.includes(value) && <div className='value'>{otherValue}</div>}
//         </span>
//       )}
//     </>
//   );
// };

// export default ({ data }: { data: DetailsData }) => {
//   console.log(data);
//   return (
//     <div className={'pdf-warp'}>
//       <div className={'header'}>
//         <div className={'subtitle'}>
//           重庆医科大学附属儿童医院双向转诊单
//           <div>双向转诊单患者记录单</div>
//         </div>
//       </div>
//       <hr />
//       <div className={'body'}>
//         <table className={'table'}>
//           <tr className={'tr narrow'}>
//             <td className={'td'}>
//               <div className='letter'>S</div>
//               <div>个人</div>
//               <div>信息</div>
//             </td>
//             <td className={'td'} colSpan={6}>
//               <div className={'td-row'}>
//                 <span>姓名：</span>
//                 <div className='value'>{data?.patientName}</div>
//                 <span>年龄：</span>
//                 <div className='value'>{data?.patientAge}</div>
//                 <span>性别：</span>
//                 <div className='value'>{data?.patientSex}</div>
//               </div>
//               <div className={'td-row'}>
//                 <span>转出医院：</span>
//                 <div className='value'>{data?.fromHisName}</div>
//               </div>
//               <div className={'td-row'}>
//                 <span>转出初步诊断：</span>
//                 <div className='value'>{data?.diagnosis}</div>
//               </div>
//               <div className={'td-row'}>
//                 <span>转出时间：</span>
//                 <div className='value'>
//                   {data?.appointDate &&
//                     moment(data?.appointDate).format('YYYY-MM-DD')}
//                 </div>
//               </div>
//               {data?.referralType === '住院' && (
//                 <div className={'td-row'}>
//                   <span>隔离措施：</span>
//                   <span style={{ marginLeft: 20 }}>
//                     无{' '}
//                     <input
//                       type='checkbox'
//                       readOnly
//                       checked={data.referralApplyExtradata?.glyq === '无'}
//                     />
//                   </span>
//                   <span style={{ marginLeft: 20 }}>有：</span>
//                   <span style={{ marginLeft: 20 }}>
//                     <span style={{ marginLeft: 10 }}>
//                       <input
//                         type='checkbox'
//                         readOnly
//                         checked={data.referralApplyExtradata?.glyq === '空气'}
//                       />{' '}
//                       空气
//                     </span>
//                     <span style={{ marginLeft: 10 }}>
//                       <input
//                         type='checkbox'
//                         readOnly
//                         checked={data.referralApplyExtradata?.glyq === '飞沫'}
//                       />
//                       飞沫
//                     </span>
//                     <span style={{ marginLeft: 10 }}>
//                       <input
//                         type='checkbox'
//                         readOnly
//                         checked={data.referralApplyExtradata?.glyq === '消化道'}
//                       />
//                       消化道
//                     </span>
//                     <span style={{ marginLeft: 10 }}>
//                       <input
//                         type='checkbox'
//                         readOnly
//                         checked={data.referralApplyExtradata?.glyq === '接触'}
//                       />
//                       接触
//                     </span>
//                     <span style={{ marginLeft: 10 }}>
//                       <input
//                         type='checkbox'
//                         readOnly
//                         checked={
//                           data.referralApplyExtradata?.glyq === '保护性隔离'
//                         }
//                       />
//                       保护性隔离
//                     </span>
//                     <span style={{ marginLeft: 10 }}>
//                       <input
//                         type='checkbox'
//                         readOnly
//                         checked={
//                           data.referralApplyExtradata?.glyq === '严密隔离'
//                         }
//                       />
//                       严密隔离
//                     </span>
//                   </span>
//                 </div>
//               )}
//             </td>
//           </tr>
//           <tr className={'tr narrow'}>
//             <td className={'td'}>
//               <div className='letter'>B</div>
//               <div>简要</div>
//               <div>病史</div>
//             </td>
//             <td className={'td value bold text'} colSpan={6}>
//               主诉及简要病史（转运原因）: {data?.resume}
//             </td>
//           </tr>
//           {data?.referralType === '住院' && (
//             <tr className={'tr narrow'}>
//               <td className={'td'}>
//                 <div className='letter'>A</div>
//                 <div>病情</div>
//                 <div>评估</div>
//               </td>
//               <td className={'td value bold narrow'} colSpan={6}>
//                 <div className={'td-row'}>
//                   <span>体温：</span>
//                   <div className='value'>
//                     {data.referralApplyExtradata?.temperature}
//                   </div>
//                   ℃<span style={{ marginLeft: 20 }}>脉搏：</span>
//                   <div className='value'>
//                     {data.referralApplyExtradata?.pulse}
//                   </div>
//                   次/分
//                   <span style={{ marginLeft: 20 }}>呼吸：</span>
//                   <div className='value'>
//                     {data.referralApplyExtradata?.breathing}
//                   </div>
//                   次/分
//                   <span style={{ marginLeft: 20 }}>血压：</span>
//                   <div className='value'>
//                     {data.referralApplyExtradata?.bloodPressure}
//                   </div>
//                   mmHg
//                 </div>
//                 <div className={'td-row'}>
//                   <span>SPO2：</span>
//                   <div className='value'>
//                     {data.referralApplyExtradata?.spo2}
//                   </div>
//                   %<span style={{ marginLeft: 20 }}>瞳孔：</span>
//                   <span style={{ marginLeft: 10, marginRight: 15 }}>左</span>
//                   反射
//                   <div className='value'>
//                     {data.referralApplyExtradata?.leftPupil}
//                   </div>
//                   <span style={{ marginLeft: 20, marginRight: 15 }}>右</span>
//                   反射
//                   <div className='value'>
//                     {data.referralApplyExtradata?.rightPupil}
//                   </div>
//                   <span style={{ marginLeft: 20 }}>体重：</span>
//                   <div className='value'>
//                     {data.referralApplyExtradata?.weight}
//                   </div>
//                   kg
//                 </div>
//                 <div className='td-row'>
//                   <span>意识：</span>
//                   <Items
//                     items={['清醒', '嗜睡', '意识模糊', '昏睡', '谵妄']}
//                     showOther={false}
//                     value={data.referralApplyExtradata?.consciousness}
//                   />
//                 </div>
//                 <div className='td-row'>
//                   <span>静脉通道：</span>
//                   <Items
//                     items={['无', '有', '单通道', '双通道']}
//                     showOther={false}
//                     value={data.referralApplyExtradata?.intravenousLine}
//                   />
//                 </div>
//                 <div className='td-row'>
//                   <span>静脉通道局部情况：</span>
//                   <Items
//                     items={['良好', '渗出', '肿胀']}
//                     value={data.referralApplyExtradata?.localConditions}
//                   />
//                 </div>
//                 <div className='td-row'>
//                   <span>深静脉置管：</span>
//                   <Items
//                     items={['无', '有']}
//                     value={data.referralApplyExtradata?.sjmzg}
//                   />
//                 </div>
//                 <div className='td-row'>
//                   <span>皮肤情况：</span>
//                   <Items
//                     items={['完整', '压疮']}
//                     other={'皮肤情况说明'}
//                     value={data.referralApplyExtradata?.pfqk}
//                     otherValue={data.referralApplyExtradata?.pfqkSm}
//                   />
//                 </div>
//                 <div className='td-row'>
//                   <span>置引流管情况：</span>
//                   <Items
//                     items={['无', '有']}
//                     value={data.referralApplyExtradata?.zylg}
//                     otherValue={data.referralApplyExtradata?.zylgSm}
//                   />
//                 </div>
//                 <div className='td-row'>
//                   <span>跌伤/坠床危险因素：</span>
//                   <Items
//                     items={['无', '有']}
//                     showOther={false}
//                     value={data.referralApplyExtradata?.dszcwxys}
//                   />
//                 </div>
//                 <div className='td-row'>
//                   <span>转科带药：</span>
//                   <span style={{ marginLeft: 10 }}>
//                     使用中：无
//                     <input
//                       type='checkbox'
//                       readOnly
//                       checked={data.referralApplyExtradata?.zkdysyz === '无'}
//                     />
//                   </span>
//                   <span style={{ marginLeft: 20 }}>
//                     有
//                     <input
//                       type='checkbox'
//                       readOnly
//                       checked={data.referralApplyExtradata?.zkdysyz === '有'}
//                     />
//                     ：
//                     <div className='value'>
//                       {data.referralApplyExtradata?.zkdysyzSm}
//                     </div>
//                   </span>
//                 </div>
//                 <div className='td-row'>
//                   <span style={{ marginLeft: 88 }}>
//                     尚未使用：无
//                     <input
//                       type='checkbox'
//                       readOnly
//                       checked={data.referralApplyExtradata?.zkdyswyz === '无'}
//                     />
//                   </span>
//                   <span style={{ marginLeft: 20 }}>
//                     有
//                     <input
//                       type='checkbox'
//                       readOnly
//                       checked={data.referralApplyExtradata?.zkdyswyz === '有'}
//                     />
//                     <div className='value'>
//                       {data.referralApplyExtradata?.zkdyswyzSm}
//                     </div>
//                   </span>
//                 </div>
//               </td>
//             </tr>
//           )}
//           <tr className={'tr narrow'}>
//             <td className={'td'}>
//               <div className='letter'>R</div>
//               <div>建议</div>
//             </td>
//             <td className={'td value'} colSpan={6}>
//               <div className={'td-row'}>
//                 <span>拟转入科室：</span>
//                 <div className='value'>{data?.toDeptName}</div>
//               </div>
//               <div className={'td-row'}>
//                 <span>其他需要说明的情况：</span>
//                 <div className='value'>{data?.otherContent}</div>
//               </div>
//             </td>
//           </tr>
//           <tr className={'tr narrow'}>
//             <td className={'td'}>
//               <div>签名</div>
//             </td>
//             <td className={'td value'} colSpan={6}>
//               <div className={'td-row'}>
//                 <span>转出医院人员签名：</span>
//                 <div className='value' style={{ width: '30%' }} />
//               </div>
//               <div className={'td-row'}>
//                 <span>接收医院人员签名：</span>
//                 <div className='value' style={{ width: '30%' }} />
//                 <span>接收时间：</span>
//                 <div className='value' style={{ width: '30%' }} />
//               </div>
//             </td>
//           </tr>
//         </table>
//       </div>
//     </div>
//   );
// };
