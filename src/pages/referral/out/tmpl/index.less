.pdf-warp {
  width: 100%;
  padding: 65px 55px 55px;
  background: #ffffff;
  user-select: none;

  .red {
    color: #f40;
  }

  .header {
    margin-bottom: 25px;
    text-align: center;

    .title {
      font-weight: 700;
      font-size: 26px;

      img {
        width: 580px;
        height: 131px;
      }
    }

    .subtitle {
      font-weight: 500;
      font-size: 24px;
    }
  }

  .body {
    width: 100%;

    .table {
      width: 100%;

      .tr {
        border-bottom: 1px solid #333333;

        &:first-child {
          border-top: 1px solid #333333;
        }
        &.narrow {
          > .td {
            padding: 12px 15px !important;
            > .td-row {
              margin-bottom: 2px;
            }
          }
        }
      }

      .bold {
        font-weight: 500;
      }

      .text {
        min-height: 80px;
      }
      .td {
        padding: 20px 15px;
        font-weight: 500;
        font-size: 16px;
        text-align: center;
        border-left: 1px solid #333333;

        .letter {
          font-weight: 600;
          font-size: 32px;
        }

        .td-row {
          margin-bottom: 6px;
          font-weight: 400;
          text-align: left;

          .value {
            position: relative;
            display: inline-block;
            margin-right: 15px;
            padding: 0 15px;

            &:after {
              position: absolute;
              right: 0;
              left: 0;
              display: block;
              width: 100%;
              height: 1px;
              background: #333333;
              content: ' ';
            }
          }
        }

        .info {
          margin-left: 60%;
        }

        &.value {
          font-weight: 400;
          text-align: left;
        }

        .bold {
          font-size: 18px;
        }

        .text-right {
          //text-align: right;
        }

        &:last-child {
          border-right: 1px solid #333333;
        }
      }
    }
  }
}
