import React from 'react';
import MyTableList from '@components/myTableList';
import useApi, { OperType } from '../apis';
import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  LinkButton,
  useModal,
  handleSubmit,
  useTableRowSelect
} from 'parsec-admin';
import { Button } from 'antd';
import env from '@src/configs/env';

export default () => {
  const hisId = env.hisId;
  const {
    data: { data: hospital }
  } = useApi.列出医院ID和医院名称({
    params: { hisId },
    initValue: {
      data: []
    }
  });
  const showModal = useModal(
    {
      onSubmit: values => {
        values.hisId = hisId;
        values.hospitalName = hospital?.find(
          ({ id }) => id === values.hospitalId
        )?.name;
        return handleSubmit(() =>
          useApi[values.id ? '修改转诊科室' : '添加转诊科室'].request(values)
        );
      },
      title: '添加 / 编辑科室',
      items: [
        {
          name: 'id',
          render: false
        },
        {
          label: '科室名称',
          name: 'name',
          required: true
        },
        {
          label: '所属医院',
          name: 'hospitalId',
          required: true,
          render: (
            <ArrSelect
              options={
                hospital?.map(({ name, id }) => ({
                  value: id,
                  children: name
                })) || []
              }
            />
          )
        }
      ]
    },
    [hospital]
  );
  return (
    <MyTableList
      action={<Button onClick={() => showModal()}>添加科室</Button>}
      {...useTableRowSelect([
        {
          actionText: '批量停用',
          onClick: ids =>
            useApi.批量修改转诊科室状态.request({
              ids: ids.join(),
              hisId,
              operType: OperType.停用
            })
        },
        {
          actionText: '批量启用',
          onClick: ids =>
            useApi.批量修改转诊科室状态.request({
              ids: ids.join(),
              hisId,
              operType: OperType.启用
            })
        },
        {
          actionText: '批量删除',
          danger: true,
          onClick: ids =>
            useApi.批量修改转诊科室状态.request({
              ids: ids.join(),
              hisId,
              operType: OperType.删除
            })
        }
      ])}
      columns={[
        {
          title: '名称',
          dataIndex: 'name',
          search: true
        },
        {
          title: '所属医院',
          dataIndex: 'hospitalName',
          search: true
        },
        {
          title: '状态',
          dataIndex: 'status',
          render: v => (v === '1' ? '启用' : v === '2' && '停用'),
          search: (
            <ArrSelect
              options={[
                { value: '1', children: '启用' },
                { value: '2', children: '停用' }
              ]}
            />
          )
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          render: record => {
            const actionText = record.status === '1' ? '停用' : '启用';
            return (
              <ActionsWrap>
                <LinkButton
                  onClick={() =>
                    actionConfirm(
                      () =>
                        useApi.批量修改转诊科室状态.request({
                          ids: record.id,
                          hisId,
                          operType:
                            record.status === '1'
                              ? OperType.停用
                              : OperType.启用
                        }),
                      actionText
                    )
                  }>
                  {actionText}
                </LinkButton>
                <LinkButton onClick={() => showModal(record)}>编辑</LinkButton>
                <LinkButton
                  onClick={() =>
                    actionConfirm(
                      () =>
                        useApi.批量修改转诊科室状态.request({
                          ids: record.id,
                          hisId,
                          operType: OperType.删除
                        }),
                      '删除'
                    )
                  }>
                  删除
                </LinkButton>
              </ActionsWrap>
            );
          }
        }
      ]}
      getList={({ pagination: { current }, params }) => {
        return useApi.分页查询科室信息.request({
          page: current,
          limit: 10,
          ...params
        });
      }}
    />
  );
};
