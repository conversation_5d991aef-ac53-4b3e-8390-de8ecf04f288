import React, { useCallback, useMemo, useState } from 'react';
import useApi from '../apis';
import { ArrSelect, CardLayout } from 'parsec-admin';
import { Wrapper } from '@pages/statistics/user';
import NavItem from '@pages/statistics/user/components/navItem';
import SelectDate from '@pages/index/Charts/SelectDate';
import { LineChart } from '@pages/statistics/user/components/userStatics';
import { Form, Radio } from 'antd';
import env from '@src/configs/env';

export default () => {
  const hisId = env.hisId;
  const {
    data: {
      data: {
        totalReferralIn = 0,
        totalReferralInPass = 0,
        totalReferralOut = 0,
        totalReferralOutPass = 0,
        avgAuditTime = ''
      } = {}
    }
  } = useApi.转诊概况分析({
    params: { hisId },
    initValue: {
      data: {}
    }
  });
  const items = useMemo(
    () => [
      {
        title: '转出数量',
        img: '',
        number: totalReferralOut
      },
      {
        title: '转出通过数量',
        img: '',
        number: totalReferralOutPass
      },
      {
        title: '转入数量',
        img: '',
        number: totalReferralIn
      },
      {
        title: '转入通过数量',
        img: '',
        number: totalReferralInPass
      },

      {
        title: '审核平均响应时间',
        img: '',
        number: avgAuditTime
      }
    ],
    [
      avgAuditTime,
      totalReferralIn,
      totalReferralInPass,
      totalReferralOut,
      totalReferralOutPass
    ]
  );
  return (
    <div style={{ overflow: 'hidden' }}>
      <CardLayout>
        <Wrapper>
          <h5>转诊概况</h5>
          <div className='head'>
            {items.map(item => (
              <NavItem
                key={item.title}
                img={item.img}
                title={item.title}
                number={item.number}
                unit={' '}
              />
            ))}
          </div>
          <h5>转诊数量</h5>
          <Part1 />
          <h5>平均响应速度</h5>
          <Part2 />
        </Wrapper>
      </CardLayout>
    </div>
  );
};

const Part1 = () => {
  const hisId = env.hisId;
  const [params1, setParams1] = useState<
    [string | undefined, string | undefined]
  >();
  const [deptId, setDeptId] = useState<number>();
  const [referralType, setReferralType] = useState<string>('in');
  const {
    data: { data: hospital }
  } = useApi.列出医院ID和医院名称({
    params: { hisId },
    initValue: {
      data: []
    }
  });
  const [hospitalId, setHospitalId] = useState<number>();
  const {
    data: { data: dept }
  } = useApi.列出科室ID和科室名称({
    params: { hisId, hospitalId },
    initValue: {
      data: []
    }
  });
  const { loading: loading1, data: { data: data1 } = {} } = useApi.转诊数量分析(
    {
      params: {
        referralType,
        deptId,
        hospitalId,
        hisId,
        startDate: params1?.[0] || '',
        endDate: params1?.[1] || ''
      },
      needInit: !!params1?.[0],
      initValue: {
        data: []
      }
    }
  );
  const { loading: loading2, data: { data: data2 } = {} } = useApi.转诊数量分析(
    {
      params: {
        referralType,
        deptId,
        hospitalId,
        status: 2,
        hisId,
        startDate: params1?.[0] || '',
        endDate: params1?.[1] || ''
      },
      needInit: !!params1?.[0],
      initValue: {
        data: []
      }
    }
  );
  return (
    <>
      <Form layout={'inline'}>
        <Form.Item>
          <Radio.Group
            value={referralType}
            buttonStyle='solid'
            onChange={v => setReferralType(v.target.value)}>
            <Radio.Button value='in'>转入</Radio.Button>
            <Radio.Button value='out'>转出</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <div className={'date'} style={{ marginTop: -10, marginRight: 10 }}>
          <SelectDate
            hideToday
            onChange={useCallback(
              ([date, date2]) =>
                setParams1([
                  date.format('YYYY-MM-DD'),
                  date2.format('YYYY-MM-DD')
                ]),
              []
            )}
          />
        </div>
        <Form.Item label={'医院'}>
          <ArrSelect
            dropdownMatchSelectWidth={false}
            virtual={false}
            onChange={(v: any) => {
              setDeptId(undefined);
              setHospitalId(v);
            }}
            options={
              hospital?.map(({ name, id }) => ({
                value: id,
                children: name
              })) || []
            }
          />
        </Form.Item>
        <Form.Item label={'科室'}>
          <ArrSelect
            dropdownMatchSelectWidth={false}
            virtual={false}
            value={deptId}
            onChange={(v: any) => setDeptId(v)}
            options={
              dept?.map(({ name, id }) => ({
                value: id,
                children: name
              })) || []
            }
          />
        </Form.Item>
      </Form>
      <div className='container' style={{ height: 500 }}>
        <LineChart
          xData={useMemo(() => data1?.map(({ dt }) => dt) || [], [data1])}
          options={useMemo(
            () => [
              {
                name: '发起转诊数量',
                data: data1?.map(({ totalSum }) => totalSum) || [],
                color: '#D7E8F7'
              },
              {
                name: '转诊通过数量',
                data: data2?.map(({ totalSum }) => totalSum) || [],
                color: '#A4E1DB'
              }
            ],
            [data1, data2]
          )}
          loading={loading1 || loading2}
        />
      </div>
    </>
  );
};

const Part2 = () => {
  const hisId = env.hisId;
  const [params1, setParams1] = useState<
    [string | undefined, string | undefined]
  >();
  const [deptId, setDeptId] = useState<number>();

  const {
    data: { data: hospital }
  } = useApi.列出医院ID和医院名称({
    params: { hisId },
    initValue: {
      data: []
    }
  });
  const [hospitalId, setHospitalId] = useState<number>();
  const {
    data: { data: dept }
  } = useApi.列出科室ID和科室名称({
    params: { hisId, hospitalId },
    initValue: {
      data: []
    }
  });
  const {
    loading,
    data: { data: { dept: data1, mch: data2 } = {} } = {}
  } = useApi.转诊响应速度分析({
    params: {
      deptId,
      hospitalId,
      hisId,
      startDate: params1?.[0] || '',
      endDate: params1?.[1] || ''
    },
    needInit: !!params1?.[0],
    initValue: {
      data: {
        dept: [],
        mch: []
      }
    }
  });
  return (
    <>
      <Form layout={'inline'}>
        <div className={'date'} style={{ marginTop: -10, marginRight: 10 }}>
          <SelectDate
            hideToday
            onChange={useCallback(
              ([date, date2]) =>
                setParams1([
                  date.format('YYYY-MM-DD'),
                  date2.format('YYYY-MM-DD')
                ]),
              []
            )}
          />
        </div>
        <Form.Item label={'医院'}>
          <ArrSelect
            dropdownMatchSelectWidth={false}
            virtual={false}
            onChange={(v: any) => {
              setDeptId(undefined);
              setHospitalId(v);
            }}
            options={
              hospital?.map(({ name, id }) => ({
                value: id,
                children: name
              })) || []
            }
          />
        </Form.Item>
        <Form.Item label={'科室'}>
          <ArrSelect
            dropdownMatchSelectWidth={false}
            virtual={false}
            value={deptId}
            onChange={(v: any) => setDeptId(v)}
            options={
              dept?.map(({ name, id }) => ({
                value: id,
                children: name
              })) || []
            }
          />
        </Form.Item>
      </Form>
      <div className='container' style={{ height: 500 }}>
        <LineChart
          xData={useMemo(() => data1?.map(({ dt }) => dt) || [], [data1])}
          options={useMemo(
            () => [
              {
                name: '科室审核响应速度',
                data: data1?.map(({ totalSum }) => totalSum) || [],
                color: '#D7E8F7'
              },
              {
                name: '双转办审核响应速度',
                data: data2?.map(({ totalSum }) => totalSum) || [],
                color: '#A4E1DB'
              }
            ],
            [data1, data2]
          )}
          loading={loading}
        />
      </div>
    </>
  );
};
