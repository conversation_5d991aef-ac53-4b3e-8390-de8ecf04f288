import createApiHooks from 'create-api-hooks';
import { ListApiResponseData, ApiResponse } from '@src/configs/apis';
import request from '@utils/jsonRequest';

export enum OperType {
  启用 = 'valid',
  停用 = 'invalid',
  删除 = 'del'
}

// export interface DetailsData {
//   id: string;
//   createTime: string;
//   updateTime: string;
//   patHisId: string;
//   patCardNo: string;
//   hospitalNo: string;
//   patientName: string;
//   treatmentType: string;
//   patientInsure: string;
//   patientIdNo: string;
//   patientPhone: string;
//   patientAge: string;
//   patientBirthday: string;
//   patientSex: string;
//   patientAddress: string;
//   patientNative: string;
//   patientCase: string;
//   parentName: string;
//   parentIdNo: string;
//   parentPhone: string;
//   parentRelation: string;
//   fromhisId: string;
//   fromHisName: string;
//   fromHisLevel: string;
//   fromDeptId: string;
//   fromDeptName: string;
//   fromDoctorId: number;
//   fromDoctorName: string;
//   tohisId: string;
//   toHisName: string;
//   toHisLevel: string;
//   toDeptId: number;
//   toDeptName: string;
//   referralType: string;
//   appointDate: string;
//   confimDate: string;
//   diagnosis: string;
//   resume: string;
//   otherContent: string;
//   reports: string;
//   consentUrl: string;
//   remark: string;
//   applyUrl: string;
//   hospitalDoctorId: string;
//   hospitalDoctorName: string;
//   status: string;
//   hisCostTime: number;
// }

export interface DetailsData {
  id: 1626060374831;
  openId: null;
  patHisId: null;
  patCardNo: 'S8002026759';
  patientName: '符川';
  treatmentType: '1';
  patientIdNo: '500242199306302233';
  patientAge: '28';
  patientBirthday: '1993-06-30 00:00:00';
  patientSex: 'M';
  patientAddress: null;
  patientInsure: '4';
  patientNative: '重庆市-重庆市-渝北区';
  parentName: '无';
  parentIdNo: '无';
  parentPhone: '***********';
  patientPhone: '***********';
  parentRelation: '5';
  hisId: 2219;
  hisName: '重庆医科大学附属口腔医院';
  doctorId: '2208';
  doctorName: '鲁琦';
  deptId: '016';
  deptName: '颌面外二科';
  hospitalId: 1610962279854;
  hospitalLevel: '11';
  hospitalName: string;
  hospitalDeptId: '1610962299510';
  hospitalDeptName: '下级放射科';
  diagnosis: '无|肝癌|无;无|直肠癌|无';
  consentUrl: string;
  applyUrl: null;
  applyType: 1;
  hospitalDoctorId: 190829000000919;
  hospitalDoctorName: null;
  auditProcess: '6';
  auditFailReason: null;
  auditLastTime: '2021-07-12 14:28:20';
  repeatTime: 0;
  status: '2';
  remark: '231azf';
  createTime: '2021-07-12 11:26:15';
  updateTime: '2021-07-12 14:29:50';
  patientCase: '{"patientNative":["重庆市","重庆市","渝北区"],"id":1297,"name":"符川","hisId":2219,"hisName":null,"userId":33400,"channelType":null,"patientType":0,"relationType":5,"idType":1,"idNo":"500242199306302233","sex":"M","birthday":"1993-06-30 00:00:00","mobile":"***********","address":"","bindStatus":1,"parentName":null,"parentIdType":0,"parentIdNo":null,"patCardType":6,"patCardNo":"S8002026759","consumeType":null,"isDefalut":0,"isSelf":null,"syncStatus":null,"type":5,"idImage":null,"patInNo":"","bindMedicareCard":0,"height":null,"weight":"32123","married":null,"smoking":null,"patHisId":"","patHisNo":"**********","createTime":"2021-03-31 11:41:05","updateTime":"2021-07-07 10:46:39","accountId":null,"openId":"oGNvot1GObWeCap6HEWNOdYCxRDs","cardStatus":5,"healthCardId":null,"qrCodeText":null,"nation":null,"country":null,"birthPlace":null,"school":null,"patientImage":"","hospitalUserId":null,"patientId":null,"patientName":null,"patientMobile":null,"patientSex":null,"isDefault":null,"patientImg":null,"patientAge":null,"platformId":null,"patientAddress":null,"realName":null,"birth":null,"platformSource":null,"age":null,"inquiryTime":null,"inquiryDept":null,"inquiryDoctor":null,"inquiryPurpose":null,"userName":null,"startDate":null,"endDate":null,"patientIds":null,"treatmentType":{"value":"1","label":"门诊"},"patientInsure":{"value":"4","label":"异地农合医保"}}';
  reports: string;
  mainPresentMedicalHistory: '并噶';
  mainPassedMedicalHistory: '没噶';
  mainPhysicalCharacteristics: 'awef';
  takedMedicalTreatment: '没有';
  referralReason: 'qgs';
  auditMethod: null;
  createDate: '2021-07-12 11:26:15';
  auditLastDate: null;
  auditProcessName: '转诊成功';
  treatmentTypeName: '门诊';
  patientInsureName: '异地农合医保';
  auditStatus: null;
  auditId: null;
  hisAuditDoctorId: null;
  hisAuditDoctorName: null;
  hospitalAuditDoctorId: null;
  hospitalAuditDoctorName: null;
  appointDate: string;
  hisLevelName: string;
  referralAuditList: [
    {
      id: 1626060374918;
      hisId: 2219;
      hisName: '重庆医科大学附属口腔医院';
      applyId: 1626060374831;
      type: '1';
      typeName: '转出医院科室审核';
      hospitalId: 1610962279854;
      hisDoctorId: 220;
      hisDoctorName: null;
      hospitalDoctorId: null;
      hospitalDoctorName: null;
      status: '2';
      auditTime: '2021-07-12 11:29:17';
      auditSecond: 183;
      extenField: null;
      remarker: null;
      createTime: '2021-07-12 11:26:15';
      updateTime: '2021-07-12 11:29:17';
      createDate: '2021-07-12 11:26:15';
      referralApply: null;
    }
  ];
  referralAuditLogsList: [
    {
      id: 1;
      applyId: 1626060374831;
      hisId: 2219;
      hisDoctorId: 2208;
      hisDoctorName: '卢琦';
      type: '2';
      typeName: '转出医院科室审核';
      nextType: '4';
      nextTypeName: '转入医院科室审核';
      status: string;
      auditTime: '2021-07-13 09:52:01';
      remarker: null;
      createTime: '2021-07-13 09:52:06';
      updateTime: '2021-07-13 09:52:06';
    }
  ];
  vagueName: null;
  startDate: null;
  endDate: null;
  doctorPhone: null;
  hospitalDeptAuditDoctorPhone: null;
  hospitalLevelName: '一级丙等';
  referralDoctorId: null;
}

export default {
  转诊概况分析: createApiHooks(data =>
    request.post<
      ApiResponse<{
        totalReferral: number;
        totalReferralIn: number;
        totalReferralInPass: number;
        totalReferralOut: number;
        totalReferralOutPass: number;
        totalAdoptReferral: number;
        avgAuditTime: string;
      }>
    >('/mch/health/api/referral/index/survey', data)
  ),
  分页查询医院信息: createApiHooks(data =>
    request.post<ListApiResponseData<Record<string, unknown>>>(
      '/mch/health/api/referral/hospital/listPage',
      data
    )
  ),
  分页查询职能部门信息: createApiHooks(data =>
    request.post<ListApiResponseData<Record<string, unknown>>>(
      '/mch/health/api/referral/department/listPage',
      data
    )
  ),
  分页查询转诊医生信息: createApiHooks(data =>
    request.post<ListApiResponseData<Record<string, unknown>>>(
      '/mch/health/api/referral/doctor/listPage',
      data
    )
  ),
  分页查询科室信息: createApiHooks(data =>
    request.post<ListApiResponseData<Record<string, unknown>>>(
      '/mch/health/api/referral/dept/listPage',
      data
    )
  ),
  分页查询申请信息: createApiHooks(data =>
    request.post<ListApiResponseData<Record<string, unknown>>>(
      '/mch/health/api/referral/apply/list',
      data
    )
  ),
  转诊医院批量修改状态: createApiHooks(
    (data: { ids: string; hisId: string; operType: OperType }) =>
      request.post('/mch/health/api/referral/hospital/updateBatch', data)
  ),
  批量修改转诊医生状态: createApiHooks(
    (data: { ids: string; hisId: string; operType: OperType }) =>
      request.post('/mch/health/api/referral/doctor/updateBatch', data)
  ),
  批量修改转诊科室状态: createApiHooks(
    (data: { ids: string; hisId: string; operType: OperType }) =>
      request.post('/mch/health/api/referral/dept/updateBatch', data)
  ),
  批量修改职能部门状态: createApiHooks(
    (data: { ids: string; hisId: string; operType: OperType }) =>
      request.post('/mch/health/api/referral/department/updateBatch', data)
  ),
  添加转诊医院: createApiHooks(data =>
    request.post('/mch/health/api/referral/hospital/add', data)
  ),
  添加职能部门: createApiHooks(data =>
    request.post('/mch/health/api/referral/department/add', data)
  ),
  添加转诊科室: createApiHooks(data =>
    request.post('/mch/health/api/referral/dept/add', data)
  ),
  添加转诊医生: createApiHooks(data =>
    request.post('/mch/health/api/referral/doctor/add', data)
  ),
  修改转诊医生: createApiHooks(data =>
    request.post('/mch/health/api/referral/doctor/update', data)
  ),
  修改职能部门: createApiHooks(data =>
    request.post('/mch/health/api/referral/department/update', data)
  ),
  修改转诊科室: createApiHooks(data =>
    request.post('/mch/health/api/referral/dept/update', data)
  ),
  修改转诊医院: createApiHooks(data =>
    request.post('/mch/health/api/referral/hospital/update', data)
  ),
  列出医院ID和医院名称: createApiHooks(data =>
    request.post<
      ApiResponse<{ name: string; id: number; auditMethod?: string }[]>
    >('/mch/health/api/referral/hospital/selection', data)
  ),
  列出科室ID和科室名称: createApiHooks(data =>
    request.post<ApiResponse<{ name: string; id: number; no: string }[]>>(
      '/mch/health/api/referral/dept/selection',
      data
    )
  ),
  列出子部门ID和子部门名称: createApiHooks(data =>
    request.post<ApiResponse<{ name: string; id: number }[]>>(
      '/mch/health/api/referral/department/selection',
      data
    )
  ),
  根据条件查询转诊医生及配置信息: createApiHooks(data =>
    request.post<ApiResponse<any>>(
      '/mch/health/api/referral/doctor/getBy',
      data
    )
  ),
  获取审核流程信息: createApiHooks(data =>
    request.post<
      ApiResponse<{
        auditList: {
          typeName: string;
          status: string;
          remarker: string;
          id: number;
        }[];
      }>
    >('/mch/health/api/referral/audit/getBy', data)
  ),
  确认审核流程: createApiHooks(data =>
    request.post('/mch/health/api/referral/audit/confirm', data)
  ),
  获取转诊申请详情: createApiHooks(data =>
    request.post<ApiResponse<any>>('/mch/health/api/referral/apply/info', data)
  ),
  批量导入医生: createApiHooks((data: { fileUrl: string; hisId: number }) =>
    request.post('/mch/health/api/referral/doctor/addBatch', data)
  ),
  转诊数量分析: createApiHooks(
    (data: {
      hisId: string;
      startDate: string;
      referralType?: string;
      endDate: string;
      status?: number;
      deptId?: number;
      hospitalId?: number;
    }) =>
      request.post<ApiResponse<{ dt: string; totalSum: number }[]>>(
        '/mch/health/api/referral/index/apply/analysis',
        data
      )
  ),
  转诊响应速度分析: createApiHooks(
    (data: {
      hisId: string;
      startDate: string;
      endDate: string;
      status?: number;
      deptId?: number;
      hospitalId?: number;
    }) =>
      request.post<
        ApiResponse<{
          dept: { dt: string; totalSum: number }[];
          mch: { dt: string; totalSum: number }[];
        }>
      >('/mch/health/api/referral/index/apply/speed/analysis', data)
  ),
  重置医生密码: createApiHooks((data: { hisId: string; doctorId: number }) =>
    request.post('/mch/health/api/referral/doctor/resetpassword', data)
  )
};
