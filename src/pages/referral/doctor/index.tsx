import React from 'react';
import MyTableList from '@components/myTableList';
import useApi, { OperType } from '../apis';
import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  LinkButton,
  useTableRowSelect
} from 'parsec-admin';
import { Button, Space } from 'antd';
import { useHistory } from 'react-router-dom';
import env from '@src/configs/env';

export default () => {
  const hisId = env.hisId;
  const history = useHistory();
  return (
    <MyTableList
      action={
        <Space>
          <Button onClick={() => history.push('/referral/doctor/add')}>
            添加医生
          </Button>
          {/* <Button onClick={() => showImportModal()}>批量导入</Button> */}
        </Space>
      }
      {...useTableRowSelect([
        {
          actionText: '批量停用',
          onClick: ids =>
            useApi.批量修改转诊医生状态.request({
              ids: ids.join(),
              hisId,
              operType: OperType.停用
            })
        },
        {
          actionText: '批量启用',
          onClick: ids =>
            useApi.批量修改转诊医生状态.request({
              ids: ids.join(),
              hisId,
              operType: OperType.启用
            })
        },
        {
          actionText: '批量删除',
          danger: true,
          onClick: ids =>
            useApi.批量修改转诊医生状态.request({
              ids: ids.join(),
              hisId,
              operType: OperType.删除
            })
        }
      ])}
      columns={[
        {
          title: '名称',
          dataIndex: 'name',
          search: true
        },
        {
          title: '工号',
          dataIndex: 'doctorId'
        },
        {
          title: '职称',
          dataIndex: 'level'
        },
        {
          title: '科室',
          dataIndex: 'deptName',
          search: true
        },
        {
          title: '状态',
          dataIndex: 'status',
          render: v => (v === '1' ? '启用' : v === '2' && '停用'),
          search: (
            <ArrSelect
              options={[
                { value: '1', children: '启用' },
                { value: '2', children: '停用' }
              ]}
            />
          )
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          render: record => {
            const actionText = record.status === '1' ? '停用' : '启用';
            return (
              <ActionsWrap>
                <LinkButton
                  onClick={() =>
                    actionConfirm(
                      () =>
                        useApi.批量修改转诊医生状态.request({
                          ids: record.id,
                          hisId,
                          operType:
                            record.status === '1'
                              ? OperType.停用
                              : OperType.启用
                        }),
                      actionText
                    )
                  }>
                  {actionText}
                </LinkButton>
                <LinkButton
                  onClick={() => history.push(`/referral/doctor/${record.id}`)}>
                  编辑
                </LinkButton>
                {/* <LinkButton
                  onClick={() =>
                    actionConfirm(
                      () =>
                        useApi.重置医生密码.request({
                          hisId,
                          doctorId: record.id
                        }),
                      '重置密码'
                    )
                  }>
                  重置密码
                </LinkButton> */}
                <LinkButton
                  onClick={() =>
                    actionConfirm(
                      () =>
                        useApi.批量修改转诊医生状态.request({
                          ids: record.id,
                          hisId,
                          operType: OperType.删除
                        }),
                      '删除'
                    )
                  }>
                  删除
                </LinkButton>
              </ActionsWrap>
            );
          }
        }
      ]}
      getList={({ pagination: { current }, params }) => {
        return useApi.分页查询转诊医生信息.request({
          page: current,
          limit: 10,
          ...params
        });
      }}
    />
  );
};
