import React, { useEffect } from 'react';
import {
  FormDescriptions,
  CardLayout,
  UploadImg,
  FixedFormActions,
  Form,
  CheckboxGroup,
  ArrSelect,
  handleSubmit
} from 'parsec-admin';
import useApi from '../../apis';
import { useParams } from 'react-router-dom';
import { useSessionStorage } from 'react-use';
import { Input, Button, Space, InputNumber } from 'antd';
import { useHistory } from 'react-router';
import { useEffectState } from 'parsec-hooks';
import env from '@src/configs/env';

const { TextArea } = Input;

export default () => {
  const { id } = useParams<{ id: string }>();
  const isAdd = id === 'add';
  const hisId = env.hisId;
  const [hisName] = useSessionStorage<any>('hisName');
  const {
    data: {
      data: [data]
    },
    loading: detailLoading
  } = useApi.根据条件查询转诊医生及配置信息({
    params: { id, hisId },
    needInit: !isAdd,
    initValue: { data: [{}] }
  });
  useEffect(() => {
    if (data.adminConfig) {
      data.adminConfig = data.adminConfig.split(',');
    }
  }, [data]);
  const [hospitalId, setHospitalId] = useEffectState<number>(data?.hospitalId);
  const {
    data: { data: hospital }
  } = useApi.列出医院ID和医院名称({
    params: { hisId },
    initValue: {
      data: []
    }
  });

  const {
    data: { data: dept }
  } = useApi.列出科室ID和科室名称({
    params: { hisId, hospitalId },
    needInit: !!hospitalId,
    initValue: {
      data: []
    }
  });
  const {
    data: { data: department }
  } = useApi.列出子部门ID和子部门名称({
    params: { hisId, hospitalId },
    needInit: !!hospitalId,
    initValue: {
      data: []
    }
  });
  const [form] = Form.useForm();
  const history = useHistory();
  const { request: submit, loading } = useApi[
    isAdd ? '添加转诊医生' : '修改转诊医生'
  ]({ needInit: false });
  return (
    <CardLayout title={'医生信息'} loading={detailLoading}>
      <FormDescriptions
        data={data}
        form={form}
        formProps={{
          requiredMark: true
        }}
        edit
        items={[
          {
            label: '医生头像',
            span: 3,
            name: 'image',
            formItemProps: {
              extra: '建议尺寸为 120px * 134px',
              render: <UploadImg arrValue={false} />
            }
          },
          { label: '姓名', name: 'name', required: true },
          { label: '工号', name: 'doctorId', required: true },
          { label: '职称', name: 'level', required: true },
          {
            label: '从业年限',
            name: 'workingLife',
            required: true,
            formItemProps: {
              render: () => (
                <InputNumber
                  placeholder={'从业年限'}
                  style={{ width: '100%' }}
                />
              )
            }
          },
          { label: '手机号', name: 'mobile', required: true },
          {
            label: '医院',
            name: 'hospitalId',
            required: true,
            formItemProps: {
              render: (
                <ArrSelect
                  onChange={(v: any) => {
                    form.resetFields(['deptId']);
                    data.hospitalId = v;
                    setHospitalId(v);
                  }}
                  options={
                    hospital?.map(({ name, id }) => ({
                      value: id,
                      children: name
                    })) || []
                  }
                />
              )
            }
          },
          {
            label: '科室',
            name: 'deptId',
            required: true,
            formItemProps: {
              render: (
                <ArrSelect
                  disabled={!hospitalId}
                  stringValue
                  options={
                    dept?.map(({ name, id }) => ({
                      value: id + '',
                      children: name
                    })) || []
                  }
                />
              )
            }
          },
          {
            label: '职能部门',
            name: 'deptmentId',
            formItemProps: {
              render: (
                <ArrSelect
                  disabled={!hospitalId}
                  options={
                    department?.map(({ name, id }) => ({
                      value: id,
                      children: name
                    })) || []
                  }
                />
              )
            }
          },
          {
            label: '转诊管理',
            name: 'adminConfig',
            span: 3,
            formItemProps: {
              render: (
                <CheckboxGroup
                  hideCheckAll
                  options={[
                    { value: '1', label: '发起转诊' },
                    { value: '2', label: '查看转诊列表' },
                    { value: '3', label: '科室审核' },
                    { value: '4', label: '双转办审核' }
                  ]}
                />
              )
            }
          },
          // {
          //   name: 'a',
          //   formItemProps: {
          //     render: false
          //   }
          // },
          // {
          //   label: '转诊管理',
          //   name: 'b',
          //   span: 3,
          //   hidden: !currentHospital?.auditMethod,
          //   formItemProps: {
          //     valuePropName: 'checked',
          //     render: <Switch />,
          //     extra: (
          //       <div style={{ marginLeft: 20, marginTop: 10, width: 400 }}>
          //         <Form.Item shouldUpdate noStyle>
          //           {({ getFieldValue }) => {
          //             const checked = getFieldValue('b');
          //             return (
          //               <FormDescriptions
          //                 form={form}
          //                 edit
          //                 items={[
          //                   {
          //                     label: '监管端',
          //                     name: 'reviewAdmin',
          //                     hidden: !['1', '3'].includes(
          //                       currentHospital?.auditMethod || ''
          //                     ),
          //                     formItemProps: {
          //                       render: () => (
          //                         <CheckboxGroup
          //                           disabled={!checked}
          //                           hideCheckAll
          //                           options={[{ value: 1, label: '转诊管理' }]}
          //                         />
          //                       )
          //                     }
          //                   },
          //                   {
          //                     label: '医生端',
          //                     name: 'reviewDoctor',
          //                     hidden: !['2', '3'].includes(
          //                       currentHospital?.auditMethod || ''
          //                     ),
          //                     formItemProps: {
          //                       render: (
          //                         <CheckboxGroup
          //                           disabled={!checked}
          //                           hideCheckAll
          //                           options={[
          //                             { value: 1, label: '转诊管理' },
          //                             { value: 2, label: '审核' }
          //                           ]}
          //                         />
          //                       )
          //                     }
          //                   }
          //                 ]}
          //               />
          //             );
          //           }}
          //         </Form.Item>
          //       </div>
          //     )
          //   }
          // },
          {
            label: '擅长领域',
            name: 'specialty',
            formItemProps: {
              render: <TextArea />
            }
          },
          {
            label: '医生介绍',
            name: 'introduction',
            formItemProps: {
              render: <TextArea />
            }
          }
        ]}
      />
      <br />
      <br />
      <FixedFormActions>
        <Space>
          <Button onClick={() => history.goBack()}>取消</Button>
          <Button
            loading={loading}
            type={'primary'}
            onClick={() => {
              form.validateFields().then(values => {
                values.adminConfig = values.adminConfig?.join(',');
                values.deptmentName = department?.find(
                  ({ id }) => id === values.deptmentId
                )?.name;
                values.deptName = dept?.find(
                  ({ no }) => no + '' === values.deptId
                )?.name;
                values.hospitalName = hospital?.find(
                  ({ id }) => id === values.hospitalId
                )?.name;
                values.hisName = hisName;
                values.hisId = hisId;
                if (!isAdd) {
                  values.id = id;
                }
                // delete values.a;
                // delete values.b;
                console.log(values);
                handleSubmit(() => submit(values)).then(() => history.goBack());
              });
            }}>
            提交
          </Button>
        </Space>
      </FixedFormActions>
    </CardLayout>
  );
};
