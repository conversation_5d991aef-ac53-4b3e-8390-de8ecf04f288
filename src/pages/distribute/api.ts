import createApiHooks from 'create-api-hooks';
import { ApiResponse } from '@apiHooks';
import { request } from 'parsec-admin';
import env from '@src/configs/env';

export default {
  查询科室分布: createApiHooks(() =>
    request.get<ApiResponse<any>>('/mch/his/microDept', {
      params: { hisId: env.hisId }
    })
  ),
  新增科室分布: createApiHooks(
    (data: { hisId: string; pid: string; name: string; depts: string }) =>
      request.post<ApiResponse<any>>('/mch/his/microDept', data)
  ),
  编辑科室分布: createApiHooks(
    (data: {
      hisId: string;
      id: string | number;
      name: string;
      depts: string;
    }) =>
      request.put<ApiResponse<any>>('/mch/his/microDept', data, {
        headers: { 'Content-Type': 'formData' }
      })
  ),
  删除科室分布目录: createApiHooks((data: { id: string | number }) =>
    request.delete<ApiResponse<any>>(`/mch/his/microDept/${data?.id}`)
  )
};
