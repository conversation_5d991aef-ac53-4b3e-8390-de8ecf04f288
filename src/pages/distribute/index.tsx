import {
  actionConfirm,
  ActionsWrap,
  CardLayout,
  handleSubmit,
  LinkButton,
  useModal
} from 'parsec-admin';
import { Button, Table, Tabs } from 'antd';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import styled from 'styled-components';

import useApi from './api';
import env from '@src/configs/env';
import classNames from 'classnames';

const { TabPane } = Tabs;

export default () => {
  const [currentTab, setCurrentTab] = useState<number>(0);
  const [currentBuild, setCurrentBuild] = useState<number>(0);

  //限定第一次渲染或者切换院区
  const firstRenderOrToggleTab = useRef<boolean>(true);

  const reloadTable = useCallback((value: number | string) => {
    firstRenderOrToggleTab.current = true;
    setCurrentBuild(0);
    setCurrentTab(Number(value));
  }, []);

  const {
    data: { data },
    request,
    loading
  } = useApi.查询科室分布({
    needInit: true,
    initValue: []
  });

  const arrayGroup = useMemo(() => {
    return data?.reduce(
      (store, item) => {
        store[0].push({
          id: item.id,
          name: item.name,
          pid: item.pid
        });
        const innerPush = (arr: any, level: number) => {
          if (arr && arr?.length > 0) {
            arr?.forEach(item2 => {
              store[level].push({
                id: item2.id,
                name: item2.name,
                pid: item2.pid,
                depts: item2.depts
              });
              innerPush(item2.children, 2);
            });
          }
        };

        innerPush(item?.children, 1);

        return store;
      },
      [[], [], []]
    );
  }, [data]);

  const selectHospitalArea = useMemo(() => {
    if (arrayGroup?.[0] && arrayGroup?.[0]?.length > 0) {
      return arrayGroup?.[0]?.[currentTab];
    }
    return {};
  }, [currentTab, arrayGroup]);

  const selectBuilding = useMemo(() => {
    return arrayGroup?.[1]?.find(item => item.id === currentBuild);
  }, [arrayGroup, currentBuild]);

  useEffect(() => {
    if (selectHospitalArea?.id && firstRenderOrToggleTab.current) {
      firstRenderOrToggleTab.current = false;
      const findBuild = arrayGroup?.[1]?.find(
        item => item.pid === selectHospitalArea.id
      );

      findBuild && setCurrentBuild(findBuild.id);
    }
  }, [arrayGroup, selectHospitalArea]);

  const deleteWithId = useCallback(
    (id: number) => {
      actionConfirm(
        () =>
          useApi.删除科室分布目录
            .request({
              id: id
            })
            .then(() => {
              request();
            }),
        '删除'
      );
    },
    [request]
  );

  const switchModalVisible = useModal(
    ({
      id,
      pid,
      type,
      modalType
    }: {
      id?: number | string;
      pid?: number | string;
      type: 'hospital' | 'dept' | 'building';
      modalType: 'edit' | 'add';
    }) => {
      return {
        title: modalType === 'edit' ? '编辑科室分布' : '新增科室分布',
        onSubmit: values =>
          handleSubmit(() => {
            if (modalType === 'edit') {
              return useApi.编辑科室分布
                .request({
                  ...values,
                  id: id,
                  hisId: env.hisId
                })
                .then(() => {
                  return request();
                });
            } else {
              return useApi.新增科室分布
                .request({
                  ...values,
                  pid: pid,
                  hisId: env.hisId
                })
                .then(() => {
                  return request();
                });
            }
          }),
        items: [
          {
            label: '名称',
            name: 'name',
            required: true
          },
          {
            label: '科室名称',
            name: 'depts',
            render: type === 'dept',
            required: true
          }
        ]
      };
    }
  );

  return (
    <CardLayout>
      <Tabs
        defaultActiveKey='0'
        onTabClick={reloadTable}
        tabBarExtraContent={
          <Button
            type={'primary'}
            loading={loading}
            onClick={() => {
              switchModalVisible({
                type: 'hospital',
                modalType: 'add'
              });
            }}>
            新增院区
          </Button>
        }>
        {arrayGroup?.[0]?.map((item, index) => (
          <TabPane tab={item.name} key={index} />
        ))}
      </Tabs>
      <div style={{ marginBottom: '5px' }}>楼栋</div>
      <PanelContainer>
        <div className={'leftPanel'}>
          <div className={'listGroup'}>
            {arrayGroup?.[1]
              ?.filter(i => i.pid === selectHospitalArea.id)
              ?.map(item => {
                return (
                  <div
                    key={item?.key}
                    className={classNames('listItem', {
                      selectBuild: item.id === currentBuild
                    })}
                    onClick={() => {
                      setCurrentBuild(item.id);
                    }}>
                    <span>{item.name}</span>
                    <span className={'actionGroup'}>
                      <LinkButton
                        onClick={() => {
                          switchModalVisible({
                            ...item,
                            type: 'building',
                            modalType: 'edit'
                          });
                        }}>
                        编辑
                      </LinkButton>
                      <LinkButton
                        style={{ marginLeft: '5px' }}
                        onClick={() => {
                          deleteWithId(item.id);
                        }}>
                        删除
                      </LinkButton>
                    </span>
                  </div>
                );
              })}
          </div>
          <div className={'addButton'}>
            <Button
              type={'default'}
              onClick={() => {
                selectHospitalArea?.id &&
                  switchModalVisible({
                    pid: selectHospitalArea?.id,
                    type: 'building',
                    modalType: 'add'
                  });
              }}>
              添加楼栋
            </Button>
          </div>
        </div>
        <div className={'rightPanel'}>
          <div style={{ padding: '5px 10px' }}>
            {selectBuilding?.name || '请选择楼栋'}
          </div>
          <div className={'listGroup'}>
            <Table
              pagination={false}
              columns={[
                {
                  title: '楼层',
                  dataIndex: 'name',
                  key: 'name'
                },
                {
                  title: '科室',
                  dataIndex: 'depts',
                  key: 'depts'
                },
                {
                  title: '操作',
                  fixed: 'right',
                  render: (_, record: any) => {
                    return (
                      <ActionsWrap>
                        <LinkButton
                          onClick={() => {
                            switchModalVisible({
                              ...record,
                              type: 'dept',
                              modalType: 'edit'
                            });
                          }}>
                          编辑
                        </LinkButton>
                        <LinkButton
                          onClick={() => {
                            deleteWithId(record.id);
                          }}>
                          删除
                        </LinkButton>
                      </ActionsWrap>
                    );
                  }
                }
              ]}
              dataSource={arrayGroup?.[2]?.filter(i => i.pid === currentBuild)}
            />
          </div>
          <div className={'addButton'}>
            <Button
              type={'default'}
              onClick={() => {
                currentBuild &&
                  switchModalVisible({
                    type: 'dept',
                    pid: currentBuild,
                    modalType: 'add'
                  });
              }}>
              添加楼层
            </Button>
          </div>
        </div>
      </PanelContainer>
    </CardLayout>
  );
};

const PanelContainer = styled.div`
  display: flex;
  justify-content: space-between;

  .leftPanel,
  .rightPanel {
    border: 1px solid #999999;
    border-radius: 5px;
    min-height: 70vh;
    max-height: 100vh;
    display: flex;
    flex-direction: column;

    .ant-table-cell-fix-right {
      z-index: 0 !important;
    }

    .selectBuild {
      background-color: #0e75d8;
      color: white;

      .ant-dropdown-link {
        color: white !important;
      }
    }

    .listGroup {
      height: 60vh;
      overflow-y: auto;
      .listItem {
        padding: 10px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        cursor: pointer;

        .actionGroup {
          visibility: hidden;
        }

        &:hover {
          .actionGroup {
            visibility: visible;
          }
        }
      }
    }

    .addButton {
      margin-bottom: 10px;
      width: calc(100% - 20px);
      display: flex;
      justify-content: center;
    }
  }

  .leftPanel {
    width: 20%;
  }

  .rightPanel {
    width: 75%;
  }
`;
