import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ApiResponse,
  ListApiRequestParams,
  ListApiResponseData,
  RelationIds
} from '@configs/d';

export interface ListType {
  id: number;
  hisId: number;
  roomId: number;
  userId: number;
  roomName: string;
  patientId: number;
  patName: string;
  applySource: string;
  teamId: number;
  teamName: string;
  diseaseType: string;
  payState: string;
  mdtState: string;
  mode: number;
  resourceId: number;
  createTime: string;
  updateTime: string;
  mdtStartTime: string;
  mdtEndTime: string;
  mdtFee: number;
  orderId: number;
  orderSerialNumber: string;
  purpose: string;
  rejectReviewReason: string;
  payTime: string;
  reviewTime: string;
  applyCancelTime: string;
  reviewCancelTime: string;
  finishTime: string;
  hospitalName: string;
  districtId: number;
  districtName: string;
  roomAddress: string;
  reportUrl: string;
  contactPhone: string;
  patCardNo: string;
  patSex: string;
  patAgeStr: string;
  leadingDeptId: number;
}

export interface MdtOfflineApply {
  id: number;
  hisId: number;
  mdtOfflineId: number;
  userId: number;
  patientId: number;
  patName: string;
  patPhone: string;
  patCardNo: string;
  patSex: string;
  patBirthday: string;
  createTime: string;
  updateTime: string;
  chiefComplaint: string;
  symptom: string;
  allergies: string;
  medicalHistory: string;
  operationHistory: string;
  initialDiagnosis: string;
  anamnesis: string;
  examination: string;
  imageData: string;
  fileData: string;
  videoData: string;
}

export interface Member {
  id: number;
  hisId: number;
  mdtOfflineId: number;
  memberRole: string;
  doctorId: string;
  doctorName: string;
  cpHospitalId: number;
  hospitalName: string;
  createTime: string;
  updateTime: string;
  deptId: number;
  deptName: string;
  level: string;
}

export interface DetailType extends ListType {
  mdtOfflineApply: MdtOfflineApply;
  members: Member[];
  eventLogs: [
    {
      id: number;
      createTime: string;
      updateTime: string;
      hisId: number;
      mdtOfflineId: number;
      eventType: string;
      eventRole: string;
      fromState: string;
      toState: string;
      eventDesc: string;
    }
  ];
}
export interface ReportDetail {
  patientId: string; // 就诊人id
  patName: string; // 就诊人姓名
  patSex: string; // 性别
  patBirthday: string; // 生日
  patAgeStr: string; // 年龄描述
  patCardNo: string; // 就诊卡号
  patPhone: string; // 就诊人电话
  mdtOfflineId: string; // MDT会诊id
  initialDiagnosis: {
    hosIcdCode: 'J11.101';
    hosIcdName: '流行性感冒';
  }[]; // 初步诊断
  chiefComplaint: string; // 主诉
  medicalHistory: string; // 现病史
  anamnesis: string; // 既往史
  examination: string; // 体格检查
  auxiliaryExam: string; // 辅助检查
  doctorSummaryList: Array<{
    name: string; // 医生名
    deptName: string; // 科室名
    summary: string; // 会诊意见
  }>;
  summary: string; // 会诊意见
}
export interface Team {
  id?: string;
  hisId?: string;
  teamName: string;
  diseaseType: string;
  mode: number;
  enable: string;
  avatarImage: string;
  visitSlot: VisitSlot[];
  intro: string;
  mdtRoomId: string;
  hisRegNo: string;
  leadingDeptId: string;
  leadingDeptName: string;
  leadingDeptMainId: string;
  creatorName: string;
  creatorAccountId: string;
  createTime: string;
  updateTime: string;
  price: number;
  memberAmount: string;
  deptAmount: string;
  teamMembers: {
    id: '@natural'; //成员id
    mdtTeamId: '@natural'; //团队id
    cpHospitalId: '@natural'; //医院id
    doctorId: '@string(5)'; //医生编号（上级医院匹配医生编号，下级医院匹配手机号）
    doctorName: '@cname'; //医生姓名
    memberRole: string; //角色，MEMBER成员，COORDINATOR协调员
    sort: number; //排序，正序排列
    createTime: '@datetime'; //创建时间
    updateTime: '@datetime'; //更新时间
    hospitalName: '@cword医院'; //医院名称
    deptId: '@natural'; //科室id
    deptName: '@cword科室'; //科室名称
    deptSort: '@integer(1,100)'; //科室排序
    doctorLevel: '主任医生'; //医生职称
    doctorImage: '@image'; //医生头像
  }[];
}

interface VisitSlot {
  week: string;
  startTime: string;
  endTime: string;
  timeDesc: string;
}

export interface Room {
  id: string;
  hisId: string;
  roomName: string;
  roomNo: string;
  enable: string;
  districtId: string;
  districtName: string;
  remark: string;
  address: string;
  creatorName: string;
  creatorAccountId: string;
  createTime: string;
  updateTime: string;
}

export interface MdtEnum {
  MdtOfflinePayStateEnum: EnumInfo[];
  MdtOfflineApplySourceEnum: EnumInfo[];
  MdtEventRoleEnum: EnumInfo[];
  MdtMemberRoleEnum: EnumInfo[];
  MdtOfflineStateEnum: EnumInfo[];
  MdtEventTypeEnum: EnumInfo[];
  MdtOfflineReviewEnum: EnumInfo[];
}

export interface MdtStatis {
  id: string;
  leadingDeptId: string; // 牵头科室ID
  leadingDeptName: string; // 牵头科室
  teamCount: number; // MDT门诊团队数量
  applyCount: number; // MDT门诊申请次数
  finishedCount: number; // MDT门诊开展例数
  paidAmount: number; // 交易总额(元
  refundRatio: string; // 医生退款率
}

interface EnumInfo {
  name: string;
  desc: string;
}

interface Approve {
  id: string;
  reviewState: string;
  reason?: string;
}

interface ConsultationRoom {
  roomName: '@cword(6)'; //会诊室名称
  roomNo: ''; //会诊室编号
  districtId: '@natural'; //院区id
  districtName: '@cword(6)'; //院区名称
  remark: ''; //备注
  enable: '@pick(0,1)'; //是否启用（1是0否）
  address: '@county@cword(2)街@integer(1,100)号@cword(4)小区'; //会诊室地点
}
export function findEnumDesc(enums: EnumInfo[] = [], name: string) {
  return enums?.find(item => item.name === name)?.desc || '-';
}
export function transitionEnum(enums: any[] = []) {
  return enums?.map(item => {
    return {
      label: item?.dictKey,
      value: item?.dictValue?.value
    };
  });
}
interface BatchScheduleDetail {
  startTime: '2020-09-18 12:00:00'; //号源开始时间
  endTime: '2020-09-18 13:00:00'; //号源结束时间
  totalNum: 10; //该时段总的号源数量
  leftNum: 10; //该时段剩余号源数量
  scheduleList: [
    {
      id: '@natural'; //主键ID
      createTime: '@datetime'; //创建时间
      updateTime: '@datetime'; //更新时间
      relationId: '@natural'; //关联主键
      visitDate: '2020-09-18'; //问诊日期
      startTime: '2020-09-18 12:00:00'; //号源开始时间
      endTime: '2020-09-18 13:00:00'; //号源结束时间
      totalResourceNum: 10; //该时段总的号源数量
      leftResourceNum: 10; //该时段剩余号源数量
      isPublish: 0; //发布状态 0未发布 1已发布
      type: '@pick(1,2,3)'; //号源类别 1房间，2团队
    }
  ];
}
interface createSchedule {
  relation: RelationIds[]; //关联信息，支持多个对象同时排班
  endTime: '2020-09-18 18:00:00'; // 开始排班时间
  startTime: '2020-09-18 12:00:00'; // 结束排班时间
  minuteInterval: 60; // 间隔时间
  totalResourceNum: 10; // 排班时间内放号数量
  visitDate: '2020-09-18'; // 排班日期
  type: '@pick(1,2)'; //号源类别 1房间，2团队
  cover: '@pick(1,0)'; //是否覆盖医生已有排班 1是，0否
}

interface ScheduleDetail {
  id: '@natural'; //主键ID
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //更新时间
  relationId: '@natural'; //关联主键
  visitDate: '2020-09-18'; //问诊日期
  startTime: '2020-09-18 12:00:00'; //号源开始时间
  endTime: '2020-09-18 13:00:00'; //号源结束时间
  totalResourceNum: 10; //该时段总的号源数量
  leftResourceNum: 10; //该时段剩余号源数量
  isPublish: 0; //发布状态 0未发布 1已发布
  type: '@pick(1,2,3)'; //号源类别 1房间，2团队
}

interface Batch {
  visitDate: string;
  type: string;
  relationId: string;
}

export default {
  分页查询线下MDT列表: createApiHooks(
    (
      params: {
        patName?: number | string;
        roomId?: number | string;
        payState?: number | string;
        mdtState?: number | string;
        teamId?: number | string;
        searchStartTime?: number | string;
        searchEndTime?: number | string;
        mode?: number | string;
      } & ListApiRequestParams
    ) =>
      request.get<ListApiResponseData<ListType>>('/mch/cooperate/mdt-offline', {
        params
      })
  ),
  分页查询团队列表: createApiHooks(
    (
      params: {
        teamName?: string;
        creatorName?: string;
        enable?: string;
      } & ListApiRequestParams
    ) =>
      request.get<ListApiResponseData<Team>>('/mch/cooperate/mdt-team', {
        params
      })
  ),
  新增团队: createApiHooks((data: Team) =>
    request.post<ApiResponse<any>>(`/mch/cooperate/mdt-team`, data, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  ),
  分页查询会诊室列表: createApiHooks(
    (
      params: {
        roomName?: string;
        creatorName?: string;
        enable?: string;
      } & ListApiRequestParams
    ) =>
      request.get<
        ListApiResponseData<
          ConsultationRoom & {
            id: '@natural'; //线下房间ID
            hisId: '@natural'; //医院id
            creatorName: '@cname'; //创建人姓名
            creatorAccountId: '@natural'; //创建人账号id
            createTime: '@datetime'; //创建时间
            updateTime: '@datetime'; //更新时间
          }
        >
      >('/mch/cooperate/mdt-room-offline', {
        params
      })
  ),
  新增会诊室: createApiHooks((data: ConsultationRoom) =>
    request.post<ApiResponse<any>>(`/mch/cooperate/mdt-room-offline`, data)
  ),
  编辑会诊室: createApiHooks((data: { id: string } & ConsultationRoom) =>
    request.put<ApiResponse<any>>(`/mch/cooperate/mdt-room-offline`, data)
  ),
  编辑团队: createApiHooks((data: { id: string } & Team) =>
    request.put<ApiResponse<any>>(`/mch/cooperate/mdt-team`, data)
  ),
  启用停用会诊室: createApiHooks((id: string) =>
    request.put<ApiResponse<any>>(
      `/mch/cooperate/mdt-room-offline/on-off/${id}`
    )
  ),
  启用停用团队: createApiHooks((id: string) =>
    request.put<ApiResponse<any>>(`/mch/cooperate/mdt-team/on-off/${id}`)
  ),
  取消线下MDT申请: createApiHooks((id: string) =>
    request.put<ApiResponse<any>>(`/mch/cooperate/mdt-offline/cancel/${id}`)
  ),
  线下会诊只退款: createApiHooks((id: number | string) =>
    request.put<ApiResponse<any>>(`/mch/cooperate/mdt-offline/refund/${id}`)
  ),
  上传会诊报告: createApiHooks((
    data: { id: string; reportUrl: string } //线下会诊id //会诊报告
  ) =>
    request.put<ApiResponse<any>>(
      `/mch/cooperate/mdt-offline/upload-report`,
      data
    )
  ),
  根据id删除会诊室: createApiHooks((id: string) =>
    request.delete<ApiResponse<any>>(`/mch/cooperate/mdt-room-offline/${id}`)
  ),
  根据id删除团队: createApiHooks((id: string) =>
    request.delete<ApiResponse<any>>(`/mch/cooperate/mdt-team/${id}`)
  ),
  根据id查询会诊室详情: createApiHooks((id: string) =>
    request.get<ApiResponse<Team>>(`/mch/cooperate/mdt-room-offline/${id}`)
  ),
  根据id查询团队详情: createApiHooks((id: string) =>
    request.get<ApiResponse<Team>>(`/mch/cooperate/mdt-team/${id}`)
  ),
  院区列表: createApiHooks(() =>
    request.get<
      ApiResponse<
        {
          id: '@natural'; //主键
          no: 'SN@integer(10000,99999)'; //院区编码
          name: '@cword(2)院区'; //院区名称
          pid: -1; //父级id 等于-1时认为是院区
          img: '@image'; //院区logo
          sortNo: '@integer(1,100)'; //序号
          address: '@region'; //院区位置
        }[]
      >
    >(`/mch/his/deptMain/district`, {
      params: {
        status: 1,
        hisType: 1
      }
    })
  ),
  查询线下MDT详情: createApiHooks(id =>
    request.get<{ data: DetailType }>(`/mch/cooperate/mdt-offline/${id}`)
  ),
  查询MDT报告详情: createApiHooks(id =>
    request.get<{ data: ReportDetail }>(
      `/mch/cooperate/mdt-offline/report/${id}`
    )
  ),
  查询全部可用团队列表: createApiHooks((params: { teamName?: string }) =>
    request.get<ApiResponse<Team[]>>(`/mch/cooperate/mdt-team/available`, {
      params
    })
  ),
  团队成员排序: createApiHooks(
    (params: {
      id: string; //团队id
      teamMembers: [
        {
          id: '@integer'; //成员id
          sort: '@integer(1,100)'; //排序，正序排列
        }
      ];
    }) => {
      return request.put<ApiResponse<any>>(
        `/mch/cooperate/mdt-team/member-sort`,
        params
      );
    }
  ),
  查询会诊团队列表: createApiHooks((params: { teamName?: string }) => {
    return request.get<{ data: Team[] }>('/mch/cooperate/mdt-team/available', {
      params
    });
  }),
  查询会诊室列表: createApiHooks(
    (params: { roomName?: string; districtId?: string }) => {
      return request.get<{ data: Room[] }>(
        '/mch/cooperate/mdt-room-offline/available',
        { params }
      );
    }
  ),
  查询会诊相关枚举: createApiHooks(() => {
    return request.get<{ data: MdtEnum }>('/common/cooperate/mdt-offline/enum');
  }),
  审核线下MDT申请: createApiHooks((params: Approve) => {
    return request.put('/mch/cooperate/mdt-offline/review', params);
  }),
  医联体医院: createApiHooks(() => {
    return request.get<
      ApiResponse<
        {
          id: '@natural'; //
          hisId: '@natural'; //上级医院id, hisId==id表示上级，hisId！=id表示下级
          name: '@cword(6)'; //医院名称
          level: "@pick('三级甲等','三级乙等','三级未定级')"; //医院级别
        }[]
      >
    >('/mch/cooperate/cooperate-hospital/auth');
  }),
  查询医联体医院全部科室列表: createApiHooks(
    (params: { cpHospitalId?: string }) => {
      return request.get<
        ApiResponse<
          {
            id: '@natural'; //id
            hisId: '@natural'; //上级医院id
            cpHospitalId: '@natural'; //医联体医院id，如果为空表示上级医院，如果不为空，表示下级医院
            cpHospitalName: '@cword(6)'; //医联体医院名称
            no: ''; //科室编码
            name: '@cword(6)'; //科室名称
            summary: ''; //科室简介
            tel: '138@natural(00000000,99999999)'; //电话
            sortNo: '@integer(0, 1000000)'; //科室排序 从小到大排
            img: '@image(100x100)'; //科室图片地址
            initials: ''; //拼音首字母
            state: '@integer(0, 1)'; //0停用 1启用
            skill: ''; //科室专长
            createTime: '@datetime'; //创建时间
            updateTime: '@datetime'; //更新时间
            standardDeptNo: '11.10'; // 标准科室编码
            standardDeptName: '内科'; // 标准科室名称
          }[]
        >
      >('/mch/cooperate/cooperate-dept/hospital', {
        params
      });
    }
  ),
  查询医联体医院全部医生列表: createApiHooks(
    (params: { cpHospitalId?: string; cpDeptId?: string }) => {
      return request.get<
        ApiResponse<
          {
            id: '@natural'; //
            hisId: '@natural'; //上级医院id
            cpHospitalId: '@natural'; //医联体医院id，cpHospitalId==hisId 上级医院，，cpHospitalId!=hisId下级医院
            cpHospitalName: '@cword(6)'; //医联体医院名称
            cpDeptId: '@natural(1,1000)'; //医联体科室id
            cpDeptName: '@cword(6)'; //医联体科室名
            doctorId: string;
            name: '@cword(6)'; //医生名
            mobile: '1@integer(3000000000, 9900000000)'; //手机号
            staffNo: '@string(8)'; //医生工号
            sex: "@pick('男','女')"; //医生性别
            level: "@picker('11','12','13')"; //医生级别
            type: '@pick(1,2)'; //类型 1.医生  2.护士
            image: '@image(100x100)'; //医生头像
            specialty: '@cparagraph'; //专业技能
            introduction: '@cparagraph'; //医生介绍
            sortNo: '@integer(0, 1000000)'; //排序字段 数字越大越靠前
            state: '@integer(0, 1)'; //0启用 1启用
            workingLife: '@integer(0, 1000000)'; //从业年限
            createTime: '@datetime'; //创建时间
            updateTime: '@datetime'; //更新时间
            roleId: '@natural'; //医联体角色id
          }[]
        >
      >('/mch/cooperate/cooperate-doctor/hospital', {
        params
      });
    }
  ),
  创建排班: createApiHooks((params: createSchedule) =>
    request.post<ApiResponse<any>>('/mch/cooperate/mdt-schedule', params, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  ),
  批量创建排班: createApiHooks((params: createSchedule) =>
    request.post<ApiResponse<any>>(
      '/mch/cooperate/mdt-schedule/batch-create',
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  日历高亮: createApiHooks((params: { relationId: string; type: string }) =>
    request.get<ApiResponse<string[]>>(
      '/mch/cooperate/mdt-schedule/high-light',
      {
        params
      }
    )
  ),
  批量排班日历高亮: createApiHooks((params: Batch) =>
    request.post<ApiResponse<string[]>>(
      '/mch/cooperate/mdt-schedule/batch-high-light',
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  排班详情: createApiHooks((params: Batch) =>
    request.get<ApiResponse<ScheduleDetail[]>>(
      '/mch/cooperate/mdt-schedule/one-day',
      {
        params
      }
    )
  ),
  批量排班详情: createApiHooks(
    (params: {
      relation: RelationIds[]; //关联信息，支持多个对象同时排班
      visitDate: string; // 排班日期
      type: string; //号源类别 1房间，2团队
    }) =>
      request.post<ApiResponse<BatchScheduleDetail[]>>(
        '/mch/cooperate/mdt-schedule/batch-one-day',
        params,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
  ),
  发布排班: createApiHooks((params: { ids: string }) =>
    request.post<ApiResponse<any>>(
      '/mch/cooperate/mdt-schedule/publish',
      params
    )
  ),
  删除排班: createApiHooks((params: { ids: string }) =>
    request.delete<ApiResponse<any>>('/mch/cooperate/mdt-schedule', {
      data: params
    })
  ),
  复用排班: createApiHooks(
    (params: {
      relationId: string; //关联主键ID,会诊室排班：会诊室id，团队排班：团队id
      sourceDate: string; //源日期
      targetDate: string; //目标日期，多个日期已","分割
      type: string; //号源类别 1房间，2团队
    }) =>
      request.post<ApiResponse<any>>(
        '/mch/cooperate/mdt-schedule/copy',
        params,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
  ),
  批量复用排班: createApiHooks(
    (params: {
      relation: RelationIds[]; //关联信息，支持多个对象同时排班
      sourceDate: string; //源日期
      targetDate: string; //目标日期，多个日期已","分割
      type: string; //号源类别 1房间，2团队
    }) =>
      request.post<ApiResponse<any>>(
        '/mch/cooperate/mdt-schedule/batch-copy',
        params,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
  ),
  MDT订单统计: createApiHooks(
    (params: { startTime?: string; endTime?: string; mode?: string }) =>
      request.get<ApiResponse<MdtStatis[]>>(
        `/mch/cooperate/mdt-offline/stats`,
        {
          params
        }
      )
  ),
  /**
   * 打印报告完整版 & 打印报告患者版 isComplete -> true & false
   */
  reportVersion: createApiHooks(
    (params: { id?: string; isComplete?: boolean }) =>
      request.get<Blob>(`/mch/cooperate/mdt-offline/pdf-report`, {
        params,
        responseType: 'blob'
      })
  ),
  /**
   * 发布报告
   */
  reportRelease: createApiHooks((params: { id?: string }) =>
    request.put(`/mch/cooperate/mdt-offline/report/publish`, params)
  ),
  调整会诊医师: createApiHooks(
    (data: {
      roomId?: string;
      operType?: string;
      doctors?: Array<{
        account: ''; //医生的账号，可能是手机号等不同的值，使用账号+hisId可以在不同的表中查到医生的数据
        deptId: 0; //会诊科室ID
        deptName: ''; //会诊科室名称
        title: ''; //会诊人员职称
        image: ''; //会诊人员头像
        name: ''; //会诊人员名称
      }>;
    }) =>
      request.put<ApiResponse<any>>(
        `/mch/cooperate/mdt-offline/change-room-doctor`,
        {
          ...data
        }
      )
  ),
  更新会诊时间: createApiHooks((data: { id?: string; resourceId?: string }) =>
    request.put<ApiResponse<any>>(
      `/mch/cooperate/mdt-offline/change-resource`,
      {
        ...data
      }
    )
  )
};
