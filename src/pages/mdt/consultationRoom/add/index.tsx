import { ArrSelect, CardLayout, handleSubmit } from 'parsec-admin';
import { Button, Form, Input, Radio, Spin } from 'antd';
import { useHistory, useParams } from 'react-router';
import apis from '@pages/mdt/api';
import { useEffect } from 'react';

export default ({ type }: { type: 'add' | 'update' }) => {
  const history = useHistory();
  const [form] = Form.useForm();
  const { id } = useParams<{ id: string }>();
  const { request: add, loading: addLoading } = apis.新增会诊室({
    needInit: false
  });
  const { request: update, loading: updateLoading } = apis.新增会诊室({
    needInit: false
  });
  const { data: detail, loading: detailLoading } = apis.根据id查询会诊室详情({
    needInit: type === 'update' && !!id,
    params: id
  });
  const { data: hisList } = apis.院区列表();
  useEffect(() => {
    if (detail?.data) {
      form.setFieldsValue({ ...detail?.data });
    }
  }, [detail, form]);
  return (
    <CardLayout title={'会诊室信息'}>
      <Spin spinning={detailLoading}>
        <Form wrapperCol={{ span: 8 }} labelCol={{ span: 2 }} form={form}>
          <Form.Item
            label={'会诊室名称'}
            name={'roomName'}
            required
            rules={[{ required: true, message: '请输入会诊室名称' }]}>
            <Input placeholder={'请输入会诊室名称'} />
          </Form.Item>
          <Form.Item
            label={'会诊室编号'}
            name={'roomNo'}
            required
            rules={[{ required: true, message: '请输入会诊室编号' }]}>
            <Input placeholder={'请输入会诊室编号'} />
          </Form.Item>
          <Form.Item
            label={'所属院区'}
            name={'districtId'}
            required
            rules={[{ required: true, message: '请选择所属院区' }]}>
            <ArrSelect
              options={(hisList?.data || [])?.map(item => {
                return {
                  label: item.name,
                  value: item.id
                };
              })}
              placeholder={'请选择所属院区'}
            />
          </Form.Item>
          <Form.Item
            label={'会诊室地点'}
            name={'address'}
            required
            rules={[{ required: true, message: '请输入会诊室地点' }]}>
            <Input placeholder={'请输入会诊室地点'} />
          </Form.Item>
          <Form.Item
            label={'状态'}
            name={'enable'}
            required
            rules={[{ required: true, message: '请选择状态' }]}>
            <Radio.Group>
              <Radio value={1}>启用</Radio>
              <Radio value={0}>停用</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label={'备注'} name={'remark'}>
            <Input.TextArea placeholder={'请输入备注'} />
          </Form.Item>
        </Form>
        <div style={{ padding: '30px' }}>
          <Button
            type={'primary'}
            loading={addLoading || updateLoading}
            onClick={() => {
              form.validateFields().then(res => {
                const value = {
                  ...res,
                  districtName:
                    hisList?.data?.find(item => item.id === res.districtId)
                      ?.name || ''
                };
                handleSubmit(() =>
                  type === 'update' && !!id
                    ? update({ ...value, id })
                    : add({ ...value })
                ).then(() => {
                  history.goBack();
                });
              });
            }}>
            保存
          </Button>
          <Button
            style={{ marginLeft: '20px' }}
            ghost
            type={'primary'}
            onClick={() => {
              history.goBack();
            }}>
            取消
          </Button>
        </div>
      </Spin>
    </CardLayout>
  );
};
