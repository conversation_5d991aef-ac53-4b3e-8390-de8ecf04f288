import apis from '@pages/mdt/api';
import { Button, Radio } from 'antd';
import {
  actionConfirm,
  ActionsWrap,
  LinkButton,
  TableList
} from 'parsec-admin';
import { useMemo, useState } from 'react';
import { useHistory } from 'react-router';
import { Enable } from '@pages/mdt/mdtTeam';
export default () => {
  const [mdtState, setMdtState] = useState<string>('');
  const history = useHistory();
  return (
    <TableList
      tableTitle={
        <Radio.Group
          value={mdtState}
          onChange={e => setMdtState(e.target.value)}>
          {[
            { name: '', desc: '全部' },
            { name: '0', desc: '未启用' },
            { name: '1', desc: '已启用' }
          ].map(item => {
            return (
              <Radio.Button key={item.name} value={item.name}>
                {item.desc}
              </Radio.Button>
            );
          })}
        </Radio.Group>
      }
      exportExcelButton
      showTool={false}
      action={
        <>
          <Button
            type={'primary'}
            onClick={() => {
              history.push('/mdtManage/consultationRoom/add');
            }}>
            + 添加会诊室
          </Button>
        </>
      }
      getList={async ({ pagination: { current, pageSize }, params }) => {
        const res = await apis.分页查询会诊室列表.request({
          pageNum: current,
          numPerPage: pageSize,
          ...params
        });
        return {
          list: (res?.data?.recordList || []) as any,
          total: res?.data?.totalCount || 0
        };
      }}
      pagination={{
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '40', '60', '100'],
        showTotal: total => `共 ${total} 条数据`
      }}
      params={{ enable: mdtState }}
      columns={useMemo(() => {
        return [
          {
            title: '院区',
            dataIndex: 'districtName'
          },
          {
            title: '会诊室名称',
            dataIndex: 'roomName',
            search: true
          },
          {
            title: '会诊室编号',
            dataIndex: 'roomNo'
          },
          {
            title: '创建人',
            dataIndex: 'creatorName',
            search: true
          },
          {
            title: '创建时间',
            dataIndex: 'createTime'
          },
          {
            title: '状态',
            dataIndex: 'enable',
            render: v => Enable[v] || '-'
          },
          {
            title: '操作',
            fixed: 'right',
            width: 200,
            render: (_, record: any) => {
              const isEnable = record?.enable === 0;
              return (
                <ActionsWrap max={4}>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () => apis.启用停用会诊室.request(record?.id),
                        isEnable ? '启用' : '停用'
                      );
                    }}>
                    {isEnable ? '启用' : '停用'}
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      history.push(
                        '/mdtManage/consultationRoom/update/' + record.id
                      );
                    }}>
                    编辑
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      history.push(
                        '/mdtManage/consultationRoom/scheduleShift/' + record.id
                      );
                    }}>
                    排班
                  </LinkButton>
                  {isEnable && (
                    <LinkButton
                      style={{ color: '#dc4747' }}
                      onClick={() => {
                        actionConfirm(
                          () => apis.根据id删除会诊室.request(record?.id),
                          '',
                          {
                            template: '删除操作不可撤销，是否确定删除？'
                          }
                        );
                      }}>
                      删除
                    </LinkButton>
                  )}
                </ActionsWrap>
              );
            }
          }
        ];
      }, [history])}
    />
  );
};
