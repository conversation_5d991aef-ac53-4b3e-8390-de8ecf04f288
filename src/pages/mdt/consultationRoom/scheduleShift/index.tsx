import apis from '@pages/mdt/api';
import { ArrSelect, BlankLayout } from 'parsec-admin';
import { Space } from 'antd';
import React, { useState } from 'react';
import { useParams } from 'react-router';
import SchedulingManagement from '../components/schedulingManagement';

export default () => {
  const { id } = useParams<{ id: string }>();
  const { data: hisList } = apis.院区列表();
  const [roomSearch, setRoomSearch] = useState<string>();
  const [districtId, setDistrictId] = useState<string>();
  const [consultationRoomId, setConsultationRoomId] = useState<
    string | undefined
  >(id);
  const {
    data: { data: roomList }
  } = apis.查询会诊室列表({
    params: { roomName: roomSearch, districtId: districtId },
    initValue: { data: [] },
    debounceInterval: 500
  });
  return (
    <BlankLayout>
      <Space style={{ marginBottom: '30px' }}>
        <Space>
          院区：
          <ArrSelect
            style={{ width: 200 }}
            value={districtId}
            onChange={e => {
              if (e) {
                setConsultationRoomId(undefined);
              }
              setDistrictId(e as string);
            }}
            options={(hisList?.data || [])?.map(item => {
              return {
                label: item.name,
                value: item.id
              };
            })}
            placeholder={'请选择所属院区'}
          />
        </Space>
        <Space style={{ marginLeft: '30px' }}>
          会诊室：
          <ArrSelect
            onSearch={setRoomSearch}
            searchValue={roomSearch}
            value={consultationRoomId}
            onChange={e => setConsultationRoomId(e as string)}
            placeholder={'请选择会诊室'}
            style={{ width: 200 }}
            options={
              roomList?.map(item => ({
                label: item.roomName,
                value: item.id + ''
              })) || []
            }
          />
        </Space>
      </Space>
      <SchedulingManagement
        id={consultationRoomId || ''}
        type={'1'}
        isBatch={false}
      />
    </BlankLayout>
  );
};
