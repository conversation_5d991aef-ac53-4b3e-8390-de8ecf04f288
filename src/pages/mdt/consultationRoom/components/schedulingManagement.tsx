import styled from 'styled-components';
import {
  Button,
  Empty,
  Form,
  InputNumber,
  message,
  Modal,
  Spin,
  TimePicker,
  Typography
} from 'antd';
import moment, { Moment } from 'moment';
import React, { useMemo, useState } from 'react';
import './index.less';
import CheckCalendar from './checkCalendar';
import { actionConfirm, DetailLayout } from 'parsec-admin';
import { DeleteOutlined, DiffOutlined, SaveOutlined } from '@ant-design/icons';
import apis from '@pages/mdt/api';
import { RelationIds } from '@configs/d';
import storage from '@utils/storage';

const { Paragraph } = Typography;
export default ({
  isBatch,
  id,
  type
}: {
  isBatch: boolean;
  id: string;
  type: string;
}) => {
  const now = moment().format('YYYY-MM-DD');
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [showDate, setShowDate] = useState<Moment>(moment());
  const [selectDate, setSelectDate] = useState<string[]>([]);
  const [selectScheduleItem, setSelectScheduleItem] = useState<string[]>([]);
  const isShow = useMemo(() => {
    const formatDate2 = showDate.format('YYYY-MM-DD');
    return now <= formatDate2;
  }, [showDate, now]);
  const scheduleIds = useMemo(() => {
    if (isBatch) {
      return storage.get('relationIds') || [];
    }
    return [];
  }, [isBatch]);
  const {
    data: { data: dateList },
    request: getDateList
  } = apis.日历高亮({
    params: {
      relationId: id,
      type
    },
    initValue: { data: [] },
    needInit: !!id && !isBatch
  });
  const {
    data: { data: dateListBatch },
    request: getDateListBatch
  } = apis.批量排班日历高亮({
    params: {
      visitDate: showDate.format('YYYY-MM-DD'),
      type,
      relationId: id
    },
    initValue: { data: [] },
    needInit: isBatch
  });
  const { request: Reuse, loading: reuseLoading } = apis.复用排班({
    needInit: false
  });
  const { request: ReuseBatch, loading: reuseLoadingBatch } = apis.批量复用排班(
    {
      needInit: false
    }
  );
  const {
    data: { data: detailList },
    request: getDetail
  } = apis.排班详情({
    params: {
      relationId: id,
      type,
      visitDate: showDate.format('YYYY-MM-DD')
    },
    initValue: { data: [] },
    needInit: !!id && !isBatch
  });
  const {
    data: { data: detailListBatch },
    request: getDetailBatch
  } = apis.批量排班详情({
    params: {
      type,
      visitDate: showDate.format('YYYY-MM-DD'),
      relation: scheduleIds
    },
    initValue: { data: [] },
    needInit: isBatch && scheduleIds.length > 0
  });
  const showDetailList: any[] = useMemo(() => {
    if (isBatch) {
      return (detailListBatch || []).map(v => {
        return {
          ...v,
          id: v.startTime
        };
      });
    }
    return detailList || [];
  }, [detailList, detailListBatch, isBatch]);
  const { request: createRequest } = apis.创建排班({
    needInit: false
  });
  const { request: createRequestBatch } = apis.批量创建排班({
    needInit: false
  });
  const [createLoading, setCreateLoading] = useState(false);
  const getListData = (value: Moment) => {
    let listData = '未排';
    const formatDate2 = value.format('YYYY-MM-DD');
    if (((isBatch ? dateListBatch : dateList) || []).includes(formatDate2)) {
      listData = '已排';
    }
    return listData || '';
  };

  const checkDateFullCellRender = (value: Moment) => {
    const isCheck =
      showDate.format('YYYY-MM-DD') === value.format('YYYY-MM-DD');
    // const unCheck = value.format('YYYY-MM-DD') < now;
    return (
      <div className={'dateItem'}>
        <div
          className={`${isCheck ? 'checkDate' : ''}`}
          style={{ paddingTop: '3px', boxSizing: 'border-box' }}>
          {value.format('DD')}
          <div style={{ fontSize: '10px', marginTop: '-3px' }}>
            {getListData(value)}
          </div>
        </div>
      </div>
    );
  };
  const dateFullCellRender = (value: Moment) => {
    const isCheck = selectDate.includes(value.format('YYYY-MM-DD'));
    const unCheck = value.format('YYYY-MM-DD') < now;
    return (
      <div
        onClick={() => {
          if (!unCheck) {
            if (isCheck) {
              setSelectDate(prevState =>
                prevState.filter(v => v !== value.format('YYYY-MM-DD'))
              );
            } else {
              setSelectDate(prevState => [
                ...prevState,
                value.format('YYYY-MM-DD')
              ]);
            }
          } else {
            message.warning('不能选择过期的日期');
          }
        }}
        className={
          unCheck ? 'uncheck date1' : isCheck ? 'check date1' : 'date1'
        }>
        {value.format('D')}
      </div>
    );
  };
  function getScheduleItems(arr: any[] = []): JSX.Element[] {
    return arr.map(childrenItem => {
      const isSelect = selectScheduleItem.includes(childrenItem?.id);
      let show = '';
      if (childrenItem?.startTime) {
        show += moment(childrenItem?.startTime).format('HH:mm');
      }
      if (childrenItem?.endTime) {
        show += '-' + moment(childrenItem?.endTime).format('HH:mm');
      }
      const isCheck =
        !!childrenItem?.leftResourceNum || !!childrenItem?.leftNum;
      show +=
        '排:' +
        (childrenItem?.totalResourceNum || childrenItem?.totalNum || 0) +
        '余:' +
        (childrenItem?.leftResourceNum || childrenItem?.leftNum || 0);
      const className = !isCheck
        ? 'scheduleInformation uncheck'
        : isSelect
        ? 'scheduleInformation check'
        : 'scheduleInformation';
      return (
        <div
          key={childrenItem.id}
          onClick={() => {
            if (isCheck) {
              if (isSelect) {
                setSelectScheduleItem(prevState =>
                  prevState.filter(v => v !== childrenItem?.id)
                );
              } else {
                setSelectScheduleItem(prevState => [
                  ...prevState,
                  childrenItem?.id
                ]);
              }
            }
          }}>
          <Paragraph
            className={className}
            ellipsis={{
              rows: 1,
              tooltip: `${show}`
            }}>
            {show}
          </Paragraph>
        </div>
      );
    });
  }
  const { loading: loadingPublish, request: requestPublish } = apis.发布排班({
    needInit: false
  });

  const init = async () => {
    const visitDate = showDate.format('YYYY-MM-DD');
    if (isBatch) {
      const params = {
        visitDate: visitDate,
        type: type
      };
      await getDateListBatch({
        ...params,
        relationId: id
      });
      await getDetailBatch({
        ...params,
        relation: scheduleIds
      });
    } else {
      const params = {
        relationId: id,
        type
      };
      await getDateList(params);
      await getDetail({
        ...params,
        visitDate
      });
    }
  };
  return (
    <Spin spinning={loadingPublish}>
      <Box>
        <div>
          <div className={'title'}> 排班日期</div>

          <CheckCalendar
            value={showDate}
            className={'dateWrap'}
            dateFullCellRender={checkDateFullCellRender}
            onSelect={e => {
              setShowDate(e);
            }}
          />

          {/*<div className={'prompt'}>*/}
          {/*  注：日期置灰表示历史日期不可排班，小蓝点表示已排版，小灰点表示已排班且号源已被挂满。*/}
          {/*</div>*/}
        </div>
        <div>
          <div className={'title'}>{showDate.format('ll') + '排班详情'}</div>
          <div className={'formBox'}>
            <Form form={form} layout={'inline'}>
              <Form.Item
                label={'排班时间'}
                name={'date'}
                rules={[{ required: true, message: '请选择排班时间' }]}>
                <TimePicker.RangePicker
                  style={{ width: '187px' }}
                  format={'HH:mm'}
                />
              </Form.Item>
              <Form.Item
                label={'间隔时间'}
                name={'minuteInterval'}
                rules={[{ required: true, message: '请输入间隔时间' }]}>
                <InputNumber
                  style={{ maxWidth: '190px' }}
                  placeholder='请输入间隔时间'
                  min={0}
                  controls={false}
                  addonAfter={'分钟'}
                />
              </Form.Item>
              <Form.Item
                label={'放号数量'}
                name={'totalResourceNum'}
                rules={[{ required: true, message: '请输入放号数量' }]}>
                <InputNumber
                  style={{ minWidth: '67px', maxWidth: '100px' }}
                  controls={false}
                  placeholder='请输入放号数量'
                />
              </Form.Item>
              {/*{isBatch && (*/}
              {/*  <Form.Item*/}
              {/*    label={'是否覆盖已有排班'}*/}
              {/*    name={'cover'}*/}
              {/*    initialValue={0}*/}
              {/*    valuePropName={'checked'}>*/}
              {/*    <Switch checkedChildren='是' unCheckedChildren='否' />*/}
              {/*  </Form.Item>*/}
              {/*)}*/}

              <Form.Item style={{ margin: '0 10px' }}>
                <Button
                  type={'primary'}
                  loading={createLoading}
                  disabled={!isShow}
                  onClick={() => {
                    form.validateFields().then(res => {
                      setCreateLoading(true);
                      const visitDate = showDate.format('YYYY-MM-DD');
                      res.startTime =
                        visitDate + ' ' + res.date[0].format('LTS');
                      res.endTime = visitDate + ' ' + res.date[1].format('LTS');
                      delete res.date;
                      delete res.dept;

                      let relationIds: RelationIds[] = [{ relationId: id }];
                      if (isBatch) {
                        res.cover = res?.cover ? 1 : 0;
                        relationIds = scheduleIds;
                      }
                      const params = {
                        ...res,
                        type,
                        relation: relationIds,
                        visitDate
                      };
                      (isBatch
                        ? createRequestBatch(params)
                        : createRequest(params)
                      )
                        .then(async () => {
                          await init();
                          message.success('生成排班成功');
                        })
                        .finally(() => {
                          setCreateLoading(false);
                        });
                    });
                  }}>
                  生成排班
                </Button>
              </Form.Item>
            </Form>
            <div className={'prompt'}>
              注：排班时间指需要排班的时间段（如：8:00-12:00），间隔时间指在排班时间内需间隔多长时间排一次号（如：15分钟），放号数量指在每一个间隔时间内排多少号数（如：1），点击生成排班则系统自动创建8:00-8:15、8:15-8:30···11:45-12:00，16个时间段，每个时间段1个号源。
            </div>
          </div>

          {!showDetailList?.length ? (
            <Empty
              className={'prompt empty'}
              description={`本日还未排班${
                isShow ? '，可在页面上方生成排班~' : ''
              }`}
            />
          ) : (
            <div>
              <div>
                <Button
                  icon={<DiffOutlined />}
                  type={'text'}
                  disabled={!isShow}
                  onClick={() => setOpen(true)}>
                  复用排班
                </Button>

                <Button
                  icon={<DeleteOutlined />}
                  type={'text'}
                  disabled={!isShow}
                  onClick={() => {
                    if (selectScheduleItem.length) {
                      let arr: any[] = [];
                      if (isBatch) {
                        showDetailList
                          .filter(v => selectScheduleItem.includes(v.id))
                          .forEach(l => {
                            if (l?.scheduleList && l?.scheduleList?.length) {
                              l.scheduleList.forEach(k => {
                                arr.push(k.id);
                              });
                            }
                          });
                      } else {
                        arr = selectScheduleItem;
                      }
                      actionConfirm(
                        () =>
                          apis.删除排班
                            .request({
                              ids: arr.join(',')
                            })
                            .then(async () => {
                              await init();
                              setSelectScheduleItem([]);
                            }),
                        '删除'
                      );
                    } else {
                      message.warning('当前没有选择排班');
                    }
                  }}>
                  删除排班
                </Button>

                <Button
                  icon={<SaveOutlined />}
                  type={'text'}
                  disabled={!isShow}
                  onClick={() => {
                    if (showDetailList?.length) {
                      let arr: any[] = [];
                      if (isBatch) {
                        //批量排班是查找日期的scheduleList下面的ID
                        showDetailList.forEach(item => {
                          if (
                            item?.scheduleList &&
                            item?.scheduleList?.length
                          ) {
                            item.scheduleList.forEach(v => {
                              arr.push(v.id);
                            });
                          }
                        });
                      } else {
                        arr = showDetailList.map(item => item.id);
                      }
                      requestPublish({
                        ids: arr.join(',')
                      }).then(async () => {
                        await init();
                        message.success('发布成功！');
                      });
                    } else {
                      message.warning('当前没有排班');
                    }
                  }}>
                  发布排班
                </Button>

                <Button
                  type={'text'}
                  disabled={!isShow}
                  onClick={() => {
                    if (showDetailList?.length) {
                      if (
                        showDetailList?.length === selectScheduleItem.length
                      ) {
                        setSelectScheduleItem([]);
                      } else {
                        setSelectScheduleItem(
                          showDetailList.map(item => item.id)
                        );
                      }
                    }
                  }}>
                  {selectScheduleItem.length === showDetailList?.length
                    ? '取消全选'
                    : '全选'}
                </Button>
                <DetailLayout
                  cardsProps={[
                    {
                      title: '',
                      children: (
                        <ScheduleItem>
                          {getScheduleItems(showDetailList)}
                        </ScheduleItem>
                      )
                    }
                  ]}
                />
              </div>
            </div>
          )}
        </div>
      </Box>
      <Modal
        visible={open}
        onCancel={() => {
          setOpen(false);
          setSelectDate([]);
        }}
        okButtonProps={{
          loading: isBatch ? reuseLoadingBatch : reuseLoading
        }}
        onOk={() => {
          if (selectDate.length) {
            const params = {
              type,
              targetDate: selectDate.join(','),
              sourceDate: showDate.format('YYYY-MM-DD')
            };
            (isBatch
              ? ReuseBatch({
                  ...params,
                  relation: scheduleIds
                })
              : Reuse({
                  ...params,
                  relationId: id
                })
            ).then(() => {
              setOpen(false);
              setSelectDate([]);
              message.success('复用成功！');
            });
          }
        }}
        title={'选择将当前排班复用到以下日期'}>
        <CheckCalendar dateFullCellRender={dateFullCellRender} />
      </Modal>
    </Spin>
  );
};
const Box = styled.div`
  display: flex;
  > div {
    .totalBox {
      width: 80%;
      display: flex;
      flex-wrap: wrap;
      margin: 0 auto;
      > div {
        width: 50%;
        font-weight: bold;
      }
    }
    .prompt {
      padding: 0 15px;
      color: rgba(164, 164, 164);
    }
    .empty {
      margin-top: 20px;
    }
    .title {
      width: 100%;
      text-align: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      font-weight: bold;
      padding-bottom: 10px;
    }
    :first-child {
      width: 25%;
      min-width: 260px;
      border-right: 1px solid rgba(0, 0, 0, 0.1);
    }
    :last-child {
      width: 75%;
      .formBox {
        margin-top: 10px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        > form {
          padding: 0 15px;
        }
        .ant-form-item {
          margin-bottom: 10px;
        }
      }
    }
  }
`;
const ScheduleItem = styled.div`
  display: flex;
  flex-flow: wrap;
  width: 100%;
  > div {
    width: 25%;
    text-align: center;
    padding: 0 10px;
    .scheduleInformation {
      width: 100%;
      padding: 2px 5px;
      border: 1px solid #69a1ea;
      display: inline-block;
      border-radius: 5px;
      cursor: pointer;
    }
  }
`;
