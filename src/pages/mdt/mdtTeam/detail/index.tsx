import { CardLayout, FormDescriptions, getPrice } from 'parsec-admin';
import apis from '@pages/mdt/api';
import { useHistory, useParams } from 'react-router';
import { Avatar, Button, Col, Image, Row, Space } from 'antd';
import React, { useMemo } from 'react';
import './index.less';
enum Mode {
  '线上会诊' = 1,
  '线下会诊' = 2,
  '线上会诊、线下会诊' = 3
}
enum Week {
  '周一' = 1,
  '周二',
  '周三',
  '周四',
  '周五',
  '周六',
  '周日'
}
export default () => {
  const history = useHistory();
  const { id } = useParams<{ id: string }>();
  const { data: detail, loading: detailLoading } = apis.根据id查询团队详情({
    needInit: !!id,
    params: id
  });
  const deptObj = useMemo(() => {
    const cpDeptNameList: string[] = ['协调员'];
    if (detail?.data?.teamMembers) {
      detail?.data?.teamMembers?.forEach(item => {
        if (
          !cpDeptNameList.includes(item.deptName) &&
          item.memberRole !== 'COORDINATOR'
        ) {
          cpDeptNameList.push(item.deptName);
        }
      });
      const obj: any = {};
      obj['协调员'] = detail?.data?.teamMembers.filter(
        v => v.memberRole === 'COORDINATOR'
      );
      cpDeptNameList?.forEach(item => {
        if (item !== '协调员') {
          obj[item] = detail?.data?.teamMembers.filter(
            v => v.deptName === item && v.memberRole !== 'COORDINATOR'
          );
        }
      });
      return obj;
    }
    return {};
  }, [detail]);
  console.log(deptObj, 'deptObj');
  return (
    <>
      <CardLayout
        loading={detailLoading}
        title={<b style={{ fontSize: '17px' }}>团队信息</b>}>
        <Space align={'start'}>
          <Image
            src={detail?.data?.avatarImage}
            style={{
              width: '100px',
              height: '100px',
              borderRadius: '50%',
              marginTop: '20px',
              marginRight: '20px'
            }}
          />
          <FormDescriptions
            column={4}
            items={[
              {
                label: <b style={{ fontSize: '17px' }}>团队名称</b>,
                name: 'teamName',
                span: 4,
                render: v => <b style={{ fontSize: '17px' }}>{v}</b>
              },
              {
                label: '病种名称',
                span: 2,
                name: 'diseaseType'
              },
              {
                label: '会诊方式',
                span: 2,
                name: 'mode',
                render: v => (v ? Mode[v] : '-')
              },
              {
                label: '出诊时间',
                span: 2,
                name: 'visitSlot',
                render: (v: any) => {
                  if (v?.length) {
                    return v.map(item => {
                      return (
                        <p>{`${Week[item.week]}${item.timeDesc} ${
                          item.startTime
                        }-${item.endTime}`}</p>
                      );
                    });
                  }
                  return '-';
                }
              },
              {
                label: '会诊室',
                span: 2,
                name: 'mdtRoomName'
              },
              {
                label: '线下号源ID',
                span: 2,
                name: 'hisRegNo'
              },
              {
                label: '线下科室ID',
                span: 2,
                name: 'hisDeptId'
              },
              {
                label: '会诊费用',
                span: 4,
                name: 'price',
                render: v => getPrice(v) + '元'
              },
              {
                label: '团队介绍',
                span: 4,
                name: 'intro',
                render: v => (
                  <div dangerouslySetInnerHTML={{ __html: v || '' }}></div>
                )
              }
            ]}
            data={detail?.data || {}}
          />
        </Space>
      </CardLayout>
      <CardLayout title={'团队成员'}>
        <div style={{ marginBottom: '20px' }}>
          牵头科室：{detail?.data?.leadingDeptName || '-'}
        </div>
        {Object.keys(deptObj).map(item => {
          return (
            <Row>
              {deptObj[item]?.map((v, index) => {
                return (
                  <Col span={6} className={'deptItem'}>
                    {index === 0 ? (
                      <div className={'tag'}>
                        {v.memberRole === 'COORDINATOR'
                          ? '协调员'
                          : v?.deptName}
                      </div>
                    ) : (
                      <div className={'tag'} />
                    )}
                    <Space
                      style={{
                        marginTop: '12px',
                        width: '100%'
                      }}>
                      <Avatar
                        style={{ width: '50px', height: '50px' }}
                        src={v?.doctorImage}
                      />
                      <div
                        style={{
                          width: '100%'
                        }}>
                        <Space
                          align={'center'}
                          style={{
                            justifyContent: 'space-between',
                            width: '100%'
                          }}>
                          {`${v?.doctorId}|${v.doctorName}|${v.doctorLevel}`}
                          <div className={'sort'}>科室序号{v?.sort || 1}</div>
                        </Space>
                        <div> {`${v.deptName}|${v.hospitalName}`}</div>
                      </div>
                    </Space>
                  </Col>
                );
              })}
            </Row>
          );
        })}
        <div style={{ padding: '30px' }}>
          <Button
            style={{ marginRight: '20px' }}
            type={'primary'}
            onClick={() => {
              history.push('/mdtManage/mdtTeam/update/' + id);
            }}>
            编辑
          </Button>
          <Button
            type={'primary'}
            onClick={() => {
              history.push('/mdtManage/mdtTeam/sort/' + id);
            }}>
            团队成员排序
          </Button>
        </div>
      </CardLayout>
    </>
  );
};
