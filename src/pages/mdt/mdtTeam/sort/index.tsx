import { ArrSelect, CardLayout } from 'parsec-admin';
import Sortable from 'sortablejs';
import { useEffect, useRef, useState } from 'react';
import { Button, Col, Empty, message, Row, Space } from 'antd';
import { useHistory, useParams } from 'react-router';
import useApi from '../../api';
import styled from 'styled-components';

export default () => {
  const { id } = useParams<{ id: string }>();
  const history = useHistory();
  const sortRef = useRef<any>(null);
  const [teamName, setTeamName] = useState('');
  const [teamId, setTeamId] = useState<string>(id !== 'All' ? id : '');
  const { request: sortRequest, loading: sortLoading } = useApi.团队成员排序({
    needInit: false
  });
  const { data: teamData } = useApi.查询全部可用团队列表({
    params: { teamName: teamName },
    debounceInterval: 500
  });
  const { request: getDetail, data: teamDetail } = useApi.根据id查询团队详情({
    needInit: id !== 'All' && id === teamId,
    params: id
  });
  useEffect(() => {
    if (sortRef?.current) {
      sortRef.current?.destroy();
    }
    const sortBox = document.getElementById('sortBox');
    if (sortBox) {
      sortRef.current = new Sortable(sortBox, {
        ghostClass: 'blue-background-class',
        animation: 300
        // dragClass: 'moveBox'
      });
    }
  }, [teamDetail]);
  return (
    <CardLayout title={'医生排序'}>
      <Space>
        <ArrSelect
          searchValue={teamName}
          onSearch={setTeamName}
          value={teamId}
          onChange={value => setTeamId(value as string)}
          style={{ width: '180px' }}
          stringValue
          options={(teamData?.data || []).map(item => {
            return {
              label: item.teamName,
              value: item.id
            };
          })}
          placeholder={'请选择团队'}
        />
        <div style={{ marginLeft: '100px' }}>
          <Button
            type={'primary'}
            style={{ marginRight: '20px' }}
            onClick={() => {
              if (teamId) {
                getDetail(teamId);
              } else {
                message.error('请选择团队');
              }
            }}>
            查询
          </Button>
          <Button type={'primary'} ghost>
            重置
          </Button>
        </div>
      </Space>

      <SortWrap>
        {teamDetail?.data?.teamMembers?.length ? (
          <Row id={'sortBox'} style={{ padding: '30px 0', marginTop: '30px' }}>
            {(teamDetail.data.teamMembers || [])
              ?.filter(item => item.memberRole !== 'COORDINATOR')
              ?.sort((a, b) => a.sort - b.sort)
              ?.map(item => (
                <Col span={3} data-Id={item.id}>
                  <DoctorName className={'sortActive'}>
                    {item.doctorName}
                  </DoctorName>
                </Col>
              ))}
          </Row>
        ) : (
          <Empty style={{ padding: '30px 0' }} />
        )}
        <div style={{ padding: '30px 0' }}>
          <Button
            type={'primary'}
            loading={sortLoading}
            onClick={() => {
              if (!teamId) {
                message.error('请选择团队');
                return;
              }
              if (!teamDetail?.data?.teamMembers?.length) {
                message.error('当前没有可排序的数据');
                return;
              }
              const list = sortRef?.current?.toArray() || [];
              if (list?.length) {
                sortRequest({
                  id: teamId,
                  teamMembers: list.map((item, index) => {
                    return { id: item, sort: index + 1 };
                  })
                }).then(res => {
                  if (res?.code === 0) {
                    message.success('提交成功');
                    history.goBack();
                  }
                });
              }
            }}>
            保存当前排序
          </Button>
          <Button
            style={{ marginLeft: '20px' }}
            ghost
            type={'primary'}
            onClick={() => {
              history.goBack();
            }}>
            取消
          </Button>
        </div>
      </SortWrap>
    </CardLayout>
  );
};
const SortWrap = styled.div`
  .blue-background-class {
    .sortActive {
      background: #3fa5f2;
      color: white;
    }
  }
`;
const DoctorName = styled.div`
  cursor: pointer;
  display: inline-block;
  border: 1px solid #b7b4b4;
  border-radius: 5px;
  padding: 5px 15px;
`;
