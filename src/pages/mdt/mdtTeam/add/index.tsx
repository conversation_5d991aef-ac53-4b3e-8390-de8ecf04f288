import {
  ArrSelect,
  CardLayout,
  CheckboxGroup,
  Editor,
  handleSubmit,
  TableList,
  UploadImg
} from 'parsec-admin';
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Row,
  Space,
  Spin,
  Tabs,
  TimePicker
} from 'antd';
import { DeleteOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { useHistory, useParams } from 'react-router';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import apis, { transitionEnum } from '@pages/mdt/api';
import useApi from '@pages/hospital/mattersNeedingAtention/apis';
import env from '@configs/env';
import moment from 'moment';
import './index.less';

export default ({ type }: { type: 'add' | 'update' }) => {
  const history = useHistory();
  const { id } = useParams<{ id: string }>();
  const [form1] = Form.useForm();
  const [form2] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [activeKey, setActiveKey] = useState('团队信息');
  const [visAble, setVisAble] = useState(false);
  const [multiple, setMultiple] = useState(false);

  const [rowList, setRowList] = useState<any[]>([]);
  const [rowList2, setRowList2] = useState<any[]>([]);
  const [doctorList, setDoctorList] = useState<any[]>([]);
  useEffect(() => {
    setRowList(rowList2);
  }, [rowList2]);
  const [roomSearch, setRoomSearch] = useState<string>();
  const [cpHospitalId, setCpHospitalId] = useState<string>();
  const { data: mdtDiseaseType } = useApi.查询字典列表({
    initValue: { data: {} },
    params: {
      groupCode: 'mdtDiseaseType'
    }
  });
  const { data: detail, loading: detailLoading } = apis.根据id查询团队详情({
    needInit: type === 'update' && !!id,
    params: id
  });
  useEffect(() => {
    if (detail?.data?.id) {
      const now = moment().format('YYYY-MM-DD');
      form1.setFieldsValue({
        ...detail.data,
        price: (detail?.data?.price || 0) / 100,
        mode: detail?.data?.mode === 3 ? [1, 2] : [detail?.data.mode],
        visitSlot: detail?.data?.visitSlot?.map(item => {
          return {
            timeDesc: item.timeDesc,
            week: item.week,
            date: [
              moment(now + ' ' + item.startTime),
              moment(now + ' ' + item.endTime)
            ]
          };
        })
      });
      const coordinator: any[] = [];
      const teamPeopleList: any[] = [];
      detail?.data?.teamMembers?.forEach(item => {
        const key = {
          ...item,
          name: item.doctorName,
          cpDeptName: item.deptName,
          level: item.doctorLevel,
          cpHospitalName: item.hospitalName,
          cpDeptId: item.deptId
        };
        if (item.memberRole === 'COORDINATOR') {
          coordinator.push(key);
        } else {
          teamPeopleList.push(key);
        }
      });
      form2.setFieldsValue({
        leadingDeptId: detail.data.leadingDeptId,
        coordinator,
        teamPeopleList
      });
    }
  }, [detail, form1, form2]);
  const {
    data: { data: roomList }
  } = apis.查询会诊室列表({
    params: { roomName: roomSearch },
    initValue: { data: [] },
    debounceInterval: 500
  });
  const { data: hisList } = apis.医联体医院({
    needInit: activeKey === '团队成员'
  });
  const { request: add, loading: addLoading } = apis.新增团队({
    needInit: false
  });
  const { request: update, loading: updateLoading } = apis.编辑团队({
    needInit: false
  });
  const { data: deptList } = apis.查询医联体医院全部科室列表({
    needInit: activeKey === '团队成员' && !!cpHospitalId,
    params: {
      cpHospitalId: cpHospitalId
    }
  });
  const { data: leadingDeptList } = apis.查询医联体医院全部科室列表({
    needInit: activeKey === '团队成员' && !!env.hisId,
    params: {
      cpHospitalId: env.hisId
    }
  });
  const form1ValidateFields = useCallback(() => {
    const { visitSlot } = form1.getFieldsValue();
    if (visitSlot?.filter(Boolean)?.length) {
      form1
        .validateFields()
        .then(() => {
          if (activeKey === '团队信息') {
            setActiveKey('团队成员');
          }
        })
        .catch(() => {
          if (activeKey === '团队成员') {
            setActiveKey('团队信息');
          }
        });
    } else {
      message.error('请添加出诊时间');
      if (activeKey === '团队成员') {
        setActiveKey('团队信息');
      }
    }
  }, [activeKey, form1]);
  return (
    <CardLayout>
      <Spin spinning={detailLoading}>
        <Tabs activeKey={activeKey} onChange={setActiveKey}>
          <Tabs.TabPane key={'团队信息'} tab={'团队信息'}>
            <Form form={form1}>
              <Form
                form={form1}
                wrapperCol={{ span: 15 }}
                labelCol={{ span: 6 }}>
                <Row>
                  <Col span={6}>
                    <Form.Item
                      name={'avatarImage'}
                      label={'团队头像'}
                      extra='建议尺寸为126px * 168px'>
                      <UploadImg
                        length={1}
                        arrValue={false}
                        showUploadList={{
                          showPreviewIcon: true,
                          showRemoveIcon: true,
                          showDownloadIcon: false
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={9}>
                    <Form.Item
                      label={'团队名称'}
                      name={'teamName'}
                      required
                      rules={[{ required: true, message: '请输入团队名称' }]}>
                      <Input placeholder={'请输入团队名称'} />
                    </Form.Item>
                    <Form.Item
                      label={'会诊方式'}
                      required
                      name={'mode'}
                      rules={[{ required: true, message: '请选择会诊方式' }]}>
                      <CheckboxGroup
                        hideCheckAll
                        options={[
                          {
                            label: '线下会诊',
                            value: 1
                          },
                          {
                            label: '线上会诊',
                            value: 2
                          }
                        ]}
                      />
                    </Form.Item>
                    <Form.Item label={'线下号源ID'} name={'hisRegNo'}>
                      <Input placeholder={'请输入线下号源ID'} />
                    </Form.Item>
                    <Form.Item name={'hisDeptId'} label={'线下科室ID'}>
                      <Input placeholder={'请输入线下科室ID'} />
                    </Form.Item>
                  </Col>
                  <Col span={9}>
                    <Form.Item
                      label={'疾病种类'}
                      required
                      name={'diseaseType'}
                      rules={[{ required: true, message: '请选择疾病种类' }]}>
                      <ArrSelect
                        placeholder={'请选择疾病种类'}
                        options={transitionEnum(
                          mdtDiseaseType?.data?.recordList || []
                        )}
                      />
                    </Form.Item>
                    <Form.Item name={'mdtRoomId'} label={'会诊室'}>
                      <ArrSelect
                        onSearch={setRoomSearch}
                        searchValue={roomSearch}
                        placeholder={'请选择会诊室'}
                        options={
                          roomList?.map(item => ({
                            ...item,
                            label: item.roomName,
                            value: item.id
                          })) || []
                        }
                      />
                    </Form.Item>
                    <Form.Item
                      name={'price'}
                      label={'会诊费用'}
                      required
                      rules={[{ required: true, message: '请输入会诊费用' }]}>
                      <Input placeholder={'请输入会诊费用'} />
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
              <Form.Item label={'出诊时间'} required>
                <Form.List name={'visitSlot'}>
                  {(fields, { add, remove }) => (
                    <div
                      style={{
                        position: 'relative',
                        width: fields?.length ? '570px' : '90px',
                        height: fields?.length ? 'auto' : '40px'
                      }}>
                      <Button
                        ghost
                        type={'primary'}
                        style={{ position: 'absolute', top: '0', right: '0' }}
                        onClick={() => {
                          add();
                        }}>
                        + 添加
                      </Button>
                      {fields.map(({ key, name, ...restField }) => (
                        <Space
                          key={key}
                          style={{ display: 'flex', marginBottom: 8 }}
                          align='baseline'>
                          <Form.Item
                            {...restField}
                            name={[name, 'week']}
                            rules={[{ required: true, message: '请选择星期' }]}>
                            <ArrSelect
                              style={{ width: 100 }}
                              options={[
                                '周一',
                                '周二',
                                '周三',
                                '周四',
                                '周五',
                                '周六',
                                '周日'
                              ].map((item, index) => {
                                return { label: item, value: index + 1 };
                              })}
                            />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            name={[name, 'timeDesc']}
                            rules={[{ required: true, message: '请选择午别' }]}>
                            <ArrSelect
                              style={{ width: 100 }}
                              options={['上午', '下午']}
                            />
                          </Form.Item>
                          <Form.Item shouldUpdate>
                            {({ getFieldValue }) => {
                              return (
                                <Form.Item
                                  {...restField}
                                  name={[name, 'date']}
                                  rules={[
                                    { required: true, message: '请选择时间' },
                                    {
                                      required: true,
                                      validator: (rule, value) => {
                                        const timeDesc = getFieldValue(
                                          'visitSlot'
                                        )[name]?.timeDesc;
                                        if (!timeDesc) {
                                          return Promise.resolve();
                                        } else {
                                          const now = `${moment().format(
                                            'YYYY-MM-DD'
                                          )} `;
                                          if (timeDesc === '上午') {
                                            const startTime = moment(
                                              now + '08:00:00'
                                            );
                                            const endTime = moment(
                                              now + '12:00:00'
                                            );
                                            return moment(
                                              now + value[0].format('HH:mm')
                                            ) >= startTime &&
                                              moment(
                                                now + value[1].format('HH:mm')
                                              ) <= endTime
                                              ? Promise.resolve()
                                              : Promise.reject(
                                                  '上午时间选择范围为08:00-12:00'
                                                );
                                          }
                                          if (timeDesc === '下午') {
                                            const startTime = moment(
                                              now + '13:30:00'
                                            );
                                            const endTime = moment(
                                              now + '18:00:00'
                                            );

                                            return moment(
                                              now + value[0].format('HH:mm')
                                            ) >= startTime &&
                                              moment(
                                                now + value[1].format('HH:mm')
                                              ) <= endTime
                                              ? Promise.resolve()
                                              : Promise.reject(
                                                  '下午时间选择范围为13:30-18:00'
                                                );
                                          }
                                        }
                                        return Promise.resolve();
                                      }
                                    }
                                  ]}>
                                  <TimePicker.RangePicker
                                    style={{ width: '187px' }}
                                    format={'HH:mm'}
                                  />
                                </Form.Item>
                              );
                            }}
                          </Form.Item>

                          <MinusCircleOutlined onClick={() => remove(name)} />
                        </Space>
                      ))}
                    </div>
                  )}
                </Form.List>
              </Form.Item>
              <Form.Item
                label={'团队简介'}
                required
                name={'summary'}
                rules={[{ required: true, message: '请输入团队简介' }]}>
                <Input.TextArea />
              </Form.Item>
              <Form.Item
                label={'团队介绍'}
                required
                name={'intro'}
                rules={[{ required: true, message: '请输入团队介绍' }]}>
                <Editor className={'editorWrap'} />
              </Form.Item>
            </Form>
            <div style={{ padding: '30px' }}>
              <Button
                type={'primary'}
                onClick={() => {
                  form1ValidateFields();
                }}>
                下一步
              </Button>
              <Button
                style={{ marginLeft: '20px' }}
                ghost
                type={'primary'}
                onClick={() => {
                  history.goBack();
                }}>
                取消
              </Button>
            </div>
          </Tabs.TabPane>
          <Tabs.TabPane key={'团队成员'} tab={'团队成员'}>
            <Form form={form2}>
              <Form.Item
                label={'牵头科室'}
                required
                labelCol={{ span: 2 }}
                name={'leadingDeptId'}
                rules={[{ required: true, message: '请选择牵头科室' }]}>
                <ArrSelect
                  style={{ width: 200 }}
                  placeholder={'请选择牵头科室'}
                  options={
                    leadingDeptList?.data?.map(item => {
                      return {
                        label: item.name,
                        value: item.no
                      };
                    }) || []
                  }
                />
              </Form.Item>
              <Form.Item shouldUpdate>
                {({ getFieldValue }) => {
                  const list = getFieldValue('coordinator') || [];
                  // console.log(list);
                  return (
                    <Form.Item
                      label={'团队协调员'}
                      required
                      labelCol={{ span: 2 }}
                      name={'coordinator'}
                      rules={[{ required: true, message: '请选择团队协调员' }]}>
                      {!list?.length && (
                        <Button
                          ghost
                          type={'primary'}
                          onClick={() => {
                            setMultiple(false);
                            setVisAble(true);
                          }}>
                          + 添加
                        </Button>
                      )}
                      {list.map(item => {
                        return (
                          <div style={{ lineHeight: '32px' }}>
                            {`${item?.name} ${item?.cpDeptName}|${item?.level}|${item?.cpHospitalName}`}{' '}
                            <span
                              onClick={() => {
                                form2.setFieldValue('coordinator', []);
                              }}>
                              <DeleteOutlined
                                style={{ color: 'red', marginLeft: '20px' }}
                              />
                              删除
                            </span>
                          </div>
                        );
                      })}
                    </Form.Item>
                  );
                }}
              </Form.Item>
              <Form.Item shouldUpdate>
                {({ getFieldValue }) => {
                  const list = getFieldValue('teamPeopleList') || [];
                  return (
                    <Form.Item
                      label={'团队成员'}
                      required
                      labelCol={{ span: 2 }}
                      name={'teamPeopleList'}
                      rules={[{ required: true, message: '请选择团队成员' }]}>
                      <Button
                        ghost
                        style={{ marginBottom: '20px' }}
                        type={'primary'}
                        onClick={() => {
                          setMultiple(true);
                          setVisAble(true);
                          setRowList2(list);
                        }}>
                        + 添加
                      </Button>
                      <Form.List name={'teamPeopleList'}>
                        {(fields, { remove }) => (
                          <div>
                            {fields.map(({ key, name, ...restField }) => {
                              return (
                                <div>
                                  {list[name]?.showDeptName && (
                                    <div style={{ fontWeight: 'bolder' }}>
                                      {list[name]?.showDeptName}
                                    </div>
                                  )}
                                  <div
                                    style={{
                                      justifyContent: 'space-between',
                                      display: 'flex',
                                      alignItems: 'baseline',
                                      width: '500px',
                                      paddingLeft: '20px'
                                    }}>
                                    {`${list[name]?.name} ${list[name]?.level}|${list[name]?.cpHospitalName}`}
                                    <Space align='baseline'>
                                      科内排序：
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'sort']}
                                        rules={[
                                          {
                                            required: true,
                                            message: '请输入排序'
                                          }
                                        ]}>
                                        <Input
                                          style={{
                                            width: 80,
                                            marginRight: '5px'
                                          }}
                                        />
                                      </Form.Item>
                                      <span
                                        onClick={() => {
                                          remove(name);
                                          // form2.setFieldValue('teamPeople', );
                                        }}>
                                        <DeleteOutlined
                                          style={{
                                            color: 'red',
                                            marginLeft: '20px'
                                          }}
                                        />
                                        删除
                                      </span>
                                    </Space>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </Form.List>
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Form>
            <div style={{ padding: '30px' }}>
              <Button
                loading={addLoading || updateLoading}
                type={'primary'}
                onClick={() => {
                  form1ValidateFields();
                  form2.validateFields().then(res => {
                    const form1Value = form1.getFieldsValue();
                    const arr: any[] = [];
                    if (res?.coordinator?.length) {
                      res?.coordinator.forEach(item => {
                        arr.push({
                          cpHospitalId: item.cpHospitalId,
                          doctorId: item.doctorId,
                          deptId: item.cpDeptId,
                          memberRole: 'COORDINATOR',
                          sort: 1
                        });
                      });
                    }
                    if (res?.teamPeopleList?.length) {
                      res?.teamPeopleList.forEach(item => {
                        arr.push({
                          cpHospitalId: item.cpHospitalId,
                          doctorId: item.doctorId,
                          deptId: item.cpDeptId,
                          memberRole: 'MEMBER',
                          sort: Number(item.sort)
                        });
                      });
                    }
                    const leadingDept = leadingDeptList?.data?.find(
                      item => item.no === res?.leadingDeptId
                    );
                    const value = {
                      ...form1Value,
                      price: form1Value?.price * 100,
                      teamMembers: arr,
                      mode:
                        form1Value?.mode?.length === 2
                          ? 3
                          : form1Value?.mode?.[0],
                      // intro: JSON.stringify(form1Value.intro),
                      leadingDeptId: res?.leadingDeptId,
                      leadingDeptName: leadingDept?.name,
                      leadingDeptMainId: leadingDept?.id,
                      visitSlot: form1Value.visitSlot.map(item => {
                        return {
                          timeDesc: item.timeDesc,
                          week: item.week,
                          startTime: item?.date[0].format('HH:mm'),
                          endTime: item?.date[1].format('HH:mm')
                        };
                      })
                    };
                    delete value.teamPeopleList;
                    delete value.coordinator;
                    handleSubmit(() =>
                      type === 'update' && !!id
                        ? update({ ...value, id: Number(id) })
                        : add({ ...value })
                    ).then(() => {
                      history.goBack();
                    });
                  });
                }}>
                保存
              </Button>
              <Button
                style={{ marginLeft: '20px' }}
                ghost
                type={'primary'}
                onClick={() => {
                  setActiveKey('团队信息');
                }}>
                取消
              </Button>
            </div>
            <Modal
              width={900}
              title={'选择医师'}
              visible={visAble}
              onCancel={() => {
                setRowList2([]);
                setVisAble(false);
              }}
              onOk={() => {
                if (multiple) {
                  const cpDeptNameList: string[] = [];
                  rowList?.forEach(item => {
                    if (!cpDeptNameList.includes(item.cpDeptName)) {
                      cpDeptNameList.push(item.cpDeptName);
                    }
                  });
                  const obj: any = {};
                  cpDeptNameList?.forEach(item => {
                    obj[item] = rowList.filter(v => v.cpDeptName === item);
                  });
                  const arr: any = [];
                  Object.keys(obj).forEach(item => {
                    if (obj[item]?.length) {
                      obj[item].forEach((v, index) => {
                        if (!v?.sort) {
                          v.sort = 1;
                        }
                        if (index === 0) {
                          arr.push({ ...v, showDeptName: v.cpDeptName });
                        } else {
                          arr.push(v);
                        }
                      });
                    }
                  });
                  form2.setFieldValue('teamPeopleList', arr);
                } else {
                  form2.setFieldValue('coordinator', rowList);
                }

                setRowList2([]);
                setVisAble(false);
              }}>
              <ModalWrap>
                <TableList
                  showTool={false}
                  tableTitle={'医师列表'}
                  showHeader={false}
                  rowKey={'doctorId'}
                  rowSelection={{
                    type: multiple ? 'checkbox' : 'radio',
                    selectedRowKeys: rowList?.map(item => item.doctorId),
                    onChange: (_, selectedRows) => {
                      // const arr: any[] = selectedRows;
                      // if (multiple && arr?.length) {
                      //   const coordinator = form2.getFieldValue('coordinator');
                      //   if (
                      //     coordinator?.length &&
                      //     arr?.find(
                      //       item =>
                      //         item.cpDeptId + item.doctorId ===
                      //         coordinator[0].cpDeptId + coordinator[0].doctorId
                      //     )
                      //   ) {
                      //     message.error('该医生已经选择为协调员');
                      //     return;
                      //   }
                      // }
                      // if (!multiple && arr?.length) {
                      //   const teamPeopleList = form2.getFieldValue(
                      //     'teamPeopleList'
                      //   );
                      //   if (
                      //     teamPeopleList?.length &&
                      //     teamPeopleList?.find(
                      //       item =>
                      //         item.cpDeptId + item.doctorId ===
                      //         arr[0].cpDeptId + arr[0].doctorId
                      //     )
                      //   ) {
                      //     message.error(
                      //       '该医生已经在团队里面，不可选择为协调员'
                      //     );
                      //     return;
                      //   }
                      // }
                      setRowList2(prevState => {
                        if (
                          doctorList.some(item =>
                            prevState?.find(
                              v =>
                                v.doctorId + v.cpHospitalId ===
                                item.doctorId + item.cpHospitalId
                            )
                          )
                        ) {
                          return [
                            ...selectedRows,
                            ...(prevState.filter(
                              item =>
                                !doctorList?.find(
                                  v =>
                                    v.doctorId + v.cpHospitalId ===
                                    item.doctorId + item.cpHospitalId
                                )
                            ) || [])
                          ];
                        } else {
                          return [...prevState, ...selectedRows];
                        }
                      });
                    }
                  }}
                  pagination={false}
                  getList={useCallback(async ({ params }) => {
                    const p: any = { ...params };
                    if (!p?.cpHospitalId) {
                      return {
                        list: [],
                        total: 0
                      };
                    }
                    const result = await apis.查询医联体医院全部医生列表.request(
                      {
                        ...p
                      }
                    );
                    setDoctorList(result?.data || []);
                    return {
                      list: result?.data || [],
                      total: result?.data?.length
                    };
                  }, [])}
                  searchFormProps={{
                    form: searchForm
                  }}
                  form={searchForm}
                  columns={useMemo(
                    () => [
                      {
                        searchIndex: 'cpHospitalId',
                        title: '机构',
                        search: (
                          <ArrSelect
                            onChange={value => {
                              setCpHospitalId(value as string);
                              searchForm.setFieldsValue({
                                cpHospitalId: value,
                                cpDeptId: undefined
                              });
                            }}
                            options={
                              hisList?.data?.map(item => {
                                return {
                                  label: item.name,
                                  value: item.id
                                };
                              }) || []
                            }
                          />
                        )
                      },
                      {
                        searchIndex: 'cpDeptId',
                        title: '',
                        search: (
                          <ArrSelect
                            options={
                              deptList?.data?.map(item => {
                                return {
                                  label: item.name,
                                  value: item.id
                                };
                              }) || []
                            }
                          />
                        )
                      },
                      {
                        title: '',
                        dataIndex: 'name',
                        render: (v, record: any) => {
                          return `${v} ${record?.cpDeptName}|${record?.level}|${record?.cpHospitalName}`;
                        }
                      }
                    ],
                    [deptList?.data, hisList?.data, searchForm]
                  )}
                />
                <TableList
                  showTool={false}
                  tableTitle={'已选择'}
                  showHeader={false}
                  dataSource={rowList2}
                  pagination={false}
                  columns={[
                    {
                      title: '',
                      dataIndex: 'name',
                      render: (v, record: any) => {
                        return (
                          <Space align={'center'}>
                            {`${v} ${record?.cpDeptName}|${record?.level}|${record?.cpHospitalName}`}{' '}
                            <DeleteOutlined
                              style={{ color: 'red' }}
                              onClick={() => {
                                setRowList2(prevState =>
                                  prevState.filter(
                                    item => item.id !== record?.id
                                  )
                                );
                              }}
                            />
                          </Space>
                        );
                      }
                    }
                  ]}
                />
              </ModalWrap>
            </Modal>
          </Tabs.TabPane>
        </Tabs>
      </Spin>
    </CardLayout>
  );
};
const ModalWrap = styled.div`
  .ant-card {
    margin: 0;
    .tableList-header {
      padding: 0;
    }
  }
  .tableList-search-wrap {
    margin: 0;
    .ant-card-body {
      padding: 0;
    }
  }
  .ant-table-cell {
    padding: 0;
  }
`;
