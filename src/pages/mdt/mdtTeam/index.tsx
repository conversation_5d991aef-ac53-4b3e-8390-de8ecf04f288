import apis from '@pages/mdt/api';
import { Button, Radio } from 'antd';
import {
  actionConfirm,
  ActionsWrap,
  LinkButton,
  TableList
} from 'parsec-admin';
import { useMemo, useState } from 'react';
import { useHistory } from 'react-router';
enum Mode {
  '线下' = 1,
  '线上',
  '线上，线下'
}
export enum Enable {
  '未启用',
  '已启用'
}
export default () => {
  const [mdtState, setMdtState] = useState<string>('');
  const history = useHistory();
  return (
    <TableList
      tableTitle={
        <Radio.Group
          value={mdtState}
          onChange={e => setMdtState(e.target.value)}>
          {[
            { name: '', desc: '全部' },
            { name: '0', desc: '未启用' },
            { name: '1', desc: '已启用' }
          ].map(item => {
            return (
              <Radio.Button key={item.name} value={item.name}>
                {item.desc}
              </Radio.Button>
            );
          })}
        </Radio.Group>
      }
      exportExcelButton
      showTool={false}
      action={
        <>
          <Button
            type={'primary'}
            onClick={() => {
              history.push('/mdtManage/mdtTeam/add');
            }}>
            + 创建团队
          </Button>
          <Button
            type={'primary'}
            onClick={() => {
              history.push('/mdtManage/mdtTeam/sort/All');
            }}>
            团队排序
          </Button>
        </>
      }
      getList={async ({ pagination: { current, pageSize }, params }) => {
        const res = await apis.分页查询团队列表.request({
          pageNum: current,
          numPerPage: pageSize,
          ...params
        });
        return {
          list: (res?.data?.recordList || []) as any,
          total: res?.data?.totalCount || 0
        };
      }}
      pagination={{
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '40', '60', '100'],
        showTotal: total => `共 ${total} 条数据`
      }}
      params={{ enable: mdtState }}
      scroll={{ x: 1500 }}
      columns={useMemo(() => {
        return [
          {
            title: '团队名称',
            dataIndex: 'teamName',
            search: true
          },
          {
            title: '创建人',
            searchIndex: 'creatorName',
            search: true
          },
          {
            title: '病种名称',
            dataIndex: 'diseaseType'
          },
          {
            title: '牵头科室',
            dataIndex: 'leadingDeptName'
          },
          {
            title: '会诊方式',
            dataIndex: 'mode',
            render: v => (v ? Mode[Number(v)] || '-' : '-')
          },
          {
            title: '团队人数',
            dataIndex: 'memberAmount',
            width: '160'
          },
          {
            title: '科室数量',
            dataIndex: 'deptAmount'
          },
          {
            title: '状态',
            dataIndex: 'enable',
            render: v => Enable[v] || '-'
          },
          {
            title: '线下号源ID',
            dataIndex: 'hisRegNo'
          },
          {
            title: '创建时间',
            dataIndex: 'createTime'
          },
          {
            title: '操作',
            fixed: 'right',
            width: 220,
            render: (_, record: any) => {
              const isEnable = record?.enable === 0;
              return (
                <ActionsWrap max={4}>
                  <LinkButton
                    onClick={() => {
                      history.push('/mdtManage/mdtTeam/detail/' + record.id);
                    }}>
                    详情
                  </LinkButton>

                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () => apis.启用停用团队.request(record?.id),
                        isEnable ? '启用' : '停用'
                      );
                    }}>
                    {isEnable ? '启用' : '停用'}
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      history.push('/mdtManage/mdtTeam/update/' + record.id);
                    }}>
                    编辑
                  </LinkButton>
                  {isEnable && (
                    <LinkButton
                      style={{ color: '#dc4747' }}
                      onClick={() => {
                        actionConfirm(
                          () => apis.根据id删除团队.request(record?.id),
                          '',
                          {
                            template: '删除操作不可撤销，是否确定删除？'
                          }
                        );
                      }}>
                      删除
                    </LinkButton>
                  )}
                </ActionsWrap>
              );
            }
          }
        ];
      }, [history])}
    />
  );
};
