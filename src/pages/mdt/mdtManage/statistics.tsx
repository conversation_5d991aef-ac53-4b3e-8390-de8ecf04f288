import apis from '@pages/mdt/api';
import { Radio } from 'antd';
import { DayRangePicker, TableList } from 'parsec-admin';
import { useState } from 'react';

export default () => {
  const [mdtState, setMdtState] = useState<string>('1');
  return (
    <TableList
      tableTitle={
        <Radio.Group
          value={mdtState}
          onChange={e => setMdtState(e.target.value)}>
          {[
            { name: '1', desc: '线下MDT门诊数据统计' },
            { name: '2', desc: '线上MDT门诊数据统计' }
          ].map(item => {
            return (
              <Radio.Button key={item.name} value={item.name}>
                {item.desc}
              </Radio.Button>
            );
          })}
        </Radio.Group>
      }
      exportExcelButton
      showTool={false}
      getList={({ params }: { params: any }) => {
        return apis.MDT订单统计.request({
          mode: mdtState,
          ...params
        }).then(res => {
          return {
            list: res.data || [],
            total: 999
          };
        });
      }}
      pagination={false}
      params={{ mode: mdtState }}
      columns={[
        {
          title: '序号',
          width: '80',
          render: (_, record, index) => {
            return index + 1;
          }
        },
        {
          title: '日期',
          width: '160',
          dataIndex: 'createTime',
          render: false,
          search: (
            <DayRangePicker
              placeholder={['开始时间', '结束时间']}
              valueFormat={'YYYY-MM-DD HH:mm:ss'}
            />
          ),
          searchIndex: ['startTime', 'endTime']
        },
        {
          title: '牵头科室',
          dataIndex: 'leadingDeptName'
        },
        {
          title: 'MDT门诊团队数量',
          dataIndex: 'teamCount'
        },
        {
          title: 'MDT门诊申请次数',
          dataIndex: 'applyCount'
        },
        {
          title: 'MDT门诊开展例数',
          dataIndex: 'finishedCount'
        },
        {
          title: '交易总额（元）',
          dataIndex: 'paidAmount',
          render: v => '¥' + v
        },
        {
          title: '医生退款率',
          dataIndex: 'refundRatio'
        }
      ]}
    />
  );
};
