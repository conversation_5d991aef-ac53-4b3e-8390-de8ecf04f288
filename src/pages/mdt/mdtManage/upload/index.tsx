import { CardLayout, handleSubmit, UploadImg, useModal } from 'parsec-admin';
import { Button, Col, Image, Row } from 'antd';
import { useParams } from 'react-router';
import React, { useMemo } from 'react';
import apis from '@pages/mdt/api';

export default () => {
  const { id } = useParams<{ id: string }>();
  const { data: detail, request: getDetail } = apis.查询线下MDT详情({
    params: id,
    needInit: true,
    initValue: {}
  });
  const imgList = useMemo(() => {
    try {
      if (detail?.data?.reportUrl) {
        return detail.data.reportUrl.split(',');
      }
      return [];
    } catch (e) {
      console.log(e);
      return [];
    }
  }, [detail]);
  const uploadHook = useModal(() => {
    return {
      title: '报告上传',
      onSubmit: values =>
        handleSubmit(() =>
          apis.上传会诊报告
            .request({
              reportUrl: values.reportUrl?.join('') || '',
              id: id
            })
            .then(() => {
              getDetail(id);
            })
        ),
      items: [
        {
          label: '',
          name: 'reportUrl',
          formItemProps: {
            extra: '请上传会诊报告图片，支持jpg,pdf,jpeg,png等格式',
            wrapperCol: { offset: 2, style: { alignItems: 'center' } }
          },
          render: (
            <UploadImg
              length={10}
              arrValue={true}
              showUploadList={{
                showPreviewIcon: true,
                showRemoveIcon: true,
                showDownloadIcon: false
              }}
            />
          )
        }
      ]
    };
  }, [id]);
  return (
    <CardLayout title={'会诊报告'}>
      <Row>
        {imgList.map(item => (
          <Col key={item} span={6}>
            <Image src={item} width={150} height={150} />
          </Col>
        ))}
      </Row>
      <Button
        type={'primary'}
        ghost
        onClick={() => {
          uploadHook({ reportUrl: imgList });
        }}>
        上传报告
      </Button>
    </CardLayout>
  );
};
