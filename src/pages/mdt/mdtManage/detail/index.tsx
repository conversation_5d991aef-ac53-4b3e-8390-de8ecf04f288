import apis, {
  DetailType,
  MdtOfflineApply,
  Member,
  findEnumDesc
} from '@pages/mdt/api';
import getSex from '@src/utils/getSex';
import { Button, Image, Tabs, Input } from 'antd';
import {
  CardLayout,
  FormDescriptions,
  TableList,
  actionConfirm,
  getPrice,
  useModal,
  handleSubmit
} from 'parsec-admin';
import { useHistory, useParams } from 'react-router';
import Source from '../Edit/component/sourceInput';
import TeamMember from '../Edit/component/teamMember';

export default () => {
  const { id } = useParams<{ id: string }>();
  const history = useHistory();
  const { data: detail, request: getDetail } = apis.查询线下MDT详情({
    params: id,
    needInit: true,
    initValue: {}
  });
  const {
    data: { data: mdtEnum }
  } = apis.查询会诊相关枚举({
    initValue: { data: {} }
  });
  const auditModal = useModal(({ id, type }) => {
    const text = type === 'BACK' ? '退回' : type === 'REJECT' ? '不通过' : '';
    return {
      title: `请填写${text}原因:`,
      onSubmit: values => {
        const params = {
          ...values,
          id: id,
          reviewState: type
        };
        return handleSubmit(() => apis.审核线下MDT申请.request(params)).then(
          () => {
            getDetail();
          }
        );
      },
      items: [
        {
          label: '',
          name: 'reason',
          formItemProps: {
            wrapperCol: { span: 24 },
            rules: [{ required: true, message: `请输入${text}原因` }]
          },
          render: <Input.TextArea placeholder={`请输入${text}原因`} />
        }
      ]
    };
  }, []);
  const switchModalVisible = useModal(data => {
    return {
      title: '更改时间',
      onSubmit: values =>
        handleSubmit(() => {
          return handleSubmit(() =>
            apis.更新会诊时间.request({ ...values })
          ).then(() => {
            getDetail();
          });
        }),
      myFormProps: {
        initValues: {
          ...data
        }
      } as any,
      items: [
        {
          name: 'id',
          render: false
        },
        {
          label: '',
          name: 'resourceId',
          formItemProps: {
            hasFeedback: true,
            rules: [
              {
                required: true
              }
            ]
          },
          render: (
            <Source
              teamId={data.roomId}
              type={'1'}
              initDate={data.mdtStartTime}
            />
          )
        }
      ]
    };
  });
  const switchModalTeam = useModal(data => {
    return {
      title: '更改成员',
      onSubmit: values =>
        handleSubmit(() => {
          return handleSubmit(() =>
            apis.调整会诊医师.request({
              roomId: data.id,
              doctors: values.members
            })
          ).then(() => {
            getDetail();
          });
        }),
      myFormProps: {
        initValues: {
          ...data
        }
      } as any,
      items: [
        {
          name: 'id',
          render: false
        },
        {
          label: '',
          name: 'members',
          formItemProps: {
            hasFeedback: true,
            rules: [
              {
                required: true
              }
            ]
          },
          render: <TeamMember />
        }
      ]
    };
  });
  return (
    <CardLayout>
      <Tabs>
        <Tabs.TabPane tab='申请信息' key='申请信息'>
          <FormDescriptions
            title={'患者信息'}
            data={detail?.data}
            edit={false}
            column={3}
            items={[
              {
                label: '姓名',
                name: 'patName',
                render: (_, record: DetailType) => {
                  return `${record.patName} | ${getSex(record.patSex)} | ${
                    record.patAgeStr
                  }`;
                }
              },
              {
                label: '患者id',
                name: 'patCardNo'
              },
              {
                label: '联系电话',
                name: 'contactPhone'
              },
              {
                label: '申请端',
                name: 'applySource',
                render: applySource => {
                  return findEnumDesc(
                    mdtEnum.MdtOfflineApplySourceEnum,
                    applySource as string
                  );
                }
              }
            ]}
          />
          <FormDescriptions<MdtOfflineApply>
            title={'病情信息'}
            data={detail?.data?.mdtOfflineApply}
            edit={false}
            column={1}
            items={[
              {
                label: '症状描述',
                name: 'symptom'
              },
              {
                label: '过敏史',
                name: 'allergies'
              },
              {
                label: '慢病史',
                name: 'medicalHistory'
              },
              {
                label: '手术史',
                name: 'operationHistory'
              },
              {
                label: '上传检验检查资料',
                name: 'imageData',
                render: imageData => {
                  try {
                    const list = JSON.parse(imageData as string) || [];
                    if (list?.length) {
                      return list.map(item => (
                        <Image
                          width={120}
                          key={item}
                          style={{ marginRight: '20px' }}
                          height={120}
                          src={item as string}
                        />
                      ));
                    }
                    return '-';
                  } catch (e) {
                    console.log(e);
                    return '-';
                  }
                }
              },
              {
                label: '文档资料',
                name: 'fileData',
                render: fileData => {
                  try {
                    const list = JSON.parse(fileData as string) || [];
                    if (list?.length) {
                      return list.map(item => (
                        <p>
                          <a href={item as string}>{item}</a>;
                        </p>
                      ));
                    }
                    return '-';
                  } catch (e) {
                    console.log(e);
                    return '-';
                  }
                }
              },
              {
                label: '视频资料',
                name: 'videoData',
                render: videoData => {
                  try {
                    const list = JSON.parse(videoData as string) || [];
                    if (list?.length) {
                      return list.map(item => (
                        <p>
                          <a href={item as string}>{item}</a>;
                        </p>
                      ));
                    }
                    return '-';
                  } catch (e) {
                    console.log(e);
                    return '-';
                  }
                }
              }
            ]}
          />
          {detail?.data?.mdtState === 'WAIT_REVIEW' && (
            <>
              <Button
                style={{ marginRight: '20px' }}
                type='primary'
                onClick={() => {
                  actionConfirm(
                    () =>
                      apis.审核线下MDT申请.request({
                        id: id,
                        reviewState: 'PASS'
                      }),
                    '审核通过'
                  ).then(() => {
                    getDetail();
                  });
                }}>
                同意
              </Button>
              <Button
                style={{ marginRight: '20px' }}
                onClick={() => auditModal({ id: id, type: 'BACK' })}>
                退回
              </Button>
              <Button onClick={() => auditModal({ id: id, type: 'REJECT' })}>
                不同意
              </Button>
            </>
          )}
          {detail?.data?.mdtState === 'WAIT_CANCEL_REVIEW' && (
            <>
              <Button
                style={{ marginRight: '20px' }}
                type='primary'
                onClick={() => {
                  actionConfirm(
                    () => apis.取消线下MDT申请.request(id),
                    '退费'
                  ).then(() => {
                    getDetail();
                  });
                }}>
                确认退费
              </Button>
              <Button
                onClick={() => {
                  history.goBack();
                }}>
                返回
              </Button>
            </>
          )}
        </Tabs.TabPane>
        <Tabs.TabPane tab='会诊信息' key='会诊信息'>
          <FormDescriptions
            title={'会诊信息'}
            data={detail?.data}
            edit={false}
            items={[
              {
                span: 24,
                label: '会诊状态',
                name: 'mdtState',
                render: mdtState =>
                  findEnumDesc(mdtEnum.MdtOfflineStateEnum, mdtState as string)
              },
              {
                span: 1,
                label: '会诊方式',
                name: 'mode',
                render: value => {
                  const modeMap = {
                    1: '线下',
                    2: '线上'
                  };
                  return modeMap[value as string] || '未知';
                }
              },
              {
                span: 1,
                label: '院区',
                name: 'districtName'
              },
              {
                span: 1,
                label: '会诊室',
                name: 'roomName'
              },
              {
                span: 24,
                label: '会诊团队',
                name: 'teamName'
              },
              {
                span: 24,
                label: '会诊时间',
                name: 'mdtStartTime',
                render: (value, record) => {
                  console.log('record======>', record);
                  return (
                    <>
                      <span style={{ marginRight: '10px' }}>{value}</span>
                      <Button
                        disabled={['REFUND', 'CANCELED'].includes(
                          record?.mdtState
                        )}
                        onClick={() => switchModalVisible({ ...record })}>
                        更改时间
                      </Button>
                    </>
                  );
                }
              },
              {
                span: 24,
                label: '参与科室',
                render: (_, record: DetailType) => {
                  const members = record.members;
                  const deptNames: string[] = [];
                  members.forEach((item: Member) => {
                    if (!deptNames.includes(item.deptName)) {
                      deptNames.push(item.deptName);
                    }
                  });
                  return deptNames.join(',');
                }
              },
              {
                span: 24,
                label: '邀请其他专家',
                render: () => '无'
              },
              {
                span: 24,
                label: '会诊专家',
                layout: 'vertical',
                render: (_, record: DetailType) => {
                  const members = record.members;
                  return (
                    <div>
                      <div>
                        <Button
                          disabled={['REFUND', 'CANCELED'].includes(
                            record?.mdtState
                          )}
                          onClick={() =>
                            switchModalTeam({
                              ...record,
                              members: (record.members || []).map(x => {
                                return {
                                  ...x,
                                  cpDeptId: x.deptId,
                                  name: x.doctorName,
                                  cpHospitalName: x.hospitalName,
                                  cpDeptName: x.deptName
                                };
                              })
                            })
                          }>
                          编辑成员
                        </Button>
                      </div>
                      <div>
                        {members.map((item: Member) => {
                          return (
                            <p>
                              {`${item.deptName || '-'}${
                                record?.leadingDeptId === item.deptId
                                  ? '(牵头科室)'
                                  : ''
                              }: ${item.doctorName} (${item.level} | ${
                                item.hospitalName
                              })`}
                            </p>
                          );
                        })}
                      </div>
                    </div>
                  );
                }
              },
              {
                span: 24,
                label: '会诊费用',
                name: 'mdtFee',
                render: mdtFee => `${getPrice(mdtFee as number)} 元`
              }
            ]}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab='操作记录' key='操作记录'>
          <TableList
            dataSource={detail?.data?.eventLogs}
            tableTitle={'审核记录'}
            showTool={false}
            columns={[
              {
                title: '操作类别',
                dataIndex: 'eventType',
                render: eventType =>
                  findEnumDesc(mdtEnum.MdtEventTypeEnum, eventType as string)
              },
              {
                title: '操作时间',
                dataIndex: 'createTime'
              },
              {
                title: '操作人',
                dataIndex: 'eventRole',
                render: eventRole =>
                  findEnumDesc(
                    mdtEnum.MdtOfflineApplySourceEnum,
                    eventRole as string
                  )
              },
              {
                title: '备注',
                dataIndex: 'eventDesc'
              }
            ]}
          />
        </Tabs.TabPane>
      </Tabs>
    </CardLayout>
  );
};
