import React from 'react';
import styled from 'styled-components';
import { Steps } from 'antd';

interface Iprops {
  current: number;
}
const { Step } = Steps;
export default (props: Iprops) => {
  return (
    <Wrapper>
      <Steps current={props.current}>
        {['患者信息', '病情信息', '会诊信息', '创建成功'].map((item, index) => {
          return <Step title={item} key={index} />;
        })}
      </Steps>
    </Wrapper>
  );
};

const Wrapper = styled.div`
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  }
`;
