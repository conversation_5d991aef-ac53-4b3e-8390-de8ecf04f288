import {
  ArrSelect,
  CardLayout,
  FixedFormActions,
  FormDescriptions,
  UploadImg,
  getPrice
} from 'parsec-admin';
import {
  LoadingOutlined,
  PlusOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { Button, DatePicker, Form, Space, Input, message, Upload } from 'antd';
import MySteps from './steps';
import styled from 'styled-components';
import { useCallback, useMemo, useState } from 'react';
import { CheckCircleFilled, SearchOutlined } from '@ant-design/icons';
import MutiInput from './component/mutiInput';
import Source from './component/sourceInput';
import TeamMember from './component/teamMember';
import moment from 'moment';
import apis, { MdtDetail, Paitent } from './api';
import apis1 from '../../api';

import useApi from '@apiHooks';
import { RcFile } from 'antd/es/upload/interface';
import getSex from '@src/utils/getSex';
import { useConfig } from '@src/store/hisConfig';
const { TextArea } = Input;

const modes: any = { 1: '线下', 2: '线上', 3: '全部' };

// const getBase64 = (img: RcFile, callback: (url: string) => void) => {
//   const reader = new FileReader();
//   reader.addEventListener('load', () => callback(reader.result as string));
//   reader.readAsDataURL(img);
// };
/**
 *
 * @param file 文件类型
 * @returns bool
 */
const beforeUpload = (file: RcFile) => {
  const isDcmFile =
    file.type === 'application/dicom' ||
    file.name.toLowerCase().endsWith('.dcm');
  if (!isDcmFile) {
    message.error('You can only upload DICOM (.dcm) files!');
  }

  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('File must be smaller than 10MB!');
  }

  return isDcmFile && isLt10M;
};

export default () => {
  const [form] = Form.useForm();
  const [form1] = Form.useForm();
  const [stage, setStage] = useState(0);
  const [teamId, setTeamId] = useState('' as any);
  const [type, setType] = useState('1' as any);
  const [dcmURl, setdcmURl] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [suffererId, setSufferer] = useState('');
  const [teamData, setTeamData] = useState<MdtDetail>();
  const [patientIdOptions, setPatientIdOptions] = useState<
    {
      key: string;
      value: number;
    }[]
  >([]);
  const [paitent, setPaitent] = useState<Paitent[]>([]);
  const {
    data: { data: teamList },
    request: getTeamList
  } = apis1.查询会诊团队列表({
    initValue: { data: [] },
    needInit: true
  });
  const realationID = useMemo(() => {
    return (teamList || []).find(item => item.id === teamId)?.mdtRoomId;
  }, [teamId, teamList]);
  const { uploadApi } = useConfig();

  const uploadHandler = useCallback(
    async (params: { file?: File }) => {
      if (!params.file) {
        return;
      }
      try {
        setLoading(true);
        message.loading({
          content: '上传中...'
        });
        const url = await uploadApi(params.file);
        console.log('url', url);
        setdcmURl(url);
        setLoading(false);
      } finally {
        message.destroy();
      }
    },
    // [onChange]
    []
  );
  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>上传</div>
    </div>
  );

  /**
   * 根据患者id 进行数据信息的切换
   * @param id 患者id
   */
  const hanlerSelect = id => {
    const findPatient = paitent?.find(item => item.id === id);
    form.setFieldsValue({
      patientId: findPatient?.id,
      patName: findPatient?.name,
      patSex: findPatient?.sex,
      birthday: moment(findPatient?.birthday),
      contactPhone: findPatient?.mobile
    });
  };
  const handleSearch = () => {
    // todo 暂时 先替换为 输入查询条件 进行查询，禁用掉下边输入框
    if (!suffererId) {
      message.error('请输入患者Id查询');
      return;
    }
    // const { patName } = form.getFieldsValue();
    apis.条件查询就诊人详情
      .request({
        patCardNo: suffererId
        // name: patName
      })
      .then(res => {
        // todo 根据要求 卡号查询出来的就诊人list 就诊患者id 需要组装成 下拉选择的数据
        if (res?.data.length === 0) {
          message.error('未查到相关信息');
          return;
        }
        setPaitent(res?.data);
        const formatPatientIdOptions =
          res.data &&
          res?.data.map(item => ({
            key: `${item?.id}-${item?.name}`,
            value: item.id
          }));
        setPatientIdOptions(formatPatientIdOptions);
        if (res?.data?.length === 1) {
          const paitent = res?.data?.[0];
          // todo 单独处理患者信息 因为位下拉模式
          const options =
            res.data &&
            res.data.map(item => ({
              key: `${item?.id}-${item?.name}`,
              value: item.id
            }));
          setPatientIdOptions(options);
          form.setFieldsValue({
            // patientId: `${paitent?.id}-${paitent.name}`,
            patName: paitent?.name,
            patSex: paitent?.sex,
            birthday: moment(paitent?.birthday),
            contactPhone: paitent?.mobile
          });
        }
      });
  };
  return (
    <Wrapper>
      <CardLayout>
        <MySteps current={stage} />
      </CardLayout>
      <CardLayout
        style={{
          display: stage === 0 ? 'flex' : 'none'
        }}>
        <FormDescriptions
          title={'患者信息'}
          formProps={{
            requiredMark: true
          }}
          extra={
            <div style={{ display: 'flex' }}>
              <Input
                placeholder='请输入患者Id查询'
                value={suffererId}
                style={{ marginRight: 10 }}
                onChange={e => setSufferer(e.target.value)}
              />
              <Button size={'small'} onClick={handleSearch}>
                <SearchOutlined />
                查询
              </Button>
            </div>
          }
          form={form}
          edit={true}
          // disabled={true}
          items={[
            {
              label: '患者ID',
              name: 'patientId',
              required: true,
              formItemProps: {
                render: (
                  <ArrSelect
                    allowClear={true}
                    fieldNames={{ label: 'key', value: 'value' }}
                    options={patientIdOptions || []}
                    onChange={hanlerSelect}
                  />
                )
              }
            },
            {
              label: '姓名',
              name: 'patName',
              required: true
            },
            {
              label: '性别',
              name: 'patSex',
              required: true,
              formItemProps: {
                render: (
                  <ArrSelect
                    allowClear={false}
                    options={[
                      { value: 'F', children: '女' },
                      { value: 'M', children: '男' }
                    ]}
                  />
                )
              }
            },
            {
              label: '出生日期',
              name: 'birthday',
              required: true,
              formItemProps: {
                render: (
                  <DatePicker
                    disabledDate={current => {
                      return current && current > moment().endOf('day');
                    }}
                  />
                )
              }
            },
            {
              label: '联系电话',
              name: 'contactPhone',
              required: true,
              formItemProps: {
                rules: [
                  {
                    pattern: /^[1][3-9][0-9]{9}$/,
                    required: true,
                    message: '请输入正确的号码!'
                  }
                ]
              }
            },
            {
              label: '家庭住址',
              name: 'address'
            }
          ]}
        />
      </CardLayout>
      <CardLayout
        style={{
          display: stage === 1 ? 'flex' : 'none'
        }}>
        <FormDescriptions
          title={'病情信息'}
          formProps={{
            requiredMark: true
          }}
          column={3}
          form={form}
          edit={true}
          items={[
            {
              span: 3,
              label: '主诉',
              name: 'chiefComplaint',
              required: true,
              formItemProps: {
                render: <TextArea showCount maxLength={1000} />
              }
            },
            {
              span: 3,
              label: '初步诊断',
              name: 'initialDiagnosis',
              required: true,
              formItemProps: {
                render: <MutiInput />
              }
            },
            {
              span: 3,
              label: '现病史',
              name: 'medicalHistory',
              required: true,
              formItemProps: {
                render: <TextArea showCount maxLength={1000} />
              }
            },
            {
              span: 3,
              label: '既往史',
              name: 'anamnesis',
              formItemProps: {
                render: <TextArea showCount maxLength={1000} />
              }
            },
            {
              span: 3,
              label: '体格检查',
              name: 'examination',
              formItemProps: {
                render: <TextArea showCount maxLength={1000} />
              }
            },
            {
              span: 3,
              label: '检验检查资料',
              name: 'imageData',
              formItemProps: {
                extra:
                  '(图片支持JPEG、JPG、PNG、BMP、GIF、TIFF格式，限制单张图片不超过5M)',
                render: (
                  <UploadImg
                    length={5}
                    arrValue={true}
                    showUploadList={{
                      showPreviewIcon: true,
                      showRemoveIcon: true,
                      showDownloadIcon: false
                    }}
                  />
                )
              }
            },
            {
              span: 3,
              label: '检查文档资料',
              name: 'fileData',
              formItemProps: {
                extra: '(请上传DCM格式的DICOM文件)',
                render: (
                  // <UploadImg
                  //   length={5}
                  //   arrValue={true}
                  //   showUploadList={{
                  //     showPreviewIcon: true,
                  //     showRemoveIcon: true,
                  //     showDownloadIcon: false
                  //   }}
                  // />
                  <Upload
                    listType='picture-card'
                    className='avatar-uploader'
                    showUploadList={false}
                    beforeUpload={beforeUpload}
                    customRequest={uploadHandler as any}>
                    {dcmURl ? (
                      // <Image
                      //   src={dcmURl}
                      //   alt='avatar'
                      //   style={{ width: '100%' }}
                      // />
                      <div
                        style={{
                          fontSize: '24px', // 调整图标大小
                          color: '#52c41a', // 一般成功颜色
                          cursor: 'pointer' // 鼠标样式为手指
                        }}
                        onClick={() => {
                          if (dcmURl) {
                            window.open(dcmURl, '_blank');
                          } else {
                            message.error('Download URL not available');
                          }
                        }}>
                        <CheckCircleOutlined />
                      </div>
                    ) : (
                      uploadButton
                    )}
                  </Upload>
                )
              }
            },
            {
              span: 3,
              label: '视频资料',
              name: 'videoData',
              formItemProps: {
                extra: '（视频仅支持MP4格式，单个视频不超过200M',
                render: (
                  <UploadImg
                    length={5}
                    arrValue={true}
                    showUploadList={{
                      showPreviewIcon: true,
                      showRemoveIcon: true,
                      showDownloadIcon: false
                    }}
                  />
                )
              }
            },
            {
              span: 3,
              label: '备注',
              name: 'followAppointDirector',
              formItemProps: {
                render: <TextArea />
              }
            }
          ]}
        />
      </CardLayout>
      <CardLayout
        style={{
          display: stage === 2 ? 'flex' : 'none'
        }}>
        <FormDescriptions
          title={'会诊信息'}
          formProps={{
            requiredMark: true
          }}
          form={form}
          edit={true}
          items={[
            {
              label: '会诊目的',
              name: 'purpose',
              required: true
            },
            {
              label: '会诊团队',
              name: 'teamId',
              required: true,
              formItemProps: {
                render: (
                  <ArrSelect
                    options={teamList.map(item => {
                      return {
                        label: item.teamName,
                        value: item.id
                      };
                    })}
                    showSearch
                    onSearch={value => {
                      getTeamList({
                        teamName: value
                      });
                    }}
                    onChange={v => {
                      setTeamId(v);
                      console.log(v);
                    }}
                  />
                )
              }
            },
            {
              label: '会诊方式',
              name: 'mode',
              required: true,
              formItemProps: {
                initialValue: '1',
                render: (
                  <ArrSelect
                    //todo  线下会诊:1, 线上会诊: 2
                    options={[
                      { value: '2', label: '线下会诊' },
                      { value: '1', label: '线上会诊' }
                    ]}
                    onChange={v => {
                      setType(v);
                    }}
                  />
                )
              }
            },
            {
              label: '会诊时间',
              name: 'resourceId',
              span: 3,
              required: true,
              formItemProps: {
                render: <Source teamId={realationID} type={type} />
              }
            },
            {
              label: '邀请其他专家',
              name: 'inviteExperts',
              span: 3,
              formItemProps: {
                render: <TeamMember />
              }
            }
          ]}
        />
      </CardLayout>
      <CardLayout
        style={{
          display: stage === 3 ? 'flex' : 'none'
        }}>
        <FormDescriptions
          title={
            <Space size={10}>
              <CheckCircleFilled
                style={{ color: '#72c240', fontSize: '32px' }}
              />
              会诊提交成功
            </Space>
          }
          form={form1}
          edit={false}
          data={{
            teamName: teamData?.teamName,
            mdtStartTime: teamData?.mdtStartTime,
            mode: teamData?.mode,
            patName: teamData?.patName,
            purpose: teamData?.purpose,
            mdtFee: teamData?.mdtFee
          }}
          items={[
            {
              span: 3,
              label: '会诊团队',
              name: 'teamName'
            },
            {
              span: 3,
              label: '会诊时间',
              name: 'mdtStartTime',
              render: (v, r: any) => (v ? v : '-')
            },
            {
              span: 3,
              label: '会诊方式',
              name: 'mode',
              render: v => (v ? modes[v] : '-')
            },
            {
              span: 3,
              label: '患者信息',
              name: 'patName',
              render: (v, r) =>
                `${v} -${getSex((teamData && teamData?.patSex) as string)}- ${
                  teamData?.patAgeStr
                }`
            },
            {
              span: 3,
              label: '会诊目的',
              name: 'purpose',
              render: (v, r) => (v ? v : '-')
            },
            {
              span: 3,
              label: '会诊费用',
              name: 'mdtFee',
              render: v => '¥' + getPrice(v)
            }
          ]}
        />
      </CardLayout>

      <FixedFormActions>
        <Space>
          {stage !== 0 && (
            <Button
              type='ghost'
              onClick={() => {
                setStage(stage - 1);
              }}>
              上一步
            </Button>
          )}
          {stage !== 3 && (
            <Button
              type='primary'
              onClick={() => {
                if (stage === 0) {
                  form
                    .validateFields([
                      'patientId',
                      'patName',
                      'patSex',
                      'birthday',
                      'contactPhone'
                    ])
                    .then(() => {
                      setStage(1);
                    });
                }
                if (stage === 1) {
                  form
                    .validateFields([
                      'chiefComplaint',
                      'initialDiagnosis',
                      'medicalHistory'
                    ])
                    .then(() => {
                      setStage(2);
                    });
                }
                if (stage === 2) {
                  form.validateFields().then(async values => {
                    const JSON2Str = str => {
                      let Datas = '[]';
                      try {
                        Datas = JSON.stringify(values[str]);
                      } catch (e) {
                        Datas = '[]';
                      }
                      values[str] = Datas;
                    };
                    if (values.imageData) {
                      JSON2Str('imageData');
                    }
                    if (values.fileData) {
                      JSON2Str('fileData');
                    }
                    if (values.videoData) {
                      JSON2Str('videoData');
                    }
                    // todo 根据后端数据结构 组装字段
                    const newInitialDiagnosis = values?.initialDiagnosis.map(
                      item => ({
                        hosIcdCode: item?.name?.hosIcdCode,
                        hosIcdName: item?.name?.hosIcdName
                      })
                    );
                    // apis.新增MDT申请
                    //   .request({
                    //     ...values,
                    //     initialDiagnosis: newInitialDiagnosis,
                    //     birthday: moment(values.birthday).format('YYYY-MM-DD'),
                    //     roomId: realationID
                    //   })
                    //   .then(res => {
                    //     console.log('res', res.data);
                    //     form1.setFieldsValue({
                    //       ...res.data,
                    //       teamName: res?.data?.teamName,
                    //       mdtStartTime: res?.data?.mdtStartTime,
                    //       mode: res?.data?.mode,
                    //       patName: res?.data?.patName,
                    //       purpose: res?.data?.purpose,
                    //       mdtFee: res?.data?.mdtFee
                    //     });
                    //   });

                    const res = await apis.新增MDT申请.request({
                      ...values,
                      initialDiagnosis: newInitialDiagnosis,
                      birthday: moment(values.birthday).format('YYYY-MM-DD'),
                      roomId: realationID
                    });
                    setTeamData(res.data);
                    // form1.setFieldsValue({
                    //   // ...res.data,
                    //   // teamName: res?.data?.teamName,
                    //   mdtStartTime: res?.data?.mdtStartTime,
                    //   mode: 222,
                    //   patName: res?.data?.patName,
                    //   purpose: res?.data?.purpose,
                    //   mdtFee: res?.data?.mdtFee,
                    //   teamName: res?.data?.teamName
                    // });
                    setStage(3);
                  });
                }
              }}>
              下一步
            </Button>
          )}
          {stage === 3 && (
            <Button
              type='ghost'
              onClick={() => {
                setStage(stage - 1);
              }}>
              返回
            </Button>
          )}
        </Space>
      </FixedFormActions>
    </Wrapper>
  );
};

const Wrapper = styled.div`
  margin: -30px 1px 0;
  .tip {
    padding-left: 25%;
  }
`;
