import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiRequestParams,
  ListApiResponseData,
  ApiResponse
} from '@src/configs/apis';
import { HosptitalGetBy, HosptitalGetByList } from '@src/pages/authority/d';

export interface MenuItem {
  childMenu: MenuItem[];
  code: string;
  id: number;
  name: string;
  operatePurview: string;
  parentId: number;
  sort: number;
  type: number;
  url: string;
  userId: number;
}

export interface AddHospParams {
  hisId: string;
  name: string;
  contactNumber: string;
  adminAccount: string;
  adminName: string;
  moIds: string;
}
export interface Paitent {
  id: 44; // 主键ID
  name: 'test_1380c647a56a'; // 就诊人姓名
  hisId: 20; // 医院id
  hisName: 'test_50bb148d527c'; // 医院名称
  userId: 24; // 所属用户id
  channelType: 'test_33ab5e17fec3'; // 就诊卡功能类型;3位二进制数字,每位数字分别代表体检卡,住院卡,门诊卡;  例：001-只是门诊卡, 011-既是住院卡又是门诊卡, 111-表示体检卡,住院卡,门诊卡三卡合一
  patientType: 56; // 就诊人类型【0-默认，1-儿童，2-妇女，3-老人】
  relationType: 13; // 与本人关系id
  idType: 42; // 证件类型（1-身份证 2-港澳居民身份证 3-台湾居民身份证 4-护照 5-监护人身份证 6-军官证 7-回乡证 8-出生医院证明 9-户口簿 10-台胞证）
  idNo: 'test_c9e2812978f0'; // 就诊人证件号
  sex: 'test_fb3ccb3f4eab'; // 性别（F-女 M-男）
  birthday: '2014-08-03 01:04:43'; // 出生日期
  mobile: 'test_6831eedad425'; // 电话号码
  address: 'test_a2e4d7058f0c'; // 住址
  bindStatus: 65; // 是否绑卡（0-否 1-是）
  parentName: 'test_c75d83e9e81c'; // 监护人姓名
  parentIdType: 3; // 监护人证件类型（1-身份证 2-港澳居民身份证 3-台湾居民身份证 4-护照 5-监护人身份证 6-军官证 7-回乡证 8-出生医院证明 9-户口簿 10-台胞证）
  parentIdNo: 'test_2f5c50e4854a'; // 监护人证件号
  patCardType: 23; // 医院卡号类型(11：二代身份证;12：港澳居民身份证;13：台湾居民身份证;14：护照;21：院内诊疗卡;22：社保卡;23：医保卡;24：区域健康卡;)
  patCardNo: 'test_f61fadc75cae'; // 医院卡号id
  consumeType: 'test_0d69bd1b7624'; // 消费类型（自费/医保）
  isDefalut: 96; // 标志（1-默认就诊人）
  isSelf: 18; // 是否是自己
  syncStatus: 37; // 同步状态（0-未同步 1-已同步）
  type: 42; // 类型
  idImage: 'test_a7710e03af93'; // 身份验证图片
  patInNo: 'test_d6d05fc939c8'; // his住院id
  bindMedicareCard: 49; // 0 未绑定医保卡 1 已绑定医保卡
  height: 40; // 身高
  weight: 'test_71973fbaf42e'; // 体重
  married: 69; // 婚否 0 未婚 1 已婚
  smoking: 10; // 吸烟史 吸烟的年数 0为不吸烟
  patHisId: 'test_a082ff8b89ad'; // his门诊中的patientId
  patHisNo: 'test_5b05295ba9fb'; // his门诊中患者id
  createTime: '2027-01-31 20:22:05'; // 创建时间
  updateTime: '2018-10-31 15:19:27'; // 最后修改时间
  accountId: 87; // 账号id
  openId: 'test_4e8c1dcf92c0'; // openid
  cardStatus: 26; // 0.成功 1.绑定失败 2.取消绑定 3.申请审核 4.审核不通过 5.审核通过 6.人脸识别失败 7.未进行人脸识别
  healthCardId: 'test_ea91b18a93ce'; // 健康卡id
  qrCodeText: 'test_11d16c3cf910'; // 二维码文本
  nation: 'test_f0b9432ccb9a'; // 民族
  country: 'test_f1ded587eda9'; // 国籍
  birthPlace: 'test_7b30b90d7753'; // 出生地
  school: 'test_a54a4e1e7ced'; // 就读学校
  patientImage: 'test_353c3be7ea5d'; // 就诊人头像
  hospitalUserId: 'test_dc0b05f62de0'; // his就诊人唯一用户id（儿童医院）
  bindTime: '2018-07-10 17:05:11'; // 绑定时间
  level: 48; // 信息等级, 默认10
  fromHis: 40; // patCardNo, patHisNo是否来自his, 0: 否, 1: 是
  faceAuthStatus: 87; // 是否通过人脸认证
}
export interface Dictionaries {
  id: 1; //Id
  hisId: 'query.hisId'; // 医院id
  dictGroupCode: 'query.groupCode'; // 字典组code
  dictKey: '@word'; // 字典键
  dictValue: {
    value: '@word';
  }; // 字典值
  sortNo: '@natural(1, 100)'; // 排序号 数值越大越靠前
}
export interface MdtDetail {
  id: 80;
  createTime: '2028-05-19 16:14:23';
  updateTime: '2016-02-17 00:06:37';
  hisId: 10; // 医院id
  roomId: 48; // 会诊室id
  roomName: 'test_d919a51ada0e'; // 会诊室名称
  userId: 33; // 用户id
  patientId: 70; // 就诊人id
  patName: 'test_79a27381bb3d'; // 就诊人姓名
  applySource: 'MCH'; // 申请来源
  teamId: 89; // 团队id
  teamName: 'test_7e56b0660911'; // 团队名称
  diseaseType: 'test_6b86cccabc91'; // 病种类别
  mode: 20; // 会诊方式, 1:线下，2线上，3全部
  payState: 'UNPAY'; // 支付状态
  mdtState: 'REVIEWED'; // 会诊状态
  resourceId: 23; // 号源id
  mdtStartTime: '2026-08-22 10:23:34'; // 会诊开始时间
  mdtEndTime: '2031-04-05 13:50:47'; // 会诊截止时间
  mdtFee: 88; // 金额（分）
  orderId: 37; // 订单id
  orderSerialNumber: 'test_d31e4d2b1502'; // 订单序列号
  purpose: 'test_711d404a9f87'; // 会诊目的
  rejectReviewReason: 'test_5da87e5a3501'; // 审核失败原因
  applyCancelReason: 'test_670e669f10a9'; // 申请取消原因
  payTime: '2031-03-12 11:46:38'; // 支付时间
  reviewTime: '2028-02-07 17:40:20'; // 审核时间
  applyCancelTime: '2020-12-08 13:57:57'; // 申请取消时间
  cancelTime: '2034-02-24 21:20:39'; // 取消时间
  finishTime: '2014-10-11 11:36:23'; // 完成时间
  hospitalName: 'test_f43ea5eaac25'; // 医院名称
  districtId: 80; // 院区id
  districtName: 'test_d765d09495c1'; // 院区名称
  roomAddress: 'test_090b8d9f38c7'; // 诊室位置
  reportUrl: 'test_3c27955b5ec9'; // 会诊报告
  contactPhone: 'test_cd70660b2d33'; // 预留电话
  patSex: 'test_db4203f570ca'; // 就诊人性别
  patCardNo: 'test_16d0da922ef7'; // 就诊卡号
  patAgeStr: 'test_8d44134a1eb5'; // 就诊人年龄描述
  leadingDeptId: 18; // 牵头科室id
  leadingDeptName: 'test_313009961395'; // 牵头科室名称
  resourceCode: 'test_c76b4be39bec'; // 号源唯一编号
  hospitalTradeNo: 'test_0e26fbedebfc'; // 医院订单号
  hisReceiptNo: 'test_051324bce1ef'; // 医院发票号
  hisRegNo: 'test_ed4d89ec165d'; // 医院号源ID
  hisDeptId: 'test_a09b50e6653a'; // 医院号源科室ID
  videoStatus: 70; // 视频会诊状态（0等待呼叫, 1呼叫中, 2视频通话中, 3电话挂起（意外退出））
}
export default {
  医疗机构列表: createApiHooks(
    (data: ListApiRequestParams & { hisId?: string }) =>
      request.post<
        ListApiResponseData<{
          adminAccount: string;
          adminName: string;
          certFirstDate: string;
          certFirstTime: string;
          certUpdateDate: string;
          certUpdateTime: string;
          contactNumber: string;
          createTime: string;
          grading: string;
          hisId: string;
          honorImages: string;
          hospitalAddress: string;
          hospitalIntroduction: string;
          hospitalLevel: string;
          hospitalName: string;
          id: number;
          instCode: string;
          instInfoSafeEnsure: string;
          instRegisterNumber: string;
          level: string;
          licence: string;
          licenseEndDate: string;
          licenseEndTime: string;
          licenseStartDate: string;
          licenseStartTime: string;
          name: string;
          serviceScope: string;
          status: string;
          updateTime: string;
        }>
      >('/mch/his/hospitalInfo/page', data)
  ),
  修改医院: createApiHooks(
    (data: {
      id: number;
      hisId?: string;
      status?: string;
      activityBanner?: string;
      notice?: string;
      hospitalLogo?: string;
      hospitalBanner?: string;
    }) =>
      request.post<ApiResponse<Record<string, unknown>>>(
        '/mch/his/hospitalInfo/update',
        data,
        {
          headers: { 'Content-Type': 'application/json' }
        }
      )
  ),
  获取医院列表: createApiHooks((params: HosptitalGetBy) =>
    request.post<HosptitalGetByList>('/mch/his/hospitalInfo/getBy', params)
  ),
  重置密码: createApiHooks(
    (params: { account: string; hisId: string; id: number }) =>
      request.post('/mch/user/doctorAccount/password/reset', params)
  ),
  查询所有菜单权限: createApiHooks((params: { clientType?: string }) =>
    request.get<{ data: MenuItem[] }>('/mch/his/role/getAllMenuOperate', {
      params
    })
  ),
  添加医疗机构: createApiHooks((data: AddHospParams) =>
    request.post('/mch/his/hospitalInfo/add', data)
  ),
  条件查询就诊人详情: createApiHooks(
    (data: { patCardNo?: string; name?: string }) =>
      request.post<{ data: Paitent[] }>('/mch/user/patient/getBy', data)
  ),
  查询字典列表: createApiHooks(
    (
      params: { groupCode: string; dictValue?: string } & ListApiRequestParams
    ) =>
      request.get<ListApiResponseData<Dictionaries>>(
        `/kaiqiao/his/dictItem/page/${params.groupCode}`,
        { params }
      )
  ),
  新增MDT申请: createApiHooks(
    (data: { startTime?: string; endTime?: string; mode?: string }) =>
      request.post<ApiResponse<MdtDetail>>(`/mch/cooperate/mdt-offline`, {
        ...data
      })
  ),
  调整会诊医师: createApiHooks(
    (data: {
      roomId?: string;
      operType?: string;
      doctors?: Array<{
        account: ''; //医生的账号，可能是手机号等不同的值，使用账号+hisId可以在不同的表中查到医生的数据
        deptId: 0; //会诊科室ID
        deptName: ''; //会诊科室名称
        title: ''; //会诊人员职称
        image: ''; //会诊人员头像
        name: ''; //会诊人员名称
      }>;
    }) =>
      request.put<ApiResponse<any>>(
        `/doctor/cooperate/mdt-offline/change-room-doctor`,
        {
          ...data
        }
      )
  ),
  更新会诊时间: createApiHooks((data: { id?: string; resourceId?: string }) =>
    request.put<ApiResponse<any>>(
      `/doctor/cooperate/mdt-offline/change-resource`,
      {
        ...data
      }
    )
  )
};
