import React, { useEffect, useState } from 'react';
import { Input, Button } from 'antd';
import { Space } from '@kqinfo/ui';
import { useModal } from 'parsec-admin';
import { MinusCircleFilled } from '@ant-design/icons';
import MyTableList from '@src/components/myTableList';
import useApi from '../api';
export const mockMyTableList = (data: any[]) => {
  return Promise.resolve({
    msg: '',
    code: 200,
    data: {
      recordList: data,
      totalCount: data.length,
      currentPage: 1
    }
  }) as any;
};
interface Value {
  dictKey?: string;
  dictValue?: string;
}

interface FormValue {
  hosIcdCode?: string;
  hosIcdName?: string;
}

interface Props {
  value?: FormValue[];
  onChange?: (value: FormValue[]) => void;
}

const MutiInput = (props: Props) => {
  const { value, onChange } = props;
  const [list, setList] = React.useState<Value[]>([]);
  const [selectRows, setSelectRows] = useState<Value[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const onSelectChange = (selectedRowKeys: React.Key[], selectedRows) => {
    setSelectedRowKeys(selectedRowKeys as string[]);
    setSelectRows(selectedRows);
  };
  const deleteItem = (item: Value) => {
    const newList = list.filter(v => v.dictKey !== item.dictKey);
    setSelectRows(newList);
    setList(newList);
    setSelectedRowKeys(newList.map(v => v.dictKey) as string[]);
  };

  const openModal = useModal(() => {
    return {
      title: '选择诊断',
      width: 800,
      onSubmit: async () => {
        setList(selectRows);
        onChange?.(
          selectRows.map(v => ({
            hosIcdCode: v.dictKey,
            hosIcdName: v.dictValue
          }))
        );
      },
      children: (
        <MyTableList
          rowSelection={{
            selectedRowKeys,
            onChange: onSelectChange,
            getCheckboxProps: (record: any) => {
              return {
                disabled: !!list.find(v => v.dictKey === record?.dictKey)
              };
            }
          }}
          getList={({ params }) =>
            useApi.查询字典列表.request({
              ...params,
              groupCode: 'mainDiagnosis'
            })
          }
          rowKey='dictKey'
          columns={[
            { title: '诊断编码', dataIndex: 'dictKey' },
            {
              title: '诊断名称',
              dataIndex: 'dictValue',
              render: v => v.value,
              search: (
                <Input
                  style={{ width: 300 }}
                  placeholder='请输入诊断编号或名称'
                />
              )
            }
          ]}
        />
      )
    };
  }, [selectedRowKeys]);
  useEffect(() => {
    if (value) {
      setList(
        value.map(v => ({ dictKey: v.hosIcdCode, dictValue: v.hosIcdName }))
      );
    }
  }, [value]);
  return (
    <div>
      <Button type='primary' onClick={() => openModal()}>
        ICD-10
      </Button>
      <Space vertical size={10} style={{ width: '100%' }}>
        {/* {(list.length !== 0 ? list : [{ dictValue: undefined }]).map((v, i) => { */}
        {(list ?? []).map((v: any, i) => {
          return (
            <Space size={10} alignItems={'center'}>
              <Input
                key={i}
                style={{ width: 250 }}
                disabled
                placeholder='请选择'
                value={v?.dictValue?.value}
              />
              <MinusCircleFilled
                onClick={() => deleteItem(v)}
                style={{ color: 'red', cursor: 'pointer' }}
              />
            </Space>
          );
        })}
        {list.length === 0 && (
          <Input style={{ width: 250 }} disabled placeholder='请选择' />
        )}
      </Space>
    </div>
  );
};

export default MutiInput;
