import { DatePicker, Row, Col, message } from 'antd';
import { ArrSelect } from 'parsec-admin';
import moment from 'moment';
import apis from '@pages/mdt/api';
import { useEffect, useState } from 'react';
interface Props {
  value?: string;
  onChange?: (value: string) => void;
  teamId?: any;
  type?: any;
  initDate?: string;
}
const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
const Source = (props: Props) => {
  console.log('props', props);
  const [showDate, setShowDate] = useState(undefined as any);
  const {
    data: { data: dateList }
  } = apis.日历高亮({
    params: {
      relationId: props.teamId,
      type: props.type || '1'
    },
    initValue: { data: [] },
    needInit: !!props.teamId && !!props.type
  });
  const {
    data: { data: detailList }
  } = apis.排班详情({
    params: {
      relationId: props.teamId,
      type: props.type || '1',
      visitDate: showDate
    },
    initValue: { data: [] },
    needInit: !!props.teamId && !!props.type && !!showDate
  });
  console.log('detailList======>', detailList);
  useEffect(() => {
    if (props.initDate) {
      setShowDate(moment(props.initDate).format('YYYY-MM-DD'));
    }
  }, [props.initDate]);
  return (
    <Row>
      <Col span={10} style={{ marginRight: 20 }}>
        日期：
        <DatePicker
          value={moment(showDate) as any}
          disabledDate={current => {
            console.log('current', current);
            return (
              current &&
              !(dateList || []).includes(
                moment(current)
                  .format('YYYY-MM-DD')
                  .toString()
              )
            );
          }}
          onChange={(v: any) => {
            console.log(v);
            setShowDate(moment(v).format('YYYY-MM-DD'));
          }}
        />
      </Col>
      <Col span={10}>
        号源:
        <ArrSelect
          style={{ width: 300 }}
          value={props.value}
          options={(detailList || []).map(item => {
            return {
              value: item.id,
              label: `${item.visitDate} ${
                weeks[moment(item.visitDate).day()]
              } (${moment(item.startTime).format('HH:mm')}-${moment(
                item.endTime
              ).format('HH:mm')})`
            };
          })}
          onChange={(v: any) => {
            if (!showDate) {
              message.info('请选择日期');
              return;
            }
            if (!v) {
              message.info('请选择号源');
            }
            props.onChange && props.onChange(v);
          }}
        />
      </Col>
    </Row>
  );
};

export default Source;
