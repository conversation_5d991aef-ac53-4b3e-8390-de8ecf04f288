import { Modal, Tag, Space, Form } from 'antd';
import { useCallback, useState, useMemo } from 'react';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { ArrSelect, TableList } from 'parsec-admin';
import apis from '../../../api';
type Invite = {
  hisId?: string;
  cpHospitalId?: string;
  cpDeptId?: string;
  doctorId?: string;
};
interface Props {
  value?: Array<Invite>;
  onChange?: (value: Array<Invite>) => void;
}

const TeamMember = ({ value, onChange }: Props) => {
  console.log('value', value);
  const [searchForm] = Form.useForm();
  const [show, setShow] = useState(false);
  // const [rowList] = useState<any[]>([]);
  const [rowList2, setRowList2] = useState<any[]>([]);
  const [doctorList, setDoctorList] = useState<any[]>([]);
  const [cpHospitalId, setCpHospitalId] = useState<string>();
  const { data: deptList } = apis.查询医联体医院全部科室列表({
    needInit: !!cpHospitalId,
    params: {
      cpHospitalId: cpHospitalId
    }
  });
  const { data: hisList } = apis.医联体医院({
    needInit: true
  });
  console.log('deptList', deptList);
  return (
    <>
      <Space wrap>
        {(value || []).map((item: any) => (
          <Tag
            closable
            onClose={() => {
              onChange &&
                onChange((value || []).filter((x: any) => x.id !== item.id));
            }}
            style={{ fontSize: '14px' }}
            key={item.id}>
            {item.name} ({item.cpHospitalName}|{item.cpDeptName}|{item.level})
          </Tag>
        ))}
        <Tag
          className='site-tag-plus'
          style={{ fontSize: '14px' }}
          onClick={() => {
            setRowList2(value ? [...value] : []);
            setShow(true);
          }}>
          <PlusOutlined /> 邀请专家
        </Tag>
      </Space>
      <Modal
        width={900}
        title={'选择医师'}
        visible={show}
        onCancel={() => {
          setRowList2([]);
          setShow(false);
        }}
        onOk={() => {
          // const cpDeptNameList: string[] = [];
          // rowList?.forEach(item => {
          //   if (!cpDeptNameList.includes(item.cpDeptName)) {
          //     cpDeptNameList.push(item.cpDeptName);
          //   }
          // });
          // const obj: any = {};
          // cpDeptNameList?.forEach(item => {
          //   obj[item] = rowList.filter(v => v.cpDeptName === item);
          // });
          // const arr: any = [];
          // Object.keys(obj).forEach(item => {
          //   if (obj[item]?.length) {
          //     obj[item].forEach((v, index) => {
          //       if (!v?.sort) {
          //         v.sort = 1;
          //       }
          //       if (index === 0) {
          //         arr.push({ ...v, showDeptName: v.cpDeptName });
          //       } else {
          //         arr.push(v);
          //       }
          //     });
          //   }
          // });

          onChange && onChange(rowList2);
          setShow(false);
          setRowList2([]);
        }}>
        <ModalWrap>
          <TableList
            showTool={false}
            tableTitle={'医师列表'}
            showHeader={false}
            rowKey={'doctorId'}
            rowSelection={{
              type: 'checkbox',
              selectedRowKeys: rowList2?.map(item => item.doctorId),
              onChange: (_, selectedRows) => {
                setRowList2(prevState => {
                  if (
                    doctorList.some(item =>
                      prevState?.find(
                        v =>
                          v.doctorId + v.cpHospitalId ===
                          item.doctorId + item.cpHospitalId
                      )
                    )
                  ) {
                    return [
                      ...selectedRows,
                      ...(prevState.filter(
                        item =>
                          !doctorList?.find(
                            v =>
                              v.doctorId + v.cpHospitalId ===
                              item.doctorId + item.cpHospitalId
                          )
                      ) || [])
                    ];
                  } else {
                    return [...prevState, ...selectedRows];
                  }
                });
              }
            }}
            pagination={false}
            getList={useCallback(async ({ params }) => {
              const p: any = { ...params };
              if (!p?.cpHospitalId) {
                return {
                  list: [],
                  total: 0
                };
              }
              const result = await apis.查询医联体医院全部医生列表.request({
                ...p
              });
              setDoctorList(result?.data || []);
              return {
                list: (result?.data || []).map(item => {
                  return {
                    ...item,
                    deptId: item.cpDeptId,
                    deptName: item.cpDeptName,
                    account: item.doctorId
                  };
                }),
                total: result?.data?.length
              };
            }, [])}
            searchFormProps={{
              form: searchForm
            }}
            form={searchForm}
            columns={useMemo(
              () => [
                {
                  searchIndex: 'cpHospitalId',
                  title: '机构',
                  search: (
                    <ArrSelect
                      onChange={value => {
                        setCpHospitalId(value as string);
                        searchForm.setFieldsValue({
                          cpHospitalId: value,
                          cpDeptId: undefined
                        });
                      }}
                      options={
                        hisList?.data?.map(item => {
                          return {
                            label: item.name,
                            value: item.id
                          };
                        }) || []
                      }
                    />
                  )
                },
                {
                  searchIndex: 'cpDeptId',
                  title: '',
                  search: (
                    <ArrSelect
                      options={
                        deptList?.data?.map(item => {
                          return {
                            label: item.name,
                            value: item.id
                          };
                        }) || []
                      }
                    />
                  )
                },
                {
                  title: '',
                  dataIndex: 'name',
                  render: (v, record: any) => {
                    return `${v} ${record?.cpDeptName}|${record?.level}|${record?.cpHospitalName}`;
                  }
                }
              ],
              [deptList?.data, hisList?.data, searchForm]
            )}
          />
          <TableList
            showTool={false}
            tableTitle={'已选择'}
            showHeader={false}
            dataSource={rowList2}
            pagination={false}
            columns={[
              {
                title: '',
                dataIndex: 'name',
                render: (v, record: any) => {
                  return (
                    <Space align={'center'}>
                      {`${v} ${record?.cpDeptName}|${record?.level}|${record?.cpHospitalName}`}{' '}
                      <DeleteOutlined
                        style={{ color: 'red' }}
                        onClick={() => {
                          setRowList2(prevState =>
                            prevState.filter(item => item.id !== record?.id)
                          );
                        }}
                      />
                    </Space>
                  );
                }
              }
            ]}
          />
        </ModalWrap>
      </Modal>
    </>
  );
};
const ModalWrap = styled.div`
  .ant-card {
    margin: 0;
    .tableList-header {
      padding: 0;
    }
  }
  .tableList-search-wrap {
    margin: 0;
    .ant-card-body {
      padding: 0;
    }
  }
  .ant-table-cell {
    padding: 0;
  }
`;

export default TeamMember;
