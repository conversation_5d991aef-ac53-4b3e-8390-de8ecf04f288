import apis from '@pages/mdt/api';
import { Button, Form, Space } from 'antd';
import { CardLayout, FormDescriptions } from 'parsec-admin';
import { useParams } from 'react-router';
import { saveAs } from 'file-saver';
import getSex from '@src/utils/getSex';

export default () => {
  const { id, mode } = useParams<{ id: string; mode: string }>();
  const [form] = Form.useForm();
  const { data: detail } = apis.查询MDT报告详情({
    params: id,
    needInit: true,
    initValue: {}
  });
  /**
   * 打印报告（完整版） & 打印报告（患者版） true & false
   */
  const { request: handlerReportFull } = apis.reportVersion({
    params: {
      id,
      isComplete: true
    },
    needInit: false
  });
  /**
   * 发布报告
   */
  // const {
  //   loading: reportLoading,
  //   request: handlerReportRelease
  // } = apis.reportRelease({
  //   params: {
  //     id
  //   },
  //   needInit: false
  // });

  // 打印报告完成版
  const handlePrintReport = async (isComplete: boolean) => {
    try {
      const res = await handlerReportFull({
        id,
        isComplete
      });
      const fileName = `报告_${isComplete ? '完整版' : '患者版'}.pdf`;
      saveAs(res, fileName);
      // 封装的下载方法
      // downloadBinaryFile(
      //   res as BlobPart,
      //   `报告_${isComplete ? '完整版' : '患者版'}.pdf`,
      //   'application/pdf'
      // );
    } catch (error) {
      // message.error('打印失败' + error);
    }
  };
  return (
    <>
      <CardLayout>
        <FormDescriptions
          title={'患者信息'}
          column={2}
          data={{ ...detail.data }}
          items={[
            {
              span: 2,
              label: '姓名',
              children: `${detail?.data?.patName} | ${getSex(
                detail?.data?.patSex
              )} | ${detail?.data?.patAgeStr}`
            },
            {
              span: 1,
              label: '患者ID',
              children: detail?.data?.patCardNo
            },
            {
              span: 1,
              label: '联系电话',
              children: detail?.data?.patPhone
            },
            {
              span: 1,
              label: 'MDT编号',
              children: detail?.data?.mdtOfflineId
            },
            {
              span: 1,
              label: '初步诊断',
              // children: detail?.data?.initialDiagnosis
              children: (
                detail?.data?.initialDiagnosis || []
              ).map((item, index) => <div key={index}>{item.hosIcdName}</div>)
            }
          ]}
        />
        <FormDescriptions
          title={'病情信息'}
          edit={false}
          column={1}
          items={[
            {
              label: '主诉',
              children: detail?.data?.chiefComplaint
            },
            {
              label: '现病史',
              children: detail?.data?.medicalHistory
            },
            {
              label: '既往史',
              children: detail?.data?.anamnesis
            },
            {
              label: '体格检查',
              children: detail?.data?.examination
            },
            {
              label: '辅助检查',
              children: detail?.data?.auxiliaryExam
            }
          ]}
        />
        <FormDescriptions
          title={'专家意见'}
          form={form}
          data={{ ...detail.data }}
          edit={false}
          column={1}
          items={[
            {
              label: '意见列表',
              children: (detail?.data?.doctorSummaryList || []).map(
                (item, index) => (
                  <div key={index}>
                    {item.deptName}({item.name}):{item.summary}
                  </div>
                )
              )
            }
          ]}
        />
        <FormDescriptions
          title={'会诊总结'}
          form={form}
          data={{ ...detail.data }}
          edit={false}
          column={1}
          items={[
            {
              label: '处理建议',
              children: detail?.data?.summary
            },
            {
              label: '会诊诊断',
              children: (
                detail?.data?.initialDiagnosis || []
              ).map((item, index) => <div key={index}>{item.hosIcdName}</div>)
            }
          ]}
        />
        {mode && Number(mode) === 2 && (
          <Space size={10}>
            <Button
              type='primary'
              // loading={repLoading}
              onClick={() => handlePrintReport(true)}>
              报告打印（完整版）
            </Button>
            <Button
              type='primary'
              // loading={repLoading}
              onClick={() => handlePrintReport(false)}>
              报告打印（患者版）
            </Button>
            {/* <Button
              type='primary'
              loading={reportLoading}
              onClick={() => handlerReportRelease()}>
              发布报告
            </Button> */}
          </Space>
        )}
      </CardLayout>
    </>
  );
};
