import apis, { ListType, findEnumDesc } from '@pages/mdt/api';
import getSex from '@src/utils/getSex';
import { Button } from 'antd';
import {
  ActionsWrap,
  ArrSelect,
  DayRangePicker,
  LinkButton,
  TableList,
  getPrice,
  actionConfirm
} from 'parsec-admin';
import { useMemo, useState } from 'react';
import { useHistory } from 'react-router';

export default () => {
  const [teamSearch, setTeamSearch] = useState<string>();
  const [roomSearch, setRoomSearch] = useState<string>();
  const [modeSearch, setModeSearch] = useState<string>();

  const {
    data: { data: teamList }
  } = apis.查询会诊团队列表({
    params: { teamName: teamSearch },
    initValue: { data: [] },
    debounceInterval: 500
  });

  const {
    data: { data: roomList }
  } = apis.查询会诊室列表({
    params: { roomName: roomSearch },
    initValue: { data: [] },
    debounceInterval: 500
  });
  const {
    data: { data: mdtEnum }
  } = apis.查询会诊相关枚举({
    initValue: { data: {} }
  });
  // const {
  //   data: { data: mdtMode }
  // } = apis.查询会诊团队列表({
  //   params: { teamName: modeSearch },
  //   initValue: { data: [] },
  //   debounceInterval: 500
  // });
  const history = useHistory();
  return (
    <TableList<ListType, any>
      exportExcelButton
      showTool={false}
      action={
        <Button
          type={'primary'}
          onClick={() => {
            history.push('/mdtManage/mdt/edit');
          }}>
          + 新建
        </Button>
      }
      getList={async ({ pagination: { current, pageSize }, params }) => {
        const res = await apis.分页查询线下MDT列表.request({
          pageNum: current,
          numPerPage: pageSize,
          ...params
        });
        return {
          list: res?.data?.recordList || [],
          total: res?.data?.totalCount || 0
        };
      }}
      pagination={{
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '40', '60', '100'],
        showTotal: total => `共 ${total} 条数据`
      }}
      scroll={{ x: 1500 }}
      columns={useMemo(() => {
        return [
          {
            title: '序号',
            width: '100',
            render: (_, record, index) => {
              return index + 1;
            }
          },
          {
            title: '申请时间',
            width: '160',
            dataIndex: 'createTime',
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                valueFormat={'YYYY-MM-DD HH:mm:ss'}
              />
            ),
            searchIndex: ['searchStartTime', 'searchEndTime']
          },
          {
            title: '申请人',
            dataIndex: 'applySource',
            render: applySource => {
              return findEnumDesc(
                mdtEnum.MdtOfflineApplySourceEnum,
                applySource
              );
            }
          },
          {
            title: '会诊团队',
            dataIndex: 'teamName',
            searchIndex: 'teamId',
            search: (
              <ArrSelect
                onSearch={setTeamSearch}
                searchValue={teamSearch}
                options={
                  teamList?.map(item => ({
                    label: item.teamName,
                    value: item.id
                  })) || []
                }
              />
            )
          },
          {
            title: '会诊方式',
            dataIndex: 'mode',
            render: value => {
              const modeMap = {
                1: '线下',
                2: '线上'
              };
              return modeMap[value] || '未知';
            },
            searchIndex: 'mode',
            search: (
              <ArrSelect
                onSearch={setModeSearch}
                searchValue={modeSearch}
                options={[
                  { label: '全部', value: 0 },
                  { label: '线下', value: 1 },
                  { label: '线上', value: 2 }
                ]}
              />
            )
          },
          {
            title: '患者信息',
            dataIndex: 'patInfo',
            searchIndex: 'patName',
            search: true,
            render: (_, record: ListType) => {
              return `${record.patName} ${getSex(record.patSex)} ${
                record.patAgeStr
              }`;
            }
          },
          {
            title: '会诊室',
            dataIndex: 'roomName',
            searchIndex: 'roomId',
            search: (
              <ArrSelect
                onSearch={setRoomSearch}
                searchValue={roomSearch}
                options={
                  roomList?.map(item => ({
                    ...item,
                    label: item.roomName,
                    value: item.id
                  })) || []
                }
              />
            )
          },
          {
            title: '会诊时间',
            dataIndex: 'mdtStartTime',
            width: '160'
          },
          {
            title: '金额（元)',
            dataIndex: 'mdtFee',
            render: mdtFee => getPrice(mdtFee)
          },
          {
            title: '订单编号',
            dataIndex: 'orderSerialNumber'
          },
          {
            title: '支付状态',
            dataIndex: 'payState',
            search: (
              <ArrSelect
                onSearch={setRoomSearch}
                searchValue={roomSearch}
                options={
                  mdtEnum?.MdtOfflinePayStateEnum?.map(item => ({
                    ...item,
                    label: item.desc,
                    value: item.name
                  })) || []
                }
              />
            ),
            render: payState => {
              const key = findEnumDesc(
                mdtEnum.MdtOfflinePayStateEnum,
                payState
              );
              if (key === '退款失败') {
                return <span style={{ color: '#ed4e4e' }}>{key}</span>;
              }
              return key;
            }
          },
          {
            title: '会诊状态',
            dataIndex: 'mdtState',
            render: mdtState => {
              const key = findEnumDesc(mdtEnum.MdtOfflineStateEnum, mdtState);
              if (['申请失败', '申请异常'].includes(key)) {
                return <span style={{ color: '#ed4e4e' }}>{key}</span>;
              }
              return key;
            },
            search: (
              <ArrSelect
                options={[
                  { name: '', desc: '全部' },
                  ...(mdtEnum?.MdtOfflineStateEnum || [])
                ].map(item => {
                  return {
                    label: item.desc,
                    value: item.name
                  };
                })}
              />
            )
          },
          {
            title: '操作',
            fixed: 'right',
            width: 110,
            render: (_, record: ListType) => (
              <ActionsWrap>
                {record?.mdtState === 'FINISH' && (
                  <LinkButton
                    onClick={() => {
                      history.push(
                        `/mdtManage/mdt/report/${record.id}/${record.mode}`
                      );
                    }}>
                    查看报告
                  </LinkButton>
                )}
                {['FINISH'].includes(record?.mdtState) && (
                  <LinkButton
                    onClick={() => {
                      history.push('/mdtManage/mdt/upload/' + record.id);
                    }}>
                    上传报告
                  </LinkButton>
                )}
                {['WAIT_REVIEW'].includes(record?.mdtState) && (
                  <LinkButton
                    onClick={() => {
                      history.push('/mdtManage/mdt/mdtDetail/' + record.id);
                    }}>
                    审核
                  </LinkButton>
                )}
                <LinkButton
                  onClick={() => {
                    history.push('/mdtManage/mdt/mdtDetail/' + record.id);
                  }}>
                  查看
                </LinkButton>
                {[
                  'FAIL_NOTIFY_HIS',
                  'EXCEPTION_NOTIFY_HIS',
                  'REJECT_REVIEW'
                ].includes(record?.mdtState) &&
                  !['REFUND'].includes(record?.payState) && (
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () => apis.线下会诊只退款.request(record.id),
                          '退款'
                        );
                      }}>
                      退款
                    </LinkButton>
                  )}
                {['REVIEWED'].includes(record?.mdtState) && (
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () => apis.取消线下MDT申请.request(String(record.id)),
                        '',
                        {
                          template: '确认退款，同步取消会诊？'
                        }
                      );
                    }}>
                    退款
                  </LinkButton>
                )}
              </ActionsWrap>
            )
          }
        ];
      }, [
        teamList,
        roomList,
        teamSearch,
        roomSearch,
        history,
        mdtEnum,
        modeSearch
      ])}
    />
  );
};
