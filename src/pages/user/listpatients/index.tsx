import React, { useMemo, useRef } from 'react';
import {
  DayRangePicker,
  ArrSelect,
  ActionsWrap,
  LinkButton
} from 'parsec-admin';
import useApi from './api';
import { Button } from 'antd';
import MyTableList from '@components/myTableList';
import usePatientsDetailModal from '@pages/user/listpatients/usePatientsDetailModal';

export default () => {
  const listParam = useRef({});
  const showModal = usePatientsDetailModal();
  return (
    <MyTableList
      action={
        <Button
          onClick={() => {
            useApi.就诊人列表导出(listParam.current);
          }}
          type='primary'>
          导出
        </Button>
      }
      tableTitle='就诊人管理'
      getList={({ params }) => {
        listParam.current = params;
        return useApi.就诊人列表.request(params);
      }}
      columns={useMemo(
        () => [
          {
            title: '用户姓名',
            fixed: 'left',
            dataIndex: 'name',
            width: 120
          },
          {
            title: '就诊人姓名',
            dataIndex: 'patientName',
            width: 120
          },
          {
            title: '性别',
            dataIndex: 'sex',
            width: 120
          },
          {
            title: '年龄',
            dataIndex: 'patientAge',
            width: 120
          },
          {
            title: '患者ID',
            dataIndex: 'patCardNo',
            width: 120
          },
          {
            title: '身份证号码',
            dataIndex: 'idNo',
            width: 180
          },
          {
            title: '手机号',
            dataIndex: 'mobile',
            width: 120
          },
          // {
          //   title: '地域',
          //   dataIndex: 'address',
          //   width: 120
          // },
          {
            title: '绑定时间',
            dataIndex: 'createTime',
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                disabledDate={current => {
                  return current && current.valueOf() > Date.now();
                }}
              />
            ),
            searchIndex: ['startDate', 'endDate'],
            width: 120
          },
          {
            title: '性别',
            dataIndex: 'sex',
            search: <ArrSelect options={{ M: '男', F: '女' }} />,
            render: false,
            width: 120
          },
          {
            title: '就诊人姓名',
            dataIndex: 'name',
            search: true,
            render: false,
            width: 120
          },
          {
            title: '操作',
            dataIndex: 'id',
            width: 120,
            render: r => (
              <ActionsWrap>
                <LinkButton
                  onClick={() => {
                    showModal({ id: r });
                  }}>
                  详情
                </LinkButton>
              </ActionsWrap>
            )
          }
        ],
        [showModal]
      )}
    />
  );
};
