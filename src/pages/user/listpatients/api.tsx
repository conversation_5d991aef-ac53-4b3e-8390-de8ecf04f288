import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import qs from 'qs';
import { ListApiRequestParams, ListApiResponseData } from '@src/configs/apis';
import { formatParamsWithMoment } from '@src/utils/common';
import env from '@src/configs/env';

export default {
  就诊人列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        startDate?: string;
        endDate?: string;
      }
    ) =>
      request.post<
        ListApiResponseData<{
          id: number;
          name: string;
          hisId: string;
          hisName: string;
          userId: number;
          channelType: string;
          patientType: number;
          relationType: number;
          idType: number;
          idNo: string;
          sex: string;
          birthday: string;
          mobile: string;
          address: string;
          bindStatus: number;
          parentName: string;
          parentIdType: number;
          parentIdNo: string;
          patCardType: number;
          patCardNo: string;
          consumeType: string;
          isDefalut: number;
          isSelf: string;
          syncStatus: string;
          type: number;
          idImage: string;
          patInNo: string;
          bindMedicareCard: number;
          height: string;
          weight: string;
          married: string;
          smoking: string;
          patHisId: string;
          patHisNo: string;
          createTime: string;
          updateTime: string;
          accountId: string;
          patientId: string;
          patientName: string;
          patientMobile: string;
          patientSex: string;
          isDefault: string;
          patientImg: string;
          patientAge: string;
          platformId: string;
          patientAddress: string;
          openId: string;
          realName: string;
          birth: string;
          platformSource: string;
          age: string;
          inquiryTime: string;
          inquiryDept: string;
          inquiryDoctor: string;
          inquiryPurpose: string;
          userName: string;
        }>
      >('/mch/user/patient/page', data)
  ),
  就诊人列表导出: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${env.apiHost}/mch/user/patient/export?${qs.stringify(
      formatParamsWithMoment(p)
    )}`;
    window.open(url);
  },
  查询就诊人详情: createApiHooks(id =>
    request.get<{
      data: {
        id: number; // 主键ID
        name: string; // 就诊人姓名
        hisId: number; // 医院id
        hisName: string; // 医院名称
        userId: number; // 所属用户id
        idType: number; // 证件类型（1-身份证 2-港澳居民身份证 3-台湾居民身份证 4-护照）
        idNo: string; // 就诊人证件号
        mobile: string; // 电话号码
        birthday: string; // 生日
        age: string; // 年龄
        address: string; // 住址
        relationType: number; // 与本人关系id
        parentName: string; // 监护人姓名
        parentIdType: number; // 监护人证件类型（1-身份证 2-港澳居民身份证 3-台湾居民身份证 4-护照）
        parentIdNo: string; // 监护人证件号
        bindCardList: // 绑定列表
        {
          id: number; // 主键ID
          name: string; // 就诊人姓名
          mobile: string; // 电话号码
          isDefault: number; // 标志
          bindTime: string; // 绑定时间
        }[];
      };
    }>(`/mch/user/patient/detail/${id}`)
  )
};
