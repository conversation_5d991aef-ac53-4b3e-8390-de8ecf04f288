import React, { useState } from 'react';
import { Form, FormDescriptions, useModal, DetailLayout } from 'parsec-admin';
import useApi from './api';
import dayjs from 'dayjs';
import { desenStr } from 'parsec-hooks';
import { Space } from '@kqinfo/ui';
import { Table } from 'antd';

export default () => {
  const [form] = Form.useForm();
  const [idNo, setIdNo] = useState(false);
  const [mobile, setMobile] = useState(false);
  const [parentIdNo, setParentIdNo] = useState(false);
  // 获取商品详情
  const { data, request, loading } = useApi.查询就诊人详情({
    initValue: {},
    needInit: false
  });
  console.log(loading);
  const switchIdType = type => {
    switch (type) {
      case 1:
        return '身份证';
      case 2:
        return '港澳居民身份证';
      case 3:
        return '台湾居民身份证';
      case 4:
        return '护照';
      default:
        return '-';
    }
  };
  // const switchRelationType = type => {
  //   switch (type) {
  //     case 1:
  //       return '本人';
  //     case 2:
  //       return '配偶';
  //     case 3:
  //       return '父母';
  //     case 4:
  //       return '子女';
  //     case 5:
  //       return '他人';
  //     default:
  //       return '-';
  //   }
  // };
  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '电话号码',
      dataIndex: 'mobile',
      key: 'mobile'
    },
    {
      title: '类别',
      dataIndex: 'isDefault',
      key: 'isDefault',
      render: r => (r === 1 ? '默认就诊人' : '非默认就诊人')
    },
    {
      title: '绑定时间',
      dataIndex: 'bindTime',
      key: 'bindTime'
    }
  ];

  return useModal(
    ({ switchVisible, ...val }) => {
      console.log(data, 'data2');
      if (val.id && val.id !== data?.data?.id) request(val.id);
      return {
        title: '就诊人详情',
        width: 1000,
        destroyOnClose: true,
        footer: null,
        bodyStyle: { padding: 0 },
        children: (
          <DetailLayout
            style={{ paddingTop: 0, margin: 0 }}
            cardsProps={[
              {
                children: (
                  <>
                    <FormDescriptions
                      title={'就诊人信息'}
                      data={data?.data}
                      form={form}
                      items={[
                        {
                          label: '证件类型',
                          name: 'idType',
                          render: v => switchIdType(v)
                        },
                        {
                          label: '证件号码',
                          name: 'idNo',
                          style: { width: '330px' },
                          render: r => {
                            if (!r) return '-';
                            else
                              return (
                                <Space size={10}>
                                  {idNo
                                    ? r
                                    : desenStr({ str: r as any, type: 'ID' })}
                                  <div
                                    onClick={() => setIdNo(!idNo)}
                                    style={{
                                      cursor: 'pointer',
                                      textDecoration: 'underline',
                                      color: '#027DB4'
                                    }}>
                                    {idNo ? '隐藏信息' : '展示完整信息'}
                                  </div>
                                </Space>
                              );
                          }
                        },
                        {
                          label: '手机号码',
                          name: 'mobile',
                          render: r => {
                            if (!r) return '-';
                            else
                              return (
                                <Space size={10}>
                                  {mobile
                                    ? r
                                    : desenStr({
                                        str: r as any,
                                        type: 'phone'
                                      })}
                                  <div
                                    onClick={() => setMobile(!mobile)}
                                    style={{
                                      cursor: 'pointer',
                                      textDecoration: 'underline',
                                      color: '#027DB4'
                                    }}>
                                    {mobile ? '隐藏信息' : '展示完整信息'}
                                  </div>
                                </Space>
                              );
                          }
                        },
                        {
                          label: '年龄',
                          name: 'age'
                        },
                        {
                          label: '出生日期',
                          name: 'birthday',
                          render: r => dayjs(r as any).format('YYYY年MM月DD日')
                        },
                        {
                          label: '住址',
                          name: 'address'
                        }
                      ]}
                    />
                    {data?.data?.parentIdNo && (
                      <FormDescriptions
                        title={'监护人信息'}
                        data={data?.data}
                        form={form}
                        items={[
                          // {
                          //   label: '关系',
                          //   name: 'relationType',
                          //   render: (v: any) => switchRelationType(v)
                          // },
                          {
                            label: '姓名',
                            name: 'parentName'
                          },
                          {
                            label: '证件类型',
                            name: 'parentIdType',
                            render: v => switchIdType(v)
                          },
                          {
                            label: '证件号码',
                            name: 'parentIdNo',
                            render: r => {
                              if (!r) return '-';
                              else
                                return (
                                  <Space size={10}>
                                    {parentIdNo
                                      ? r
                                      : desenStr({
                                          str: r as any,
                                          type: 'ID'
                                        })}
                                    <div
                                      onClick={() => setParentIdNo(!parentIdNo)}
                                      style={{
                                        cursor: 'pointer',
                                        textDecoration: 'underline',
                                        color: '#027DB4'
                                      }}>
                                      {parentIdNo ? '隐藏信息' : '展示完整信息'}
                                    </div>
                                  </Space>
                                );
                            }
                          }
                        ]}
                      />
                    )}
                    <FormDescriptions title={'绑定记录'} items={[]} />
                    <Table
                      style={{ marginBottom: '20px' }}
                      pagination={false}
                      bordered={true}
                      columns={columns}
                      dataSource={data?.data?.bindCardList}
                    />
                  </>
                )
              }
            ]}
          />
        )
      };
    },
    [data, parentIdNo, mobile, idNo]
  );
};
