import React, { useMemo, useRef } from 'react';
import {
  DayRangePicker,
  ArrSelect,
  handleSubmit,
  ActionsWrap
} from 'parsec-admin';
import useApi, { platformSourceObj } from './api';
import MyTableList from '@components/myTableList';
import { Button, Avatar } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import env from '@src/configs/env';

export default () => {
  const hisId = env.hisId;
  const { loading: loadingImport, request: requestImport } = useApi.批量导入({
    needInit: false
  });
  const listParam = useRef({});

  return (
    <MyTableList
      tableTitle='用户管理'
      action={
        <ActionsWrap>
          {hisId && hisId === '41122' && (
            <Button
              icon={<UploadOutlined />}
              loading={loadingImport}
              onClick={() => {
                const el = document.createElement('input');
                el.setAttribute('type', 'file');
                el.addEventListener('change', () => {
                  const file = el.files?.[0];
                  if (file) {
                    handleSubmit(() => requestImport({ file }), '导入');
                  }
                });
                el.click();
                el.remove();
              }}>
              导入用户
            </Button>
          )}
          <Button
            onClick={() => {
              useApi.用户列表导出(listParam.current);
            }}
            type='primary'>
            导出
          </Button>
        </ActionsWrap>
      }
      getList={({ params }) => {
        listParam.current = params;
        return useApi.用户列表.request(params);
      }}
      columns={useMemo(
        () => [
          {
            title: '头像',
            dataIndex: 'headImage',
            render: x => {
              return <Avatar src={x} />;
            },
            fixed: 'left',
            width: 80
          },
          {
            title: '姓名',
            dataIndex: 'realName',
            render: false,
            search: true,
            width: 120
          },
          {
            title: '昵称',
            dataIndex: 'nickName',
            width: 120
          },
          {
            title: '姓名',
            dataIndex: 'realName',
            width: 120
          },
          {
            title: '绑卡数',
            dataIndex: 'bindCardNum',
            width: 120
          },
          {
            title: '注册时间',
            dataIndex: 'createTime',
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                valueFormat={'YYYY-MM-DD HH:mm:ss'}
                disabledDate={current => {
                  return current && current.valueOf() > Date.now();
                }}
              />
            ),
            searchIndex: ['startDate', 'endDate'],
            width: 120
          },
          {
            title: '医院名称',
            dataIndex: 'hisName',
            width: 120
          },
          {
            title: '用户昵称',
            dataIndex: 'nickName',
            search: true,
            render: false,
            width: 120
          },
          {
            title: '渠道',
            dataIndex: 'platformSource',
            search: <ArrSelect options={platformSourceObj} />,
            render: false,
            width: 120
          },
          {
            title: '开启随访',
            dataIndex: 'authFollow ',
            searchIndex: 'authFollow ',
            render: false,
            search: sessionStorage.getItem('isFlowUp') === '"1"' && (
              <ArrSelect
                options={{
                  0: '未开启',
                  1: '已开启'
                }}
              />
            ),
            width: 120
          }
        ],
        []
      )}
    />
  );
};
