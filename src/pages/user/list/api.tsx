import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import qs from 'qs';
import { ListApiRequestParams, ListApiResponseData } from '@src/configs/apis';
import { formatParamsWithMoment } from '@src/utils/common';
import env from '@src/configs/env';

export const platformSourceObj = {
  1: '微信',
  2: '支付宝',
  3: '微信小程序',
  21: '随访'
};

export default {
  用户列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        startDate?: string;
        endDate?: string;
      }
    ) =>
      request.post<
        ListApiResponseData<{
          id: number;
          hisId: string;
          accountName: string;
          nickName: string;
          phone: string;
          idNo: string;
          sex: string;
          email: string;
          unionId: string;
          isRealName: string;
          realName: string;
          idType: string;
          bindCardNum: number;
          password: string;
          status: string;
          extFields: string;
          createTime: string;
          updateTime: string;
          platformId: string;
          channelId: string;
          kqOpenId: string;
          platformSource: string;
          headImage: string;
          city: string;
          country: string;
          province: string;
          mobile: string;
          openId: string;
          idNumber: string;
          attentionStatus: string;
          age: string;
          modifyTime: string;
          sessionKey: string;
          expiresIn: string;
          accountId: string;
          doctorUserId: string;
          channelIdType: string;
          userType: string;
          startTime: string;
          endTime: string;
          name: string;
          gender: string;
        }>
      >('/mch/user/account/page', data)
  ),
  用户列表导出: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${env.apiHost}/mch/user/account/export?${qs.stringify(
      formatParamsWithMoment(p)
    )}`;
    window.open(url);
  },
  批量导入: createApiHooks((params: { file: File }) => {
    return request.post('/mch/user/account/import', params);
  })
};
