import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ListApiRequestParams, ListApiResponseData } from '@apiHooks';

interface ListItem {
  id: string;
  hisId: number;
  hisName: string;
  districtCode: string;
  districtName: string;
  districtAddress: string;
  districtLng: string;
  districtLat: string;
  signInRange: number;
  bluetoothStatus: number;
  positionStatus: number;
}

export default {
  签到配置列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        districtCode?: string;
      }
    ) =>
      request.get<ListApiResponseData<ListItem>>(
        '/intelligent/mch/intelligent/sign-config',
        {
          params: data
        }
      )
  ),
  新增签到配置: createApiHooks(
    (params: {
      hisId: string;
      hisName?: string;
      districtCode: string;
      districtName: string;
      districtAddress: string;
      districtLng: string;
      districtLat: string;
      signInRange: string;
      bluetoothStatus?: string;
      positionStatus?: string;
    }) =>
      request.post<{
        code: number;
        msg: string;
        hisCostTime: number;
        success: boolean;
        data: ListItem;
      }>('/intelligent/mch/intelligent/sign-config', params)
  ),
  修改签到配置: createApiHooks(
    (params: {
      id: string;
      hisId: string;
      hisName?: string;
      districtCode: string;
      districtName: string;
      districtAddress: string;
      districtLng: string;
      districtLat: string;
      signInRange: string;
      bluetoothStatus?: string;
      positionStatus?: string;
    }) =>
      request.put<{
        code: number;
        msg: string;
        hisCostTime: number;
        success: boolean;
        data: ListItem;
      }>('/intelligent/mch/intelligent/sign-config', params, {
        headers: { 'Content-Type': 'formData' }
      })
  ),
  删除签到配置: createApiHooks((params: { id: string }) =>
    request.delete<{
      code: number;
      msg: string;
      data: any;
    }>(`/intelligent/mch/intelligent/sign-config/${params.id}`)
  ),
  签到配置详情: createApiHooks((params: { id: string }) =>
    request.get<{
      code: number;
      msg: string;
      hisCostTime: number;
      success: boolean;
      data: ListItem;
    }>(`/intelligent/mch/intelligent/sign-config/${params.id}`)
  ),
  修改签到方式: createApiHooks(
    (params: {
      id: string;
      bluetoothStatus?: string;
      positionStatus?: string;
    }) =>
      request.put<{
        code: number;
        msg: string;
        hisCostTime: number;
        success: boolean;
        data: ListItem;
      }>(`/intelligent/mch/intelligent/sign-config/type`, params, {
        headers: { 'Content-Type': 'formData' }
      })
  )
};
