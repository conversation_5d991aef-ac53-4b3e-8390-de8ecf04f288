import React, { useMemo } from 'react';
import {
  ActionsWrap,
  LinkButton,
  actionConfirm,
  useModal,
  handleSubmit,
  ArrSelect
} from 'parsec-admin';
import { Button, Switch } from 'antd';
import useApi from '../api';
import useApi2 from '../../organization/list/api';
import env from '@configs/env';
import MyTableList from '@components/myTableList';

export default () => {
  const hisId = env.hisId;
  const { data: list } = useApi2.医疗机构列表({
    params: {
      pageNum: 1,
      numPerPage: 90,
      hisId: hisId
    },
    initValue: {}
  });

  const configureModalVisible = useModal(
    values => ({
      title: '新增/编辑配置',
      onSubmit: ({ ...params }: any) => {
        if (!params.hisId) {
          return Promise.reject();
        }
        params.hisName = list?.data?.recordList?.find(
          item => item.hisId === params.hisId + ''
        )?.hospitalName;
        console.log(params, 'params');
        return handleSubmit(
          () =>
            (params.id ? useApi.修改签到配置 : useApi.新增签到配置).request(
              params
            ),
          `${params.id ? '修改' : '新增'}`
        );
      },
      items: [
        {
          name: 'id',
          render: false
        },
        {
          label: '所属机构',
          name: 'hisId',
          required: true,
          render: v => (
            <ArrSelect
              options={
                list.data?.recordList
                  ? list.data.recordList?.map(item => {
                      return {
                        children: item.hospitalName,
                        value: item.hisId
                      };
                    })
                  : []
              }
            />
          )
        },
        {
          label: '院区名称',
          name: 'districtName',
          required: true
        },
        {
          label: '院区编号',
          name: 'districtCode',
          required: true
        },
        {
          label: '院区地址',
          name: 'districtAddress',
          required: true
        },
        {
          label: '院区经度',
          name: 'districtLng',
          required: true
        },
        {
          label: '院区维度',
          name: 'districtLat',
          required: true
        },
        {
          label: '院区签到范围',
          name: 'signInRange',
          required: true
        }
      ]
    }),
    [list]
  );

  return (
    <MyTableList
      action={
        <Button type='primary' onClick={() => configureModalVisible()}>
          新增
        </Button>
      }
      killHisId
      tableTitle='院区列表'
      rowKey='id'
      getList={({ params }) => {
        return useApi.签到配置列表.request({ ...params, hisId: hisId });
      }}
      showTool={false}
      columns={useMemo(
        () => [
          {
            title: '机构id',
            dataIndex: 'hisId',
            width: 100
          },
          {
            title: '机构名称',
            dataIndex: 'hisName',
            width: 200,
            align: 'center'
          },
          {
            title: '院区名称',
            searchIndex: 'districtName',
            width: 200
          },
          {
            title: '院区地址',
            dataIndex: 'districtAddress',
            width: 120,
            align: 'center'
          },
          {
            title: '定位签到状态',
            dataIndex: 'positionStatus',
            width: 150,
            align: 'center',
            render: (v, record: any) => (
              <Switch
                checkedChildren='ON'
                unCheckedChildren='OFF'
                checked={!!record.positionStatus}
                onClick={() => {
                  actionConfirm(
                    () =>
                      useApi.修改签到方式.request({
                        id: record.id,
                        positionStatus: record.positionStatus ? '0' : '1',
                        bluetoothStatus: record.bluetoothStatus
                          ? record.positionStatus
                            ? '1'
                            : '0'
                          : '0'
                      }),
                    record.positionStatus ? '关闭' : '开启'
                  );
                }}
              />
            )
          },
          {
            title: '蓝牙签到状态',
            dataIndex: 'bluetoothStatus',
            width: 120,
            align: 'center',
            render: (v, record: any) => (
              <Switch
                checkedChildren='ON'
                unCheckedChildren='OFF'
                checked={!!record.bluetoothStatus}
                onClick={() => {
                  actionConfirm(
                    () =>
                      useApi.修改签到方式.request({
                        id: record.id,
                        bluetoothStatus: record.bluetoothStatus ? '0' : '1',
                        positionStatus: record.positionStatus
                          ? record.bluetoothStatus
                            ? '1'
                            : '0'
                          : '0'
                      }),
                    record.bluetoothStatus ? '关闭' : '开启'
                  );
                }}
              />
            )
          },
          {
            title: '操作',
            fixed: 'right',
            width: 150,
            align: 'center',
            render: record => (
              <ActionsWrap max={99}>
                <LinkButton
                  onClick={() => {
                    configureModalVisible({
                      ...record
                    });
                  }}>
                  编辑
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.删除签到配置.request({
                          id: record.id
                        }),
                      '删除'
                    );
                  }}>
                  删除
                </LinkButton>
              </ActionsWrap>
            )
          }
        ],
        [configureModalVisible]
      )}
    />
  );
};
