import Charts from '@components/charts';
import echarts from 'echarts';
import { useCallback, useMemo, useState } from 'react';
import styled from 'styled-components';
import StyledComponent from './CommChart.style';
import SelectDate from './SelectDate';
import useApi from './api';
import { ChartItem } from './d';

interface CommObjChartProps {
  id?: number;
  chartId?: number;
}
export default (props: CommObjChartProps) => {
  // const { id } = props;
  const [params, setParams] = useState<any>();

  const { data, loading } = useApi.公共统计接口({
    initValue: [
      {
        title: '发出消息',
        point: [
          {
            x: '2023-08-16',
            y: 1
          },
          {
            x: '2023-08-17',
            y: 1
          },
          {
            x: '2023-08-18',
            y: 0
          },
          {
            x: '2023-08-19',
            y: 0
          },
          {
            x: '2023-08-20',
            y: 0
          },
          {
            x: '2023-08-21',
            y: 0
          },
          {
            x: '2023-08-22',
            y: 1
          },
          {
            x: '2023-08-23',
            y: 1
          }
        ]
      },
      {
        title: '发送成功',
        point: [
          {
            x: '2023-08-16',
            y: 0
          },
          {
            x: '2023-08-17',
            y: 0
          },
          {
            x: '2023-08-18',
            y: 0
          },
          {
            x: '2023-08-19',
            y: 0
          },
          {
            x: '2023-08-20',
            y: 0
          },
          {
            x: '2023-08-21',
            y: 0
          },
          {
            x: '2023-08-22',
            y: 0
          },
          {
            x: '2023-08-23',
            y: 0
          }
        ]
      },
      {
        title: '问诊目的占比',
        point: [
          {
            x: '专病知识',
            y: 4
          },
          {
            x: '用药指导',
            y: 4
          },
          {
            x: '生活方式',
            y: 4
          },
          {
            x: '疾病预防',
            y: 4
          },
          {
            x: '诊疗流程',
            y: 4
          },
          {
            x: '饮食习惯',
            y: 4
          }
        ]
      }
    ],
    params: {
      configId: 48,
      params
    },
    // needInit: !!id && !!params
    needInit: false
  });
  const barOptions: echarts.EChartOption = useMemo(() => {
    const items = (data[0] ? data : []) as ChartItem[]; //FIXME 数据没对齐，先用列表第一个
    return {
      color: ['#9AE5CD', '#558AD1'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
        }
      },
      legend: {
        top: 0,
        right: '3%',
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12
      },
      grid: {
        top: 30,
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: items[0]?.point.map(point => point.x) ?? [],
          axisTick: {
            alignWithLabel: true
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: function(value: number) {
              return value;
            }
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: items?.slice(0, 2).map(item => {
        return {
          name: item.title,
          type: 'bar',
          barWidth: '18%',
          data: item.point?.map(point => point.y)
        };
      })
    };
  }, [data]);

  function JudeTypeIsChartItem(item: any): item is ChartItem {
    return Boolean(item?.point);
  }

  const hasListData = useCallback(
    (index: number) => {
      const item = data?.[index];
      return JudeTypeIsChartItem(item) && item?.point.length > 0;
    },
    [data]
  );

  const pieOptions: echarts.EChartOption = useMemo(() => {
    return {
      color: hasListData(2)
        ? ['#2773D1', '#674CD9', '#5BADD0', '#7DA6E7', '#7CCFD0', '#8772E1']
        : ['#ccc'],
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12,
        bottom: 10,
        width: 180,
        data: JudeTypeIsChartItem(data?.[2])
          ? data?.[2]?.point.map(item => item.x)
          : ['暂无数据']
      },
      series: [
        {
          name: '问诊目的',
          type: 'pie',
          radius: ['50%', '65%'],
          top: 0,
          bottom: 55,
          label: {
            show: true,
            formatter: '{b}: \n{d}%'
          },
          avoidLabelOverlap: false,
          data:
            JudeTypeIsChartItem(data?.[2]) && data?.[2]?.point.length > 0
              ? data?.[2]?.point.map(item => ({
                  value: Number((item.y / 100).toFixed(2)),
                  name: item.x
                }))
              : [{ value: 0, name: '暂无数据' }],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
  }, [data, hasListData]);

  return (
    <Wrap>
      <div>
        <StyledComponent hideBorder>
          <div className='head'>
            <div className='title'>消息提醒</div>
            <div className='date'>
              <SelectDate
                onChange={useCallback(v => {
                  console.log('TODO', v);
                  setParams({
                    beginTime: v[0].format('YYYY-MM-DD 00:00:00'),
                    endTime: v[1].format('YYYY-MM-DD 23:59:59')
                  });
                }, [])}
              />
            </div>
          </div>
          <div className='container'>
            <Charts
              loading={loading}
              option={barOptions}
              style={{ width: '100%', height: '100%' }}
            />
          </div>
        </StyledComponent>
      </div>
      <div>
        <StyledComponent hideBorder>
          <div className='head'>
            <div className='title'>健康宣教</div>
          </div>
          <div className='container'>
            <Charts
              loading={loading}
              option={pieOptions}
              style={{ width: '100%', height: '100%' }}
            />
          </div>
        </StyledComponent>
      </div>
    </Wrap>
  );
};

const Wrap = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background-color: #fff;
  justify-content: space-between;
  border: 1px solid #e2e2e2;
  > div:nth-child(1) {
    width: 60%;
    height: 100%;
  }
  > div:nth-child(2) {
    width: 38%;
    height: 100%;
  }
`;
