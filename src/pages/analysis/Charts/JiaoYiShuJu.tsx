import React from 'react';
import CommObjChart from './CommObjChart';
import { ChartComponent } from './index';
const Comp: ChartComponent = props => {
  return (
    <CommObjChart
      {...props}
      icon={require('./images/jysj.png')}
      title='交易数据'
      items={[
        {
          filedName: 'transactionMoney',
          title: '总交易额',
          unit: '元',
          handleFileNameValue: val => (val / 100).toFixed(2)
        },
        { filedName: 'transactionAmount', title: '交易笔数', unit: '笔' },
        {
          filedName: 'refoundMoney',
          title: '退款金额',
          unit: '元',
          handleFileNameValue: val => (val / 100).toFixed(2)
        }
      ]}
    />
  );
};
export default Comp;
