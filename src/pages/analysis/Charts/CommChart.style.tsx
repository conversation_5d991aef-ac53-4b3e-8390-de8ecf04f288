import styled from 'styled-components';
export default styled.div<{ hideBorder?: boolean }>`
  height: 100%;
  width: 100%;
  background: #fff;
  border: ${props => (props.hideBorder ? undefined : '1px solid #e2e2e2')};
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  > .head {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
    flex-wrap: wrap;
    padding: 0 10px 10px 10px;
    > .title {
      padding-right: 10px;
      margin: 10px 0 0 0;
    }
    > .date {
    }
  }
  > .container {
    flex: 1;
    width: 100%;
    overflow: hidden;
  }
`;
