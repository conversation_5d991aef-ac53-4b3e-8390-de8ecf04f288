import React from 'react';
import CommObjChart from './CommObjChart';
import { ChartComponent } from './index';
const Comp: ChartComponent = props => {
  return (
    <CommObjChart
      {...props}
      icon={require('./images/cfsj.png')}
      title='处方数据'
      items={[
        { filedName: 'prescription', title: '处方数量', unit: '张' },
        { filedName: 'prescriptionPaid', title: '已缴费量', unit: '张' },
        { filedName: 'prescriptionFailPass', title: '不合格量', unit: '张' }
      ]}
    />
  );
};
export default Comp;
