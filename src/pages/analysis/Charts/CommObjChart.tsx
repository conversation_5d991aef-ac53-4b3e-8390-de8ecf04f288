import React, { Fragment, useMemo, useState } from 'react';
import StyledComponent, { GapCol, GapRow } from './CommObjChart.style';
import { ObjChatItem } from './d';

interface CommObjChartProps {
  id?: number;
  chartId?: number;
  icon?: string;
  title: React.ReactNode;
  items: Array<{
    filedName: keyof ObjChatItem;
    title: React.ReactNode;
    unit: React.ReactNode;
    handleFileNameValue?: (val: any) => any;
  }>;
}
export default (props: CommObjChartProps) => {
  const { icon, title, items } = props;
  const [data] = useState([]);
  // const { data } = useApi.公共统计接口({
  //   initValue: [],
  //   params: {
  //     configId: id as any
  //   },
  //   needInit: !!id
  // });
  const { main, newItems } = useMemo(() => {
    const vItems = items.map(x => {
      return {
        ...x,
        value: (data as ObjChatItem[]).find(y =>
          Object.keys(y).includes(x.filedName)
        )?.[x.filedName]
      };
    });
    if (vItems.length > 0) {
      return {
        main: vItems[0],
        newItems: vItems.slice(1, items.length)
      };
    } else {
      return {
        newItems: vItems
      };
    }
  }, [data, items]);
  const mainHandleFileNameValue =
    main?.handleFileNameValue ?? ((val: any) => val);
  return (
    <StyledComponent>
      <div className='title'>
        {icon && <img src={icon} alt='icon' className='icon' />}
        <span className='text'>{title}</span>
      </div>
      <div className='main'>
        <div className='title'>{main?.title || '-'}</div>
        <div className='value'>
          {mainHandleFileNameValue(main?.value) ?? '-'}
          {main?.unit}
        </div>
      </div>
      <GapRow />
      <div className='items'>
        {newItems.map((item, index) => {
          const { handleFileNameValue = val => val } = item;
          return (
            <Fragment key={index}>
              {index !== 0 && <GapCol key={'gap' + index} />}
              <div className='item' key={index}>
                <div className='title'>{item.title || '-'}</div>
                <div className='value'>
                  {handleFileNameValue(item.value) ?? '-'}
                  {item.unit}
                </div>
              </div>
            </Fragment>
          );
        })}
      </div>
    </StyledComponent>
  );
};
