import React from 'react';
import CommObjChart from './CommObjChart';
import { ChartComponent } from './index';
const Comp: ChartComponent = props => {
  return (
    <CommObjChart
      {...props}
      icon={require('./images/jysj.png')}
      title='医患沟通'
      items={[
        {
          filedName: 'transactionMoney',
          title: '今日回复数',
          unit: '条',
          handleFileNameValue: () => '0'
        },
        {
          filedName: 'transactionAmount',
          title: '我发起的',
          unit: '',
          handleFileNameValue: () => '0'
        },
        {
          filedName: 'transactionAmount',
          title: '我接收的',
          unit: '',
          handleFileNameValue: () => '0'
        }
      ]}
    />
  );
};
export default Comp;
