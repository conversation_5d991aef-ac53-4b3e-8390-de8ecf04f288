import styled from 'styled-components';
export default styled.div`
  background: #fff;
  text-align: center;
  height: 100%;
  width: 100%;
  display: flex;
  justify-items: center;
  align-items: center;
  flex-direction: column;
  overflow: hidden;
  > .title {
    flex: 2;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    font-size: 18px;
    line-height: 18px;
    font-weight: bold;
    color: #666666;
    width: 100%;
    > .icon {
      height: 20px;
      margin-right: 5px;
    }
    > .text {
    }
  }
  > .main {
    flex: 4;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    > .title {
      font-size: 18px;
      line-height: 24px;
      color: #666666;
    }
    > .value {
      font-size: 30px;
      line-height: 40px;
      font-weight: bold;
      color: #2880d9;
    }
  }
  > .items {
    flex: 3;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    > .item {
      flex: 1;
      > .title {
        font-size: 17px;
        line-height: 22px;
        color: #666666;
      }
      > .value {
        font-size: 17px;
        line-height: 22px;
        font-weight: bold;
        color: #343434;
      }
    }
  }
`;

export const GapRow = styled.div`
  height: 1px;
  width: 90%;
  background: #fff;
  margin: auto;
`;
export const GapCol = styled.div`
  width: 1px;
  height: 70%;
  background: #fff;
  margin: auto;
`;
