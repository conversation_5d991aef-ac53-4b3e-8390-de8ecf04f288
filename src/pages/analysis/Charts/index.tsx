import Jian<PERSON>ang<PERSON>uanJiao from './JianKangXuanJiao';
import KeShiSuiFang from './KeShiSuiFang';
import KeShiSuiFangChart from './KeShiSuiFangChart';
import ManYiDu from './ManYiDu';
import OperationGuide from './OperationGuide';
import SuiFangFan<PERSON>ui from './SuiFangFanKui';
import SuiFangTongJi from './SuiFangTongJi';
import XiaoXiTiXing from './XiaoXiTiXing';
import XiaoXiTiXingCharts from './XiaoXiTiXingCharts';
import <PERSON><PERSON>uanGouTong from './YiHuanGouTong';
import YuanJiSuiFang from './YuanJiSuiFang';
export type ChartComponent = React.FunctionComponent<{
  id?: number; // 没有id表示是预览模式
  chartId?: number; // 本地图表代码的id
}>;
export interface Comp {
  id: number;
  chartId: number;
  name: string;
  thumbnail: string;
  defaultSizeW?: number;
  defaultSizeH?: number;
  comp: ChartComponent;
}
export const comps: Comp[] = [
  {
    id: 600,
    chartId: 6000,
    name: '院级随访',
    thumbnail: require('./images/thumbnail/jiaoyishuju.jpg'),
    defaultSizeW: 6,
    defaultSizeH: 4,
    comp: YuanJiSuiFang
  },
  // {
  //   id: 60,
  //   chartId: 99,
  //   name: '院级随访',
  //   thumbnail: require('./images/thumbnail/yonghushuju.jpg'),
  //   defaultSizeW: 6,
  //   defaultSizeH: 4,
  //   comp: SuiFangTongJi
  // },
  {
    id: 601,
    chartId: 6001,
    name: '科室随访',
    thumbnail: require('./images/thumbnail/yonghushuju.jpg'),
    defaultSizeW: 6,
    defaultSizeH: 4,
    comp: KeShiSuiFang
  },
  {
    id: 602,
    chartId: 6002,
    name: '健康宣教',
    thumbnail: require('./images/thumbnail/wenzhenshuju.jpg'),
    defaultSizeW: 6,
    defaultSizeH: 4,
    comp: JianKangXuanJiao
  },
  {
    id: 603,
    chartId: 6003,
    name: '消息提醒',
    thumbnail: require('./images/thumbnail/chufangshuju.jpg'),
    defaultSizeW: 6,
    defaultSizeH: 4,
    comp: XiaoXiTiXing
  },
  {
    id: 604,
    chartId: 6004,
    name: '医患沟通',
    thumbnail: require('./images/thumbnail/yishishuju.jpg'),
    defaultSizeW: 6,
    defaultSizeH: 4,
    comp: YiHuanGouTong
  },
  {
    id: 605,
    chartId: 6005,
    name: '随访统计',
    thumbnail: require('./images/thumbnail/yonghutongji.jpg'),
    defaultSizeW: 8,
    defaultSizeH: 9,
    comp: SuiFangTongJi
  },
  {
    id: 606,
    chartId: 6006,
    name: '消息提醒',
    thumbnail: require('./images/thumbnail/jiaoyitongji.jpg'),
    defaultSizeW: 8,
    defaultSizeH: 9,
    comp: XiaoXiTiXingCharts
  },
  {
    id: 607,
    chartId: 6007,
    name: '满意度',
    thumbnail: require('./images/thumbnail/chufangtongji.jpg'),
    defaultSizeW: 8,
    defaultSizeH: 9,
    comp: ManYiDu
  },
  {
    id: 608,
    chartId: 6008,
    name: '随访反馈',
    thumbnail: require('./images/thumbnail/wenzhentongji.jpg'),
    defaultSizeW: 8,
    defaultSizeH: 9,
    comp: SuiFangFanKui
  },

  {
    id: 609,
    chartId: 6009,
    name: '操作指南',
    thumbnail: require('./images/thumbnail/caozuozhinan.jpg'),
    defaultSizeW: 8,
    defaultSizeH: 9,
    comp: OperationGuide
  },
  {
    id: 610,
    chartId: 6010,
    name: '科室随访',
    thumbnail: require('./images/thumbnail/chufangtongji.jpg'),
    defaultSizeW: 8,
    defaultSizeH: 9,
    comp: KeShiSuiFangChart
  }
];
