import Charts from '@components/charts';
import echarts from 'echarts';
import { useCallback, useMemo, useState } from 'react';
import StyledComponent from './CommChart.style';
import SelectDate from './SelectDate';
import useApi from './api';
import { ChartItem } from './d';

interface CommObjChartProps {
  id?: number;
  chartId?: number;
}
export default (props: CommObjChartProps) => {
  const { id } = props;
  const [params, setParams] = useState<any>();

  const { data, loading } = useApi.公共统计接口({
    initValue: [
      {
        title: '处方数量',
        point: [
          {
            x: '2023-08-16',
            y: 0
          },
          {
            x: '2023-08-17',
            y: 0
          },
          {
            x: '2023-08-18',
            y: 0
          },
          {
            x: '2023-08-19',
            y: 0
          },
          {
            x: '2023-08-20',
            y: 0
          },
          {
            x: '2023-08-21',
            y: 0
          },
          {
            x: '2023-08-22',
            y: 0
          },
          {
            x: '2023-08-23',
            y: 1
          }
        ]
      }
    ],
    params: {
      configId: id as any,
      params
    },
    // needInit: !!id && !!params
    needInit: false
  });

  const mapOptions: echarts.EChartOption = useMemo(() => {
    const items = data as ChartItem[];
    // const items = (data[0] ? data : []) as ChartItem[]; //FIXME 数据没对齐，先用列表第一个
    return {
      color: ['#4F8BCF'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        top: '3%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: items[0]?.point.map(point => point.x) ?? [],
          axisTick: {
            alignWithLabel: true
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: function(value: number) {
              return value;
            }
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: items.map(item => {
        return {
          name: item.title,
          type: 'bar',
          areaStyle: {},
          smooth: true,
          barWidth: '30%',
          data: item.point?.map(point => point.y)
        };
      })
    };
  }, [data]);

  return (
    <StyledComponent>
      <div className='head'>
        <div className='title'>科室随访</div>
        <div className='date'>
          <SelectDate
            onChange={useCallback(v => {
              setParams({
                beginTime: v[0].format('YYYY-MM-DD 00:00:00'),
                endTime: v[1].format('YYYY-MM-DD 23:59:59')
              });
            }, [])}
          />
        </div>
      </div>
      <div className='container'>
        <Charts
          loading={loading}
          option={mapOptions}
          style={{ width: '100%', height: '100%' }}
        />
      </div>
    </StyledComponent>
  );
};
