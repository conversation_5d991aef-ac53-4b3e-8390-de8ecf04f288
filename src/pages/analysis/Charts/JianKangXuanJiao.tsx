import CommObjChart from './CommObjChart';
import { ChartComponent } from './index';
const Comp: ChartComponent = props => {
  return (
    <CommObjChart
      {...props}
      icon={require('./images/jysj.png')}
      title='健康宣教'
      items={[
        {
          filedName: 'transactionMoney',
          title: '今日任务数',
          unit: '',
          handleFileNameValue: () => '2/4'
        },
        {
          filedName: 'transactionAmount',
          title: '个人发布',
          unit: '',
          handleFileNameValue: () => '0/1'
        },
        {
          filedName: 'transactionAmount',
          title: '宣教已读',
          unit: '',
          handleFileNameValue: () => '1/2'
        }
      ]}
    />
  );
};
export default Comp;
