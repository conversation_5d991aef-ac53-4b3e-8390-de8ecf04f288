import React from 'react';
import styled from 'styled-components';

export default () => {
  const list = [
    {
      label: '管理后台可使用那些浏览器？',
      url:
        'https://mp.weixin.qq.com/s?__biz=MzAwOTIzNTA1Ng==&mid=2648545149&idx=1&sn=c61b2ddc00acbfc2ad63c3238ff713bb&chksm=834b8862b43c0174907f6129dda85b6378580de88d0f4426f44f76a8fdeae374ff9ed931a9a0#rd'
    },
    {
      label: '如何添加或删除科室？',
      url:
        'https://mp.weixin.qq.com/s?__biz=MzAwOTIzNTA1Ng==&mid=2648545149&idx=2&sn=4ab6f9876f9672461fed1dd757a6620b&chksm=834b8862b43c0174b6a91f1ec6442fa8de1904087d8610a095641e318539d334e74338045f29#rd'
    },
    {
      label: '如何进行医生管理？',
      url:
        'https://mp.weixin.qq.com/s?__biz=MzAwOTIzNTA1Ng==&mid=2648545149&idx=3&sn=bb5d33a41223b52ba807af5441d5bec6&chksm=834b8862b43c017474d318a7a85eee6f360458e114b58319d8805521f7383f1c0a93f47eb489#rd'
    },
    {
      label: '如何进行院区管理？',
      url:
        'https://mp.weixin.qq.com/s?__biz=MzAwOTIzNTA1Ng==&mid=2648545149&idx=4&sn=d19d83fbb1d463ebb1d8c133d8ae60aa&chksm=834b8862b43c0174b5a30e61e3d7cffdb0ae0b6f89871a78cf320737f767fd7bf61b388c58a4#rd'
    },
    {
      label: '异常订单如何查看？',
      url:
        'https://mp.weixin.qq.com/s?__biz=MzAwOTIzNTA1Ng==&mid=2648545149&idx=5&sn=bfc68c6bd24b36e97e3c0374709df139&chksm=834b8862b43c017488a7168f74942533840eae99d0c9a90fcb265a17256fd763501c5d117d55#rd'
    }
  ];

  return (
    <Wrap>
      <div>
        <div />
        <div>操作指南</div>
        <div />
      </div>
      <ul>
        {list.map(item => {
          return (
            <li
              key={item.label}
              onClick={() => window.open(item.url, '_blank')}>
              {item.label}
            </li>
          );
        })}
      </ul>
      {/* <div className='more'>查看更多</div> */}
    </Wrap>
  );
};

const Wrap = styled.div`
  border: 1px solid #e2e2e2;
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 15px 0;
  > div:nth-child(1) {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    color: #333333;
    margin-bottom: 20px;
    > div:nth-child(1) {
      width: 46px;
      height: 2px;
      background-color: #999999;
      margin-right: 10px;
    }
    > div:nth-child(3) {
      width: 46px;
      height: 2px;
      background-color: #999999;
      margin-left: 10px;
    }
  }
  > ul {
    > li {
      margin-bottom: 10px;
      color: #333333;
      font-size: 14px;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }
    > li:last-child {
      margin-bottom: 0;
    }
  }
  .more {
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    text-decoration: underline;
    color: #2780d9;
    cursor: pointer;
  }
`;
