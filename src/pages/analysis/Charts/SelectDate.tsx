import React, { useState, useEffect, useCallback } from 'react';
import { Select, DatePicker } from 'antd';
import styled from 'styled-components';
import moment from 'moment';
import { Moment } from 'moment';
const types = [
  { label: '今日', value: '1' },
  { label: '7日', value: '2' },
  { label: '14日', value: '3' },
  { label: '一月', value: '4' },
  { label: '自定义', value: '5' }
];
const types2 = [
  { label: '7天', value: '2' },
  { label: '14天', value: '3' },
  { label: '30天', value: '4' },
  { label: '自定义', value: '5' }
];
export default ({
  onChange,
  hideToday = false,
  isUserAnalysis = false
}: {
  onChange: (v: Moment[]) => void;
  hideToday?: boolean;
  isUserAnalysis?: boolean;
}) => {
  const [value, setValue] = useState<string>('1');
  const [date, setDate] = useState<Moment[] | null>(null);
  useEffect(() => {
    // console.log(2, date, onChange);
    date && onChange(date);
  }, [date, onChange]);

  const handleSelect = useCallback(v => {
    setValue(v);
    if (v === '1') {
      setDate([moment().startOf('day'), moment().endOf('day')]);
    }
    if (v === '2') {
      setDate([
        moment()
          .add(-7, 'day')
          .startOf('day'),
        moment().endOf('day')
      ]);
    }
    if (v === '3') {
      setDate([
        moment()
          .add(-14, 'day')
          .startOf('day'),
        moment().endOf('day')
      ]);
    }
    if (v === '4') {
      setDate([
        moment()
          .add(-1, 'M')
          .startOf('day'),
        moment().endOf('day')
      ]);
    }
  }, []);
  useEffect(() => {
    handleSelect('2');
  }, [handleSelect]);
  return (
    <StyledComponent>
      {isUserAnalysis ? (
        <div className={'radioGroup'}>
          {types2.map(v => {
            return (
              <span
                key={v.label}
                style={{ color: v.value === value ? '#2780d9' : '' }}
                onClick={() => handleSelect(v.value)}>
                {v.label}
              </span>
            );
          })}
        </div>
      ) : (
        <Select
          className='select'
          value={value}
          placeholder='请选择'
          options={hideToday ? types.slice(1) : types}
          style={{
            maxWidth: '85px'
          }}
          onSelect={handleSelect}
        />
      )}

      <DatePicker.RangePicker
        allowClear={false}
        disabled={value !== '5'}
        value={date as any}
        disabledDate={current => current && current > moment().endOf('day')}
        className='checkDate'
        style={{
          maxWidth: '250px'
        }}
        onChange={v => {
          setDate(v as Moment[]);
        }}
      />
      {/* {date && (
        <div className='date'>
          {date[0].format('YYYY-MM-DD')} ~ {date[1].format('YYYY-MM-DD')}
        </div>
      )} */}
    </StyledComponent>
  );
};

const StyledComponent = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
  > .radioGroup {
    margin: 10px 0 0 0;
    white-space: nowrap;
    min-width: 200px;
    overflow-x: auto;
    > span {
      cursor: pointer;
      padding: 0 8px;
    }
  }
  > .select {
    margin: 10px 10px 0 0;
  }

  > .checkDate {
    margin: 10px 0 0 0;
  }
`;
