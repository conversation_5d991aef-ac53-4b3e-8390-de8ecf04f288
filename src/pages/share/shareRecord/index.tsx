/*
 * @Author: hpengfei <EMAIL>
 * @Date: 2024-06-05 10:13:48
 * @LastEditors: hpengfei <EMAIL>
 * @LastEditTime: 2024-07-08 17:47:00
 * @FilePath: \ih-standard\src\pages\share\shareRecord\index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useCallback, useState } from 'react';
import { DayRangePicker, ArrSelect, ActionsWrap, getPrice } from 'parsec-admin';
import useApi from './api';
import { Button, message } from 'antd';
import MyTableList from '@components/myTableList';
import saveAs from 'file-saver';
import moment from 'moment';
export default () => {
  const [pageSize, setPageSize] = useState(0);
  const { request: handleExport, loading: exportLoading } = useApi.exportTypes({
    needInit: false
  });
  const [queryParams, setQueryParams] = useState({} as any);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const reviewStatus = [
    { id: 'INIT', name: '未支付' },
    { id: 'COMPLETE', name: '已支付' },
    { id: 'REFUND', name: '已退费' },
    { id: 'CANCEL', name: '已取消' },
    { id: 'FINISH', name: '已完成' }
  ];
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const publicityStatus = [
    { id: 'gh', name: '门诊挂号' },
    { id: '1', name: '图文问诊' },
    { id: '2', name: '电话问诊' },
    { id: '3', name: '视频问诊' }
  ];
  const getDoctorList = useCallback(params => {
    return useApi.分销列表.request({
      ...params
    });
  }, []);
  return (
    <div>
      <MyTableList
        tableTitle='分销管理'
        getList={({ params }) => {
          setPageSize(params?.pageNum);
          setQueryParams({
            ...params
          });
          return getDoctorList(params);
        }}
        scroll={{ x: 2000 }}
        action={
          <ActionsWrap>
            <Button
              type={'default'}
              loading={exportLoading}
              onClick={() => {
                const {
                  orderCreateEndTime,
                  orderCreateStartTime
                } = queryParams;
                if (orderCreateEndTime && orderCreateStartTime) {
                  const orderCreateDiff = moment(orderCreateEndTime).diff(
                    moment(orderCreateStartTime),
                    'days'
                  );
                  if (orderCreateDiff > 30) {
                    message.error('订单查询时间不能超过30天');
                    return;
                  }
                  handleExport({
                    ...queryParams,
                    isExport: 1,
                    numPerPage: 10000
                  }).then(data => {
                    saveAs(data, `分销记录.xls`);
                  });
                } else {
                  message.error('请选择订单下单时间');
                }
              }}>
              导出
            </Button>
          </ActionsWrap>
        }
        columns={[
          {
            title: '编号',
            dataIndex: 'doctorName',
            width: 100,
            render: (_value, _row, index) => {
              return (pageSize - 1) * 10 + index + 1;
            }
          },
          {
            title: '订单编号',
            width: 200,
            dataIndex: 'orderId'
          },
          {
            title: '订单下单时间',
            width: 200,
            dataIndex: 'createTime',
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                valueFormat={'YYYY-MM-DD HH:mm:ss'}
              />
            ),
            searchIndex: ['orderCreateStartTime', 'orderCreateEndTime']
          },
          {
            title: '订单完成时间',
            width: 200,
            dataIndex: 'completionTime',
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                valueFormat={'YYYY-MM-DD HH:mm:ss'}
              />
            ),
            searchIndex: ['completionStartTime', 'completionEndTime']
          },
          {
            title: '订单状态',
            dataIndex: 'orderStatus',
            width: 150,
            search: (
              <ArrSelect
                options={reviewStatus.map(type => {
                  return {
                    value: type.id,
                    label: type.name
                  };
                })}
              />
            ),
            render: val => reviewStatus.find(item => item.id === val)?.name
          },
          {
            title: '客户姓名',
            width: 150,
            dataIndex: 'patientName'
          },
          {
            title: '客户手机号',
            dataIndex: 'patientPhone',
            width: 170
          },
          {
            title: '分销员',
            width: 120,
            dataIndex: 'distributor',
            search: true
          },
          {
            title: '分销员手机号',
            dataIndex: 'distributorPhone',
            width: 210,
            search: true
          },
          {
            title: '部门负责人',
            dataIndex: 'deptHeader',
            width: 170
          },
          {
            title: '负责人手机号',
            dataIndex: 'deptHeaderPhone',
            width: 210
          },
          {
            title: '机构名称',
            dataIndex: 'organizationName',
            width: 170,
            search: true
          },
          {
            title: '部门',
            dataIndex: 'deptName',
            width: 170,
            search: true
          },
          {
            title: '实付金额',
            dataIndex: 'actualPayment',
            width: 120,
            render: v => `${getPrice(v, 2, true)}`
          },
          {
            title: '订单类型',
            dataIndex: 'orderType',
            width: 170,
            search: (
              <ArrSelect
                options={publicityStatus.map(type => {
                  return {
                    value: type.id,
                    label: type.name
                  };
                })}
              />
            ),
            render: val => publicityStatus.find(item => item.id === val)?.name
          },
          {
            title: '订单关联医生',
            dataIndex: 'relatedDoctor',
            width: 170
          }
        ]}
      />
    </div>
  );
};
