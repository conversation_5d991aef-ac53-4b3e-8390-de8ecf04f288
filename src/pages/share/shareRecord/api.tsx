import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ListApiRequestParams, ListApiResponseData } from '@src/configs/apis';

export default {
  分销列表: createApiHooks(
    (params: ListApiRequestParams & { nameFullTransFlag: number }) =>
      request.post<
        ListApiResponseData<{
          id: number;
          orderId: string;
          createTime: string;
          orderStatus: string;
          patientName: string;
          patientPhone: string;
          distributor: string;
          distributorPhone: string;
          deptHeader: string;
          deptHeaderPhone: string;
          organizationName: string;
          actualPayment: string;
          orderType: string;
          relatedDoctor: string;
        }>
      >('/mch/register/distribution/records', params)
  ),
  exportTypes: createApiHooks(
    (params: {
      hisId?: string;
      deptName?: string;
      checkStatus?: string;
      publicStatus?: string;
      doctorName?: string;
      phone?: string;
      hospitalName?: string;
      startDate?: string;
      endDate?: string;
    }) =>
      request.get<Blob>('/mch/register/distribution/recordsExport', {
        responseType: 'blob',
        params
      })
  )
};
