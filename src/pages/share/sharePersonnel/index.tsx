import React, { useState } from 'react';
import {
  ActionsWrap,
  ArrSelect,
  LinkButton,
  actionConfirm,
  handleSubmit,
  useModal
} from 'parsec-admin';
import useApi from './api';
import { Button, Radio, Switch } from 'antd';
import env from '@configs/env';
import { saveAs } from 'file-saver';
import MyTableList from '@components/myTableList';

export default () => {
  const [pageSize, setPageSize] = useState(0);
  const [depList, setDepList] = useState<any>([]);
  const {
    data: { data: mechanismList }
  } = useApi.分销机构列表({
    initValue: [{ data: { recordList: [] } }]
  });

  const { loading: loadingImport, request: requestImport } = useApi.分销员导入({
    needInit: false
  });
  const switchModal = useModal(
    ({ id, modeType }: { modeType: 'add' | 'edit'; id: number | string }) => {
      return {
        onSubmit: values => {
          const innerPromise = useApi.分销员修改;
          const params = {
            ...values,
            id,
            hisId: env.hisId,
            organizationId: mechanismList?.recordList?.find(
              item => values?.organizationName === item.id
            )?.id,
            deptId: depList?.find(item => item.id === values?.deptName)?.id
          };
          return handleSubmit(() => {
            return innerPromise.request(params);
          });
        },
        title: modeType === 'add' ? '新增分销员' : '编辑分销员',
        items: [
          {
            name: 'organizationName',
            label: '所属机构',
            required: true,
            render: () => (
              <ArrSelect
                options={(mechanismList?.recordList || []).map(x => ({
                  value: x.id,
                  label: x.organizationName
                }))}
                onChange={val => {
                  useApi.分销部门列表
                    .request({
                      organizationName: mechanismList?.recordList?.find(
                        item => val === item.id
                      )?.organizationName
                    })
                    .then(r => {
                      if (r?.data?.recordList) {
                        setDepList(r?.data?.recordList);
                      }
                    });
                }}
              />
            )
          },
          {
            name: 'deptName',
            label: '部门',
            required: true,
            render: () => (
              <ArrSelect
                options={(depList || []).map(x => {
                  return {
                    value: x.id,
                    label: x.deptName
                  };
                })}
              />
            )
          },
          { name: 'name', label: '分销员姓名', required: true },
          {
            name: 'phone',
            label: '手机号:',
            required: true,
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '请输入手机号'
                },
                {
                  pattern: /^1(3|4|5|7|8|9)\d{9}$/,
                  message: '请输入正确的手机号'
                }
              ]
            }
          },
          {
            name: 'status',
            label: '状态',
            required: true,
            render: () => (
              <Radio.Group>
                <Radio value={0} defaultChecked={true}>
                  禁用
                </Radio>
                <Radio value={1}>启用</Radio>
              </Radio.Group>
            )
          }
        ]
      };
    },
    [depList]
  );
  return (
    <div>
      <MyTableList
        tableTitle='分销员管理'
        getList={({ params }) => {
          setPageSize(params?.pageNum);
          return useApi.分销员列表.request(params);
        }}
        action={
          <ActionsWrap>
            <Button
              type={'default'}
              onClick={() => {
                useApi.分销员导入模板下载.request().then(r => {
                  saveAs(r, '分销员导入模板.xlsx');
                });
              }}>
              导入模板下载
            </Button>
            <Button
              type={'default'}
              loading={loadingImport}
              onClick={() => {
                const el = document.createElement('input');
                el.setAttribute('type', 'file');
                el.addEventListener('change', () => {
                  const file = el.files?.[0];
                  if (file) {
                    handleSubmit(() => requestImport({ file }), '导入');
                  }
                });
                el.click();
                el.remove();
              }}>
              批量导入
            </Button>
            <Button
              type={'default'}
              onClick={() => {
                switchModal({ modeType: 'add' });
              }}>
              新增分销员
            </Button>
          </ActionsWrap>
        }
        columns={[
          {
            title: '编号',
            dataIndex: 'doctorName',
            width: 100,
            render: (_value, _row, index) => {
              return (pageSize - 1) * 10 + index + 1;
            }
          },
          {
            title: '分销员',
            width: 200,
            dataIndex: 'distributor',
            search: true
          },
          {
            title: '手机号',
            dataIndex: 'phone',
            width: 100,
            search: true
          },
          {
            title: '部门',
            width: 200,
            dataIndex: 'deptName',
            search: true
          },
          {
            title: '部门负责人',
            width: 200,
            dataIndex: 'deptHeader',
            search: true
          },
          {
            title: '所属机构',
            width: 200,
            dataIndex: 'organizationName',
            search: true
          },
          {
            title: '创建时间',
            width: 100,
            dataIndex: 'createTime'
          },
          {
            title: '状态',
            width: 100,
            dataIndex: 'status',
            render: (val, record: any) => {
              return (
                <Switch
                  checkedChildren='ON'
                  unCheckedChildren='OFF'
                  checked={record.status === 0 ? false : true}
                  onClick={() => {
                    handleSubmit(() =>
                      useApi.分销员修改.request({
                        ...record,
                        status: record.status === 0 ? 1 : 0
                      })
                    );
                  }}
                />
              );
            }
          },
          {
            title: '操作',
            width: 200,
            fixed: 'right',
            render: (v, record: any) => {
              return (
                <ActionsWrap max={6}>
                  <LinkButton
                    onClick={() => {
                      console.log('record', record);
                      switchModal({
                        modeType: 'edit',
                        ...record,
                        name: record.distributor
                      });
                    }}>
                    编辑
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(() => {
                        return useApi.删除分销员.request({
                          id: record.id
                        });
                      }, '删除');
                    }}>
                    删除
                  </LinkButton>
                </ActionsWrap>
              );
            }
          }
        ]}
      />
    </div>
  );
};
