import React, { useState } from 'react';
import {
  ActionsWrap,
  LinkButton,
  actionConfirm,
  handleSubmit,
  ArrSelect,
  useModal
} from 'parsec-admin';
import useApi from './api';
import env from '@configs/env';
import { Button, Radio, Switch } from 'antd';
import MyTableList from '@components/myTableList';

export default () => {
  const [pageSize, setPageSize] = useState(0);
  const {
    data: { data: mechanismList }
  } = useApi.分销机构列表({
    initValue: [{ data: { recordList: [] } }]
  });
  const switchModal = useModal(
    ({ id, modeType }: { modeType: 'add' | 'edit'; id: number | string }) => {
      return {
        onSubmit: values => {
          console.log(id, modeType);
          const innerPromise = useApi.分销部门修改;
          const params = {
            ...values,
            id,
            hisId: env.hisId,
            organizationId: mechanismList?.recordList?.find(
              item => values?.organizationName === item.id
            )?.id
          };
          return handleSubmit(() => {
            return innerPromise.request(params);
          });
        },
        title: modeType === 'add' ? '新增部门' : '编辑部门',
        items: [
          {
            name: 'organizationName',
            label: '所属机构',
            required: true,
            render: () => (
              <ArrSelect
                options={(mechanismList?.recordList || []).map(x => ({
                  value: x.id,
                  label: x.organizationName
                }))}
              />
            )
          },
          { name: 'deptName', label: '部门名称', required: true },
          { name: 'deptHeader', label: '负责人姓名', required: true },
          {
            name: 'deptHeaderPhone',
            label: '负责人手机号',
            required: true,
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '请输入手机号'
                },
                {
                  pattern: /^1(3|4|5|7|8|9)\d{9}$/,
                  message: '请输入正确的手机号'
                }
              ]
            }
          },
          {
            name: 'status',
            label: '状态',
            required: true,
            render: () => (
              <Radio.Group>
                <Radio value={0} defaultChecked={true}>
                  禁用
                </Radio>
                <Radio value={1}>启用</Radio>
              </Radio.Group>
            )
          }
        ]
      };
    }
  );
  return (
    <div>
      <MyTableList
        tableTitle='分销部门'
        getList={({ params }) => {
          setPageSize(params?.pageNum);
          return useApi.分销部门列表.request(params);
        }}
        action={
          <ActionsWrap>
            <Button
              type={'default'}
              onClick={() => {
                switchModal({ modeType: 'add' });
              }}>
              新增分销部门
            </Button>
          </ActionsWrap>
        }
        columns={[
          {
            title: '编号',
            dataIndex: 'doctorName',
            width: 100,
            render: (_value, _row, index) => {
              return (pageSize - 1) * 10 + index + 1;
            }
          },
          {
            title: '机构名称',
            width: 200,
            dataIndex: 'organizationName',
            search: true
          },
          {
            title: '部门',
            width: 200,
            dataIndex: 'deptName',
            search: true
          },
          {
            title: '部门负责人',
            width: 200,
            dataIndex: 'deptHeader',
            search: true
          },
          {
            title: '部门负责人电话',
            dataIndex: 'deptHeaderPhone',
            width: 100
          },
          {
            title: '创建时间',
            width: 100,
            dataIndex: 'createTime'
          },
          {
            title: '状态',
            width: 100,
            dataIndex: 'status',
            render: (val, record: any) => {
              return (
                <Switch
                  checkedChildren='ON'
                  unCheckedChildren='OFF'
                  checked={record.status === 0 ? false : true}
                  onClick={() => {
                    handleSubmit(() =>
                      useApi.分销部门修改.request({
                        ...record,
                        status: record.status === 0 ? 1 : 0
                      })
                    );
                  }}
                />
              );
            }
          },
          {
            title: '操作',
            width: 200,
            fixed: 'right',
            render: (v, record: any) => {
              return (
                <ActionsWrap max={6}>
                  <LinkButton
                    onClick={() => {
                      switchModal({ modeType: 'edit', ...record });
                    }}>
                    编辑
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(() => {
                        return useApi.删除部门机构.request({
                          id: record.id
                        });
                      }, '删除');
                    }}>
                    删除
                  </LinkButton>
                </ActionsWrap>
              );
            }
          }
        ]}
      />
    </div>
  );
};
