/*
 * @Author: hpengfei <EMAIL>
 * @Date: 2024-06-05 14:08:42
 * @LastEditors: hpengfei <EMAIL>
 * @LastEditTime: 2024-06-05 18:27:32
 * @FilePath: \ih-standard\src\pages\share\shareMechanism\api.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ApiResponse,
  ListApiRequestParams,
  ListApiResponseData
} from '@src/configs/apis';

export default {
  分销部门列表: createApiHooks((params: ListApiRequestParams) =>
    request.post<
      ListApiResponseData<{
        id: number;
        organizationName: string;
        organizationId: string;
        deptName: string;
        deptHeader: string;
        deptHeaderPhone: string;
        createTime: string;
        status: string;
      }>
    >('/mch/register/distribution/dept/list', params)
  ),
  分销部门修改: createApiHooks(
    (params: {
      organizationName: string;
      header: string;
      headerPhone: string;
      createTime: string;
      status: string;
      id: string;
    }) =>
      request.post<ApiResponse<any>>(
        '/mch/register/distribution/dept/save',
        params,
        {
          headers: { 'Content-Type': 'application/json' }
        }
      )
  ),
  删除部门机构: createApiHooks((params: { id: string }) =>
    request.delete(`/mch/register/distribution/dept/delete/${params.id}`)
  ),
  分销机构列表: createApiHooks((params: ListApiRequestParams) =>
    request.post<
      ListApiResponseData<{
        id: number;
        organizationName: string;
        header: string;
        headerPhone: string;
        createTime: string;
        status: string;
      }>
    >('/mch/register/distribution/org/list', params)
  )
};
