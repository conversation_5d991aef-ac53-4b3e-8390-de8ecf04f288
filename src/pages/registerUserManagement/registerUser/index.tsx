import React from 'react';
import MyTableList from '@components/myTableList';
import {
  ActionsWrap,
  LinkButton,
  actionConfirm,
  ArrSelect
} from 'parsec-admin';
import useApi from './api';
import { useHistory } from 'react-router-dom';
import { useConfig } from '@src/store/hisConfig';
import moment from 'moment';

const options = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '启用',
    value: 0
  },
  {
    label: '停用',
    value: 1
  }
];

const TIME_UNITS = {
  hours: 'h',
  minutes: 'm',
  seconds: 's'
};

const formatTimeUnit = (value, unit) => {
  if (value === 0) return ''; // 如果值为0，不显示
  return `${value}${unit}`;
};

const getTimeStr = v => {
  try {
    const duration = moment.duration(
      moment()
        .add(v, 'seconds')
        .diff(moment())
    );
    const hours = duration.hours();
    const minutes = duration.minutes();
    const seconds = duration.seconds();
    const timeStr = [
      formatTimeUnit(hours, TIME_UNITS.hours),
      formatTimeUnit(minutes, TIME_UNITS.minutes),
      formatTimeUnit(seconds, TIME_UNITS.seconds)
    ]
      .filter(Boolean)
      .join('');

    return timeStr || '0s';
  } catch (error) {
    console.log('getTimeStr', error);
  }
  return '-';
};
const RegisterUserList: React.FC = () => {
  const history = useHistory();
  const { isICU } = useConfig();
  return (
    <MyTableList
      getList={({ pagination: { current }, params }) => {
        console.log('params======>', params);
        return useApi.userList.request({
          ...params,
          pageNum: current,
          numPerPage: 10
        });
      }}
      showTool={false}
      tableTitle='用户列表'
      exportExcelButton
      columns={[
        {
          title: '用户姓名',
          dataIndex: 'userName',
          search: true
        },
        {
          title: '手机号',
          dataIndex: 'phone',
          search: true
        },
        {
          title: '就职医院',
          dataIndex: 'hospitalName',
          search: true
        },
        {
          title: '医院等级',
          dataIndex: 'level',
          search: true
        },
        {
          title: '职业',
          dataIndex: 'job',
          search: true
        },
        ...(isICU
          ? [
              {
                title: '学习总时长',
                dataIndex: 'totalTime',
                render: v => `${getTimeStr(v)}`
              },
              {
                title: '学习次数',
                dataIndex: 'totalNum'
              }
            ]
          : []),
        {
          title: '注册时间',
          dataIndex: 'createTime'
        },
        {
          title: '用户状态',
          dataIndex: 'status',
          searchIndex: 'type',
          search: <ArrSelect options={options} />,
          render: v => {
            return v === 0 ? '启用' : '禁用';
          }
        },
        {
          title: '操作',
          fixed: 'right',
          align: 'left',
          render: (_, record: any) => {
            return (
              <ActionsWrap max={99}>
                <LinkButton
                  onClick={() => {
                    history.push(
                      `/registerUserManagement/registerUser/detail/${record.id}/${record.phone}`
                    );
                  }}>
                  查看
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.userChangeStatus.request({
                          id: record.id,
                          status: record.status === 1 ? 0 : 1
                        }),
                      record.status === 0 ? '禁用' : '启用'
                    );
                  }}>
                  {record.status === 0 ? '禁用' : '启用'}
                </LinkButton>
              </ActionsWrap>
            );
          }
        }
      ]}
    />
  );
};

export default RegisterUserList;
