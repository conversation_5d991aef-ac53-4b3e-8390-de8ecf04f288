import React, { useState } from 'react';
import { DetailLayout, FormDescriptions } from 'parsec-admin';
import { Button, Space } from 'antd';
import { useParams, useHistory } from 'react-router-dom';
import useApi from './api';
import MyTableList from '@components/myTableList';
import moment from 'moment';
import styles from './index.module.less';

interface UserItem {
  id: number;
  userName: string;
  hisId: number;
  phone: string;
  hospitalName: string;
  level: string;
  job: string;
  status: number;
  recentLoginTime: string;
  createTime: string;
  updateTime: string;
}

const UserDetail: React.FC = () => {
  const params = useParams<{ id?: string; phone?: string }>();
  const history = useHistory();
  const editId = params?.id;
  const [detail, setDetail] = useState<UserItem>({} as UserItem);

  const queryDetail = async (id: string) => {
    const res = await useApi.userDetail.request({
      id
    });
    setDetail((res.data || {}) as UserItem);
  };

  const back = () => {
    history.goBack();
  };

  React.useEffect(() => {
    if (!editId) return;
    queryDetail(editId);
  }, [editId]);

  const TIME_UNITS = {
    hours: 'h',
    minutes: 'm',
    seconds: 's'
  };

  const formatTimeUnit = (value, unit) => {
    if (value === 0) return ''; // 如果值为0，不显示
    return `${value}${unit}`;
  };

  const getTimeStr = (startTime, endTime) => {
    const duration = moment.duration(moment(endTime).diff(moment(startTime)));
    const hours = duration.hours();
    const minutes = duration.minutes();
    const seconds = duration.seconds();
    const timeStr = [
      formatTimeUnit(hours, TIME_UNITS.hours),
      formatTimeUnit(minutes, TIME_UNITS.minutes),
      formatTimeUnit(seconds, TIME_UNITS.seconds)
    ]
      .filter(Boolean)
      .join('');

    return timeStr || '0s';
  };
  return (
    <DetailLayout
      headerProps={false}
      cardsProps={[
        {
          title: '基本信息',
          children: (
            <Space align='start'>
              <FormDescriptions
                column={3}
                data={detail}
                edit={false}
                items={[
                  {
                    label: '用户姓名',
                    name: 'userName'
                  },
                  {
                    label: '手机号码',
                    name: 'phone'
                  },
                  {
                    label: '就职医院',
                    name: 'hospitalName'
                  },
                  {
                    label: '职业',
                    name: 'job'
                  },
                  {
                    label: '医院等级',
                    name: 'level'
                  },
                  {
                    label: '注册时间',
                    name: 'createTime'
                  }
                ]}
              />
            </Space>
          )
        },
        {
          title: '最近登录',
          children: <div>{detail.recentLoginTime}</div>
        },
        {
          title: '浏览记录',
          children: (
            <div className={styles.recordList}>
              <MyTableList
                tableTitle={null}
                getList={({ pagination: { current }, params: _params }) => {
                  return useApi.recordList.request({
                    ..._params,
                    pageNum: current,
                    numPerPage: 5,
                    phone: params?.phone
                  });
                }}
                bordered={true}
                size='small'
                columns={[
                  {
                    title: '内容类型',
                    dataIndex: 'typeSign',
                    render: v => {
                      return v === 'train' ? '培训' : '科研协作';
                    }
                  },
                  {
                    title: '内容标题',
                    dataIndex: 'title'
                  },
                  {
                    title: '查看时间',
                    dataIndex: 'readStartTime',
                    render: (_, record: any) => {
                      return `${getTimeStr(
                        record.readStartTime,
                        record.readEndTime
                      )}`;
                    }
                  }
                ]}></MyTableList>
            </div>
          )
        },
        {
          children: (
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button style={{ marginRight: '10px' }} onClick={back}>
                返回
              </Button>
            </div>
          )
        }
      ]}></DetailLayout>
  );
};

export default UserDetail;
