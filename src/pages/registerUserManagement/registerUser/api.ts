import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ListApiResponseData, ListApiRequestParams } from '@apiHooks';

export interface ApiResponse<D> {
  code: 0 | 200 | 999; // 999用户未登录
  msg: string | null;
  data?: D;
}

/**
 * 用户
 */
export interface UserItem {
  id: number;
  userName: string;
  hisId: number;
  phone: string;
  hospitalName: string;
  level: string;
  job: string;
  status: number;
  recentLoginTime: string;
  createTime: string;
  updateTime: string;
}

const userApis = {
  userList: createApiHooks((data: ListApiRequestParams & { hisId?: string }) =>
    request.get<ListApiResponseData<UserItem>>('/mch/user/icuAccount/list', {
      params: data
    })
  ),
  userDetail: createApiHooks((data: { id: string }) =>
    request.get<ApiResponse<UserItem>>(
      `/mch/user/icuAccount/detail?id=${data.id}`
    )
  ),
  userChangeStatus: createApiHooks((data: { id: number; status: number }) =>
    request.get(`/mch/user/icuAccount/status`, { params: data })
  ),
  recordList: createApiHooks(
    (data: ListApiRequestParams & { hisId?: string; phone?: string }) =>
      request.get<ListApiResponseData<UserItem>>(
        '/mch/cooperate/train-cooperate/trainCooperateRecordList',
        {
          params: data
        }
      )
  )
};

export default {
  ...userApis
};
