import React, { useMemo, useState } from 'react';
import {
  ActionsWrap,
  ArrSelect,
  Form,
  LinkButton,
  TableList,
  useModal,
  actionConfirm,
  useReloadTableList
} from 'parsec-admin';
import {
  Button,
  Input,
  Checkbox,
  Radio,
  Cascader,
  DatePicker,
  TimePicker,
  message
} from 'antd';
import apis, { ListType } from '@pages/operations/api';
import { Space, TransferChange } from '@kqinfo/ui';
import dayjs from 'dayjs';
import { getTreeOptionsByNo } from '@pages/hospital/doctorDetailSmart/utils';
import useApi from '@pages/hospital/doctorSmart/api';
import FormItem from 'antd/es/form/FormItem';
import moment from 'moment';

export default () => {
  const [docArr, setDocArr] = useState<any>([]);
  const [searchDocArr, setSearchDocArr] = useState<any>([]);
  const reloadTableList = useReloadTableList();

  const [form] = Form.useForm();
  const {
    data: {
      data: { recordList: depList }
    }
  } = useApi.科室管理列表({
    params: { hisType: 2 },
    initValue: { data: { recordList: [] } }
  });
  const { request: getDocList } = useApi.医生列表({
    needInit: false,
    initValue: { recordList: [] }
  });
  const { request: getDetail } = apis.查询直播提醒详情({
    needInit: false
  });
  const returnWeekDay = (v: number) => {
    switch (v) {
      case 0:
        return '星期天';
      case 1:
        return '星期一';
      case 2:
        return '星期二';
      case 3:
        return '星期三';
      case 4:
        return '星期四';
      case 5:
        return '星期五';
      case 6:
        return '星期六';
    }
  };

  const findDeptId = (id: string) => {
    console.log(id, 'id');
    let arr;
    let name;
    depList.forEach(item1 => {
      if (item1.children.length !== 0)
        item1.children.forEach(item2 => {
          if (item2.children.length !== 0)
            item2.children.forEach(item3 => {
              if (item3.no === id) {
                arr = [item1.no, item2.no, item3.no];
                name = item3.name;
              }
            });
          else if (item2.no === id) {
            arr = [item1.no, item2.no];
            name = item2.name;
          }
        });
      else if (item1.no === id) {
        arr = [item1.no];
        name = item1.name;
      }
    });
    return { arr, name };
  };

  const disabledDate = current => {
    return current && current < moment().subtract(1, 'days');
  };

  //将时分秒转为时间戳
  function time_to_sec(time) {
    if (time !== null) {
      const hour = time.split(':')[0];
      const min = time.split(':')[1];
      const sec = time.split(':')[2];
      return Number(hour * 3600) + Number(min * 60) + Number(sec);
    }
  }
  // 如果time2大于time1 返回true 否则 返回false
  function compareTime(time1, time2) {
    const sec1 = time_to_sec(time1);
    const sec2 = time_to_sec(time2);
    if (!sec1 || !sec2) return false;
    return sec2 - sec1 > 0;
  }

  const switchModalVisible = useModal(
    ({ id }) => ({
      title: id ? '编辑提醒' : '新建提醒',
      form,
      onSubmit: values => {
        const startTime = dayjs(values.time?.[0]).format('HH:mm:ss');
        if (compareTime(startTime, dayjs(new Date()).format('HH:mm:ss'))) {
          message.error('当前设置提醒时间己过，请设置正确的时间段');
          return Promise.reject();
        }
        console.log(values, 'values');
        const deptObj = findDeptId(values.deptId);
        const date = dayjs(values.date).format('YYYY-MM-DD');
        const params = {
          ...values,
          nextNoticeStartTime: `${date} ${dayjs(values.time?.[0]).format(
            'HH:mm:ss'
          )}`,
          nextNoticeEndTime: `${date} ${dayjs(values.time?.[1]).format(
            'HH:mm:ss'
          )}`,
          weekDay: returnWeekDay(dayjs(form.getFieldValue('date')).day()),
          repeatStatus: values.repeatStatus ? 1 : 0,
          doctorName: docArr?.find(item => item.doctorId === values.doctorId)
            ?.name,
          deptId: values.deptId,
          deptName: deptObj?.name
        };
        delete params.time;
        delete params.date;
        console.log(params, 'params');
        if (id)
          return apis.修改直播提醒
            .request({ ...params })
            .then(() => reloadTableList());
        else
          return apis.新增直播提醒
            .request({ ...params })
            .then(() => reloadTableList());
      },
      items: [
        {
          name: 'id',
          render: false
        },
        {
          label: '直播名称',
          name: 'liveName',
          required: true
        },
        {
          label: '科室',
          name: 'deptId',
          required: true,
          render: (
            <TransferChange
              mode={'cascade'}
              data={getTreeOptionsByNo(depList) as any}>
              <Cascader
                onChange={v => {
                  form.resetFields(['doctorId']);
                  console.log(v, 'v');
                  getDocList({
                    deptNo: v?.[v?.length - 1] + '',
                    type: '1'
                  }).then(r => {
                    setDocArr(r?.data?.recordList);
                  });
                }}
                expandTrigger='hover'
                placeholder='请选择上级科室'
                options={getTreeOptionsByNo(depList) as any}
              />
            </TransferChange>
          )
        },
        {
          label: '姓名',
          name: 'doctorId',
          required: true,
          render: (
            <ArrSelect
              placeholder={'请选择医生姓名'}
              options={
                docArr?.map(item => ({
                  ...item,
                  label: item.name,
                  value: item.doctorId
                })) || []
              }
            />
          )
        },
        {
          label: '开播提醒时间',
          required: true,
          render: (
            <Space alignItems={'center'}>
              <FormItem
                name={'date'}
                rules={[
                  {
                    required: true,
                    message: `请选择日期`
                  }
                ]}>
                <DatePicker format={'YYYY-MM-DD'} disabledDate={disabledDate} />
              </FormItem>
              <FormItem
                name={'time'}
                rules={[
                  {
                    required: true,
                    message: `请选择时间`
                  }
                ]}>
                <TimePicker.RangePicker />
              </FormItem>
            </Space>
          )
        },
        {
          label: '时间',
          name: 'repeatStatus',
          render: (
            <TransferChange>
              {(onChange, value = []) => (
                <Space alignItems={'center'} justify={'space-between'}>
                  <Input
                    disabled
                    style={{ width: 200 }}
                    value={returnWeekDay(
                      dayjs(form.getFieldValue('date')).day()
                    )}
                  />
                  <Checkbox
                    onChange={v => {
                      onChange(v.target.checked);
                    }}
                    checked={value}>
                    重复
                  </Checkbox>
                </Space>
              )}
            </TransferChange>
          )
        },
        {
          label: '状态',
          name: 'status',
          required: true,
          render: (
            <Radio.Group>
              <Radio value={0}>启用</Radio>
              <Radio value={1}>停用</Radio>
            </Radio.Group>
          )
        }
      ]
    }),
    [docArr]
  );

  return (
    <TableList<ListType, any>
      tableTitle='直播提醒管理'
      exportExcelButton
      showTool={false}
      action={
        <Button
          type={'primary'}
          onClick={() => switchModalVisible({ repeatStatus: false })}>
          + 新建
        </Button>
      }
      getList={async ({ pagination: { current }, params }) => {
        const res = await apis.分页查询直播提醒列表.request({
          page: current,
          limit: 10,
          ...params
        });
        return {
          list: res.data.recordList,
          total: res.data.totalCount,
          pageNum: res.data.currentPage
        };
      }}
      pagination={{
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '40', '60', '100'],
        showTotal: total => `共 ${total} 条数据`
      }}
      columns={useMemo(() => {
        return [
          {
            title: '直播名称',
            dataIndex: 'liveName',
            search: true
          },
          {
            title: '科室',
            dataIndex: 'deptName',
            searchIndex: 'deptId',
            search: (
              <TransferChange
                mode={'cascade'}
                data={getTreeOptionsByNo(depList) as any}>
                <Cascader
                  onChange={v => {
                    form.resetFields(['doctorId']);
                    getDocList({
                      deptNo: v?.[v?.length - 1] + '',
                      type: '1'
                    }).then(r => {
                      setSearchDocArr(r?.data?.recordList);
                    });
                  }}
                  expandTrigger='hover'
                  placeholder='请选择上级科室'
                  options={getTreeOptionsByNo(depList) as any}
                />
              </TransferChange>
            )
          },
          {
            title: '姓名',
            dataIndex: 'doctorName',
            searchIndex: 'doctorId',
            search: (
              <ArrSelect
                options={
                  searchDocArr?.map(item => ({
                    ...item,
                    label: item.name,
                    value: item.doctorId
                  })) || []
                }
              />
            )
          },
          {
            title: '提醒时间',
            dataIndex: 'nextNoticeStartTime',
            render: (_, record: ListType) =>
              `${record.nextNoticeStartTime} --${dayjs(
                record.nextNoticeEndTime
              ).format('HH:mm:ss')}`
          },
          {
            title: '星期',
            dataIndex: 'weekDay'
          },
          {
            title: '是否重复',
            dataIndex: 'repeatStatus',
            render: (_, r: ListType) => (r.repeatStatus ? '重复' : '不重复'),
            search: (
              <ArrSelect
                placeholder={'请选择是否重复'}
                options={[
                  {
                    label: '重复',
                    value: 1
                  },
                  {
                    label: '不重复',
                    value: 0
                  }
                ]}
              />
            )
          },
          {
            title: '状态',
            dataIndex: 'status',
            render: (_, r: ListType) => (r.status ? '停用' : '启用'),
            search: (
              <ArrSelect
                placeholder={'请选择是否重复'}
                options={[
                  {
                    label: '已启用',
                    value: 0
                  },
                  {
                    label: '停用',
                    value: 1
                  }
                ]}
              />
            )
          },
          {
            title: '今日提醒状态',
            dataIndex: 'todayNoticeStatus',
            render: (_, r: ListType) =>
              r.todayNoticeStatus === 0
                ? '无需提醒'
                : r.todayNoticeStatus === 1
                ? '待提醒'
                : '已提醒'
          },
          {
            title: '已提醒次数',
            dataIndex: 'noticeTimes'
          },
          {
            title: '创建时间',
            dataIndex: 'createTime'
          },
          {
            title: '操作',
            fixed: 'right',
            width: 110,
            render: (_, record: ListType) => (
              <ActionsWrap>
                <LinkButton
                  onClick={() => {
                    getDetail(record.id).then(r => {
                      getDocList({
                        deptNo: r.data.deptId,
                        type: '1'
                      }).then(r2 => {
                        setDocArr(r2?.data?.recordList);
                        switchModalVisible({
                          ...r.data,
                          deptId: r.data.deptId,
                          repeatStatus: r.data.repeatStatus === 1,
                          date: moment(r.data.nextNoticeStartTime),
                          time: [
                            moment(r.data.nextNoticeStartTime),
                            moment(r.data.nextNoticeEndTime)
                          ]
                        });
                      });
                    });
                  }}>
                  查看
                </LinkButton>
                {record.status === 0 && (
                  <LinkButton
                    onClick={() => {
                      actionConfirm(() => {
                        return apis.直播提醒停用启用.request({
                          id: record.id,
                          status: 1
                        });
                      }, '停用');
                    }}>
                    停用
                  </LinkButton>
                )}
                {record.status === 1 && (
                  <LinkButton
                    onClick={() => {
                      actionConfirm(() => {
                        return apis.直播提醒停用启用.request({
                          id: record.id,
                          status: 0
                        });
                      }, '启用');
                    }}>
                    启用
                  </LinkButton>
                )}
                <LinkButton
                  onClick={() => {
                    actionConfirm(() => {
                      return apis.批量删除直播提醒.request(record.id);
                    }, '删除');
                  }}>
                  删除
                </LinkButton>
              </ActionsWrap>
            )
          }
        ];
      }, [
        depList,
        form,
        getDetail,
        getDocList,
        searchDocArr,
        switchModalVisible
      ])}
    />
  );
};
