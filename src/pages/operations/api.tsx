import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ListApiRequestParams } from 'parsec-admin/lib/types';

export interface ListType {
  createTime: string;
  deptId: string;
  deptName: string;
  doctorId: string;
  doctorName: string;
  hisId: number;
  hisName: string;
  id: number;
  lastNoticeEndTime: string;
  lastNoticeStartTime: string;
  liveName: string;
  liveImage: string;
  nextNoticeEndTime: string;
  nextNoticeStartTime: string;
  noticeTimes: number;
  operatorId: number;
  operatorName: string;
  repeatStatus: number;
  status: number;
  todayNoticeStatus: number;
  weekDay: string;
}

interface AddType {
  doctorId: string;
  deptId: string;
  nextNoticeStartTime: string;
  nextNoticeEndTime: string;
  weekDay?: string;
  repeatStatus: number;
  remark?: string;
  extFields?: string;
}

interface DetailType {
  hisId: number;
  id: number;
  noticeTimes: number;
  operatorId: number;
  repeatStatus: number;
  status: number;
  todayNoticeStatus: number;
  createTime: string;
  liveName: string;
  liveImage: string;
  hisName: string;
  deptId: string;
  deptName: string;
  doctorId: string;
  doctorName: string;
  lastNoticeStartTime: string;
  lastNoticeEndTime: string;
  nextNoticeStartTime: string;
  nextNoticeEndTime: string;
  weekDay: string;
}

export default {
  分页查询直播提醒列表: createApiHooks(
    (
      params: {
        deptId?: number | string;
        doctorId?: number | string;
        liveName?: number | string;
        repeatStatus?: number | string;
        status?: number | string;
      } & ListApiRequestParams
    ) =>
      request.get<{
        data: {
          recordList: ListType[];
          totalCount: number;
          pageCount: number;
          numPerPage: number;
          endPageIndex: number;
          currentPage: number;
        };
      }>('/intelligent/mch/intelligent/live-notice/page', {
        params
      })
  ),
  查询直播提醒详情: createApiHooks(id =>
    request.get<{ data: DetailType }>(
      `/intelligent/mch/intelligent/live-notice/${id}`
    )
  ),
  新增直播提醒: createApiHooks((params: AddType) =>
    request.post<{ data: DetailType }>(
      `/intelligent/mch/intelligent/live-notice`,
      params,
      {
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        }
      }
    )
  ),
  修改直播提醒: createApiHooks((params: { id: string } & AddType) =>
    request.put<{ data: DetailType }>(
      `/intelligent/mch/intelligent/live-notice`,
      params
    )
  ),
  批量删除直播提醒: createApiHooks(ids =>
    request.delete(`/intelligent/mch/intelligent/live-notice/${ids}`)
  ),
  直播提醒停用启用: createApiHooks((params: { id: number; status: number }) =>
    request.put(`/intelligent/mch/intelligent/live-notice/enable`, params)
  )
};
