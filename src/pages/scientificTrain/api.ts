import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ListApiResponseData, ListApiRequestParams } from '@apiHooks';
export interface ApiResponse<D> {
  code: 0 | 200 | 999; // 999用户未登录
  msg: string | null;
  data?: D;
}
export interface DeptItem {
  id: number;
  hisId: 40009;
  name: '儿科';
  no: '10001';
  sortNo: 0;
  employeeCount: null;
  tel: '023-8973495873';
  status: 1;
  pid: 85;
  pathCode: '/p85/p86/';
  hisType: 2;
  isSummary: 1;
  address: '';
  createTime: '2021-08-23 08:53:48';
  updateTime: '2021-08-23 08:53:48';
  children: DeptItem[];
}
/**
 * 类型
 */
export interface TypeItem {
  id: number;
  status: 0 | 1;
  typeName: string;
  typeSign: 'train' | 'cooperate';
  createTime: string;
}
const typeApis = {
  typeList: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        typeSign: string;
        status?: number;
      }
    ) =>
      request.get<ListApiResponseData<TypeItem>>(
        '/mch/cooperate/train-cooperate/trainCooperateTypeList',
        {
          params: data
        }
      )
  ),
  typeDel: createApiHooks((data: { id: string }) =>
    request.delete(`/mch/cooperate/train-cooperate/deleteType?id=${data.id}`)
  ),
  typeChangeStatus: createApiHooks((data: { id: string; status: number }) =>
    request.get(`/mch/cooperate/train-cooperate/statusType`, { params: data })
  ),
  typeAddAndEdit: createApiHooks((data: { title: string }) =>
    request.post(`/mch/cooperate/train-cooperate/saveOrUpdateType`, data)
  )
};

/**
 * 内容
 */
export interface ContentItem {
  id?: number;
  title: string;
  status: 0 | 1;
  createTime: string;
  updateTime: string;
  typeId: number;
  typeName: string;
  publisherId: number;
  publisher: string;
  typeSign: string;
  content: string;
}
const contentApis = {
  contentList: createApiHooks(
    (data: ListApiRequestParams & { hisId?: string; typeSign: string }) =>
      request.get<ListApiResponseData<ContentItem>>(
        '/mch/cooperate/train-cooperate/trainCooperateList',
        {
          params: data
        }
      )
  ),
  contentDetail: createApiHooks((data: { id: string }) =>
    request.get<ApiResponse<ContentItem>>(
      `/mch/cooperate/train-cooperate/trainCooperateDetail?id=${data.id}`
    )
  ),
  contentDel: createApiHooks((data: { id: string }) =>
    request.delete(
      `/mch/cooperate/train-cooperate/deleteTrainCooperate?id=${data.id}`
    )
  ),
  contentChangeStatus: createApiHooks((data: { id: string; status: number }) =>
    request.get(`/mch/cooperate/train-cooperate/statusTrainCooperate`, {
      params: data
    })
  ),
  contentAddAndEdit: createApiHooks((data: ContentItem) =>
    request.post(
      `/mch/cooperate/train-cooperate/saveOrUpdateTrainCooperate`,
      data
    )
  )
};

export default {
  ...typeApis,
  ...contentApis
};
