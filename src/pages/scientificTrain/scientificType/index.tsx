import React from 'react';
import MyTableList from '@components/myTableList';
import {
  ActionsWrap,
  LinkButton,
  actionConfirm,
  ArrSelect,
  useModal,
  handleSubmit
} from 'parsec-admin';
import { Button, Input } from 'antd';
import useApi from '../api';
import { TypeSign, typeOptions } from '../enum';

const ScientificType: React.FC = () => {
  const openEditModal = useModal(
    ({ id }) => ({
      title: id ? '编辑' : '添加',
      onSubmit: ({ ...params }: any) => {
        const obj = {
          ...params,
          typeSign: TypeSign.COOPERATE
        };
        return handleSubmit(
          () => useApi.typeAddAndEdit.request(obj),
          `${params.id ? '修改' : '新增'}`
        );
      },
      items: [
        {
          name: 'id',
          render: false
        },
        {
          label: '科研协作类型',
          name: 'typeName',
          required: true,
          render: () => {
            return <Input maxLength={10} />;
          }
        }
      ]
    }),
    []
  );
  return (
    <MyTableList
      getList={({ pagination: { current }, params }) => {
        return useApi.typeList.request({
          ...params,
          pageNum: current,
          typeSign: TypeSign.COOPERATE
        });
      }}
      showTool={false}
      tableTitle='科研协作类型'
      action={
        <Button type={'primary'} onClick={() => openEditModal()}>
          添加
        </Button>
      }
      columns={[
        {
          title: '协作类型',
          dataIndex: 'typeName',
          search: true
        },
        {
          title: '状态',
          dataIndex: 'status',
          search: <ArrSelect options={typeOptions} />,
          render: v => {
            return v === 1 ? '禁用' : '启用';
          }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          fixed: 'right',
          align: 'left',
          render: (_, record: any) => {
            return (
              <ActionsWrap max={99}>
                <LinkButton
                  onClick={() => {
                    openEditModal({ id: record.id, typeName: record.typeName });
                  }}>
                  编辑
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.typeChangeStatus.request({
                          id: record.id,
                          status: record.status === 1 ? 0 : 1
                        }),
                      record.status === 1 ? '启用' : '禁用'
                    );
                  }}>
                  {record.status === 1 ? '启用' : '禁用'}
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.typeDel.request({
                          id: record.id
                        }),
                      '删除'
                    );
                  }}>
                  删除
                </LinkButton>
              </ActionsWrap>
            );
          }
        }
      ]}
    />
  );
};

export default ScientificType;
