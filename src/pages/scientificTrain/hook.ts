import { useState, useEffect } from 'react';
import apis from './api';
import { is } from 'cypress/types/bluebird';
interface Option {
  label: string;
  value: string | number;
}
export function useGetType(typeSign: string, isFilter = false) {
  const [typeList, setTypeList] = useState<Option[]>([]);
  useEffect(() => {
    const params: any = {
      typeSign: typeSign,
      pageNum: 1,
      numPerPage: 999
    };
    if (isFilter) {
      params.status = 0;
    }
    apis.typeList.request(params).then(res => {
      const list = (res.data?.recordList || []).map(v => {
        return {
          label: v.typeName,
          value: v.id
        };
      });
      setTypeList(list);
    });
  }, []);
  return { typeList };
}
