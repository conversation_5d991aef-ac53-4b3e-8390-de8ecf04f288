import React from 'react';
import MyTableList from '@components/myTableList';
import {
  ActionsWrap,
  LinkButton,
  actionConfirm,
  ArrSelect
} from 'parsec-admin';
import { Button } from 'antd';
import useApi from '../api';
import { useHistory } from 'react-router-dom';
import { TypeSign, contentOptions } from '../enum';
import { useGetType } from '../hook';

const ScientificContent: React.FC = () => {
  const history = useHistory();
  const { typeList } = useGetType(TypeSign.COOPERATE);
  const toDetail = (id?: string | number) => {
    const url = id
      ? `/content/scientificTrain/scientificContent/edit/${id}`
      : '/content/scientificTrain/scientificContent/add';
    history.push(url);
  };
  return (
    <MyTableList
      getList={({ pagination: { current }, params }) => {
        return useApi.contentList.request({
          ...params,
          pageNum: current,
          typeSign: TypeSign.COOPERATE
        });
      }}
      showTool={false}
      tableTitle='科研协作列表'
      action={
        <Button type={'primary'} onClick={() => toDetail()}>
          新增科研协作
        </Button>
      }
      columns={[
        {
          title: '科研协作标题',
          dataIndex: 'title',
          search: true
        },
        {
          title: '科研协作类型',
          dataIndex: 'typeName',
          searchIndex: 'typeId',
          search: <ArrSelect options={typeList} />
        },
        {
          title: '发布人',
          dataIndex: 'publisher'
        },
        {
          title: '协作状态',
          dataIndex: 'status',
          search: <ArrSelect options={contentOptions} />,
          render: v => {
            return v === 0 ? '上架' : '下架';
          }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          fixed: 'right',
          align: 'left',
          render: (_, record: any) => {
            return (
              <ActionsWrap max={99}>
                <LinkButton onClick={() => toDetail(record?.id)}>
                  编辑
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.contentChangeStatus.request({
                          id: record.id,
                          status: record.status === 1 ? 0 : 1
                        }),
                      record.status === 0 ? '下架' : '上架'
                    );
                  }}>
                  {record.status === 0 ? '下架' : '上架'}
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.contentDel.request({
                          id: record.id
                        }),
                      '删除'
                    );
                  }}>
                  删除
                </LinkButton>
              </ActionsWrap>
            );
          }
        }
      ]}
    />
  );
};

export default ScientificContent;
