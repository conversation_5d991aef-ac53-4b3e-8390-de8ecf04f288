import React from 'react';
import {
  ArrSelect,
  DetailLayout,
  FormDescriptions,
  handleSubmit,
  Editor
} from 'parsec-admin';
import { Button, Space, Form, Input } from 'antd';
import styled from 'styled-components';
import useApi from '../api';
import { useParams, useHistory } from 'react-router-dom';
import { TypeSign } from '../enum';
import { useGetType } from '../hook';

const TrainContentEdit: React.FC = () => {
  const [form] = Form.useForm();
  const params = useParams<{ id?: string }>();
  const history = useHistory();
  const editId = params?.id;
  const { typeList } = useGetType(TypeSign.COOPERATE, true);

  const queryDetail = async (id: string) => {
    const res = await useApi.contentDetail.request({
      id
    });
    form.setFieldsValue({ ...(res.data || {}) });
  };

  const submit = async () => {
    const res = await form.validateFields();
    const obj = { ...res, typeSign: TypeSign.COOPERATE };
    const apiObj = editId ? { id: editId, ...obj } : { ...obj };
    handleSubmit(() =>
      useApi.contentAddAndEdit.request(apiObj).then(() => {
        back();
      })
    );
  };

  const back = () => {
    history.goBack();
  };

  React.useEffect(() => {
    if (!editId) return;
    queryDetail(editId);
  }, []);
  return (
    <DetailLayout
      headerProps={false}
      cardsProps={[
        {
          title: editId ? '编辑培训' : '新增培训',
          children: (
            <Space align='start'>
              <FormDescriptions
                form={form}
                column={3}
                edit={true}
                formProps={{
                  requiredMark: true
                }}
                items={[
                  {
                    label: '科研协作标题',
                    required: true,
                    name: 'title'
                  },
                  {
                    label: '科研协作类型',
                    name: 'typeId',
                    formItemProps: {
                      render: (
                        <ArrSelect showSearch={false} options={typeList} />
                      )
                    },
                    required: true
                  },
                  {
                    label: '发布人',
                    name: 'publisher',
                    required: true,
                    formItemProps: {
                      render: <Input maxLength={5} placeholder='请输入发布人' />
                    }
                  },
                  {
                    label: '科研协作内容',
                    required: true,
                    name: 'content',
                    formItemProps: {
                      render: <EditWrapper />
                    }
                  }
                ]}
              />
            </Space>
          )
        },
        {
          children: (
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button style={{ marginRight: '10px' }} onClick={back}>
                取消
              </Button>
              <Button type='primary' onClick={submit}>
                确定
              </Button>
            </div>
          )
        }
      ]}></DetailLayout>
  );
};

export default TrainContentEdit;

const EditWrapper = styled(Editor)`
  .w-e-toolbar {
    z-index: 996 !important;
  }
`;
