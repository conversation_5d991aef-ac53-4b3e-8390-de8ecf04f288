import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ApiResponse, ListApiResponseData } from '@configs/d';
interface ReferralPerson {
  introducerName: '@word(3)'; //员工姓名
  introducerIdNo: '@word(13)'; //员工身份证号
  introducerMobile: '@word(11)'; //员工手机号
  showStatus: '@integer(0,1)'; //显示状态，0：关闭，1：开启
}
interface ReferralPerson2 extends ReferralPerson {
  id: '@natural'; //
}
export default {
  员工查询: createApiHooks(
    (params: {
      introducerName?: string;
      introducerIdNo?: string;
      createTimeStart?: string;
      createTimeEnd?: string;
      showStatus?: 0 | 1;
      page?: number;
      limit?: number;
    }) =>
      request.get<
        ListApiResponseData<
          | ReferralPerson2
          | {
              hisId: '@natural'; //医院id
              orderNum: number; //成功推广订单数
              createTime: '@datetime'; //创建时间
              qrUrl: '@image'; //员工二维码
              updateTime: '@datetime'; //更新时间
            }
        >
      >('/intelligent/mch/intelligent/introducer', {
        params
      })
  ),
  员工删除: createApiHooks((params: { id: string }) =>
    request.delete<ApiResponse<any>>(
      `/intelligent/mch/intelligent/introducer/${params.id}`
    )
  ),
  员工显示状态切换: createApiHooks(
    (params: { id: string; showStatus: 0 | 1 }) =>
      request.put<ApiResponse<any>>(
        `/intelligent/mch/intelligent/introducer/show-status`,
        params
      )
  ),
  员工新增: createApiHooks((params: ReferralPerson) =>
    request.post<ApiResponse<any>>(
      `/intelligent/mch/intelligent/introducer`,
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  员工修改: createApiHooks((params: ReferralPerson2) =>
    request.put<ApiResponse<any>>(
      `/intelligent/mch/intelligent/introducer`,
      params
    )
  ),
  员工预约订单查询: createApiHooks(
    (params: {
      patientName?: string;
      patientIdNo?: string;
      introducerCode?: string;
      introducerName?: string;
      registerTimeStart?: string;
      registerTimeEnd?: string;
      introduceStatus?: string; //介绍状态，E：待生效，R：待挂号，S：成功，F：失败，I：失效
      page?: number;
      limit?: number;
    }) =>
      request.get<
        ListApiResponseData<{
          id: '@natural'; //id
          hisId: '@natural'; //医院id
          patientName: '@word(3)'; //患者姓名
          patientIdNo: '@word(13)'; //患者身份证号
          patientMobile: '@word(11)'; //患者手机号
          introducerCode: '@word(4)'; //员工码
          introducerName: '@word(3)'; //员工姓名
          introduceStatus: '@pick(E,R,S,F,I)'; //介绍状态，E：待生效，R：待挂号，S：成功，F：失败，I：失效
          deptName: '@word(5)'; //挂号科室
          registerTime: '@datetime'; //挂号时间
          takeRegisterTime: '@datetime'; //取号时间
          appointmentTime: '@datetime'; //预约时间
        }>
      >('/intelligent/mch/intelligent/introduce/order', {
        params
      })
  ),
  员工订单导出: createApiHooks(
    (params: {
      patientName?: string; // 患者姓名
      patientIdNo?: string; // 患者证件号码
      introducerCode?: string; // 员工码
      introducerName?: string; // 员工姓名
      introduceStatus?: string; //状态
      registerTimeStart?: string; // 挂号时间-开始
      registerTimeEnd?: string; // 挂号时间-结束
    }) =>
      request.get<any>(
        '/intelligent/mch/intelligent/introduce/order/order-export',
        {
          params
        }
      )
  )
};
