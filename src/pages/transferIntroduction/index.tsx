import MyTableList from '@components/myTableList';
import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  DayRangePicker,
  handleSubmit,
  LinkButton,
  useModal
} from 'parsec-admin';
import React, { useState } from 'react';
import { Button, Switch, Input, Radio, Modal, message } from 'antd';
import permisstion from '@utils/permisstion';
import apis from './api';
import Qrcode from '@components/qrcode';

export default () => {
  const [open, setOpen] = useState(false);
  const [showUrl, setShowUrl] = useState<string>('');
  const formModel = useModal(({ id, introducerName, introducerIdNo }) => {
    return {
      title: `${id ? '编辑' : '新增'}员工`,
      onSubmit: values =>
        handleSubmit(() =>
          id
            ? apis.员工修改.request({ ...values, id })
            : apis.员工新增.request(values)
        ),
      items: [
        {
          label: '姓名',
          name: 'introducer<PERSON><PERSON>',
          required: true,
          render: id ? introducerName : <Input placeholder={'请输入姓名'} />
        },
        {
          label: '身份证号',
          name: 'introducerIdNo',
          required: false,
          render: id ? (
            introducerIdNo
          ) : (
            <Input placeholder={'请输入身份证号'} />
          ),
          formItemProps: {
            rules: [
              {
                pattern: /^[1-9]\d{5}(19|20)\d{2}((0[1-9]|1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
                message: '请输入正确的身份证号'
              }
            ]
          }
        },
        {
          label: '电话号',
          name: 'introducerMobile',
          required: true,
          formItemProps: {
            rules: [
              {
                required: true,
                message: '请输入手机号'
              },
              {
                pattern: /^1(3|4|5|7|8|9)\d{9}$/,
                message: '请输入正确的手机号'
              }
            ]
          }
        },
        {
          label: '显示状态',
          name: 'showStatus',
          required: true,
          render: (
            <Radio.Group>
              <Radio value={1}>开启</Radio>
              <Radio value={0}>关闭</Radio>
            </Radio.Group>
          )
        }
      ]
    };
  });
  return (
    <>
      <MyTableList
        tableTitle='员工列表'
        getList={({ pagination: { current = 1 }, params }) => {
          delete params.sort;
          return apis.员工查询.request({
            ...params,
            page: current as number,
            limit: 10
          });
        }}
        action={
          permisstion.TransferTheOperationOfTheIntroducer && (
            <Button type={'primary'} onClick={() => formModel()}>
              新增
            </Button>
          )
        }
        columns={[
          {
            title: '员工姓名',
            searchIndex: 'introducerName',
            search: true
          },
          {
            title: '姓名',
            dataIndex: 'introducerName',
            width: 100
          },
          {
            title: '证件号',
            dataIndex: 'introducerIdNo',
            search: true
          },
          {
            title: '手机号',
            dataIndex: 'introducerMobile'
          },
          {
            title: '已成功推广订单量',
            dataIndex: 'orderNum',
            width: 100
          },
          {
            title: '显示状态',
            dataIndex: 'showStatus',
            search: <ArrSelect options={{ 1: '开启', 0: '关闭' }} />,
            width: 100,
            render: (v, record: any) => (
              <Switch
                checkedChildren='开启'
                unCheckedChildren='关闭'
                checked={v}
                onChange={checked => {
                  handleSubmit(() => {
                    return apis.员工显示状态切换.request({
                      id: record?.id || '',
                      showStatus: checked ? 1 : 0
                    });
                  }, `${checked ? '开启' : '关闭'}`);
                }}
              />
            )
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                valueFormat={'YYYY-MM-DD HH:mm:ss'}
                disabledDate={current => {
                  return current && current.valueOf() > Date.now();
                }}
              />
            ),
            searchIndex: ['createTimeStart', 'createTimeEnd']
          },
          {
            title: '操作',
            fixed: 'right',
            render: ({ id, qrUrl, showStatus }, record: any) => (
              <ActionsWrap>
                <LinkButton
                  onClick={() => {
                    if (showStatus) {
                      setShowUrl(qrUrl);
                      setOpen(true);
                    } else {
                      message.error('员工的显示状态为关闭，无法显示');
                    }
                  }}>
                  生成二维码
                </LinkButton>
                {permisstion.TransferTheOperationOfTheIntroducer ? (
                  <ActionsWrap>
                    <LinkButton onClick={() => formModel(record)}>
                      编辑
                    </LinkButton>
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () => apis.员工删除.request({ id }),
                          '删除'
                        );
                      }}>
                      删除
                    </LinkButton>
                  </ActionsWrap>
                ) : null}
              </ActionsWrap>
            )
          }
        ]}
      />
      <Modal
        destroyOnClose
        bodyStyle={{ display: 'flex', justifyContent: 'center' }}
        visible={open}
        footer={null}
        onCancel={() => setOpen(false)}>
        <Qrcode url={showUrl} size={220} />
      </Modal>
    </>
  );
};
