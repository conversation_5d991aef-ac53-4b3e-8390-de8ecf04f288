import MyTableList from '@components/myTableList';
import { ArrSelect, DayRangePicker } from 'parsec-admin';
import React, { useState } from 'react';
// import { saveAs } from 'file-saver';
// import moment from 'moment';
import apis from '@pages/transferIntroduction/api';
import { Button } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import QueryString from 'qs';
const IntroduceStatus = {
  E: '待生效',
  R: '待挂号',
  S: '成功',
  F: '失败',
  I: '失效'
};
const isDev =
  // 渝康健判断
  // window.location.href.includes('localhost') ||
  window.location.href.includes('tihs');
const preFix = isDev
  ? 'https://tihs.cqkqinfo.com/test-api/api/intelligent/mch/intelligent/introduce/order/order-export?'
  : 'https://ihs.cqkqinfo.com/api/intelligent/mch/intelligent/introduce/order/order-export?';

export default () => {
  const [queryParams, setQueryParams] = useState({} as any);
  const [exportLoading, setExportLoading] = useState(false);
  return (
    <MyTableList
      tableTitle='员工预约订单列表'
      action={
        <Button
          icon={<ExportOutlined />}
          loading={exportLoading}
          onClick={() => {
            // console.log('xx');
            // return;
            setExportLoading(true);
            setTimeout(() => {
              setExportLoading(false);
            }, 1500);
            window.location.href = `${preFix}${QueryString.stringify(
              queryParams
            )}`;
          }}
          type='primary'>
          导出
        </Button>
      }
      getList={({ pagination: { current = 1 }, params }) => {
        setQueryParams({ ...params });
        return apis.员工预约订单查询.request({
          ...params,
          page: current as number,
          limit: 10
        });
      }}
      columns={[
        {
          title: '患者姓名',
          dataIndex: 'patientName',
          search: true
        },
        {
          title: '手机号',
          dataIndex: 'patientMobile'
        },
        {
          title: '证件号',
          dataIndex: 'patientIdNo',
          search: true
        },
        {
          title: '员工姓名',
          dataIndex: 'introducerName',
          search: true
        },
        {
          title: '员工码',
          dataIndex: 'introducerCode',
          search: true
        },
        {
          title: '录入时间',
          dataIndex: 'creatTime',
          search: (
            <DayRangePicker
              placeholder={['开始时间', '结束时间']}
              valueFormat={'YYYY-MM-DD HH:mm:ss'}
              disabledDate={current => {
                return current && current.valueOf() > Date.now();
              }}
            />
          ),
          searchIndex: ['createTimeStart', 'createTimeEnd']
        },
        {
          title: '订单状态',
          dataIndex: 'introduceStatus',
          search: <ArrSelect options={IntroduceStatus} />,
          render: v => IntroduceStatus[v]
        },
        {
          title: '挂号时间',
          dataIndex: 'registerTime',
          width: 120
        },
        {
          title: '取号时间',
          dataIndex: 'takeRegisterTime',
          width: 120
        },
        {
          title: '预约时间',
          dataIndex: 'appointmentTime',
          width: 120
        },
        {
          title: '订单时间',
          search: (
            <DayRangePicker
              placeholder={['开始时间', '结束时间']}
              valueFormat={'YYYY-MM-DD HH:mm:ss'}
              disabledDate={current => {
                return current && current.valueOf() > Date.now();
              }}
            />
          ),
          searchIndex: ['registerTimeStart', 'registerTimeEnd'],
          width: 120
        }
      ]}
    />
  );
};
