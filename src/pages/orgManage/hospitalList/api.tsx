import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiResponseData,
  ListApiRequestParams,
  ApiResponse
} from '@apiHooks';

export interface HospitalList {
  id: '@natural'; //
  hisId: '@natural'; //上级医院id
  name: '@cword(6)'; //医院名称
  level: "@pick('三级甲等','三级乙等','三级未定级')"; //医院级别
  state: '@integer(0, 1)'; //医院状态(0停用 1启用)
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //更新时间
}
export interface HospitalListParams {
  name?: string; //医疗机构名称
  state?: number; //按状态搜索
}
export const stateOptions = {
  0: '停用',
  1: '启用'
};

export default {
  // 机构列表
  getHospital: createApiHooks(
    (params: ListApiRequestParams & HospitalListParams) =>
      request.get<ListApiResponseData<HospitalList>>(
        '/mch/cooperate/cooperate-hospital',
        {
          params
        }
      )
  ),
  // 查询全部可用机构列表
  getAllHospital: createApiHooks((params: { searchKey?: string }) =>
    request.get<ApiResponse<HospitalList[]>>(
      '/mch/cooperate/cooperate-hospital/brief',
      {
        params
      }
    )
  ),
  // 停用启用机构
  putOnOff: createApiHooks((params: { id: string; state: number }) =>
    request.put<ApiResponse<undefined>>(
      `/mch/cooperate/cooperate-hospital/state/${params.id}`,
      params
    )
  ),
  // 删除机构
  delHospital: createApiHooks((params: { id: string }) =>
    request.delete<ApiResponse<undefined>>(
      `/mch/cooperate/cooperate-hospital/${params.id}`
    )
  ),
  // 根据id查询机构详情
  hospitalDetail: createApiHooks((params: { id: string }) =>
    request.get<ApiResponse<HospitalList>>(
      `/mch/cooperate/cooperate-hospital/${params.id}`
    )
  ),
  // 导入机构excel
  importHospital: createApiHooks((file: File) => {
    const form = new FormData();
    form.append('file', file);
    return request.post<ApiResponse<undefined>>(
      `/mch/cooperate/cooperate-hospital/import`,
      form,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
  }),
  // 编辑机构
  updateHospital: createApiHooks((params: HospitalList) =>
    request.put<ApiResponse<undefined>>(
      `/mch/cooperate/cooperate-hospital`,
      params
    )
  ),
  // 新增机构
  addHospital: createApiHooks((params: HospitalList) =>
    request.post<ApiResponse<undefined>>(
      `/mch/cooperate/cooperate-hospital`,
      params
    )
  ),
  // 下载机构导入模板
  downloadTemplate: createApiHooks(() =>
    request.get<Blob>(`/mch/cooperate/cooperate-hospital/template`, {
      responseType: 'blob'
    })
  ),
  /**
   * 获取智慧医院首页二维码
   * mch/cooperate/cooperate-hospital/qrcodeLink?hospitalId=1234
   */
  getQrCodelink: createApiHooks((params: { hospitalId: string }) =>
    request.get<ApiResponse<any>>(
      `/mch/cooperate/cooperate-hospital/qrcodeLink`,
      { params }
    )
  )
};
