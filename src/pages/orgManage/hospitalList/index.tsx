import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  DateShow,
  handleSubmit,
  LinkButton,
  useModal
} from 'parsec-admin';
import React from 'react';
import apis, { stateOptions } from './api';
import { Button, message, Modal } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
// import { PickerData } from 'antd-mobile/lib/picker/PropsType';
// import storage from '@src/utils/storage';
import MyTableList from '@components/myTableList';

// 医疗机构等次
// const HospitalLevel =
//   JSON.parse(storage.get('enums') || '{}').HospitalLevel || [];

export default () => {
  // 修改、新增
  const switchModalVisible = useModal(data => ({
    title: data.id ? '编辑' : '新增',
    onSubmit: ({ ...values }: any) =>
      handleSubmit(() => {
        return data.id
          ? apis.updateHospital.request({ ...values })
          : apis.addHospital.request({ ...values });
      }),
    items: [
      {
        label: 'id',
        name: 'id',
        render: false
      },
      {
        label: '医院名称',
        name: 'name',
        required: true
      },
      {
        label: '等级',
        name: 'level',
        required: true
      }
    ]
  }));

  // 定义 modal 的状态和内容
  const [modalVisible, setModalVisible] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [qrcodeUrl, setQrcodeUrl] = React.useState<string | null>(null);

  // 获取二维码
  const getQrcode = async (id: any) => {
    setLoading(true);
    try {
      const res = await apis.getQrCodelink.request({ hospitalId: id });
      if (res?.code === 0) {
        const url = res?.data?.qrcode;
        if (url) {
          setQrcodeUrl(url);
          setModalVisible(true);
        } else {
          message.error('未找到二维码');
        }
      } else {
        message.error('获取二维码失败');
      }
    } catch (error) {
      message.error('' + error);
    } finally {
      setLoading(false);
    }
  };

  // 关闭 modal
  const handleCloseModal = () => {
    setModalVisible(false);
    setQrcodeUrl(null);
  };

  return (
    <>
      <MyTableList
        tableTitle={'医疗机构列表'}
        showTool={false}
        showExpand={false}
        action={
          <Button
            type='primary'
            onClick={() => switchModalVisible()}
            ghost
            icon={<PlusOutlined />}>
            添加
          </Button>
        }
        scroll={{ x: 1800 }}
        columns={[
          {
            title: '医院名称',
            dataIndex: 'name',
            align: 'center',
            width: 180,
            search: true,
            searchSort: 3
          },
          {
            title: '等级',
            dataIndex: 'level',
            align: 'center',
            width: 120
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            align: 'center',
            width: 120,
            render: v => <DateShow>{v}</DateShow>
          },
          {
            title: '状态',
            dataIndex: 'state',
            align: 'center',
            render: v => stateOptions[v] || '-',
            search: <ArrSelect options={stateOptions} />,
            width: 80
          },
          {
            title: '操作',
            excelRender: false,
            align: 'center',
            fixed: 'right',
            width: 160,
            render: (v, record: any) => (
              <ActionsWrap max={8}>
                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () => {
                        return apis.putOnOff.request({
                          id: record.id,
                          state: record.state === 1 ? 2 : 1
                        });
                      },
                      record.state === 1 ? '停用' : '启用'
                    );
                  }}>
                  {record.state === 1 ? '停用' : '启用'}
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    switchModalVisible({
                      ...record
                    });
                  }}>
                  编辑
                </LinkButton>
                <LinkButton
                  style={{ color: 'red' }}
                  onClick={() => {
                    actionConfirm(() => {
                      return apis.delHospital.request({
                        id: record.id
                      });
                    }, '删除');
                  }}>
                  删除
                </LinkButton>
                <LinkButton onClick={() => getQrcode(record?.id)}>
                  生成智慧医院首页二维码
                </LinkButton>
              </ActionsWrap>
            )
          }
        ]}
        getList={({ params }) => {
          return apis.getHospital.request({
            ...params
          });
        }}
      />
      {modalVisible && (
        <Modal
          title='医院首页二维码'
          visible={modalVisible}
          onCancel={handleCloseModal}
          footer={null}>
          <div
            style={{
              textAlign: 'center',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center'
            }}>
            {loading ? (
              <p>加载中...</p>
            ) : (
              <>
                {qrcodeUrl ? (
                  <>
                    <img
                      src={qrcodeUrl}
                      alt='二维码'
                      style={{ width: '100%', maxWidth: '300px' }}
                    />
                    {/* <a href={qrcodeUrl} download='qrcode.png'>
                      <Button type='primary' style={{ marginTop: '10px' }}>
                        下载二维码
                      </Button>
                    </a> */}
                    <Button
                      type='primary'
                      style={{ marginTop: '10px' }}
                      onClick={async () => {
                        const response = await fetch(qrcodeUrl);
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'qrcode.png';
                        document.body.appendChild(a);
                        a.click();
                        a.remove();
                        window.URL.revokeObjectURL(url);
                      }}>
                      下载二维码
                    </Button>
                  </>
                ) : (
                  <p>未找到二维码</p>
                )}
              </>
            )}
          </div>
        </Modal>
      )}
    </>
  );
};
