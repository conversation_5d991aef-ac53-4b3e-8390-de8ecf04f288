import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiResponseData,
  ListApiRequestParams,
  ApiResponse
} from '@apiHooks';

export interface DeptList {
  id: '@natural'; //id
  hisId: '@natural'; //上级医院id
  cpHospitalId: '@natural'; //医联体医院id
  cpHospitalName: '@cword(6)'; //医联体医院名称
  no: ''; //科室编码
  name: '@cword(6)'; //科室名称
  summary: ''; //科室简介
  tel: '138@natural(00000000,99999999)'; //电话
  sortNo: '@integer(0, 1000000)'; //科室排序 从小到大排
  img: '@image(100x100)'; //科室图片地址
  initials: ''; //拼音首字母
  state: '@integer(0, 1)'; //0停用 1启用
  skill: ''; //科室专长
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //更新时间
  standardDeptNo: '11.10'; // 标准科室编码
  standardDeptName: '内科'; // 标准科室名称
}
export interface DeptListParams {
  name?: string; //科室名称
  cpHospitalId?: string; //医院id
  state?: string; //状态
}

export const stateOptions = {
  0: '停用',
  1: '启用'
};

export default {
  // 分页查询科室列表
  getDept: createApiHooks((params: ListApiRequestParams & DeptListParams) =>
    request.get<ListApiResponseData<DeptList>>(
      '/mch/cooperate/cooperate-dept',
      {
        params
      }
    )
  ),
  // 查询全部可用科室列表（按医院查询）
  getAllDept: createApiHooks(
    (params: {
      hospitalId?: string | number | undefined;
      deptName?: string | number | undefined;
    }) =>
      request.get<ApiResponse<DeptList>>(
        '/mch/cooperate/cooperate-dept/by-hospital',
        {
          params
        }
      )
  ),
  // 停用启用科室
  putOnOff: createApiHooks((params: { id: string; state: number }) =>
    request.put<ApiResponse<undefined>>(
      `/mch/cooperate/cooperate-dept/state/${params.id}`,
      params
    )
  ),
  // 删除科室
  delDept: createApiHooks((params: { id: string }) =>
    request.delete<ApiResponse<undefined>>(
      `/mch/cooperate/cooperate-dept/${params.id}`
    )
  ),
  // 根据id查询科室详情
  deptDetail: createApiHooks((params: { id: string }) =>
    request.get<ApiResponse<DeptList>>(
      `/mch/cooperate/cooperate-dept/${params.id}`
    )
  ),
  // 编辑科室
  updateDept: createApiHooks((params: DeptList) =>
    request.put<ApiResponse<undefined>>(`/mch/cooperate/cooperate-dept`, params)
  ),
  // 新增科室
  addDept: createApiHooks((params: DeptList) =>
    request.post<ApiResponse<undefined>>(
      `/mch/cooperate/cooperate-dept`,
      params
    )
  ),
  // 标准科室字典
  standard: createApiHooks(() =>
    request.get<ApiResponse<{ [key: string]: string }>>(
      '/mch/cooperate/cooperate-dept/standard'
    )
  )
};
