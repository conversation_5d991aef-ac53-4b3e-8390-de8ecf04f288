import { Button } from 'antd';
import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  DateShow,
  handleSubmit,
  LinkButton,
  useModal
} from 'parsec-admin';
import apis, { stateOptions } from './api';
import apis1 from '@pages/orgManage/hospitalList/api';
import { PlusOutlined } from '@ant-design/icons';
import MyTableList from '@components/myTableList';

export default () => {
  // 获取所有医院
  const { data: allHospital } = apis1.getHospital({
    needInit: true,
    params: { pageNum: 1, numPerPage: 9999 }
  });
  // 标准科室字典
  const { data: allStandard } = apis.standard({
    needInit: true
  });
  // 修改、新增
  const switchModalVisible = useModal(data => ({
    title: data.id ? '编辑' : '新增',
    onSubmit: ({ ...values }: any) => {
      // values.standardDeptName = [].find(item => item.standardDeptNo === values.standardDeptNo)?.standardDeptName;
      return handleSubmit(() => {
        return data.id
          ? apis.updateDept.request({ ...values })
          : apis.addDept.request({ ...values, state: 1 });
      });
    },
    items: [
      {
        label: 'id',
        name: 'id',
        render: false
      },
      {
        label: '所属医疗机构',
        name: 'cpHospitalId',
        render: (
          <ArrSelect
            showSearch={false}
            options={
              allHospital.data?.recordList.map(item => ({
                label: item.name,
                value: item.id
              })) || []
            }
          />
        ),
        required: true
      },
      {
        label: '科室名称',
        name: 'name',
        // render: <ArrSelect options={unitOption} />,
        required: true
      },
      {
        label: '科室编码',
        name: 'no',
        formItemProps: {
          rules: [
            {
              pattern: /^[0-9A-Za-z]+$/,
              message: '科室编码必须由字母数字组成'
            }
          ]
        },
        required: true
      },
      {
        label: '标准科室',
        name: 'standardDeptNo',
        render: <ArrSelect options={allStandard.data || {}} />,
        required: true
      }
    ]
  }));
  return (
    <MyTableList
      tableTitle={'科室列表'}
      showTool={false}
      showExpand={false}
      scroll={{ x: 1500 }}
      action={
        <Button
          type='primary'
          onClick={() => switchModalVisible()}
          ghost
          icon={<PlusOutlined />}>
          添加
        </Button>
      }
      columns={[
        {
          title: '科室名称',
          dataIndex: 'name',
          width: 140,
          align: 'center',
          search: true
        },
        {
          title: '所属医院',
          dataIndex: 'cpHospitalName',
          width: 140,
          align: 'center',
          searchIndex: 'cpHospitalId',
          search: (
            <ArrSelect
              showSearch={false}
              options={
                allHospital.data?.recordList.map(item => ({
                  label: item.name,
                  value: item.id
                })) || []
              }
            />
          )
        },
        {
          title: '标准科室',
          dataIndex: 'name',
          width: 140,
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          width: 140,
          align: 'center',
          render: v => <DateShow>{v}</DateShow>
        },
        {
          title: '状态',
          dataIndex: 'state',
          width: 140,
          align: 'center',
          render: v => stateOptions[v],
          search: <ArrSelect options={stateOptions} />
        },
        {
          title: '操作',
          excelRender: false,
          align: 'center',
          fixed: 'right',
          width: 230,
          render: record => (
            <ActionsWrap max={4}>
              <LinkButton
                onClick={() => {
                  console.log('123', 123);
                  actionConfirm(
                    () => {
                      return apis.putOnOff.request({
                        id: record.id,
                        state: record.state ? 0 : 1
                      });
                    },
                    record.state ? '停用' : '启用'
                  );
                }}>
                {record.state ? '停用' : '启用'}
              </LinkButton>
              {!record.state && (
                <ActionsWrap>
                  <LinkButton
                    style={{ color: 'red' }}
                    onClick={() => {
                      actionConfirm(() => {
                        return apis.delDept.request({
                          id: record.id
                        });
                      }, '删除');
                    }}>
                    删除
                  </LinkButton>
                </ActionsWrap>
              )}
              <LinkButton
                onClick={() =>
                  switchModalVisible({
                    ...record,
                    standardDeptNo: parseFloat(record.standardDeptNo),
                    cpHospitalId: record.cpHospitalId + ''
                    // cpHospitalId: parseInt(record.cpHospitalId)
                  })
                }>
                编辑
              </LinkButton>
            </ActionsWrap>
          )
        }
      ]}
      getList={({ params }: any) => {
        return apis.getDept.request({
          ...params,
          searchStartTime:
            params.searchStartTime && params.searchStartTime + ' 00:00:00',
          searchEndTime:
            params.searchEndTime && params.searchEndTime + ' 23:59:59'
        });
      }}
    />
  );
};
