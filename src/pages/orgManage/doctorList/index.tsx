import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  LinkButton
} from 'parsec-admin';
import { useHistory } from 'react-router-dom';
import apis, { stateOptions, pType } from './api';
import { Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import MyTableList from '@components/myTableList';
import apis2 from '@pages/orgManage/hospitalDept/api';

// 职称
// const levels = JSON.parse(storage.get('enums') || '{}').DoctorLevelEnum || [];

export default () => {
  const history = useHistory();

  // 获取所有科室
  const { data: allDept } = apis2.getDept({
    needInit: true,
    params: { pageNum: 1, numPerPage: 9999 }
  });

  return (
    <MyTableList
      tableTitle={'人员列表'}
      showTool={false}
      showExpand={false}
      action={
        <Button
          type='primary'
          onClick={() => history.push('/orgManage/doctorList/0')}
          ghost
          icon={<PlusOutlined />}>
          添加
        </Button>
      }
      scroll={{ x: 1800 }}
      columns={[
        {
          title: '姓名',
          dataIndex: 'name',
          align: 'center',
          width: 180,
          search: true,
          searchSort: 3
        },
        {
          title: '工号',
          dataIndex: 'staffNo',
          align: 'center',
          width: 120
        },
        {
          title: '人员类别',
          dataIndex: 'type',
          align: 'center',
          width: 120,
          render: v => pType[v]
        },
        {
          title: '所属科室',
          dataIndex: 'cpDeptName',
          searchIndex: 'cpDeptId',
          align: 'center',
          width: 120,
          searchSort: 2,
          search: (
            <ArrSelect
              showSearch={false}
              options={
                allDept.data?.recordList.map(item => ({
                  label: item.name,
                  value: item.id
                })) || []
              }
            />
          )
        },
        {
          title: '职称',
          dataIndex: 'level',
          align: 'center',
          width: 140
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center',
          width: 120
        },
        {
          title: '状态',
          dataIndex: 'state',
          align: 'center',
          render: v => stateOptions[v],
          search: <ArrSelect options={stateOptions} />,
          searchSort: 1,
          width: 80
        },
        {
          title: '操作',
          excelRender: false,
          align: 'center',
          fixed: 'right',
          width: 240,
          render: (v, record: any) => (
            <ActionsWrap max={8}>
              <LinkButton
                onClick={() => {
                  actionConfirm(
                    () => {
                      return apis.putOnOff.request({
                        id: record.id,
                        state: record.state ? 0 : 1
                      });
                    },
                    record.state ? '停用' : '启用'
                  );
                }}>
                {record.state ? '停用' : '启用'}
              </LinkButton>
              <LinkButton
                onClick={() => {
                  history.push('/orgManage/doctorList/' + record.id);
                }}>
                编辑
              </LinkButton>
              {/* <LinkButton
                onClick={() => history.push('/doctorList/' + record.id)}>
                查看
              </LinkButton> */}
              {!record.state && (
                <LinkButton
                  style={{ color: 'red' }}
                  onClick={() => {
                    actionConfirm(() => {
                      // console.log('123', 123);
                      return apis.delDoctor.request({
                        id: record.id
                      });
                    }, '删除');
                  }}>
                  删除
                </LinkButton>
              )}
            </ActionsWrap>
          )
        }
      ]}
      getList={({ params }) => {
        return apis.getDoctor.request({
          ...params
        });
      }}
    />
  );
};
