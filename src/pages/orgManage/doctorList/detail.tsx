// import { Table } from 'antd';
import {
  ArrSelect,
  DetailLayout,
  FormDescriptions,
  handleSubmit,
  UploadImg
} from 'parsec-admin';
import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { Button, Form, Input, Space } from 'antd';
import apis, { pType, DoctorList } from './api';
import apis1 from '@pages/orgManage/hospitalList/api';
import apis2 from '@pages/orgManage/hospitalDept/api';
import useflowApi from '../../authority/api';
import env from '@configs/env';

const { TextArea } = Input;
let avatorUrl: string | undefined;
export default () => {
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [form2] = Form.useForm();
  const hisId = env.hisId;
  const history = useHistory();
  // 详情下拉框 需要转换一下
  const [converDoctor, setConverDoctor] = useState<DoctorList>();
  // 用户详情
  const {
    data: { data: doctorDetail }
  } = apis.doctorDetail({
    params: {
      id: id
    },
    initValue: {},
    needInit: parseInt(id) !== 0
  });

  // 获取所有医院
  const { data: allHospital } = apis1.getHospital({
    needInit: true,
    params: { pageNum: 1, numPerPage: 9999, state: 1 }
  });
  // 获取所有科室
  const { data: allDept, request: getAllDept } = apis2.getDept({
    needInit: false,
    params: { pageNum: 1, numPerPage: 9999 }
  });

  // 医联体角色
  const {
    data: { data: coRoleData }
  } = useflowApi.roleList({
    initValue: {
      data: {
        recordList: []
      }
    },
    params: {
      hisId,
      clientType: '2'
    },
    needInit: !!hisId
  });

  useEffect(() => {
    if (doctorDetail) {
      avatorUrl = doctorDetail.image;
      setConverDoctor({
        ...doctorDetail,
        cpHospitalId: doctorDetail.cpHospitalId + '',
        cpDeptId: doctorDetail.cpDeptId + ''
      });
    } else {
      avatorUrl = undefined;
    }
  }, [doctorDetail]);

  // 提交表单
  const submit = async () => {
    try {
      await form.validateFields();
      await form2.validateFields();
      // console.log(form.getFieldsValue());
      // console.log(form2.getFieldsValue());
      // console.log(avatorUrl);
      const values = {
        ...form.getFieldsValue(),
        ...form2.getFieldsValue(),
        image: avatorUrl
      };
      const pro =
        parseInt(id) !== 0
          ? apis.updateDoctor.request({ ...values, id })
          : apis.addDoctor.request({ ...values });
      handleSubmit(() =>
        pro.then(() => {
          history.goBack();
        })
      );
    } catch (error) {}
  };

  return (
    <DetailLayout
      headerProps={false}
      cardsProps={[
        {
          title: '个人信息',
          children: (
            <Space align='start'>
              <div style={{ width: '200px' }}>
                <Space size={8} align='start'>
                  头像
                  <UploadImg
                    length={1}
                    value={converDoctor?.image as any}
                    onChange={(val: any) => (avatorUrl = val)}
                    arrValue={false}
                  />
                </Space>
              </div>
              <FormDescriptions
                edit={true}
                data={converDoctor}
                form={form}
                column={3}
                formProps={{
                  requiredMark: true
                }}
                items={[
                  {
                    label: '医务人员类型',
                    name: 'type',
                    formItemProps: {
                      render: <ArrSelect showSearch={false} options={pType} />
                    },
                    required: true
                  },
                  {
                    label: '姓名',
                    required: true,
                    name: 'name'
                  },
                  {
                    label: '工号',
                    required: true,
                    name: 'staffNo'
                  },
                  {
                    label: '手机号(支持手机号+短信验证码登录)',
                    required: true,
                    name: 'mobile',
                    formItemProps: {
                      render: <Input placeholder='请填写11位有效手机号码' />,
                      rules: [
                        {
                          pattern: /^1[3-9]\d{9}$/,
                          message: '请输入正确手机号'
                        }
                      ]
                    }
                  },
                  {
                    label: '身份证号',
                    required: true,
                    name: 'idNo',
                    formItemProps: {
                      rules: [
                        {
                          pattern: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/,
                          message: '请输入正确身份证号'
                        }
                      ]
                    }
                  },
                  {
                    label: '从业年限',
                    name: 'workingLife'
                  },
                  {
                    label: '医院',
                    name: 'cpHospitalId',
                    formItemProps: {
                      render: (
                        <ArrSelect
                          showSearch={false}
                          fieldNames={{ label: 'name', value: 'id' }}
                          options={allHospital?.data?.recordList || []}
                          onChange={v => {
                            getAllDept({
                              cpHospitalId: v as string,
                              pageNum: 1,
                              numPerPage: 9999
                            });
                            form.setFieldsValue({ detpId: undefined });
                          }}
                        />
                      )
                    },
                    required: true
                  },
                  {
                    label: '科室',
                    name: 'cpDeptId',
                    formItemProps: {
                      render: (
                        <ArrSelect
                          showSearch={false}
                          fieldNames={{ label: 'name', value: 'id' }}
                          options={allDept?.data?.recordList || []}
                        />
                      )
                    },
                    required: true
                  },
                  {
                    label: '职称',
                    name: 'level',
                    formItemProps: {
                      render: (
                        <ArrSelect
                          showSearch={false}
                          options={{
                            主任医师: '主任医师',
                            副主任医师: '副主任医师',
                            主治医师: '主治医师',
                            医师: '医师',
                            医士: '医士'
                          }}
                        />
                      )
                    },
                    required: true
                  },
                  {
                    label: '角色',
                    name: 'roleId',
                    formItemProps: {
                      render: (
                        <ArrSelect
                          style={{ width: '26.3%' }}
                          showSearch={false}
                          options={(coRoleData.recordList || []).map(x => ({
                            value: x.id,
                            children: x.roleName
                          }))}
                        />
                      )
                    },
                    required: true
                  }
                ]}
              />
            </Space>
          )
        },
        {
          title: '介绍信息',
          children: (
            <Space>
              <FormDescriptions
                edit={true}
                data={converDoctor}
                form={form2}
                column={2}
                items={[
                  {
                    label: '擅长领域',
                    name: 'specialty',
                    formItemProps: { render: <TextArea /> }
                  },
                  {
                    label: '医生简介',
                    name: 'introduction',
                    formItemProps: { render: <TextArea /> }
                  }
                ]}
              />
            </Space>
          )
        },
        {
          children: (
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button style={{ marginRight: '10px' }}>取消</Button>
              <Button type='primary' onClick={submit}>
                保存医生信息
              </Button>
            </div>
          )
        }
      ]}
    />
  );
};
