import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiResponseData,
  ListApiRequestParams,
  ApiResponse
} from '@apiHooks';

export interface DoctorList {
  id: '@natural'; //
  hisId: '@natural'; //上级医院id
  cpHospitalId: number | string; //医联体医院id
  cpHospitalName: '@cword(6)'; //医联体医院名称
  cpDeptId: number | string; //医联体科室id
  cpDeptName: '@cword(6)'; //医联体科室名
  name: '@cword(6)'; //医生名
  mobile: '1@integer(3000000000, 9900000000)'; //手机号
  staffNo: '@string(8)'; //医生工号
  sex: "@pick('男','女')"; //医生性别
  level: "@picker('11','12','13')"; //医生级别
  type: '@pick(1,2)'; //类型 1.医生  2.护士
  image: '@image(100x100)'; //医生头像
  specialty: '@cparagraph'; //专业技能
  introduction: '@cparagraph'; //医生介绍
  sortNo: '@integer(0, 1000000)'; //排序字段 数字越大越靠前
  state: '@integer(0, 1)'; //0启用 1启用
  workingLife: '@integer(0, 1000000)'; //从业年限
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //更新时间
}
export interface DoctorListParams {
  cpHospitalId?: string; //医联体医院id
  cpDeptId?: string; //医联体科室id
  mobile?: string; //完整手机号
  name?: string; //医生姓名
}

export const stateOptions = {
  0: '停用',
  1: '启用'
};
export const pType = {
  1: '医生',
  2: '护士'
};

export default {
  // 医生列表
  getDoctor: createApiHooks((params: ListApiRequestParams & DoctorListParams) =>
    request.get<ListApiResponseData<DoctorList>>(
      '/mch/cooperate/cooperate-doctor',
      {
        params
      }
    )
  ),
  // 查询医院全部可用医生列表
  getAllDoctor: createApiHooks(
    (params: {
      deptId?: string | number | undefined;
      hospitalId?: string | number | undefined;
      doctorName?: string;
    }) =>
      request.get<ApiResponse<DoctorList[]>>(
        '/mch/cooperate/cooperate-doctor/by-hospital',
        {
          params
        }
      )
  ),
  // 停用启用医生
  putOnOff: createApiHooks((params: { id: string; state: number }) =>
    request.put<ApiResponse<undefined>>(
      `/mch/cooperate/cooperate-doctor/state/${params.id}`,
      params
    )
  ),
  // 删除医生
  delDoctor: createApiHooks((params: { id: string }) =>
    request.delete<ApiResponse<undefined>>(
      `/mch/cooperate/cooperate-doctor/${params.id}`
    )
  ),
  // 查询医生详情
  doctorDetail: createApiHooks((params: { id: string }) =>
    request.get<ApiResponse<DoctorList>>(
      `/mch/cooperate/cooperate-doctor/${params.id}`
    )
  ),
  // 导入excel
  importDoctor: createApiHooks((file: File) => {
    const form = new FormData();
    form.append('file', file);
    return request.post<ApiResponse<undefined>>(
      `/mch/cooperate/cooperate-doctor/import`,
      form,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
  }),
  // 编辑医生
  updateDoctor: createApiHooks((params: DoctorList) =>
    request.put<ApiResponse<undefined>>(
      `/mch/cooperate/cooperate-doctor`,
      params
    )
  ),
  // 新增医生
  addDoctor: createApiHooks((params: DoctorList) =>
    request.post<ApiResponse<undefined>>(
      `/mch/cooperate/cooperate-doctor`,
      params
    )
  ),
  // 下载导入医生模板
  downloadTemplate: createApiHooks(() =>
    request.get<Blob>(`/mch/cooperate/cooperate-doctor/template`, {
      responseType: 'blob'
    })
  )
};
