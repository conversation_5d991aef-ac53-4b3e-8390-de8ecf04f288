import React, { useMemo } from 'react';
import { DayRangePicker } from 'parsec-admin';
import useApi from '../api';
import MyTableList from '@components/myTableList';
export default () => {
  return (
    <MyTableList
      tableTitle={'系统日志'}
      getList={({ params }) => useApi.loginlist.request(params)}
      columns={useMemo(
        () => [
          {
            title: '用户名',
            dataIndex: 'name',
            search: true
          },
          {
            title: '医院ID',
            dataIndex: 'hisId',
            search: true
          },
          {
            title: '医院名称',
            dataIndex: 'hisName'
          },
          {
            title: '请求参数',
            dataIndex: 'requestUrl'
          },
          {
            title: '创建时间',
            width: 150,
            dataIndex: 'createTime',
            search: <DayRangePicker placeholder={['开始时间', '结束时间']} />,
            searchIndex: ['startDate', 'endDate']
          },
          {
            title: '最后更新时间',
            dataIndex: 'updateTime'
          }
        ],
        []
      )}
    />
  );
};
