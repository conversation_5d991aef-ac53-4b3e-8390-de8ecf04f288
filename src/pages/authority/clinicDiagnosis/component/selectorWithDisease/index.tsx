import { Select, Spin } from 'antd';
import React, { useCallback, useMemo, useState } from 'react';

import usePlanApi from '../../api';

import debounce from '@utils/debounce';

interface Props {
  onChange?: (value: any) => void;
  value?: any;
  params?: any;
}

export default ({ onChange, value, params }: Props) => {
  const [searchConditionValue, setSearchConditionValue] = useState<string>('');

  const {
    data: { data },
    loading
  } = usePlanApi.病种管理分页列表({
    needInit: true,
    params: {
      name: searchConditionValue,
      numPerPage: 20,
      pageNum: 1,
      state: 'ON',
      ...params
    },
    initValue: {}
  });

  const onSearch = useCallback((value: string) => {
    setSearchConditionValue(value);
  }, []);

  const debounceFunc = useMemo(() => debounce(onSearch), [onSearch]);

  return (
    <Select
      value={data?.recordList?.find(item => item.id === value)?.name}
      showSearch
      placeholder='请选择'
      optionFilterProp='children'
      allowClear={true}
      onChange={(value, option) => {
        onChange && onChange(value);
      }}
      onSearch={value => {
        debounceFunc(value);
      }}
      filterOption={false}
      notFoundContent={loading ? <Spin size='small' /> : null}
      options={data?.recordList?.map(item => ({
        label: item.name,
        value: item.id
      }))}
    />
  );
};
