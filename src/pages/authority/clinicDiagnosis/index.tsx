import MyTableList from '@components/myTableList';
import useApi from './api';
import {
  actionConfirm,
  ActionsWrap,
  handleSubmit,
  LinkButton,
  useModal
} from 'parsec-admin';
import { Button } from 'antd';
import React, { useState } from 'react';
import SelectorWithDisease from './component/selectorWithDisease';

export default () => {
  const [selectRowKeys, setSelectRowKeys] = useState<(string | number)[]>();

  const switchLinkModal = useModal(
    ({
      id,
      modeType
    }: {
      modeType: 'addBatch' | 'edit';
      id: number | string;
    }) => {
      return {
        onSubmit: values => {
          const innerPromise =
            modeType === 'edit' ? useApi.编辑诊断与病种 : useApi.批量添加;
          const params =
            modeType === 'edit'
              ? {
                  id,
                  diseasesId: values.diseasesId
                }
              : {
                  ids: selectRowKeys?.join(','),
                  diseasesId: values.diseasesId
                };
          return handleSubmit(() => {
            return innerPromise
              .request(params as any)
              .then(() => setSelectRowKeys([]));
          });
        },
        title: '批量添加病种',
        items: [
          {
            name: 'diseasesId',
            label: '病种',
            render: <SelectorWithDisease />
          }
        ]
      };
    }
  );

  return (
    <MyTableList
      tableTitle={'门诊诊断列表'}
      getList={({ params }: { params: any }) => {
        return useApi.诊断与病种分页.request(params);
      }}
      exportExcelButton={{
        getList: ({ params }) =>
          useApi.诊断与病种分页
            .request({
              pageNum: 1,
              icdName: params.icdName,
              numPerPage: 10000
            })
            .then(res => {
              return {
                list: res.data?.recordList,
                total: res.data?.totalCount
              };
            })
        // getList?: (params: GetListParams<D, {
        //   [N in keyof P]?: P[N];
        // }>) => Promise<{
        //   list?: D[];
        //   total?: number;
        // }>;
      }}
      action={
        <ActionsWrap>
          <Button
            style={{ marginLeft: '15px' }}
            type={'default'}
            onClick={() => {
              actionConfirm(() => useApi.同步.request(), '同步');
            }}>
            同步
          </Button>
          <Button
            type={'default'}
            onClick={() => {
              // actionConfirm(() => useApi.同步.request({ hisId }), '同步');
              selectRowKeys && selectRowKeys?.length > 0 && switchLinkModal();
            }}>
            批量关联
          </Button>
        </ActionsWrap>
      }
      rowSelection={{
        onChange: selectedRowKeys => setSelectRowKeys(selectedRowKeys),
        selectedRowKeys: selectRowKeys
      }}
      columns={[
        {
          title: '疾病类型',
          width: 140,
          dataIndex: 'icdCategory'
        },
        {
          title: '疾病编码',
          width: 140,
          dataIndex: 'icdCode'
        },
        {
          title: '疾病名称',
          width: 140,
          dataIndex: 'icdName',
          searchIndex: 'icdName',
          search: true
        },
        {
          title: '关联病种',
          width: 140,
          dataIndex: 'diseasesName'
        },
        {
          title: '编辑人',
          width: 140,
          dataIndex: 'operatorName'
        },
        {
          title: '编辑时间',
          width: 140,
          dataIndex: 'updateTime'
        },
        {
          title: '操作',
          width: 80,
          fixed: 'right',
          render: (v, record: any) => {
            return (
              <ActionsWrap max={6}>
                <LinkButton
                  onClick={() => {
                    switchLinkModal({ modeType: 'edit', ...record });
                  }}>
                  编辑
                </LinkButton>
              </ActionsWrap>
            );
          }
        }
      ]}
    />
  );
};
