import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiResponseData,
  ListApiRequestParams,
  ApiResponse
} from '@apiHooks';

export default {
  编辑诊断与病种: createApiHooks(
    (data: { id: string | number; diseasesId: string | number }) =>
      request.put<ApiResponse<any>>('/mch/follow/icd-diseases/update', data, {
        params: { id: data.id }
      })
  ),
  批量添加: createApiHooks((
    data: { ids: string | number; diseasesId: number } //主键ID逗号分隔 //病种ID
  ) =>
    request.post<ApiResponse<any>>('/mch/follow/icd-diseases/batchAdd', data)
  ),
  同步: createApiHooks(() =>
    request.get<ApiResponse<any>>('/mch/follow/icd-diseases/syncIcd')
  ),
  病种管理分页列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        name?: string;
        state?: 'ON' | 'OFF';
        export?: 0 | 1;
      }
    ) =>
      request.get<
        ListApiResponseData<{
          id: '@integer(1,1000)'; //诊疗记录主键ID
          createTime: '@datetime'; //创建时间
          updateTime: '@datetime'; //修改时间
          hisId: '@integer(10000,9999)'; //医院ID
          state: "@pick('ON','OFF')"; //状态：ON启用、OFF停用
          name: '@cword(4)'; //病种名称
          sn: 'SN@integer(1000,9999)'; //病种编码
          deptId: '@integer(1,100)'; //科室主键ID
          deptName: '@cword(2)科室'; //科室名称
          creatorId: '@integer(1,100)'; //创建人ID
          creatorName: '@cname()'; //创建人姓名
        }>
      >('/mch/follow/diseases/page', {
        params
      })
  ),
  诊断与病种分页: createApiHooks(
    (params: ListApiRequestParams & { icdName?: string }) =>
      request.get<
        ListApiResponseData<{
          id: '@natural'; //主键ID
          createTime: '@datetime'; //创建时间
          updateTime: '@datetime'; //更新时间
          hisId: '8900'; //医院ID
          state: "@pick('ON','OFF')"; //状态,ON,OFF
          diseasesId: '@natural'; //病种ID
          diseasesName: '@cword(5)病'; //病种名称
          diseasesSn: '@word(5)'; //病种编码
          icdId: 'natural'; //诊断ID
          icdName: '@cword(5)病'; //诊断名称
          icdCode: '@word(5)'; //诊断编码
          icdCategory: '@cword(5)类'; //诊断类别
          operatorId: '@natural'; //操作人ID
          operatorName: '@cname'; //操作人姓名
        }>
      >('/mch/follow/icd-diseases/page', { params })
  )
};
