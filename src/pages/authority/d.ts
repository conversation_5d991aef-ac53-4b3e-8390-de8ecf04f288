// 查询账号
export interface UserParams {
  hisId?: string;
  hisName?: string;
  account?: string;
  phone?: string;
  dept?: string;
  name?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
}
// 账号列表
export interface UserList {
  account?: string;
  changePassword?: boolean;
  confirmPassword?: string;
  createTime?: string;
  departmentId?: string;
  dept?: string;
  expiresIn?: number;
  freezetime?: string;
  hisId?: number;
  hisName?: string;
  id?: number;
  ids?: string;
  loginNum?: number;
  name?: string;
  newPassword?: string;
  operType?: string;
  password?: string;
  passwordErrorNum?: number;
  passwordErrorTime?: string;
  passwordValidateEnd?: string;
  phone?: string;
  roleId?: number;
  signaturePassword?: string;
  status?: string;
  unionId?: string;
  updateTime?: string;
  userFlag?: string;
  userType?: string;
  validateEnd?: string;
  validateStart?: string;
}
// 账号表单提交
export interface UserForm {
  id?: number;
  hisId: number;
  account: string;
  password?: string;
  signaturePassword?: string;
  userType: string;
  name: string;
  dept?: string;
  departmentId?: string;
  phone?: string;
  validateStart: string;
  validateEnd: string;
  passwordErrorTime?: string;
  passwordErrorNum?: any;
  passwordValidateEnd?: string;
  loginNum?: any;
  userFlag?: string;
  unionId?: string;
  roleId?: any;
}
//条件查询医院
export interface HosptitalGetBy {
  hisId?: string;
  name?: string;
  level?: string;
  licence?: string;
  serviceScope?: string;
  honorImages?: string;
  instCode?: string;
  instRegisterNumber?: string;
  instInfoSafeEnsure?: string;
  certFirstDate?: string;
  certUpdateDate?: string;
  licenseStartDate?: string;
  licenseEndDate?: string;
  hospitalName?: string;
  hospitalLevel?: string;
  hospitalAddress?: string;
  hospitalIntroduction?: string;
  contactNumber?: string;
  adminAccount?: string;
  adminName?: string;
}
// 查询医院返回数据
export interface HosptitalGetByList {
  code: number;
  data: Array<{
    adminAccount?: string;
    adminName?: string;
    certFirstDate?: string;
    certFirstTime?: string;
    certUpdateDate?: string;
    certUpdateTime?: string;
    contactNumber?: string;
    createTime?: string;
    grading?: string;
    hisId: number;
    honorImages?: string;
    hospitalAddress?: string;
    hospitalIntroduction?: string;
    hospitalLevel?: string;
    hospitalName?: string;
    id?: number;
    instCode?: string;
    instInfoSafeEnsure?: string;
    instRegisterNumber?: string;
    level?: string;
    licence?: string;
    licenseEndDate?: string;
    licenseEndTime?: string;
    licenseStartDate?: string;
    licenseStartTime?: string;
    name?: string;
    serviceScope?: string;
    status?: string;
    updateTime?: string;
  }>;
  msg: string;
}
