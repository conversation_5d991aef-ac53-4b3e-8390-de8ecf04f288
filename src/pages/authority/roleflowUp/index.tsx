import React, { useMemo, useState } from 'react';
import {
  <PERSON><PERSON>utton,
  DayRangePicker,
  useModal,
  ActionsWrap,
  handleSubmit,
  actionConfirm,
  ArrSelect
} from 'parsec-admin';
import { Radio, Input, Switch, Button } from 'antd';
import MyTableList from '@components/myTableList';
import MenuConfig from './menuConfig';
import { PlusOutlined } from '@ant-design/icons';
import useApi, { roleStateObj } from '../api';
import env from '@configs/env';
import moment from 'moment';

export default ({ clientType }: { clientType: string }) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );
  const hisId = env.hisId;

  const {
    data: { data: menuList }
  } = useApi.getRoleMenu({
    params: { hisId, clientType },
    initValue: { data: [] }
  });

  const [menuIds, setMenuIds] = useState('');
  const switchMenuModalVisible = useModal(
    ({ id, hisId }) => {
      return {
        title: '配置菜单',
        onSubmit: () => {
          return handleSubmit(() => {
            return useApi.setSignleRoleMenu.request({
              menuIds,
              id,
              hisId
            });
          });
        },
        children: (
          <MenuConfig
            data={menuList}
            clientType={clientType}
            id={id}
            hisId={hisId}
            onChange={value => {
              console.log(value.join(','));
              setMenuIds(value.join(','));
            }}
          />
        )
      };
    },
    [menuIds, menuList]
  );

  const switchEditModalVisible = useModal(p => {
    const { id, status } = p || {};
    const isEdit = !!id;
    return {
      title: isEdit ? '编辑角色' : '添加角色',
      onSubmit: values =>
        handleSubmit(() => {
          if (isEdit) {
            return useApi.updateRole.request({
              ...values,
              id,
              hisId,
              clientType
            });
          } else {
            delete values.id;
            return useApi.addRole.request({
              ...values,
              hisId: hisId,
              clientType
            });
          }
        }),

      myFormProps: {
        initValues: {
          status
        }
      } as any,
      items: [
        {
          label: '角色名称',
          name: 'roleName',
          required: true
        },
        {
          label: '角色描述',
          name: 'describe',
          render: () => <Input.TextArea autoSize={{ minRows: 4, maxRows: 4 }} />
        },
        {
          label: '数据权限',
          name: 'dataRight',
          render: () => (
            <ArrSelect
              options={{
                SELF: '个人数据权限',
                DEPT: '科室数据权限',
                HIS: '院级数据权限'
              }}
            />
          )
        },
        {
          label: '角色状态',
          name: 'status',
          render: () => (
            <Radio.Group>
              <Radio value={'0'}>停用</Radio>
              <Radio value={'1'}>启用</Radio>
            </Radio.Group>
          ),
          formItemProps: {
            rules: [
              {
                required: true,
                message: '账号状态是必选的'
              }
            ]
          }
        }
      ]
    };
  });

  return (
    <MyTableList
      tableTitle='医联体角色列表'
      action={
        <Button
          type={'default'}
          icon={<PlusOutlined />}
          onClick={() => {
            switchEditModalVisible();
          }}>
          添加角色
        </Button>
      }
      pageHeaderProps={false}
      getList={({ params }) => {
        return useApi.getRoleList.request({
          ...params,
          createTimeStart: params?.createTimeStart
            ? moment(params?.createTimeStart).format('YYYY-MM-DD 00:00:00')
            : undefined,
          createTimeEnd: params?.createTimeEnd
            ? moment(params?.createTimeEnd).format('YYYY-MM-DD 23:59:59')
            : undefined,
          clientType
          // refundStatus: '1'x
        });
      }}
      paginationExtra={
        <div>
          {!!selectedRowKeys.length && (
            <>
              <Button
                type={'default'}
                style={{ marginRight: '10px' }}
                onClick={() => {
                  actionConfirm(
                    () =>
                      useApi.updateRoleBatch.request({
                        ids: selectedRowKeys.join(','),
                        hisId: hisId,
                        status: 0
                      }),
                    '批量停用'
                  );
                }}>
                批量停用
              </Button>
              <Button
                type={'default'}
                style={{ marginRight: '10px' }}
                onClick={() => {
                  actionConfirm(
                    () =>
                      useApi.updateRoleBatch.request({
                        ids: selectedRowKeys.join(','),
                        hisId: hisId,
                        status: 1
                      }),
                    '批量启用'
                  );
                }}>
                批量启用
              </Button>
              <Button
                type={'default'}
                style={{ marginRight: '10px' }}
                onClick={() => {
                  actionConfirm(
                    () =>
                      useApi.updateRoleBatch.request({
                        ids: selectedRowKeys.join(','),
                        hisId: hisId,
                        status: -1
                      }),
                    '批量删除'
                  );
                }}>
                批量删除
              </Button>
              <span> 已选择{selectedRowKeys.length}条</span>
            </>
          )}
        </div>
      }
      rowSelection={{
        onChange: selectedRowKeys => setSelectedRowKeys(selectedRowKeys)
      }}
      columns={useMemo(
        () => [
          {
            title: '医院名称',
            dataIndex: 'hisName',
            // search: true,
            render: false
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                disabledDate={current => {
                  return current && current.valueOf() > Date.now();
                }}
              />
            ),
            searchIndex: ['createTimeStart', 'createTimeEnd'],
            render: false
          },
          {
            title: '角色状态',
            dataIndex: 'status',
            search: <ArrSelect options={roleStateObj} />,
            render: false
          },
          {
            title: '角色名称',
            width: 130,
            dataIndex: 'roleName',
            search: true
          },
          {
            title: '角色描述',
            width: 130,
            dataIndex: 'describe'
          },
          {
            title: '角色状态',
            width: 100,
            dataIndex: 'deptName',
            render: (val, record: any) => {
              return (
                <Switch
                  checkedChildren='ON'
                  unCheckedChildren='OFF'
                  checked={record.status === '0' ? false : true}
                  onClick={() => {
                    handleSubmit(
                      () =>
                        useApi.updateRole.request({
                          ...record,
                          status: record.status === '0' ? '1' : '0'
                        }),
                      '操作'
                    );
                  }}
                />
              );
            }
          },
          {
            title: '创建时间',
            width: 130,
            dataIndex: 'createTime'
          },

          {
            title: '操作',
            fixed: 'right',
            width: 230,
            render: (record: any) => (
              <ActionsWrap max={4}>
                {clientType === '1' && (
                  <LinkButton
                    onClick={() => {
                      switchMenuModalVisible({
                        ...record
                      });
                    }}>
                    配置菜单
                  </LinkButton>
                )}

                <LinkButton
                  onClick={() => {
                    switchEditModalVisible({
                      ...record,
                      repassword: record.password
                    });
                  }}>
                  编辑
                </LinkButton>

                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.updateRole.request({
                          id: record.id,
                          hisId: record.hisId,
                          roleName: record.roleName,
                          describe: record.describe,
                          status: -1
                        }),
                      '删除'
                    );
                  }}>
                  删除
                </LinkButton>

                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.cloneRole.request({
                          id: record.id
                        }),
                      '克隆'
                    );
                  }}>
                  克隆
                </LinkButton>
              </ActionsWrap>
            )
          }
        ],
        [clientType, switchEditModalVisible, switchMenuModalVisible]
      )}
    />
  );
};
