import React, { useMemo } from 'react';
import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  handleSubmit,
  LinkButton,
  useModal
} from 'parsec-admin';
import useApi from '../api';
import MyTableList from '@components/myTableList';
import { Input, DatePicker } from 'antd';
import moment from 'moment';
const StatusObj: {
  [key: string]: string;
} = {
  1: '维护中', // 1
  0: '正常访问' // 0
};

const SwitchFlag: {
  [key: string]: string;
} = {
  1: '启用', // 1
  0: '停用' // 0
};
export default () => {
  const switchModal = useModal(({ id }) => {
    return {
      onSubmit: values => {
        return handleSubmit(() => {
          if (values.beginTime) {
            values.beginTime = values.beginTime.format('YYYY-MM-DD HH:mm:ss');
          }
          if (values.endTime) {
            values.endTime = values.endTime.format('YYYY-MM-DD HH:mm:ss');
          }
          return useApi.maintenanceUpdate.request(values);
        });
      },
      title: '编辑',
      items: [
        { name: 'id', render: false },
        { name: 'status', render: false },
        {
          name: 'moduleName',
          label: '模块名称',
          render: <Input disabled={true} placeholder='请输入模块名称' />
        },
        {
          name: 'switchFlag',
          label: '规则状态',
          render: (
            <ArrSelect options={SwitchFlag} placeholder='请选择规则状态' />
          )
        },
        {
          name: 'beginTime',
          label: '维护开始时间',
          render: (
            <DatePicker placeholder='请选择维护开始时间' showTime={true} />
          )
        },
        {
          name: 'endTime',
          label: '维护结束时间',
          render: (
            <DatePicker placeholder='请选择维护结束时间' showTime={true} />
          )
        },
        {
          name: 'content',
          label: '提示语',
          render: <Input.TextArea placeholder='请输入提示语' rows={4} />
        }
      ]
    };
  });
  return (
    <MyTableList
      tableTitle={'维护管理'}
      getList={({ params }: { params: any }) => {
        return useApi.maintenanceList.request(params);
      }}
      columns={useMemo(
        () => [
          {
            title: '模块名称',
            width: 100,
            dataIndex: 'moduleName'
          },
          {
            title: '模块状态',
            width: 100,
            dataIndex: 'status',
            search: <ArrSelect options={StatusObj} />,
            render: (v: any) => {
              return StatusObj[v];
            }
          },
          {
            title: '规则状态',
            width: 100,
            dataIndex: 'switchFlag',
            search: <ArrSelect options={SwitchFlag} />,
            render: (v: any) => {
              return SwitchFlag[v];
            }
          },
          {
            title: '提示语',
            width: 200,
            dataIndex: 'content'
          },
          {
            title: '维护开始时间',
            width: 200,
            dataIndex: 'beginTime'
          },
          {
            title: '维护结束时间',
            width: 200,
            dataIndex: 'endTime'
          },

          {
            title: '操作',
            width: 150,
            fixed: 'right',
            render: (v, record: any) => {
              return (
                <ActionsWrap>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () => {
                          return useApi.maintenanceUpdate.request({
                            id: record.id,
                            switchFlag: record.switchFlag === 0 ? 1 : 0
                          });
                        },
                        record.switchFlag === 0 ? '启用' : '停用'
                      );
                    }}>
                    {record.switchFlag === 0 ? '启用' : '停用'}
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      switchModal({
                        ...record,
                        beginTime: record.beginTime
                          ? moment(record.beginTime)
                          : undefined,
                        endTime: record.endTime
                          ? moment(record.endTime)
                          : undefined
                      });
                    }}>
                    编辑
                  </LinkButton>
                </ActionsWrap>
              );
            }
          }
        ],
        [switchModal]
      )}
    />
  );
};
