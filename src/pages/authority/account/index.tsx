import React, { useMemo, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  LinkButton,
  actionConfirm,
  useModal,
  ActionsWrap,
  handleSubmit,
  ArrSelect
} from 'parsec-admin';
import useApi, { accountStatusObj } from '../api';
import MyTableList from '@components/myTableList';
import MyPassword, { validatePasswordStrength } from '@components/MyPassword';
import { hidePhone } from '@utils/tools';
import { Button, Switch, Radio } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import permisstion from '@utils/permisstion';
import env from '@configs/env';
export default () => {
  const [passWordValue, setPassWordValue] = useState<string>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );
  const hisId = env.hisId;
  // 获取医院列表
  const {
    data: { data }
  } = useApi.getByHospit({
    initValue: {
      data: []
    },
    params: {
      hisId
    },
    needInit: !!hisId
  });
  // 获取医院列表
  const {
    data: { data: roleData }
  } = useApi.roleList({
    initValue: {
      data: {
        recordList: []
      }
    },
    params: {
      hisId
    },
    needInit: !!hisId
  });
  const switchModalVisible = useModal(({ id, userFlag }) => {
    const isEdit = !!id;
    return {
      title: isEdit ? '编辑账号' : '新增账号',
      onSubmit: values =>
        handleSubmit(() => {
          if (isEdit) {
            return useApi.updateAccount.request({
              ...values,
              userFlag,
              hisId: hisId
            });
          } else {
            delete values.id;
            return useApi.addAccount.request({
              ...values,
              userFlag,
              hisId: hisId
            });
          }
        }),
      myFormProps: {
        initValues: {
          status: '0'
        }
      } as any,
      items: [
        {
          name: 'id',
          render: false
        },
        {
          name: 'hisName',
          render: false
        },
        {
          label: '账号',
          name: 'account',
          required: true
        },
        {
          label: '密码',
          name: 'password',
          formItemProps: {
            hasFeedback: true,
            rules: [
              {
                required: !isEdit,
                validator: (rule, value) => {
                  if (
                    (validatePasswordStrength(value) >= 2 &&
                      value.length <= 16) ||
                    (isEdit && (!value || value?.length === 0))
                  ) {
                    return Promise.resolve();
                  }
                  return Promise.reject(
                    '需由8-16位字母（区分大小写）、数字或符号组成'
                  );
                },
                message: '需由8-16位字母（区分大小写）、数字或符号组成'
              }
            ]
          },
          render: (
            <MyPassword
              value={passWordValue}
              isEdit={isEdit}
              onChange={setPassWordValue}
            />
          )
        },
        // {
        //   label: '确认密码',
        //   name: 'repassword',
        //   formItemProps: {
        //     hasFeedback: true,
        //     rules: [
        //       {
        //         required: !isEdit,
        //         message: '请输入确认密码!'
        //       },
        //       ({ getFieldValue }) => ({
        //         validator(rule, value) {
        //           if (!value || getFieldValue('password') === value) {
        //             return Promise.resolve();
        //           }
        //           return Promise.reject('两次密码不同!');
        //         }
        //       })
        //     ]
        //   },
        //   render: !isEdit
        // },
        {
          label: '医院名称',
          name: 'hisId',
          render: () => (
            <ArrSelect
              options={(data || []).map(x => ({
                value: x.hisId,
                children: x.hospitalName
              }))}
            />
          ),
          required: true
        },
        {
          label: '姓名',
          name: 'name',
          required: true
        },
        {
          label: '联系电话',
          name: 'phone',
          formItemProps: {
            rules: [
              {
                pattern: /^[1][3-9][0-9]{9}$/,
                required: true,
                message: '请输入正确的号码!'
              }
            ]
          }
        },
        {
          label: '账号状态',
          name: 'status',
          render: () => (
            <Radio.Group>
              <Radio value={'0'}>禁用</Radio>
              <Radio value={'1'}>启用</Radio>
            </Radio.Group>
          ),
          required: true
        },
        {
          label: '角色',
          name: 'roleId',
          render: () => (
            <ArrSelect
              options={(roleData.recordList || []).map(x => ({
                value: x.id,
                children: x.roleName
              }))}
            />
          )
        }
      ]
    };
  });

  return (
    <MyTableList
      action={
        permisstion.canAddAccount && (
          <Button
            type={'default'}
            icon={<PlusOutlined />}
            onClick={() => {
              switchModalVisible({});
              setPassWordValue('');
            }}>
            添加账号
          </Button>
        )
      }
      paginationExtra={
        <div>
          {!!selectedRowKeys.length && permisstion.canBatchUpdateAccount && (
            <>
              <Button
                type={'default'}
                style={{ marginRight: '10px' }}
                onClick={() => {
                  actionConfirm(
                    () =>
                      useApi.updateBatch.request({
                        ids: selectedRowKeys.join(','),
                        hisId: hisId,
                        operType: 'invalid'
                      }),
                    '批量停用'
                  );
                }}>
                批量停用
              </Button>
              <Button
                type={'default'}
                style={{ marginRight: '10px' }}
                onClick={() => {
                  actionConfirm(
                    () =>
                      useApi.updateBatch.request({
                        ids: selectedRowKeys.join(','),
                        hisId: hisId,
                        operType: 'enable'
                      }),
                    '批量启用'
                  );
                }}>
                批量启用
              </Button>
              <Button
                type={'default'}
                style={{ marginRight: '10px' }}
                onClick={() => {
                  actionConfirm(
                    () =>
                      useApi.updateBatch.request({
                        ids: selectedRowKeys.join(','),
                        hisId: hisId,
                        operType: 'delete'
                      }),
                    '批量删除'
                  );
                }}>
                批量删除
              </Button>
              <span> 已选择{selectedRowKeys.length}条</span>
            </>
          )}
        </div>
      }
      getList={({ params }) => useApi.list.request(params)}
      tableTitle={'账号管理'}
      rowSelection={{
        onChange: selectedRowKeys => setSelectedRowKeys(selectedRowKeys)
      }}
      columns={useMemo(
        () => [
          {
            title: '账号',
            dataIndex: 'account',
            width: 100,
            fixed: 'left',
            search: true
          },
          {
            title: '角色',
            dataIndex: 'roleName',
            width: 200
          },
          // {
          //   title: '医院名称',
          //   dataIndex: 'hisName',
          //   width: 100,
          //   search: true
          // },
          {
            title: '姓名',
            dataIndex: 'name',
            width: 100,
            search: true
          },
          {
            title: '联系电话',
            dataIndex: 'phone',
            width: 150,
            render: val => hidePhone(val)
          },
          {
            title: '账号状态',
            dataIndex: 'status',
            width: 100,
            render: permisstion.canUpdateAccount
              ? (val, record: any) => {
                  return (
                    <Switch
                      checkedChildren='ON'
                      unCheckedChildren='OFF'
                      checked={record.status === '0' ? false : true}
                      onClick={() => {
                        handleSubmit(
                          () =>
                            useApi.actionStatus.request({
                              id: record.id,
                              hisId: record.hisId,
                              account: record.account,
                              operType:
                                record.status === '0' ? 'enable' : 'invalid'
                            })
                          // '操作成功'
                          // `确定要${record.status === '0' ? '启用' : '停用'}吗`
                        );
                      }}
                    />
                  );
                }
              : false,
            search: <ArrSelect options={accountStatusObj} />
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 150,
            search: <DayRangePicker placeholder={['开始时间', '结束时间']} />,
            searchIndex: ['startDate', 'endDate']
          },
          {
            title: '操作',
            fixed: 'right',
            width: 180,
            render: (record: any) => (
              <ActionsWrap>
                {permisstion.canUpdateAccount && (
                  <LinkButton
                    onClick={() => {
                      switchModalVisible({
                        ...record
                      });
                      setPassWordValue(record?.password);
                    }}>
                    编辑
                  </LinkButton>
                )}
                {permisstion.canDeleteAccount && (
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          useApi.actionStatus.request({
                            id: record.id,
                            hisId: record.hisId,
                            account: record.account,
                            operType: 'delete'
                          }),
                        '删除'
                      );
                    }}>
                    删除
                  </LinkButton>
                )}
              </ActionsWrap>
            )
          }
        ],
        [switchModalVisible]
      )}
    />
  );
};
