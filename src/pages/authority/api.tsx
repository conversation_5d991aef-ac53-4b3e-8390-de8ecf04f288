import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiResponseData,
  ListApiRequestParams,
  ApiResponse
} from '@apiHooks';
import {
  UserList,
  HosptitalGetBy,
  HosptitalGetByList,
  UserForm,
  UserParams
} from './d';
export const accountStatusObj: any = {
  '': '全部',
  '0': '禁用',
  '1': '启用',
  '2': '冻结',
  '3': '锁定'
};

export const roleStateObj: any = {
  '': '全部',
  '0': '停用',
  '1': '启用'
};

export interface OperationSettingResp {
  hisId: '@integer(1,100)'; //医院ID
  restrictImageInquiryConfig: '1,2,3'; //限制医生图文问诊配置，医生id主键，以逗号隔开
  followAppointDirector: '@pick(0,1)'; //智能随访任务指定负责人规则，0:按病种与科室查询负责人   1：取诊疗记录中的主治医生
  followSurveyPushAgain: '@integer(1,10)'; //问卷调查患者未反馈消息的重复信息推送，单位天，0表示不需要重复推送
  followArticlePushAgain: '@integer(1,10)'; //健康宣教患者未反馈消息的重复信息推送，单位天，0表示不需要重复推送
}

export interface RoleListItem {
  id?: number;
  hisId?: number;
  roleName?: string;
  describe?: string;
  status?: string | number;
  createTime?: string;
  updateTime?: string;
  clientType?: string;
  dataRight?: 'SELF' | 'DEPT' | 'HIS';
}

export interface MenuItem {
  childMenu: MenuItem[];
  code: string;
  id: number;
  name: string;
  operatePurview: string;
  parentId: number;
  sort: number;
  type: number;
  url: string;
  userId: number;
}

export default {
  list: createApiHooks((data: ListApiRequestParams & UserParams) =>
    request.post<ListApiResponseData<UserList>>(
      '/mch/user/doctorAccount/page',
      data
    )
  ),
  operationSetting: createApiHooks(() =>
    request.get<ApiResponse<OperationSettingResp>>(
      '/mch/his/hospital-config/operation-settings'
    )
  ),
  operationSettingEdit: createApiHooks(
    (
      data: Partial<{
        hisId: string | number; //医院ID
        restrictImageInquiryConfig: string | number; //限制医生图文问诊配置，医生id主键，以逗号隔开
        followAppointDirector: string | number; //智能随访任务指定负责人规则，0:按病种与科室查询负责人   1：取诊疗记录中的主治医生
        followSurveyPushAgain: string | number; //问卷调查患者未反馈消息的重复信息推送，单位天，0表示不需要重复推送
        followArticlePushAgain: string | number; //健康宣教患者未反馈消息的重复信息推送，单位天，0表示不需要重复推送
      }>
    ) =>
      request.put<ApiResponse<any>>(
        '/mch/his/hospital-config/operation-settings',
        data
      )
  ),
  loginlist: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        startDate?: string;
        endDate?: string;
        hospital?: string;
        account?: string;
      }
    ) =>
      request.post<
        ListApiResponseData<{
          account: string;
          accounts: string;
          afterJson: any;
          beforeJson: any;
          browser: string;
          createTime: string;
          endDate: string;
          executionTime: string;
          hisId: string;
          hisName: string;
          id: number;
          inputParam: string;
          ipAddress: string;
          level: string;
          logType: string;
          name: string;
          onlineStatus: string;
          position: string;
          recordType: string;
          remark: string;
          requestDevice: string;
          requestUrl: string;
          role: string;
          sessionId: string;
          site: string;
          startDate: string;
          type: string;
          typeName: string;
          updateTime: string;
        }>
      >('/mch/his/operationRecord/page', data)
  ),
  getByHospit: createApiHooks((params: HosptitalGetBy) =>
    request.post<HosptitalGetByList>('/mch/his/hospitalInfo/getBy', params)
  ),
  updateBatch: createApiHooks(
    (params: { ids: string; operType: string; hisId: string }) =>
      request.post('/mch/user/doctorAccount/updateBatch', params)
  ),
  actionStatus: createApiHooks(
    (params: {
      id: number;
      hisId: string;
      account: string;
      operType: string;
    }) => request.post('/mch/user/doctorAccount/update/status', params)
  ),
  reset: createApiHooks(
    (params: { account: string; hisId: string; id: number }) =>
      request.post('/mch/user/doctorAccount/password/reset', params)
  ),
  addAccount: createApiHooks((params: UserForm) =>
    request.post('/mch/user/doctorAccount/save', params)
  ),
  updateAccount: createApiHooks((params: UserForm) =>
    request.post('/mch/user/doctorAccount/update', params)
  ),
  setKickOut: createApiHooks((params: { accounts: any }) =>
    request.post('/mch/his/operationRecord/kickOut', params)
  ),
  getRoleList: createApiHooks(
    (
      params: ListApiRequestParams & {
        hisId?: string;
        hisName?: string;
        roleName?: string;
        status?: 0 | 1;
        createTimeEnd?: string | number;
        createTimeStart?: string | number;
        clientType?: string | number;
      }
    ) =>
      request.get<ListApiResponseData<RoleListItem>>('/mch/his/role/listPage', {
        params
      })
  ),
  updateRole: createApiHooks((params: RoleListItem) =>
    request.post('/mch/his/role/update', params)
  ),
  cloneRole: createApiHooks((params: { id: string }) =>
    request.post(`/mch/his/role/clone/${params.id}`)
  ),
  addRole: createApiHooks((params: RoleListItem) =>
    request.post('/mch/his/role/add', params)
  ),
  getRoleMenu: createApiHooks(
    (params: { hisId: string; clientType?: string }) =>
      request.get<{ data: MenuItem[] }>('/mch/his/role/listHospitalPurviews', {
        params
      })
  ),
  getSignleRoleMenu: createApiHooks(
    (params: { hisId: string; id: number | string; clientType?: string }) =>
      request.get<{ data: number[] }>('/mch/his/role/getUserRolePurviewIds', {
        params
      })
  ),
  setSignleRoleMenu: createApiHooks(
    (params: { hisId: string; id: number | string; menuIds: string }) =>
      request.post('/mch/his/role/editRolePurviews', params)
  ),
  updateRoleBatch: createApiHooks(
    (params: { ids: string; status: -1 | 0 | 1; hisId: string }) =>
      request.post('/mch/his/role/updateBatch', params)
  ),
  roleList: createApiHooks(
    (params: { hisId?: number | string; clientType?: string | number }) =>
      request.get<{
        data: {
          recordList: Array<{
            id: number;
            roleName: string;
          }>;
        };
      }>('/mch/his/role/listPage', {
        params: {
          ...params,
          status: 1
        }
      })
  ),
  maintenanceList: createApiHooks(
    (params: ListApiRequestParams & { hisId?: number | string }) =>
      request.post<ListApiResponseData<Record<string, unknown>>>(
        '/mch/his/maintenance/query',
        params,
        {
          headers: {
            Accept: 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/json; charset=UTF-8'
          }
        }
      )
  ),
  maintenanceUpdate: createApiHooks(
    (params: {
      id: string;
      switchFlag: number;
      status?: string;
      beginTime?: string;
      endTime?: string;
      content?: string;
    }) =>
      request.put<ListApiResponseData<Record<string, unknown>>>(
        '/mch/his/maintenance/update',
        params,
        {
          headers: {
            Accept: 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/json; charset=UTF-8'
          }
        }
      )
  )
};
