import MyTableList from '@components/myTableList';
import { ListApiResponseData } from '@src/configs/d';
import { Button } from 'antd';
import {
  actionConfirm,
  ActionsWrap,
  handleSubmit,
  LinkButton,
  useModal
} from 'parsec-admin';

import useApi from '../../api';
import useClinicApi from '@pages/authority/clinicDiagnosis/api';
import React from 'react';

import SelectorWithClinicDiagnosis from '../selectorWithClinicDiagnosis';

export default ({
  selectDisease
}: {
  selectDisease: { id: string | number; name: string };
}) => {
  const switchLinkModal = useModal(() => {
    return {
      onSubmit: values => {
        console.log(values);
        const innerPromise = useClinicApi.批量添加;
        const params = {
          ids: values?.clinicDiagnosis?.join(','),
          diseasesId: selectDisease.id
        };
        return handleSubmit(() => {
          return innerPromise.request(params as any);
        });
      },
      title: '关联诊断信息',
      items: [
        {
          label: '病种',
          render: <div>{selectDisease?.name}</div>
        },
        {
          name: 'clinicDiagnosis',
          label: '就诊信息',
          formItemProps: {
            extra:
              '说明：病种与诊断信息创建关联关系后，可用此病种用于随访的病种设置'
          },
          render: <SelectorWithClinicDiagnosis />
        }
      ]
    };
  });

  return (
    <MyTableList
      style={{ margin: '0' }}
      tableTitle={'病种关联诊断信息'}
      succinct={true}
      params={{ diseasesId: selectDisease?.id }}
      getList={({ params }) => {
        console.log(params);
        return useApi.门诊管理绑定的病种列表
          .request(params as any)
          .then(res => {
            return ({
              code: 0,
              msg: '',
              data: {
                currentPage: 1,
                totalCount: (res.data as any)?.length,
                recordList: res.data
              }
            } as unknown) as ListApiResponseData<any>;
          });
      }}
      pagination={{ pageSize: 10 }}
      action={
        <ActionsWrap>
          <Button
            type={'default'}
            onClick={() => {
              // actionConfirm(() => useApi.同步.request({ hisId }), '同步');
              switchLinkModal();
            }}>
            关联诊断信息
          </Button>
        </ActionsWrap>
      }
      columns={[
        {
          title: '病种名称',
          width: 140,
          dataIndex: 'diseasesName'
        },
        {
          title: '关联诊断信息',
          width: 140,
          dataIndex: 'icdName'
        },
        {
          title: '诊断编码',
          width: 140,
          dataIndex: 'icdCode'
        },
        {
          title: '操作',
          width: 200,
          fixed: 'right',
          render: (v, record: any) => {
            return (
              <ActionsWrap max={6}>
                <LinkButton
                  onClick={() => {
                    actionConfirm(() => {
                      return useApi.移除病种Id.request({
                        id: record.id
                      });
                    }, '删除');
                  }}>
                  删除
                </LinkButton>
              </ActionsWrap>
            );
          }
        }
      ]}
    />
  );
};
