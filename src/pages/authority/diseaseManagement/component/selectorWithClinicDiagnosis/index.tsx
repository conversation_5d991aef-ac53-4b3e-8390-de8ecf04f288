import { Divider, Input, Pagination, Select, Row, Button, Col } from 'antd';
import useApi from '@pages/authority/clinicDiagnosis/api';
import { useState } from 'react';

const { Option } = Select;

export default ({
  value,
  onChange
}: {
  value?: any;
  onChange?: (value: any) => void;
}) => {
  const [icdName, setIcdName] = useState<string>('');
  const [searchKey, setSearchKey] = useState<string>('');
  const [pageNum, setPageNum] = useState<number>(1);

  const {
    data: { data }
  } = useApi.诊断与病种分页({
    needInit: true,
    params: {
      pageNum: pageNum,
      numPerPage: 10,
      icdName: searchKey
    },
    initValue: {}
  });

  return (
    <Select
      style={{ width: 300 }}
      mode='multiple'
      placeholder='请选择主要诊断'
      value={value}
      onChange={v => onChange && onChange(v)}
      dropdownRender={menu => (
        <>
          {menu}
          <Divider style={{ margin: '8px 0' }} />
          <Row style={{ padding: '0 8px 4px' }}>
            <Col span={18}>
              <Input
                value={icdName}
                onChange={e => setIcdName(e.target.value)}
                placeholder={'输入名称可筛选'}
              />
            </Col>
            <Col span={6}>
              <Button
                onClick={() => {
                  setSearchKey(icdName);
                }}>
                搜索
              </Button>
            </Col>
          </Row>
          <Row style={{ padding: '0 8px 4px' }}>
            <Pagination
              onChange={page => setPageNum(page)}
              size='small'
              showSizeChanger={false}
              showQuickJumper={false}
              total={data?.totalCount}
            />
          </Row>
        </>
      )}>
      {data?.recordList?.map(item => (
        <Option value={item?.id} key={item?.id}>
          {item.icdName}
        </Option>
      ))}
    </Select>
  );
};
