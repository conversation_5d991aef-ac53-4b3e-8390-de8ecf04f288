import MyTableList from '@components/myTableList';
import { ListApiResponseData } from '@src/configs/d';
import { Button, Cascader } from 'antd';
import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  handleSubmit,
  LinkButton,
  useModal
} from 'parsec-admin';

import useApi from '../../api';
import React, { useCallback } from 'react';
import env from '@configs/env';

export default ({
  selectDisease
}: {
  selectDisease: { id: string | number; name: string };
}) => {
  const hisId = env.hisId;

  const filterArray = useCallback((arr: Array<any>): any => {
    return arr?.map(x => {
      return {
        label: x.name,
        value: x.id,
        children: x?.children ? filterArray(x?.children) : undefined
      };
    });
  }, []);

  const {
    data: { data: doctorList }
  } = useApi.医生账号列表({
    params: { hisType: 4 },
    needInit: !!hisId,
    initValue: {
      data: []
    }
  });

  const {
    data: {
      data: { recordList: list }
    }
  } = useApi.科室管理列表({
    params: { hisId, hisType: '4' },
    needInit: !!hisId,
    initValue: {
      data: {
        recordList: []
      }
    }
  });

  const switchLinkModal = useModal(
    ({ id, modeType }: { modeType: 'add' | 'edit'; id: number | string }) => {
      return {
        onSubmit: values => {
          console.log(values);
          const innerPromise =
            modeType === 'add'
              ? useApi.添加病种关联科室
              : useApi.编辑病种关联科室;
          const params =
            modeType === 'add'
              ? {
                  ...values,
                  diseasesId: selectDisease?.id,
                  deptId: values.deptId?.slice(-1)[0]
                }
              : {
                  ...values,
                  id,
                  diseasesId: selectDisease?.id,
                  deptId: values.deptId?.slice(-1)[0]
                };
          return handleSubmit(() => {
            return innerPromise.request(params);
          });
        },
        title: '编辑',
        items: [
          {
            name: 'diseasesId',
            label: '病种',
            render: () => {
              return selectDisease?.name;
            }
          },
          {
            name: 'deptId',
            label: '关联科室',
            render: (
              <Cascader
                placeholder='请选择上级科室'
                options={filterArray(list)}
              />
            )
          },
          {
            name: 'directorId',
            label: '随访责任人',
            render: (
              <ArrSelect
                options={
                  doctorList?.map(x => {
                    return {
                      label: x.name,
                      value: x.id
                    };
                  }) || []
                }
              />
            )
          }
        ]
      };
    },
    [doctorList, selectDisease]
  );

  return (
    <MyTableList
      style={{ margin: '0' }}
      tableTitle={'病种关联科室'}
      succinct={true}
      params={{ id: selectDisease?.id }}
      getList={({ params }) => {
        console.log(params);
        return useApi.查询病种关联的科室列表
          .request(params as any)
          .then(res => {
            return ({
              code: 0,
              msg: '',
              data: {
                currentPage: 1,
                totalCount: (res.data as any)?.length,
                recordList: res.data
              }
            } as unknown) as ListApiResponseData<any>;
          });
      }}
      pagination={{ pageSize: 10 }}
      action={
        <ActionsWrap>
          <Button
            type={'default'}
            onClick={() => {
              // actionConfirm(() => useApi.同步.request({ hisId }), '同步');
              switchLinkModal({ modeType: 'add' });
            }}>
            添加
          </Button>
        </ActionsWrap>
      }
      columns={[
        {
          title: '病种名称',
          width: 140,
          dataIndex: 'name',
          render: () => {
            return selectDisease?.name;
          }
        },
        {
          title: '关联科室',
          width: 140,
          dataIndex: 'deptName'
        },
        {
          title: '随访负责人',
          width: 140,
          dataIndex: 'directorName'
        },
        {
          title: '操作',
          width: 200,
          fixed: 'right',
          render: (v, record: any) => {
            return (
              <ActionsWrap max={6}>
                <LinkButton
                  onClick={() => {
                    const getParentId = (arr: Array<any>) => {
                      let parentId = 0;
                      arr.forEach(item => {
                        if (item.value === record.deptId) {
                          parentId = record.deptId;
                        } else if (item.children) {
                          item.children.forEach(i => {
                            if (i.value === record.deptId) {
                              parentId = item.value;
                            }
                          });
                        }
                      });
                      return parentId;
                    };
                    const innerArray = getParentId(filterArray(list));
                    switchLinkModal({
                      modeType: 'edit',
                      ...record,
                      deptId: [innerArray, record.deptId]
                    });
                  }}>
                  编辑
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    actionConfirm(() => {
                      return useApi.删除病种科室关联.request({
                        id: record.id
                      });
                    }, '删除');
                  }}>
                  删除
                </LinkButton>
              </ActionsWrap>
            );
          }
        }
      ]}
    />
  );
};
