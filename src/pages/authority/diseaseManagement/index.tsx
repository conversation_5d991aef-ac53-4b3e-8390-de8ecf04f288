import React, { useState } from 'react';
import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  handleSubmit,
  LinkButton,
  useModal
} from 'parsec-admin';
import useApi from './api';
import MyTableList from '@components/myTableList';
import { <PERSON><PERSON>, Col, Drawer, Input, Row, Tabs } from 'antd';
import DiseaseTable from './component/diseaseTable';
import ClinicDiagnosisTable from './component/clinicDiagnosisTable';

const { TabPane } = Tabs;
const { TextArea } = Input;

export default () => {
  const [visible, setVisible] = useState<boolean>(false);

  const [selectDisease, setSelectDisease] = useState<any>();

  const {
    data: { data }
  } = useApi.病种详情({
    needInit: !!selectDisease?.id,
    params: {
      id: selectDisease?.id
    },
    initValue: { data: {} }
  });

  const switchModal = useModal(
    ({ id, modeType }: { modeType: 'add' | 'edit'; id: number | string }) => {
      return {
        onSubmit: values => {
          const innerPromise =
            modeType === 'add' ? useApi.病种添加 : useApi.病种修改;
          const params = modeType === 'add' ? values : { ...values, id };
          return handleSubmit(() => {
            return innerPromise.request(params);
          });
        },
        title: '编辑',
        items: [
          { name: 'name', label: '病种名称' },
          { name: 'sn', label: '病种编码' },
          {
            name: 'summary',
            label: '病种介绍',
            render: (
              <TextArea
                rows={6}
                autoSize={true}
                maxLength={200}
                placeholder='请输入病种介绍'
              />
            )
          }
        ]
      };
    }
  );

  return (
    <React.Fragment>
      <MyTableList
        tableTitle={'维护管理'}
        getList={({ params }: { params: any }) => {
          return useApi.病种管理分页列表.request(params);
        }}
        exportExcelButton={true}
        action={
          <ActionsWrap>
            {/*<Button*/}
            {/*  type={'default'}*/}
            {/*  onClick={() => {*/}
            {/*    console.log(11);*/}
            {/*  }}>*/}
            {/*  病种同步*/}
            {/*</Button>*/}
            <Button
              type={'default'}
              onClick={() => {
                // actionConfirm(() => useApi.同步.request({ hisId }), '同步');
                switchModal({ modeType: 'add' });
              }}>
              创建病种
            </Button>
          </ActionsWrap>
        }
        columns={[
          {
            title: '病种名称',
            width: 140,
            dataIndex: 'name',
            searchIndex: 'name',
            search: true
          },
          {
            title: '病种编码',
            width: 140,
            dataIndex: 'sn'
          },
          {
            title: '创建人',
            width: 140,
            dataIndex: 'creatorName'
          },
          {
            title: '创建时间',
            width: 140,
            dataIndex: 'createTime'
          },
          {
            title: '状态',
            width: 140,
            dataIndex: 'state',
            render: v => (v === 'ON' ? '已启用' : '已停用'),
            search: (
              <ArrSelect
                options={[
                  { value: 'ON', children: '启用' },
                  { value: 'OFF', children: '停用' }
                ]}
              />
            )
          },
          {
            title: '操作',
            width: 200,
            fixed: 'right',
            render: (v, record: any) => {
              return (
                <ActionsWrap max={6}>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () => {
                          return useApi.病种停用或启用.request({
                            id: record.id
                          });
                        },
                        record.state === 'OFF' ? '启用' : '停用'
                      );
                    }}>
                    {record.state === 'OFF' ? '启用' : '停用'}
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      setSelectDisease(record);
                      setVisible(true);
                    }}>
                    详情
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      switchModal({ modeType: 'edit', ...record });
                    }}>
                    编辑
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(() => {
                        return useApi.病种删除.request({
                          id: record.id
                        });
                      }, '删除');
                    }}>
                    删除
                  </LinkButton>
                </ActionsWrap>
              );
            }
          }
        ]}
      />
      <Drawer
        width={800}
        visible={visible}
        onClose={() => setVisible(false)}
        title={'病种详情'}>
        <div>
          <Row>
            <Col span={8}>病种名称:{data?.name}</Col>
            <Col span={8}>创建人:{data?.creatorName}</Col>
            <Col span={8}>创建时间:{data?.createTime}</Col>
          </Row>
          <Row style={{ marginTop: '10px' }}>
            <Col span={24}>病种介绍:{data?.summary}</Col>
          </Row>

          <Tabs defaultActiveKey='1'>
            <TabPane tab='病种关联科室' key='1'>
              <DiseaseTable selectDisease={selectDisease} />
            </TabPane>
            <TabPane tab='病种关联诊断信息' key='2'>
              <ClinicDiagnosisTable selectDisease={selectDisease} />
            </TabPane>
          </Tabs>
        </div>
      </Drawer>
    </React.Fragment>
  );
};
