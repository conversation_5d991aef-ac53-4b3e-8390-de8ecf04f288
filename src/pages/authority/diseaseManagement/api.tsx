import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiResponseData,
  ListApiRequestParams,
  ApiResponse
} from '@apiHooks';
import { DeptItem } from '@pages/hospital/departmentSmart/api';

export default {
  科室管理列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        hisType?: string;
      }
    ) =>
      request
        .get<ApiResponse<DeptItem[]>>('/mch/his/deptMain', { params: data })
        .then(res => {
          return {
            ...res,
            data: {
              code: res.data.code,
              msg: res.data.msg,
              data: {
                currentPage: 1,
                totalCount: 1,
                recordList: res.data.data || []
              }
            }
          };
        })
  ),
  医生账号列表: createApiHooks((params: { hisType: number }) =>
    request.get<
      ApiResponse<
        {
          id: '@integer(1,100)'; //主键ID
          hisId: 40009; //医院ID
          name: '@cname'; //医生姓名
        }[]
      >
    >('/mch/his/doctorMain/list', { params })
  ),
  病种添加: createApiHooks(
    (data: {
      name: '@cword(4)'; //病种
      sn?: '@string(10)'; //编码
      summary: '@csentence'; //病种介绍
    }) => request.post<ApiResponse<any>>('/mch/follow/diseases/add', data)
  ),
  添加病种关联科室: createApiHooks(
    (data: {
      diseasesId: '@natural(100,500)'; //病种id
      deptId: '@natural(100, 500)'; //科室id
      directorId: '@natural(100, 500)'; //负责人id（doctor主键id）
    }) => request.post<ApiResponse<any>>('/mch/follow/diseases/dept', data)
  ),
  编辑病种关联科室: createApiHooks(
    (data: {
      id: '@natural(100,500)'; //关联表id
      deptId: '@natural(100, 500)'; //科室id
      directorId: '@natural(100, 500)'; //负责人id（doctor主键id）
    }) => request.put<ApiResponse<any>>('/mch/follow/diseases/dept', data)
  ),
  删除病种科室关联: createApiHooks((
    data: { id: string | number } //主键ID
  ) =>
    request.delete<ApiResponse<any>>(`/mch/follow/diseases/dept/${data.id}`)
  ),
  病种修改: createApiHooks(
    (data: {
      id: '@integer(1,100)'; //主键ID
      name: '@cword(4)'; //病种
      sn: '@string(10)'; //编码
      summary: '@csentence'; //病种介绍
    }) => request.put<ApiResponse<any>>('/mch/follow/diseases/update', data)
  ),
  病种停用或启用: createApiHooks((
    data: { id: string | number } //主键ID
  ) => request.put<ApiResponse<any>>(`/mch/follow/diseases/state/${data.id}`)),
  病种删除: createApiHooks((
    data: { id: string | number } //主键ID
  ) => request.delete<ApiResponse<any>>(`/mch/follow/diseases/${data.id}`)),
  病种详情: createApiHooks((
    data: { id: string | number } //主键ID
  ) =>
    request.get<
      ApiResponse<{
        id: '@integer(1,1000)'; //诊疗记录主键ID
        createTime: '@datetime'; //创建时间
        updateTime: '@datetime'; //修改时间
        hisId: '@integer(10000,9999)'; //医院ID
        state: "@pick('ON','OFF')"; //状态：ON启用、OFF停用
        name: '@cword(4)'; //病种名称
        sn: 'SN@integer(1000,9999)'; //病种编码
        summary: '@csentence()'; //病种介绍
        creatorId: '@integer(1,100)'; //创建人ID
        creatorName: '@cname()'; //创建人姓名
      }>
    >(`/mch/follow/diseases/${data.id}`)
  ),
  查询病种关联的科室列表: createApiHooks((
    data: { id: string | number | undefined } //主键ID
  ) =>
    request.get<
      ApiResponse<{
        id: '@integer(1,1000)'; //诊疗记录主键ID
        createTime: '@datetime'; //创建时间
        updateTime: '@datetime'; //修改时间
        hisId: '@integer(10000,9999)'; //医院ID
        state: "@pick('ON','OFF')"; //状态：ON启用、OFF停用
        name: '@cword(4)'; //病种名称
        sn: 'SN@integer(1000,9999)'; //病种编码
        summary: '@csentence()'; //病种介绍
        creatorId: '@integer(1,100)'; //创建人ID
        creatorName: '@cname()'; //创建人姓名
      }>
    >(`/mch/follow/diseases/dept/${data.id}`)
  ),
  门诊管理绑定的病种列表: createApiHooks(
    (params: {
      diseasesId: string | number;
      icdName: string;
      icdCode: string;
    }) =>
      request.get<
        ApiResponse<
          {
            id: '@natural'; //主键ID
            createTime: '@datetime'; //创建时间
            updateTime: '@datetime'; //更新时间
            hisId: '8900'; //医院ID
            state: "@pick('ON','OFF')"; //状态,ON,OFF
            diseasesId: '@natural'; //病种ID
            diseasesName: '@cword(5)病'; //病种名称
            diseasesSn: '@word(5)'; //病种编码
            icdId: 'natural'; //诊断ID
            icdName: '@cword(5)病'; //诊断名称
            icdCode: '@word(5)'; //诊断编码
            icdCategory: '@cword(5)类'; //诊断类别
            operatorId: '@natural'; //操作人ID
            operatorName: '@cname'; //操作人姓名
          }[]
        >
      >('/mch/follow/icd-diseases/list', { params })
  ),
  移除病种Id: createApiHooks((params: { id: string | number }) =>
    request.get('/mch/follow/icd-diseases/removeDiseasesId', { params })
  ),
  病种管理分页列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        name?: string;
        state?: 'ON' | 'OFF';
        export?: 0 | 1;
      }
    ) =>
      request.get<
        ListApiResponseData<{
          id: '@integer(1,1000)'; //诊疗记录主键ID
          createTime: '@datetime'; //创建时间
          updateTime: '@datetime'; //修改时间
          hisId: '@integer(10000,9999)'; //医院ID
          state: "@pick('ON','OFF')"; //状态：ON启用、OFF停用
          name: '@cword(4)'; //病种名称
          sn: 'SN@integer(1000,9999)'; //病种编码
          deptId: '@integer(1,100)'; //科室主键ID
          deptName: '@cword(2)科室'; //科室名称
          creatorId: '@integer(1,100)'; //创建人ID
          creatorName: '@cname()'; //创建人姓名
        }>
      >('/mch/follow/diseases/page', {
        params
      })
  )
};
