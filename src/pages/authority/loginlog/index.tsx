import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { LinkButton, actionConfirm, DayRangePicker } from 'parsec-admin';
import useApi from '../api';
import { Button } from 'antd';
import MyTableList from '@components/myTableList';
import permisstion from '@utils/permisstion';

export default () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );
  const [selectedRowAccounts, setSelectedRowAccounts] = useState<
    (string | number)[]
  >([]);
  const [selectedRowAccountObjs, setSelectedRowAccountObjs] = useState<
    { [key: string]: any }[]
  >([]);

  // 踢出
  const KickOut = useCallback(
    (ids, accounts) =>
      actionConfirm(
        () =>
          useApi.setKickOut.request({
            accounts: accounts
          }),
        ids.length > 1 ? `踢出此次选中的${ids.length}个账号` : '踢出该账号'
      ).then(res => {
        setTimeout(() => {
          setSelectedRowAccounts([]);
          setSelectedRowKeys([]);
          setSelectedRowAccountObjs([]);
        }, 100);
      }),
    []
  );
  useEffect(() => {
    const d: any[] = [];
    for (const i of selectedRowAccountObjs) {
      d.push(i.account);
    }
    setSelectedRowAccounts(d);
  }, [selectedRowAccountObjs]);
  return (
    <MyTableList
      tableTitle={'登录日志'}
      getList={({ params }) => useApi.loginlist.request(params)}
      paginationExtra={
        <div>
          {!!selectedRowKeys.length && permisstion.canKickout && (
            <>
              <Button
                type={'default'}
                style={{ marginRight: '10px' }}
                onClick={() => KickOut(selectedRowKeys, selectedRowAccounts)}>
                批量踢出
              </Button>
              <span> 已选择{selectedRowKeys.length}条</span>
            </>
          )}
        </div>
      }
      //
      rowSelection={{
        selectedRowKeys,
        onChange: selectedRowKeys => setSelectedRowKeys(selectedRowKeys),
        onSelect: (record: any, selected) => {
          if (selected) {
            setSelectedRowAccountObjs([...selectedRowAccountObjs, record]);
          } else {
            const d: any[] = JSON.parse(JSON.stringify(selectedRowAccountObjs));
            const idx = d.findIndex(e => e.id === record.id);
            if (idx > -1) {
              d.splice(idx, 1);
              setTimeout(() => {
                setSelectedRowAccountObjs(d);
              });
            }
          }
        },
        getCheckboxProps: (record: any) => {
          return {
            disabled: record.onlineStatus !== '1' // Column configuration not to be checked
          };
        }
      }}
      columns={useMemo(
        () => [
          {
            title: '账号',
            dataIndex: 'account',
            width: 100,
            search: true
          },
          {
            title: '所属医院',
            dataIndex: 'hisName',
            width: 100
          },
          {
            title: '姓名',
            dataIndex: 'name',
            width: 100
          },
          {
            title: '登陆IP',
            dataIndex: 'ipAddress',
            width: 150
          },
          {
            title: '浏览器',
            dataIndex: 'browser',
            width: 100
          },
          {
            title: '登陆时间',
            dataIndex: 'createTime',
            width: 200,
            search: <DayRangePicker placeholder={['开始时间', '结束时间']} />,
            searchIndex: ['startDate', 'endDate']
          },
          {
            title: '操作',
            fixed: 'right',
            width: 80,
            render: record =>
              record.onlineStatus === '1' ? (
                permisstion.canKickout && (
                  <LinkButton
                    onClick={() => {
                      KickOut(record.id, record.account);
                    }}>
                    踢出
                  </LinkButton>
                )
              ) : (
                <span>离线</span>
              )
          }
        ],
        [KickOut]
      )}
    />
  );
};
