import React, { useState, useEffect } from 'react';
import { Checkbox } from 'antd';
import styled from 'styled-components';
import useApi, { MenuItem } from '../api';

interface Iprops {
  data: MenuItem[];
  id: number | string;
  hisId: string;
  onChange: (item: number[]) => void;
}

export default (props: Iprops) => {
  const { data, id, hisId, onChange } = props;
  const [checkedList, setCheckedList] = useState<number[]>([]);

  useEffect(() => {
    setCheckedList([]);
    useApi.getSignleRoleMenu.request({ id, hisId }).then(({ data }) => {
      setCheckedList(data);
    });
  }, [id, hisId]);

  // 获取每个元素控制的子元素
  const getAllSubValues = (dataList: MenuItem[] | null): number[] => {
    if (!dataList) {
      return [];
    }
    let res = [] as number[];
    dataList.forEach(item => {
      res.push(item.id);
      if (item.childMenu) {
        res = [...res, ...getAllSubValues(item.childMenu)];
      }
    });
    return res;
  };

  // 根据Id值获取某一项
  const findItemById = (dataList: MenuItem[] | null, id: number) => {
    if (!id || !dataList) {
      return null;
    }
    let res = null as any;
    dataList.some(item => {
      if (item.id === id) {
        res = item;
        return true;
      }
      if (item.childMenu && item.childMenu.length > 0) {
        const resItem = findItemById(item.childMenu, id);
        console.log(resItem, item.childMenu, id);
        if (resItem) {
          res = resItem;
          return true;
        }
      }
      return false;
    });
    return res;
  };

  // 递归调用
  const setResult = (dataList: MenuItem[], parentId: number, res: number[]) => {
    if (!parentId) {
      return;
    }
    dataList.forEach(subItem => {
      if (subItem.id === parentId) {
        res.push(subItem.id);
        const parentItem = findItemById(data, parentId);
        if (parentItem) {
          getAllParentValues(data, parentItem, res);
        }
      } else if (subItem.childMenu && subItem.childMenu.length > 0) {
        setResult(subItem.childMenu, parentId, res);
      }
    });
  };

  // 获取每个元素的所有父元素
  const getAllParentValues = (
    topDataList: MenuItem[] | null,
    item: MenuItem,
    res: number[]
  ): void => {
    if (!topDataList || !item.parentId) {
      return;
    }
    const parentId = item.parentId;
    setResult(topDataList, parentId, res);
  };

  const handleItemChange = (
    dataList: MenuItem[] | null,
    id: number,
    item: MenuItem
  ) => {
    //获取自己id以及所有子元素id
    const allSubItems = [...getAllSubValues(dataList), id];
    const prevHasSelect = checkedList.includes(id);
    if (prevHasSelect) {
      setCheckedList(checkedList.filter(id => !allSubItems.includes(id)));
    } else {
      const res = [] as number[];
      getAllParentValues(data, item, res);
      setCheckedList(
        Array.from(new Set([...checkedList, ...allSubItems, ...res]))
      );
    }
  };

  //递归获取元素
  const newList = (dataList: MenuItem[], trIndex: number) => {
    return dataList.map(item => {
      return (
        <div key={item.id} className={`tr tr${trIndex}`}>
          <div className={`td td${trIndex}`}>
            <Checkbox
              onChange={() => handleItemChange(item.childMenu, item.id, item)}
              checked={checkedList.includes(item.id)}>
              {item.name}
            </Checkbox>
          </div>
          {item.childMenu && item.childMenu.length > 0 && (
            <div className={`td td${trIndex + 1}`}>
              {item.childMenu && newList(item.childMenu, trIndex + 1)}
            </div>
          )}
        </div>
      );
    });
  };

  useEffect(() => {
    onChange(checkedList);
  }, [onChange, checkedList]);

  return <WrapperTable>{newList(data, 0)}</WrapperTable>;
};

const WrapperTable = styled.div`
  width: 100%;
  .td0 {
    width: 140px;
  }
  .td1 {
    width: calc(100% - 140px);
  }
  .td2 {
    width: 100%;
  }
  .tr.tr1 {
    border-right: none;
  }
  .tr.tr2 {
    border-right: none;
  }
  .tr.tr3 {
    border-right: none;
  }

  .tr {
    border: 1px solid #eee;
    border-bottom: none;
    display: flex;
    align-items: center;
    > .td {
      padding: 5px 0 5px 20px;
    }
    > .td > .tr:first-of-type {
      margin-top: -5px;
      border-top: none;
    }
    > .td > .tr:last-of-type {
      border-bottom: none;
      margin-bottom: -5px;
    }
  }
  .tr:last-of-type {
    border-bottom: 1px solid #eee;
  }
`;
