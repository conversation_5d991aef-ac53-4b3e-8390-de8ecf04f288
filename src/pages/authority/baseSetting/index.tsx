import { CardLayout, FormDescriptions } from 'parsec-admin';
import React, { useEffect } from 'react';
import { InputNumber, Radio, Form } from 'antd';
import useApi from '../api';
import env from '@configs/env';
import { TransferChange } from '@kqinfo/ui';

const debounce = (func: any) => {
  let timeOutId: NodeJS.Timer;
  return (value: any) => {
    if (timeOutId) {
      clearTimeout(timeOutId);
    }
    timeOutId = setTimeout(() => {
      func(value);
    }, 300);
  };
};

export default () => {
  const [form] = Form.useForm();

  const {
    data: { data }
  } = useApi.operationSetting({
    needInit: true,
    initValue: {}
  });

  const debounceFunc = debounce(value => {
    useApi.operationSettingEdit.request({ ...value, hisId: env.hisId });
  });

  useEffect(() => {
    data && Object.keys(data)?.length > 0 && form.setFieldsValue(data);
  }, [data, form]);

  return (
    <CardLayout>
      <FormDescriptions
        title={'运营规则设置'}
        form={form}
        edit={true}
        formProps={{
          onValuesChange: value => {
            debounceFunc(value);
          }
        }}
        items={[
          {
            span: 24,
            label: '互联网医院就诊患者由就诊医生随访',
            name: 'followAppointDirector',
            formItemProps: {
              render: (
                <Radio.Group>
                  <Radio value={1}>开启</Radio>
                  <Radio value={0}>关闭</Radio>
                </Radio.Group>
              )
            }
          },
          {
            span: 24,
            label: '问卷调查患者未反馈消息的重复信息推送',
            name: 'followSurveyPushAgain',
            formItemProps: {
              render: (
                <TransferChange>
                  {(onchange, value) => (
                    <InputNumber
                      type={'number'}
                      min={0}
                      value={value}
                      onChange={value => {
                        value >= 0 && onchange(value);
                      }}
                      addonBefore='首次推送后用户无反馈'
                      addonAfter='天后再次推送'
                    />
                  )}
                </TransferChange>
              )
            }
          },
          {
            span: 24,
            label: '健康宣教患者未反馈消息的重复信息推送',
            name: 'followArticlePushAgain',
            formItemProps: {
              render: (
                <TransferChange>
                  {(onchange, value) => (
                    <InputNumber
                      type={'number'}
                      min={0}
                      value={value}
                      onChange={value => {
                        value >= 0 && onchange(value);
                      }}
                      addonBefore='首次推送后用户无反馈'
                      addonAfter='天后再次推送'
                    />
                  )}
                </TransferChange>
              )
            }
          }
        ]}
      />
    </CardLayout>
  );
};
