import React, { Fragment, useMemo, useState } from 'react';
import {
  ActionsWrap,
  LinkButton,
  actionConfirm,
  DayRangePicker,
  ArrSelect,
  handleSubmit
} from 'parsec-admin';
import useApi from './api';
import MyTableList from '@components/myTableList';
import CopyToClipboard from 'react-copy-to-clipboard';
import { Button, message, Modal, Radio, Form } from 'antd';
import { useHistory } from 'react-router-dom';
import moment from 'moment';
import env from '@configs/env';
import permisstion from '@utils/permisstion';

const articlTypes: any = {
  1: '图文',
  2: '视频'
};
export default () => {
  const history = useHistory();
  const hisId = env.hisId;

  const status = useMemo(
    () => [
      { value: '', children: '全部' },
      { value: 'DRAFT', children: '草稿' },
      { value: 'OFFLINE', children: '未发布' },
      { value: 'ONLINE', children: '已发布' }
    ],
    []
  );

  const {
    data: { data: typeData }
  } = useApi.文章类型列表({
    params: { hisId, pageNum: 1, numPerPage: 999 },
    needInit: true,
    initValue: { data: { recordList: [] } }
  });

  const [publishData, setPublishData] = useState<{
    id: string;
    type: 'OFFLINE' | 'ONLINE';
  } | null>();

  return (
    <Fragment>
      <MyTableList
        tableTitle='文章列表'
        rowKey='id'
        action={
          <ActionsWrap>
            <Button
              onClick={() => {
                history.push('/content/notice/new');
              }}>
              新增
            </Button>

            <Button
              onClick={() => {
                history.push('/content/noticeTypes');
              }}>
              维护类型
            </Button>
          </ActionsWrap>
        }
        getList={({ pagination: { current }, params }) => {
          if (params.searchStart) {
            params.searchStart = moment(params.searchStart).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          }

          if (params.searchEnd) {
            params.searchEnd = moment(params.searchEnd).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          }

          return useApi.文章列表.request({ ...params, pageNum: current });
        }}
        showTool={false}
        columns={useMemo(
          () => [
            {
              title: '文章标题',
              dataIndex: 'title',
              width: 200,
              search: true,
              render: (text, record: any) => {
                return (
                  <span>
                    {record.state === 'DRAFT' && (
                      <span style={{ color: '#ED4E56' }}>[草稿]</span>
                    )}
                    <span>{text}</span>
                  </span>
                );
              }
            },
            {
              title: '文章栏目',
              dataIndex: 'typeName',
              width: 100,
              searchIndex: 'type',
              search: (
                <ArrSelect
                  options={(typeData?.recordList || []).map(level => {
                    return {
                      value: level.id,
                      children: level.typeName
                    };
                  })}
                />
              )
            },
            {
              title: '文章类型',
              dataIndex: 'articleType',
              width: 100,
              render: v => articlTypes[v],
              searchIndex: 'articleType',
              search: (
                <ArrSelect
                  options={[
                    {
                      value: 1,
                      children: '图文'
                    },
                    {
                      value: 2,
                      children: '视频'
                    }
                  ]}
                />
              )
            },
            {
              title: '作者',
              dataIndex: 'author',
              width: 100
            },
            {
              title: '排序',
              dataIndex: 'sortNo',
              width: 100
            },
            {
              title: '发布人',
              dataIndex: 'publisher',
              width: 100
            },
            {
              title: '阅读人次',
              dataIndex: 'pv',
              width: 100
            },
            {
              title: '点赞数',
              dataIndex: 'popular',
              width: 100
            },
            // {
            //   title: '转发数',
            //   dataIndex: 'transmit',
            //   width: 100
            // },
            {
              title: '收藏数',
              dataIndex: 'collection',
              width: 100
            },
            {
              title: '公开状态',
              dataIndex: 'state',
              width: 100,
              search: <ArrSelect options={status} />,
              render: text => {
                return status.find(x => x.value === text)?.children || '';
              }
            },
            {
              title: '更新时间',
              dataIndex: 'updateTime', //updateTime
              width: 300,
              search: (
                <DayRangePicker
                  placeholder={['开始时间', '结束时间']}
                  disabledDate={current => {
                    return current && current > moment().endOf('day');
                  }}
                />
              ),
              searchIndex: ['searchStart', 'searchEnd']
            },
            {
              title: '操作',
              fixed: 'right',
              width: 250,
              align: 'left',
              render: (v, record: any) => {
                return (
                  <ActionsWrap max={99}>
                    {record.state === 'DRAFT' && (
                      <LinkButton
                        onClick={() => {
                          setPublishData({
                            id: record.id,
                            type: 'OFFLINE'
                          });
                        }}>
                        发布
                      </LinkButton>
                    )}
                    {permisstion.canArticleModify && (
                      <LinkButton
                        onClick={() => {
                          record.id &&
                            history.push('/content/notice/' + record.id);
                        }}>
                        编辑
                      </LinkButton>
                    )}

                    {record.state === 'OFFLINE' && (
                      <LinkButton
                        onClick={() => {
                          Modal.confirm({
                            title: (
                              <span>
                                <span
                                  style={{
                                    color: '#F04134'
                                  }}>
                                  公开后所有人可见
                                </span>
                                <span
                                  style={{
                                    color: '#999999'
                                  }}>
                                  ，你还要继续吗？
                                </span>
                              </span>
                            ),
                            onOk: () => {
                              handleSubmit(
                                () =>
                                  useApi.编辑文章.request({
                                    id: record.id,
                                    state: 'ONLINE'
                                  }),
                                '设置公开'
                              );
                            }
                          });
                        }}>
                        公开
                      </LinkButton>
                    )}
                    {record.state === 'ONLINE' &&
                      permisstion.canArticleOffline && (
                        <LinkButton
                          onClick={() => {
                            Modal.confirm({
                              title: (
                                <span>
                                  <span
                                    style={{
                                      color: '#F04134'
                                    }}>
                                    设为私密仅管理员可见
                                  </span>
                                  <span
                                    style={{
                                      color: '#999999'
                                    }}>
                                    ，你还要继续吗？
                                  </span>
                                </span>
                              ),
                              onOk: () => {
                                handleSubmit(
                                  () =>
                                    useApi.编辑文章.request({
                                      id: record.id,
                                      state: 'OFFLINE'
                                    }),
                                  '设置私密'
                                );
                              }
                            });
                          }}>
                          私密
                        </LinkButton>
                      )}
                    {permisstion.canArticleDelete && (
                      <LinkButton
                        onClick={() => {
                          actionConfirm(
                            () =>
                              useApi.删除文章.request({
                                id: record.id
                              }),
                            '删除'
                          );
                        }}>
                        删除
                      </LinkButton>
                    )}

                    {record.state !== 'DRAFT' && (
                      <CopyToClipboard
                        text={
                          env.smartFollowUp
                            ? `${window.location.origin}/patients/p${hisId}/#/pages/notice/index?id=${record.id}`
                            : `${window.location.origin}/patients/p${hisId}-his/#/pages/microsite/article-detail/index?id=${record.id}`
                        }
                        onCopy={() => message.success('复制成功')}>
                        <LinkButton className='btn' key='submit' type='primary'>
                          链接
                        </LinkButton>
                      </CopyToClipboard>
                    )}
                  </ActionsWrap>
                );
              }
            }
          ],
          [hisId, history, status, typeData]
        )}
      />
      <Modal
        title='发布提示'
        visible={!!publishData}
        onCancel={() => {
          setPublishData(null);
        }}
        width={400}
        onOk={() => {
          if (!publishData) {
            return;
          }
          // handleValidate(publishData.type);
          handleSubmit(
            () =>
              useApi.编辑文章.request({
                id: publishData.id,
                state: 'ONLINE'
              }),
            '发布'
          );
          setPublishData(null);
        }}
        destroyOnClose={true}>
        <Form>
          <Form.Item label='发布形式'>
            <Radio.Group
              defaultValue='OFFLINE'
              onChange={e => {
                if (!publishData) {
                  return;
                }
                setPublishData({
                  ...publishData,
                  type: e.target.value as any
                });
              }}
              options={[
                { label: '公开', value: 'ONLINE' },
                { label: '秘密', value: 'OFFLINE' }
              ]}
            />
          </Form.Item>
        </Form>
      </Modal>
    </Fragment>
  );
};
