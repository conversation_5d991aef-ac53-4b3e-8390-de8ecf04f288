import React from 'react';
import { Button } from 'antd';
import MyTableList from '@components/myTableList';
import // SortableContainer,
// SortableElement,
// SortableHandle
'react-sortable-hoc';
// import { MenuOutlined } from '@ant-design/icons';
// import arrayMove from 'array-move';
import {
  CardLayout,
  handleSubmit,
  useModal,
  actionConfirm,
  ActionsWrap,
  LinkButton
} from 'parsec-admin';

import useApi from './api';
import env from '@src/configs/env';
import permisstion from '@utils/permisstion';

// const DragHandle = SortableHandle(() => (
//   <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />
// ));

// const SortableItem = SortableElement((props: any) => <tr {...props} />);
// const SortableContainerI = SortableContainer((props: any) => (
//   <tbody {...props} />
// ));

export default () => {
  const hisId = env.hisId;
  // const [dataSource, setDataSource] = useState<any[]>([]);

  // const { data, request, loading } = useApi.文章类型列表({
  //   params: { hisId },
  //   needInit: true,
  //   initValue: { data: { recordList: [] } }
  // });

  // useEffect(() => {
  //   console.log(data.data?.recordList);
  //   setDataSource(data.data?.recordList || []);
  // }, []);

  const switchModalVisible = useModal(({ id, userFlag }) => {
    const isEdit = !!id;
    return {
      title: isEdit ? '编辑账号' : '新增账号',
      onSubmit: values =>
        handleSubmit(() => {
          if (isEdit) {
            return useApi.编辑文章类型.request({
              ...values
            });
          } else {
            delete values.id;
            return useApi.新增文章类型.request({
              ...values,
              hisId: hisId
            });
          }
        }),
      myFormProps: {
        initValues: {
          status: '0'
        }
      } as any,
      items: [
        {
          name: 'id',
          render: false
        },
        {
          label: '类型名称',
          name: 'typeName',
          required: true
        },
        {
          label: '备注',
          name: 'remark',
          required: true
        }
      ]
    };
  });

  return (
    <CardLayout>
      <MyTableList
        action={
          <div>
            {permisstion.canArticleTypeAdd && (
              <Button
                onClick={() => {
                  switchModalVisible({});
                }}
                type='primary'>
                新增
              </Button>
            )}
          </div>
        }
        getList={({ params }) =>
          useApi.文章类型列表.request({ ...params, hisId })
        }
        tableTitle={'文章类型'}
        columns={[
          // {
          //   title: '',
          //   dataIndex: 'sort',
          //   width: 30,
          //   className: 'drag-visible',
          //   render: () => <DragHandle />
          // },
          {
            title: '类型名称',
            dataIndex: 'typeName',
            width: 100,
            className: 'drag-visible'
          },
          {
            title: '备注',
            dataIndex: 'remark',
            width: 100,
            className: 'drag-visible'
          },
          {
            title: '更新时间',
            dataIndex: 'updateTime',
            width: 100,
            className: 'drag-visible'
          },
          {
            title: '操作',
            dataIndex: 'opt',
            width: 100,
            fixed: 'right',
            align: 'center',
            className: 'drag-visible',
            render: (text, record: any) => {
              return (
                <ActionsWrap max={99}>
                  {permisstion.canArticleTypeModify && (
                    <LinkButton
                      onClick={() => {
                        switchModalVisible(record);
                      }}>
                      编辑
                    </LinkButton>
                  )}
                  {permisstion.canArticleTypeDelete && (
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.删除文章类型.request({
                              id: record.id
                            }),
                          '删除'
                        );
                      }}>
                      删除
                    </LinkButton>
                  )}
                </ActionsWrap>
              );
            }
          }
        ]}
      />
    </CardLayout>
  );
};
