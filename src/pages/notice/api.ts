import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ListApiResponseData, ListApiRequestParams } from '@apiHooks';
export interface ApiResponse<D> {
  code: 0 | 200 | 999; // 999用户未登录
  msg: string | null;
  data?: D;
}
export interface DeptItem {
  id: number;
  hisId: 40009;
  name: '儿科';
  no: '10001';
  sortNo: 0;
  employeeCount: null;
  tel: '023-8973495873';
  status: 1;
  pid: 85;
  pathCode: '/p85/p86/';
  hisType: 2;
  isSummary: 1;
  address: '';
  createTime: '2021-08-23 08:53:48';
  updateTime: '2021-08-23 08:53:48';
  children: DeptItem[];
}
export default {
  文章类型列表: createApiHooks(
    (data: ListApiRequestParams & { hisId?: string }) => {
      console.log('data', data);
      return request.get<
        ListApiResponseData<{
          id: 1;
          typeName: '医院动态';
          orderSeq: 2;
          remark: '339939322232';
        }>
      >('/mch/his/articleType', { params: data });
    }
  ),
  新增文章类型: createApiHooks(
    (data: { hisId: number; typeName: string; remark: string }) =>
      request.post<ListApiResponseData<Record<string, unknown>>>(
        '/mch/his/articleType',
        data,
        {
          headers: {
            Accept: 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/json; charset=UTF-8'
          }
        }
      )
  ),
  编辑文章类型: createApiHooks(
    (data: { hisId: number; typeName: string; remark: string }) =>
      request.put<ListApiResponseData<Record<string, unknown>>>(
        '/mch/his/articleType',
        data,
        {
          headers: {
            Accept: 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/json; charset=UTF-8'
          }
        }
      )
  ),
  删除文章类型: createApiHooks((data: { id: string }) =>
    request.delete(`/mch/his/articleType/${data.id}`)
  ),
  文章列表: createApiHooks((data: Record<string, unknown>) =>
    request.get<
      ListApiResponseData<{
        id: 1;
        typeName: '医院动态1';
        orderSeq: 2;
        remark: '339939322232';
      }>
    >('/mch/his/article/', {
      params: data
    })
  ),
  文章详情: createApiHooks((data: { id: string }) =>
    request.get<{
      data: {
        content: string;
        coverImage: string;
        articleType: number;
        type: string;
        title: string;
        sortNo: number;
        state: 'DRAFT' | 'OFFLINE' | 'ONLINE';
        deptId: '';
        deptName: '';
      };
    }>(`/mch/his/article/${data.id}`)
  ),
  新增文章: createApiHooks(
    (data: {
      hisId: number;
      title: string;
      coverImage: string;
      content: string;
      sortNo?: number;
      articleType?: number;
      state: 'DRAFT' | 'OFFLINE' | 'ONLINE';
      type: string;
    }) =>
      request.post<ListApiResponseData<Record<string, unknown>>>(
        '/mch/his/article',
        data,
        {
          headers: {
            Accept: 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/json; charset=UTF-8'
          }
        }
      )
  ),
  编辑文章: createApiHooks(
    (data: {
      title?: string;
      coverImage?: string;
      content?: string;
      state?: 'DRAFT' | 'OFFLINE' | 'ONLINE';
      type?: string;
      sortNo?: number;
      articleType?: number;
      id?: string;
    }) =>
      request.put<ListApiResponseData<Record<string, unknown>>>(
        '/mch/his/article',
        data,
        {
          headers: {
            Accept: 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/json; charset=UTF-8'
          }
        }
      )
  ),
  删除文章: createApiHooks((data: { id: string }) =>
    request.delete(`/mch/his/article/${data.id}`)
  ),
  科室管理列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        hisType?: string | number; // 所属业务平台 1互联网医院、2智慧医院、3互/智
      }
    ) =>
      request
        .get<ApiResponse<DeptItem[]>>('/mch/his/deptMain', { params: data })
        .then(res => {
          return {
            ...res,
            data: {
              code: res.data.code,
              msg: res.data.msg,
              data: {
                currentPage: 1,
                totalCount: 1,
                recordList: res.data.data || []
              }
            }
          };
        })
  )
};
