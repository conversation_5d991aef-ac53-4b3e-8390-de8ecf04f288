import React, { useCallback, useEffect, useState } from 'react';

import TabPaneLayout from '@pages/hospital/info/layout';
import {
  CardLayout,
  UploadImg,
  ArrSelect,
  handleSubmit,
  Editor
} from 'parsec-admin';
import { PreviewChild } from './detail.style';
// import BraftEditor from 'braft-editor';

import {
  Space,
  Form,
  Button,
  Input,
  Row,
  Modal,
  Radio,
  InputNumber,
  Cascader
} from 'antd';
import useApi from './api';
import { useHistory, useParams } from 'react-router';
import env from '@configs/env';
import styled from 'styled-components';
import { TransferChange } from '@kqinfo/ui';
import { getTreeOptionsNo } from '@pages/hospital/doctorDetailSmart/utils';

const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 19 }
};
export default () => {
  const history = useHistory();
  const { id } = useParams<{ id: string }>(); // 如果为 new 表示新增
  const hisId = env.hisId;
  const [values, setValues] = useState<any>({});

  const onValuesChange = useCallback((changedValues, values) => {
    setValues(values);
  }, []);
  const [form] = Form.useForm();
  const {
    data: { data: depList }
  } = useApi.科室管理列表({
    initValue: { data: { recordList: [] } }
  });
  const {
    data: { data: typeData }
  } = useApi.文章类型列表({
    params: { hisId, pageNum: 1, numPerPage: 999 },
    needInit: true,
    initValue: { data: { recordList: [] } }
  });

  const {
    data: { data: detail }
  } = useApi.文章详情({
    initValue: { data: null },
    params: { id },
    needInit: id !== 'new'
  });
  // 通用的查找函数
  const findPaths = (data, targetValues) => {
    const paths: string[][] = []; // 存储符合条件的路径
    // 递归遍历函数
    const traverse = (node, currentPath) => {
      const newPath = [...currentPath, node.value]; // 构建当前路径
      // 如果当前节点的 value 在目标列表中，则保存路径
      if (targetValues.includes(node.value)) {
        paths.push(newPath);
      }
      // 如果当前节点有子节点，则递归遍历子节点
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => traverse(child, newPath));
      }
    };
    // 从根节点开始遍历
    data.forEach(rootNode => traverse(rootNode, []));
    return paths;
  };

  useEffect(() => {
    if (!detail) {
      return;
    }
    let paths: string[][] = [];
    if (typeof detail.deptId === 'string') {
      const deptIdString = detail.deptId.trim();
      if (deptIdString) {
        paths = findPaths(
          getTreeOptionsNo(depList?.recordList),
          deptIdString
            .replace(/^,|,$/g, '')
            .split(',')
            .filter(Boolean)
        );
      }
    }
    form.setFieldsValue({
      title: detail.title,
      coverImage: [detail.coverImage],
      articleType: detail.articleType,
      type: detail.type,
      sortNo: detail.sortNo,
      content: detail.content,
      deptId: paths
    });
  }, [detail, form]);

  const [loading, setLoading] = useState(false);

  const handleValidate = useCallback(
    (saveType: 'OFFLINE' | 'ONLINE' | 'DRAFT') => {
      form.validateFields().then(async (values: any) => {
        const deptIds =
          values?.deptId?.length > 1
            ? values.deptId.map(item => item[item?.length - 1]).join(',')
            : values?.deptId?.[0]?.[values.deptId[0]?.length - 1] ?? '';
        const v = {
          ...values,
          coverImage: values['coverImage']?.[0] || '',
          deptId: deptIds
        };
        setLoading(true);
        handleSubmit(() => {
          if (id !== 'new') {
            return useApi.编辑文章.request({
              id,
              ...v,
              state: saveType,
              hisId: hisId
            });
          } else {
            delete values.id;
            return useApi.新增文章.request({
              ...v,
              state: saveType,
              hisId: hisId
            });
          }
        }, '操作')
          .then(() => {
            history.goBack();
          })
          .finally(() => {
            setLoading(false);
          });
      });
    },
    [form, history, id, hisId]
  );

  const SelectType = ({ value, onChange }: { value?: any; onChange?: any }) => {
    return (
      <Row>
        <ArrSelect
          value={value}
          onChange={onChange}
          style={{
            width: '160px'
          }}
          options={(typeData?.recordList || []).map(level => {
            return {
              value: level.id,
              children: level.typeName
            };
          })}
        />
        <Button
          type='link'
          onClick={() => {
            history.push('/content/noticeTypes');
          }}>
          维护栏目
        </Button>
      </Row>
    );
  };

  const [publishData, setPublishData] = useState<{
    type: 'OFFLINE' | 'ONLINE';
  } | null>();
  return (
    <CardLayout>
      <TabPaneLayout
        title='医院简介'
        loading={false}
        formChild={
          <Form
            name='hospitalInfoForm'
            size='large'
            form={form}
            onValuesChange={onValuesChange}>
            <Form.Item
              {...layout}
              label='文章标题'
              name='title'
              rules={[
                {
                  required: true,
                  message: '请输入文章标题!'
                }
              ]}>
              <Input placeholder='请输入文章标题' />
            </Form.Item>
            <Form.Item
              {...layout}
              label='文章栏目'
              name='type'
              rules={[
                {
                  required: true,
                  message: '请选择文章栏目!'
                }
              ]}>
              <SelectType />
            </Form.Item>
            <Form.Item
              {...layout}
              label='文章类型'
              name='articleType'
              rules={[
                {
                  required: true,
                  message: '请选择文章类型!'
                }
              ]}>
              <ArrSelect
                options={[
                  {
                    value: 1,
                    children: '图文'
                  },
                  {
                    value: 2,
                    children: '视频'
                  }
                ]}
                style={{ width: 100 }}
              />
            </Form.Item>
            <Form.Item {...layout} label='科室' name='deptId'>
              <Cascader
                expandTrigger='hover'
                placeholder='请选择上级科室'
                multiple
                maxTagCount='responsive'
                options={getTreeOptionsNo(depList?.recordList) as any}
              />
            </Form.Item>
            <Form.Item {...layout} label='排序' name='sortNo'>
              <InputNumber min={1} />
            </Form.Item>
            <Form.Item
              {...layout}
              label='封面'
              name='coverImage'
              rules={[
                {
                  required: true,
                  message: '请上传封面'
                }
              ]}
              extra='支持jpg\jpeg\png等多种格式、单张图片最大支持10M'>
              <UploadImg
                showUploadList={{
                  showPreviewIcon: true,
                  showRemoveIcon: true,
                  showDownloadIcon: false
                }}
              />
            </Form.Item>
            <Form.Item
              {...layout}
              label='内容'
              name='content'
              rules={[
                {
                  required: true,
                  message: '请输入内容!'
                }
              ]}>
              <EditWrapper />
            </Form.Item>
            <Form.Item
              {...{
                wrapperCol: { offset: 8, span: 16 }
              }}
              style={{ textAlign: 'right' }}>
              <Space size={22}>
                <Button
                  loading={loading}
                  style={{ width: '136px' }}
                  onClick={() => {
                    setPublishData({ type: 'OFFLINE' });
                  }}>
                  发布
                </Button>
                {(id === 'new' || detail?.state === 'DRAFT') && (
                  <Button
                    loading={loading}
                    style={{ width: '136px' }}
                    onClick={() => {
                      handleValidate('DRAFT');
                    }}>
                    保存草稿
                  </Button>
                )}
              </Space>
            </Form.Item>
          </Form>
        }
        previewChild={
          <Space size={15} direction='vertical' style={{ width: '100%' }}>
            <PreviewChild>
              <div className='title'>{values.title}</div>
              <div
                className='content'
                dangerouslySetInnerHTML={{
                  __html: values && values['content']
                }}
              />
            </PreviewChild>
          </Space>
        }
      />
      <Modal
        title='发布提示'
        visible={!!publishData}
        onCancel={() => {
          setPublishData(null);
        }}
        width={400}
        onOk={() => {
          if (!publishData) {
            return;
          }
          handleValidate(publishData.type);
          setPublishData(null);
        }}
        destroyOnClose={true}>
        <Form>
          <Form.Item label='发布形式'>
            <Radio.Group
              defaultValue='OFFLINE'
              onChange={e => {
                setPublishData({ ...publishData, type: e.target.value as any });
              }}
              options={[
                { label: '公开', value: 'ONLINE' },
                { label: '秘密', value: 'OFFLINE' }
              ]}
            />
          </Form.Item>
        </Form>
      </Modal>
    </CardLayout>
  );
};

const EditWrapper = styled(Editor)`
  .w-e-toolbar {
    z-index: 996 !important;
  }
`;
