import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ApiResponse } from '@apiHooks';
import { ListApiResponseData } from '@configs/d';

interface ListResponse {
  id: '@natural'; // 配置id
  hisId: '@natural'; //医院id
  ruleName: '@cword(6)'; //配送规则名称
  deliveryCompany: "@pick('EMS','SF')"; //快递公司名称
  state: '@pick(0,1)'; //状态，0：禁用，1：启用
  billingMethod: "@pick('WEIGHT','AMOUNT')"; //计费方式
  createTime: '@datetime'; //
  updateTime: '@datetime'; //
  businessPlatform: 'PRESCRIPTION'; //业务平台
}

interface EnumResponse {
  BusinessPlatformEnum: [
    {
      name: 'PRESCRIPTION';
      desc: 'PRESCRIPTION';
    }
  ];
  BillingMethodEnum: [
    {
      name: 'WEIGHT';
      desc: '按总量计费';
    },
    {
      name: 'AMOUNT';
      desc: '按件计费';
    }
  ];
  DeliveryAreaTypeEnum: [
    {
      name: 'NATIONAL';
      desc: '全国';
    },
    {
      name: 'CUSTOM';
      desc: '定制';
    },
    {
      name: 'EXCLUDE';
      desc: '排除';
    }
  ];
  DeliveryEnum: [
    {
      name: 'EMS';
      desc: '中国邮政';
    },
    {
      name: 'SF';
      desc: '顺丰速递';
    }
  ];
}

interface AddParams {
  ruleName: '@cword(6)'; //配送规则名称
  deliveryCompany: "@pick('EMS','SF')"; //快递公司名称
  state: '@pick(0,1)'; //状态，0：禁用，1：启用
  billingMethod: "@pick('WEIGHT','AMOUNT')"; //计费方式
  businessPlatform: 'PRESCRIPTION'; //业务平台
  deliveryAreaList: {
    firstAmount: '@integer(0, 1000000)'; //首次数量（首总或者首个）
    firstPrice: '@integer(0, 1000000)'; //首次价格，单位分（首费）
    followAmount: '@integer(0, 1000000)'; //后续数量（续重或者续个）
    followPrice: '@integer(0, 1000000)'; //续费价格，单位分
    areas: {
      code: string | number;
      name: string;
    }[];
    priority: '@integer(0, 1000000)'; //优先级
    type: "@pick('NATIONAL','CUSTOM','EXCLUDE')"; //区域类型：全国，定制，排除
  }[];
}

interface DetailsResponse {
  id: '@natural'; //
  hisId: '@natural'; //医院id
  ruleName: '@cword(6)'; //配送规则名称
  deliveryCompany: "@pick('EMS','SF')"; //快递公司名称
  state: '@pick(0,1)'; //状态，0：禁用，1：启用
  billingMethod: "@pick('WEIGHT','AMOUNT')"; //计费方式
  createTime: '@datetime'; //
  updateTime: '@datetime'; //
  businessPlatform: 'PRESCRIPTION'; //业务平台
  deliveryAreaList: {
    firstAmount: '@integer(0, 1000000)'; //首次数量（首总或者首个）
    firstPrice: number; //首次价格，单位分（首费）
    followAmount: '@integer(0, 1000000)'; //后续数量（续重或者续个）
    followPrice: number; //续费价格，单位分
    areas: {
      code: string | number;
      name: string;
    }[];
    priority: '@integer(0, 1000000)'; //优先级
    type: "@pick('NATIONAL','CUSTOM','EXCLUDE')"; //区域类型：全国，定制，排除
  }[];
}

export default {
  分页查询查询配置: createApiHooks(
    (params: {
      deliveryCompany?: string;
      billingMethod?: string;
      numPerPage?: string | number;
      pageNum: string | number;
    }) =>
      request.get<ListApiResponseData<ListResponse>>('/mch/his/delivery-cfg', {
        params
      })
  ),
  查询快递相关枚举: createApiHooks(() =>
    request.get<ApiResponse<EnumResponse>>('/common/his/enum/delivery')
  ),
  新增配置: createApiHooks((data: AddParams) =>
    request.post<ListApiResponseData<ListResponse>>(
      '/mch/his/delivery-cfg',
      data,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  编辑配置: createApiHooks((data: AddParams & { id: number | string }) =>
    request.put<ListApiResponseData<ListResponse>>(
      '/mch/his/delivery-cfg',
      data,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  根据id查询删除配置: createApiHooks((params: { id: string | number }) =>
    request.delete<ApiResponse<any>>(`/mch/his/delivery-cfg/${params.id}`)
  ),
  根据id查询配置详情: createApiHooks((params: { id: number | string }) =>
    request.get<ApiResponse<DetailsResponse>>(
      `/mch/his/delivery-cfg/${params.id}`
    )
  )
};
