import MyTableList from '@components/myTableList';
import useApi from './api';
import React, {
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import {
  actionConfirm,
  ActionsWrap,
  ArrRadio,
  ArrSelect,
  handleSubmit,
  LinkButton,
  useModal
} from 'parsec-admin';
import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Row
} from 'antd';
import { getAddressOptions, Space } from '@kqinfo/ui';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import classnames from 'classnames';

const CitySelectorContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  .leftPanel,
  .rightPanel {
    width: 48%;
    height: 400px;
    overflow-x: hidden;
    overflow-y: auto;

    > div {
      cursor: pointer;
    }
  }

  .selectedCity {
    color: #1890ff;
  }
`;

const AddressItem = styled.div`
  border-bottom: 1px solid #eee;
  padding: 10px;
  box-sizing: border-box;

  &:last-child {
    border-bottom: none;
  }
`;

const Title = ({ mode }: { mode: 'WEIGHT' | 'AMOUNT' }) => {
  const tableLabel = useMemo(
    () =>
      mode === 'AMOUNT'
        ? ['首件(个)', '首费(个/元)', '续件(个)', '续费(个/元)']
        : ['首重(克)', '首费(元)', '续重(克)', '缴费(元)'],
    [mode]
  );
  return (
    <Row
      justify={'space-between'}
      style={{
        marginBottom: '10px',
        backgroundColor: '#eee',
        padding: '10px',
        boxSizing: 'border-box'
      }}>
      <Col span={10}>配送地址</Col>
      {tableLabel?.map((i, index) => (
        <Col key={index} span={3}>
          {i}
        </Col>
      ))}
    </Row>
  );
};

const CitySelector = ({
  disabled,
  visible: pVisible,
  setVisible: pSetVisible,
  cityInfo,
  onConfirm,
  value: pValue,
  onChange
}: {
  disabled?: boolean;
  visible?: boolean;
  setVisible?: (visible: boolean) => void;
  cityInfo: any[];
  onConfirm?: (
    data: {
      code: string | number;
      name: ReactNode;
    }[]
  ) => void;

  value?: {
    code: string | number;
    name: ReactNode;
  }[];
  onChange?: (
    data: {
      code: string | number;
      name: ReactNode;
    }[]
  ) => void;
}) => {
  const [selectCity, setSelectCity] = useState<
    {
      code: string | number;
      name: ReactNode;
    }[]
  >([]);

  const [innerVisible, setInnerVisible] = useState<boolean>(false);

  const visible = useMemo(() => innerVisible || pVisible, [
    innerVisible,
    pVisible
  ]);

  useEffect(() => {
    pValue && setSelectCity(pValue);
  }, [pValue]);

  const [selectCityValue, setSelectCityValue] = useState<string | number>('');

  useEffect(() => {
    !visible && setSelectCityValue('');
  }, [visible]);

  const init = useCallback(() => {
    if (pValue) {
      setSelectCityValue('');
      setSelectCity(pValue);
    } else {
      setSelectCityValue('');
      setSelectCity([]);
    }
  }, [pValue]);

  const setCites = useCallback(
    (params: any[]) => {
      const copySelectCity = [...params];
      const findProvince = cityInfo
        ?.map(item => {
          const { label, value, children } = item;
          const hasChecked =
            (children || [])?.findIndex(i =>
              copySelectCity?.map(i => i.code)?.includes(i.value)
            ) >= 0;
          if (hasChecked) {
            return { name: label, code: value };
          }
          return undefined;
        })
        .filter(i => i);
      const noProvince = copySelectCity.filter(i => i?.code?.length !== 2);
      const result = [...noProvince, ...findProvince];
      setSelectCity(result);
    },
    [cityInfo]
  );

  return (
    <React.Fragment>
      <Modal
        onOk={() => {
          onConfirm && onConfirm(selectCity);
          setInnerVisible(false);
          onChange && onChange(selectCity);
          init();
        }}
        visible={visible}
        onCancel={() => {
          pSetVisible && pSetVisible(false);
          setInnerVisible(false);
          init();
        }}>
        <CitySelectorContainer>
          <div className={'leftPanel'}>
            {cityInfo?.map(item => {
              const { label, value, children } = item;
              const hasChecked =
                (children || [])?.findIndex(i =>
                  selectCity?.map(i => i.code)?.includes(i.value)
                ) >= 0;
              return (
                <Space
                  size={5}
                  className={classnames({
                    selectedCity: hasChecked
                  })}
                  key={value}>
                  <Checkbox
                    checked={hasChecked}
                    onChange={e => {
                      const copySelectCity = [...selectCity];
                      children?.forEach(item => {
                        const index = copySelectCity?.findIndex(
                          i => i.code === item.value
                        );
                        copySelectCity.splice(index, 1);
                        setCites(copySelectCity);
                      });
                      if (e.target.checked) {
                        const copySelectCity = [
                          ...selectCity,
                          ...(children?.map(i => ({
                            name: i.label,
                            code: i.value
                          })) || [])
                        ];
                        setCites(copySelectCity);
                      }
                    }}
                  />
                  <div
                    onClick={() => {
                      setSelectCityValue(value);
                    }}>
                    {label}
                  </div>
                </Space>
              );
            })}
          </div>
          <div className={'rightPanel'}>
            {!selectCityValue && '请先选择省市'}
            {cityInfo
              ?.find(i => i.value === selectCityValue)
              ?.children?.map(({ label, value }) => {
                const hasChecked =
                  selectCity?.findIndex(i => i.code === value) >= 0;
                return (
                  <Space
                    size={5}
                    onTap={() => {
                      const findSelect = selectCity?.findIndex(
                        i => i.code === value
                      );
                      if (findSelect >= 0) {
                        const copySelectCity = [...selectCity];
                        copySelectCity.splice(findSelect, 1);
                        console.log('copySelectCity', copySelectCity);
                        setCites(copySelectCity);
                      } else {
                        const params = {
                          name: label,
                          code: value
                        };
                        const copySelectCity = [...selectCity, params];
                        setCites(copySelectCity);
                      }
                    }}>
                    <Checkbox checked={hasChecked} className='rightCheckBox' />
                    <div
                      key={value}
                      className={classnames({
                        selectedCity: hasChecked
                      })}>
                      {label}
                    </div>
                  </Space>
                );
              })}
          </div>
        </CitySelectorContainer>
      </Modal>
      {pValue && (
        <div
          onClick={() => {
            !(selectCity?.findIndex(i => i.code === '0') >= 0) &&
              !disabled &&
              setInnerVisible(true);
          }}>
          {pValue?.map(i => i.name)?.join(',') || '暂无'}
          {!disabled && !pValue?.map(i => i.code)?.includes('0') && (
            <span style={{ color: 'blue', marginLeft: '10px' }}>编辑</span>
          )}
        </div>
      )}
    </React.Fragment>
  );
};

const AddressSelector = ({
  index = 0,
  remove,
  disabled,
  hiddenInput,
  cityInfo,
  restField = {},
  name,
  hiddenTitleInfo
}: {
  disabled?: boolean;
  index?: number;
  hiddenInput?: boolean;
  cityInfo: any[];
  remove?: (num: number) => void;
  name?: number;
  restField?: any;
  hiddenTitleInfo?: boolean;
}) => {
  return (
    <AddressItem>
      <Space vertical alignItems={'center'}>
        {!hiddenTitleInfo && (
          <Space
            flex={1}
            justify={'space-between'}
            style={{ width: '100%', marginBottom: '10px' }}>
            {!hiddenInput && <Space>{`配送区域信息${index + 1}：`}</Space>}
            {index !== 0 && !disabled && (
              <DeleteOutlined
                style={{ color: '#c63520', fontSize: '20px' }}
                onClick={() => name && remove && remove(name)}
              />
            )}
          </Space>
        )}
        <Row
          style={{ width: '100%' }}
          justify={hiddenInput ? 'start' : 'space-between'}
          align={'middle'}>
          <Col span={hiddenInput ? 24 : 10}>
            <Form.Item
              {...restField}
              label=''
              style={{ width: '100%' }}
              name={[name, 'areas']}
              rules={[{ required: !hiddenInput }]}>
              <CitySelector disabled={disabled} cityInfo={cityInfo} />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item
              hidden={hiddenInput}
              {...restField}
              label=''
              style={{ width: '100%' }}
              name={[name, 'firstAmount']}
              rules={[{ required: !hiddenInput }]}>
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                disabled={disabled}
              />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item
              hidden={hiddenInput}
              {...restField}
              label=''
              style={{ width: '100%' }}
              name={[name, 'firstPrice']}
              rules={[{ required: !hiddenInput }]}>
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                disabled={disabled}
              />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item
              hidden={hiddenInput}
              {...restField}
              label=''
              style={{ width: '100%' }}
              name={[name, 'followAmount']}
              rules={[{ required: !hiddenInput }]}>
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                disabled={disabled}
              />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item
              hidden={hiddenInput}
              {...restField}
              label=''
              style={{ width: '100%' }}
              name={[name, 'followPrice']}
              rules={[{ required: !hiddenInput }]}>
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                disabled={disabled}
              />
            </Form.Item>
          </Col>
        </Row>
      </Space>
    </AddressItem>
  );
};

export default () => {
  const [form] = Form.useForm();

  const [visible, setVisible] = useState<boolean>(false);

  const addForm = useRef<any>();

  const [cityInfo, setCityInfo] = useState<any[]>();

  const { data } = useApi.查询快递相关枚举({
    needInit: true,
    initValue: {}
  });

  const wrapperObj = useCallback(
    (
      obj: { name: string; desc: string }[],
      format: {
        key1: string;
        key2: string;
      }
    ) => {
      return obj?.map(i => ({
        [format.key1]: i.name,
        [format.key2]: i.desc
      }));
    },
    []
  );

  useEffect(() => {
    getAddressOptions().then(v => setCityInfo(v));
  }, []);

  const switchModalVisible = useModal(
    ({ modalType }: { modalType: 'edit' | 'add' | 'watch' }) => ({
      title:
        modalType === 'add' ? '新增' : modalType === 'edit' ? '编辑' : '查看',
      form,
      width: 1000,
      onCancel: () => {
        form.resetFields();
      },
      onSubmit: (values: any) => {
        if (modalType === 'watch') {
          return Promise.resolve();
        }
        const innerPromise =
          modalType === 'add' ? useApi.新增配置 : useApi.编辑配置;
        const deliveryAreaList = [
          ...(values?.deliveryAreaList_CUSTOM?.map(i => ({
            ...i,
            type: i.type !== 'NATIONAL' ? 'CUSTOM' : 'NATIONAL',
            firstPrice: i.firstPrice * 100,
            followPrice: i.followPrice * 100
          })) || []),
          ...(values?.deliveryAreaList_EXCLUDE.map(i => ({
            ...i,
            firstAmount: '0',
            firstPrice: '0',
            followAmount: '0',
            followPrice: '0',
            type: 'EXCLUDE'
          })) || [])
        ];
        return handleSubmit(
          () =>
            innerPromise.request({
              ...values,
              deliveryAreaList: deliveryAreaList
            }),
          '提交'
        );
      },
      items: [
        {
          name: 'id',
          render: false
        },
        {
          label: '名称',
          name: 'ruleName',
          required: true,
          render: (
            <Input
              disabled={modalType === 'watch'}
              placeholder={'请输入规则名称'}
            />
          )
        },
        {
          label: '关联业务',
          name: 'businessPlatform',
          required: true,
          render: (
            <ArrSelect
              disabled={modalType === 'watch'}
              options={
                wrapperObj(data?.data?.BusinessPlatformEnum || [], {
                  key1: 'value',
                  key2: 'label'
                }) || []
              }
            />
          )
        },
        {
          label: '快递公司',
          name: 'deliveryCompany',
          required: true,
          render: (
            <ArrSelect
              disabled={modalType === 'watch'}
              options={
                wrapperObj(data?.data?.DeliveryEnum || [], {
                  key1: 'value',
                  key2: 'label'
                }) || []
              }
            />
          )
        },
        {
          label: '状态',
          name: 'state',
          required: true,
          render: (
            <ArrRadio
              disabled={modalType === 'watch'}
              radios={[
                { value: 1, children: '启用' },
                { value: 0, children: '禁用' }
              ]}
            />
          )
        },
        {
          label: '计费方式',
          name: 'billingMethod',
          required: true,
          render: (
            <ArrRadio
              disabled={modalType === 'watch'}
              radios={
                wrapperObj(data?.data?.BillingMethodEnum || [], {
                  key1: 'value',
                  key2: 'children'
                }) || []
              }
            />
          )
        },
        {
          label: '配送区域',
          name: 'deliveryAreaList_CUSTOM',
          render: (
            <Form.List
              key={'deliveryAreaList_CUSTOM'}
              name='deliveryAreaList_CUSTOM'>
              {(fields, { add, remove }) => (
                <Space vertical style={{ marginTop: '30px' }}>
                  <Title mode={form.getFieldValue('billingMethod')} />
                  {fields?.map(({ key, name, ...restField }, index) => (
                    <AddressSelector
                      key={name}
                      disabled={modalType === 'watch'}
                      cityInfo={cityInfo || []}
                      index={index}
                      remove={remove}
                      name={name}
                      restField={restField}
                    />
                  ))}
                  {modalType !== 'watch' && (
                    <Form.Item>
                      <Button
                        onClick={() => {
                          addForm.current = add;
                          setVisible(true);
                        }}
                        block
                        icon={<PlusOutlined />}>
                        新增配送区域
                      </Button>
                    </Form.Item>
                  )}
                </Space>
              )}
            </Form.List>
          )
        },
        {
          label: '不配送区域',
          name: 'deliveryAreaList_EXCLUDE',
          render: (
            <Form.List
              key={'deliveryAreaList_EXCLUDE'}
              name='deliveryAreaList_EXCLUDE'>
              {(fields, { add, remove }) => (
                <Space vertical style={{ marginTop: '30px' }}>
                  {fields?.map(({ key, name, ...restField }, index) => (
                    <AddressSelector
                      key={name}
                      hiddenInput={true}
                      disabled={modalType === 'watch'}
                      cityInfo={cityInfo || []}
                      index={index}
                      remove={remove}
                      name={name}
                      restField={restField}
                    />
                  ))}
                  {modalType !== 'watch' && fields?.length < 1 && (
                    <Form.Item>
                      <Button
                        onClick={() => {
                          addForm.current = add;
                          setVisible(true);
                        }}
                        block
                        icon={<PlusOutlined />}>
                        新增不配送区域
                      </Button>
                    </Form.Item>
                  )}
                </Space>
              )}
            </Form.List>
          )
        }
      ]
    })
  );

  const getDetails = (
    id: number | string,
    modalType: 'edit' | 'add' | 'watch'
  ) => {
    useApi.根据id查询配置详情
      .request({
        id: id
      })
      .then(res => {
        if (res.code === 0) {
          console.log(
            {
              ...res.data,
              modalType,
              deliveryAreaList_CUSTOM:
                res?.data?.deliveryAreaList?.filter(i =>
                  ['CUSTOM', 'NATIONAL'].includes(i.type)
                ) || [],
              deliveryAreaList_EXCLUDE:
                res?.data?.deliveryAreaList?.filter(i =>
                  ['EXCLUDE'].includes(i.type)
                ) || []
            },
            '测试数据'
          );
          switchModalVisible({
            ...res.data,
            modalType,
            deliveryAreaList_CUSTOM:
              res?.data?.deliveryAreaList
                ?.filter(i => ['CUSTOM', 'NATIONAL'].includes(i.type))
                ?.map(i => ({
                  ...i,
                  firstPrice: i.firstPrice / 100,
                  followPrice: i.followPrice / 100
                })) || [],
            deliveryAreaList_EXCLUDE:
              res?.data?.deliveryAreaList?.filter(i =>
                ['EXCLUDE'].includes(i.type)
              ) || []
          });
        }
      });
  };

  return (
    <React.Fragment>
      <CitySelector
        cityInfo={cityInfo || []}
        visible={visible}
        setVisible={setVisible}
        onConfirm={data => {
          console.log('selectData', data);
          addForm.current && addForm.current({ areas: data });
          addForm.current = undefined;
          setVisible(false);
        }}
      />
      <MyTableList
        tableTitle='配送费列表'
        getList={({ params }) =>
          useApi.分页查询查询配置.request({
            ...params
          })
        }
        action={
          <ActionsWrap>
            <Button
              onClick={() => {
                form.resetFields();
                switchModalVisible({
                  modalType: 'add',
                  deliveryAreaList_EXCLUDE: [],
                  deliveryAreaList_CUSTOM: [
                    {
                      areas: [{ code: '0', name: '全国[默认邮费]' }],
                      type: 'NATIONAL'
                    }
                  ]
                });
              }}>
              添加
            </Button>
          </ActionsWrap>
        }
        columns={[
          {
            title: '名称',
            width: 160,
            dataIndex: 'ruleName'
          },
          {
            title: '关联业务',
            width: 180,
            dataIndex: 'businessPlatform',
            render: v => {
              return data?.data?.BusinessPlatformEnum?.find(i => i.name === v)
                ?.desc;
            }
          },
          {
            title: '计费方式',
            width: 180,
            dataIndex: 'billingMethod',
            render: v => {
              return data?.data?.BillingMethodEnum?.find(i => i.name === v)
                ?.desc;
            },
            searchIndex: 'billingMethod',
            search: (
              <ArrSelect
                options={
                  wrapperObj(data?.data?.BillingMethodEnum || [], {
                    key1: 'value',
                    key2: 'label'
                  }) || []
                }
              />
            )
          },
          {
            title: '快递公司',
            width: 180,
            dataIndex: 'deliveryCompany',
            searchIndex: 'deliveryCompany',
            render: v => {
              return data?.data?.DeliveryEnum?.find(i => i.name === v)?.desc;
            },
            search: (
              <ArrSelect
                options={
                  wrapperObj(data?.data?.DeliveryEnum || [], {
                    key1: 'value',
                    key2: 'label'
                  }) || []
                }
              />
            )
          },
          {
            title: '状态',
            width: 180,
            dataIndex: 'state',
            render: v => (v === 1 ? '启用' : '禁用')
          },
          {
            title: '操作',
            fixed: 'right',
            width: 120,
            render: (v, record: any) => (
              <ActionsWrap>
                <LinkButton
                  onClick={() => {
                    getDetails(record.id, 'watch');
                  }}>
                  查看
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    getDetails(record.id, 'edit');
                  }}>
                  编辑
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.根据id查询删除配置.request({
                          id: record.id
                        }),
                      '删除'
                    );
                  }}>
                  删除
                </LinkButton>
              </ActionsWrap>
            )
          }
        ]}
      />
    </React.Fragment>
  );
};
