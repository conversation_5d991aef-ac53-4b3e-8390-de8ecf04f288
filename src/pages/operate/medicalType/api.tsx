import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ListApiResponseData } from '@configs/d';

export default {
  分页查询病种医保编码: createApiHooks((params: any) =>
    request.get<ListApiResponseData<MedicalTypeItem[]>>(
      '/mch/his/diagnosis/medical-insurance-code',
      { params }
    )
  ),
  添加病种医保编码: createApiHooks((data: MedicalTypeItem) =>
    request.post<null>('/mch/his/diagnosis/medical-insurance-code', data, {
      headers: {
        'Content-Type': 'application/json; charset=UTF-8'
      }
    })
  ),
  编辑病种医保编码: createApiHooks((data: Partial<MedicalTypeItem>) =>
    request.put<null>('/mch/his/diagnosis/medical-insurance-code', data, {
      headers: {
        'Content-Type': 'application/json; charset=UTF-8'
      }
    })
  ),
  病种医保编码详情: createApiHooks(
    ({ id }: { medicalType: 'cn' | 'en'; id: number }) =>
      request.get<null>(`/mch/his/diagnosis/medical-insurance-code/${id}`)
  ),
  删除病种医保编码: createApiHooks(({ id }: { id: number }) =>
    request.delete<null>(`/mch/his/diagnosis/medical-insurance-code/${id}`)
  ),
  病种医保编码导入模板下载: createApiHooks(() =>
    request.get<Blob>(
      `/mch/his/diagnosis/medical-insurance-code/import/template`,
      {
        responseType: 'blob'
      }
    )
  ),
  病种医保编码导入: createApiHooks((params: { file: File }) =>
    request.post('/mch/his/diagnosis/medical-insurance-code/import', params)
  ),
  病种医保编码导出: createApiHooks(() =>
    request.get<Blob>(`/mch/his/diagnosis/medical-insurance-code/export`, {
      responseType: 'blob'
    })
  )
};

type MedicalTypeItem = {
  medicalType: 'cn' | 'en';
  hosIcdCode: 'A001.1'; // 医院ICD码
  hosIcdName: '伤寒并发支气管炎'; // 医院ICD名称
  medicalInsuranceCode: 'A001'; // 医保诊断编码
  medicalInsuranceName: '医保诊断名称'; // 医保诊断名称
  status: 0 | 1; // 启用状态 0-禁用，1-启用
  id?: number;
};
