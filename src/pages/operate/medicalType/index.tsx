import MyTableList from '@components/myTableList';
import useApi from './api';
import { useRef } from 'react';
import { saveAs } from 'file-saver';
import {
  actionConfirm,
  ActionsWrap,
  ArrRadio,
  ArrSelect,
  DayRangePicker,
  handleSubmit,
  LinkButton,
  useModal
} from 'parsec-admin';
import { Button, Form } from 'antd';

const medicalTypeOption = [
  {
    label: '中医',
    value: 'CHINESE_MEDICINE'
  },
  {
    label: '西医',
    value: 'WESTERN_MEDICINE'
  }
];

export default () => {
  const [form] = Form.useForm();
  const listParam = useRef({});

  const {
    loading: exportLoading,
    request: reqExport
  } = useApi.病种医保编码导出({
    needInit: false
  });

  const {
    loading: importLoading,
    request: reqImport
  } = useApi.病种医保编码导入({
    needInit: false
  });

  const {
    loading: templateExportLoading,
    request: reqTemplateExport
  } = useApi.病种医保编码导入模板下载({
    needInit: false
  });

  const switchModalVisible = useModal(({ id }: { id?: number }) => ({
    title: id ? '编辑病种医保编码' : '添加病种医保编码',
    form,
    width: 600,
    destroyOnClose: true,
    onSubmit: (values: any) => {
      return handleSubmit(() =>
        (id ? useApi.编辑病种医保编码 : useApi.添加病种医保编码).request(values)
      );
    },
    items: [
      {
        name: 'id',
        render: false
      },
      {
        label: '分类',
        name: 'medicalType',
        required: true,
        render: <ArrSelect options={medicalTypeOption} />
      },
      {
        label: '医院诊断编码',
        name: 'hosIcdCode',
        required: true
      },
      {
        label: '医院诊断名称',
        name: 'hosIcdName',
        required: true
      },
      {
        label: '医保诊断编码',
        name: 'medicalInsuranceCode'
      },
      {
        label: '医保诊断名称',
        name: 'medicalInsuranceName'
      },
      {
        label: '状态',
        name: 'status',
        required: true,
        render: (
          <ArrRadio
            radios={[
              { value: 1, children: '启用' },
              { value: 0, children: '禁用' }
            ]}
          />
        )
      }
    ]
  }));

  return (
    <MyTableList
      tableTitle='疾病编码'
      scroll={{ x: 2000 }}
      getList={({ params }) => {
        return useApi.分页查询病种医保编码.request(params);
      }}
      action={
        <ActionsWrap max={5}>
          <Button
            type='primary'
            onClick={() => {
              form.resetFields();
              switchModalVisible({});
            }}>
            添加
          </Button>
          <Button
            type={'default'}
            loading={templateExportLoading}
            onClick={() => {
              reqTemplateExport().then(r => {
                saveAs(r, '病种医保编码.xlsx');
              });
            }}>
            下载导入模板
          </Button>
          <Button
            loading={exportLoading}
            onClick={() => {
              reqExport(listParam.current).then(data =>
                saveAs(data, '病种医保编码.xlsx')
              );
            }}>
            导出EXCEL
          </Button>
          <Button
            loading={importLoading}
            onClick={() => {
              const el = document.createElement('input');
              el.setAttribute('type', 'file');
              el.addEventListener('change', () => {
                const file = el.files?.[0];
                if (file) {
                  handleSubmit(() => reqImport({ file }), '导入');
                }
              });
              el.click();
              el.remove();
            }}>
            导入EXCEL
          </Button>
        </ActionsWrap>
      }
      columns={[
        {
          title: '诊断分类',
          width: 140,
          dataIndex: 'medicalType',
          render: v => medicalTypeOption?.find(i => i.value === v)?.label,
          search: <ArrSelect options={medicalTypeOption} />
        },
        {
          title: '医院诊断编码',
          width: 140,
          dataIndex: 'hosIcdCode'
        },
        {
          title: '医院诊断名称',
          width: 180,
          search: true,
          dataIndex: 'hosIcdName'
        },
        {
          title: '医保诊断编码',
          width: 140,
          dataIndex: 'medicalInsuranceCode'
        },
        {
          title: '医保诊断名称',
          width: 180,
          search: true,
          dataIndex: 'medicalInsuranceName'
        },
        {
          title: '添加时间',
          width: 180,
          dataIndex: 'createTime',
          search: (
            <DayRangePicker
              placeholder={['开始时间', '结束时间']}
              disabledDate={current => {
                return current && current.valueOf() > Date.now();
              }}
            />
          ),
          searchIndex: ['addTimeStart', 'addTimeEnd']
        },
        {
          title: '状态',
          width: 100,
          dataIndex: 'status',
          render: v => (v === 1 ? '启用' : '禁用'),
          search: (
            <ArrSelect
              options={[
                { label: '禁用', value: 0 },
                { label: '启用', value: 1 }
              ]}
            />
          )
        },
        {
          title: '操作人',
          dataIndex: 'operator',
          width: 130
        },
        {
          title: '操作',
          fixed: 'right',
          width: 180,
          render: (v, record: any) => (
            <ActionsWrap>
              <LinkButton
                onClick={() => {
                  switchModalVisible(record);
                }}>
                编辑
              </LinkButton>
              <LinkButton
                onClick={() => {
                  actionConfirm(
                    () =>
                      useApi.编辑病种医保编码.request({
                        id: record.id,
                        status: record.status === 0 ? 1 : 0
                      }),
                    record.status === 0 ? '启用' : '停用'
                  );
                }}>
                {record.status === 0 ? '启用' : '停用'}
              </LinkButton>
              <LinkButton
                onClick={() => {
                  actionConfirm(
                    () =>
                      useApi.删除病种医保编码.request({
                        id: record.id
                      }),
                    '删除'
                  );
                }}>
                删除
              </LinkButton>
            </ActionsWrap>
          )
        }
      ]}
    />
  );
};
