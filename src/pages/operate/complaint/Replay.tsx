import React from 'react';
import { CardLayout, FormDescriptions, Form } from 'parsec-admin';
import { Image, Space } from 'antd';
import styled from 'styled-components';
import useApi from '../api';
import Chat from '@components/chat';
import { useParams } from 'react-router-dom';
import env from '@src/configs/env';

const editSate = false;

export default () => {
  const { id } = useParams<any>();
  const hisId = env.hisId;
  const [form] = Form.useForm();
  //反馈意见详情
  const {
    request: requestDetail,
    data: { data },
    loading: detailLoading
  } = useApi.complaintsDetail({
    params: { id: id || '', hisId: hisId },
    needInit: !!(id && hisId),
    initValue: { data: {} }
  });
  // 回复反馈
  const {
    request: requestReplay,
    loading: replayLoading
  } = useApi.complaintsReplay({
    needInit: false,
    initValue: {}
  });
  return (
    <Wrapper edit={false}>
      <CardLayout title={'患者信息'} loading={detailLoading}>
        <FormDescriptions
          data={data}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '反馈时间',
              name: 'createDate'
            },
            {
              label: '姓名',
              name: 'patientName'
            },
            {
              label: '联系电话',
              name: 'phone'
            },
            {
              label: '投诉原因',
              name: 'complaintsReason'
            },
            {
              label: '图片',
              name: 'complaintsCert',
              render: (v: any) =>
                v ? (
                  <Space>
                    {v.split(',').map(url => (
                      <Image width={80} src={url} />
                    ))}
                  </Space>
                ) : (
                  '-'
                )
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'投诉内容'} loading={detailLoading}>
        <Chat
          list={[
            {
              action: 'replyContent' as any,
              actionTrigger: 'string',
              content: data.complaintsContent || '',
              createDate: data.createDate,
              createTime: data.createDate,
              direction: 'TO_DOCTOR' as const,
              doctorIsShow: '1' as const,
              id: 99999999,
              inquiryId: 0,
              orderId: 0,
              sender: '',
              type: 'BIZ' as const,
              updateDate: data.updateDate,
              updateTime: data.updateTime,
              url: '',
              userId: 0,
              userIsShow: '1' as const,
              voiceContent: '',
              voiceTime: 0,
              userName: data.patientName || ''
            },
            ...(data?.replyList || []).map(item => {
              return {
                action: 'replyContent' as any,
                actionTrigger: 'string',
                content: item.replyContent || '',
                createDate: item.createDate,
                createTime: item.createDate,
                direction: (item.type === '2'
                  ? 'TO_USER'
                  : 'TO_DOCTOR') as 'TO_USER',
                doctorIsShow: '1' as const,
                id: 99999999,
                inquiryId: 0,
                orderId: 0,
                sender: '',
                type: 'BIZ' as const,
                updateDate: item.updateDate,
                updateTime: item.updateTime,
                url: item.replyUrl,
                userId: 0,
                userIsShow: '1' as const,
                voiceContent: '',
                voiceTime: 0,
                userName:
                  (item.type === '2' ? '管理员' : data.patientName) || ''
              } as any;
            })
          ].reverse()}
          loading={replayLoading}
          isNeedReplay={true}
          isConsult={false}
          submit={v =>
            requestReplay({
              complaintsId: id,
              hisId: hisId,
              replyContent: v.content,
              type: '2'
            }).then(() => {
              requestDetail({ id: id || '', hisId: hisId });
            })
          }
        />
      </CardLayout>
    </Wrapper>
  );
};

const Wrapper = styled.div<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
  .ant-table {
    > .ant-table-container {
      > .ant-table-content {
        > table {
          > tbody {
            > tr {
              > td {
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
`;
