import React, { useMemo, useState, useRef } from 'react';
import {
  LinkButton,
  DayRangePicker,
  RouteComponentProps,
  ArrSelect,
  ActionsWrap,
  actionConfirm
} from 'parsec-admin';
import { Button, Modal } from 'antd';
import useApi, { StatusObj } from '../api';
import { hidePhone } from '@utils/tools';
import styled from 'styled-components';
import MyTableList from '@components/myTableList';
import Beyond from '@components/beyond';
import permisstion from '@utils/permisstion';
import env from '@configs/env';

export default ({ history }: RouteComponentProps) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );
  const listParam = useRef({});
  const hisId = env.hisId;
  function showConfirm(val: string) {
    Modal.info({
      title: '投诉内容',
      content: (
        <div>
          <p>{val}</p>
        </div>
      )
    });
  }
  return (
    <MyTableList
      action={
        <Button
          onClick={() => {
            useApi.列表导出(listParam.current);
          }}
          type='primary'>
          导出
        </Button>
      }
      getList={({ params }) => {
        listParam.current = params;
        return useApi.list.request({ ...params });
      }}
      tableTitle={'意见反馈'}
      rowSelection={{
        onChange: selectedRowKeys => setSelectedRowKeys(selectedRowKeys)
      }}
      paginationExtra={
        <div>
          {!!selectedRowKeys.length && (
            <>
              <Button
                type={'default'}
                style={{ marginRight: '10px' }}
                onClick={() => {
                  actionConfirm(
                    () =>
                      useApi.complaintsUpdateBatch.request({
                        ids: selectedRowKeys.join(','),
                        hisId: hisId,
                        status: '0'
                      }),
                    '批量忽略'
                  );
                }}>
                批量忽略
              </Button>
              <span> 已选择{selectedRowKeys.length}条</span>
            </>
          )}
        </div>
      }
      columns={useMemo(
        () => [
          {
            title: '反馈时间',
            dataIndex: 'createDate',
            width: 180
          },
          {
            title: '姓名',
            dataIndex: 'userName',
            width: 100,
            search: true
          },
          {
            title: '联系电话',
            dataIndex: 'phone',
            width: 150,
            render: val => hidePhone(val)
          },
          {
            title: '原因',
            width: 200,
            dataIndex: 'complaintsReason'
          },
          {
            title: '投诉时间',
            dataIndex: 'createDate',
            search: <DayRangePicker placeholder={['开始时间', '结束时间']} />,
            searchIndex: ['startDate', 'endDate'],
            render: false
          },
          {
            title: '处理状态',
            dataIndex: 'status',
            search: <ArrSelect options={StatusObj} />,
            render: false
          },
          {
            title: '投诉内容',
            dataIndex: 'complaintsContent',
            width: 250,
            render: (_, record: any) => {
              return (
                <Content>
                  <div
                    className='leftbox'
                    onClick={() => showConfirm(record.complaintsContent)}>
                    <Beyond clamp={2}>{record.complaintsContent || ''}</Beyond>
                  </div>
                  <div className='rightbox'>
                    {record.complaintsCert
                      ? record.complaintsCert
                          .split(',')
                          .map((item: string, index: string) => {
                            return (
                              <Thumb
                                key={index}
                                onClick={() => {
                                  window.open(item);
                                }}>
                                <img src={item} alt={item} />
                              </Thumb>
                            );
                          })
                      : []}
                  </div>
                </Content>
              );
            }
          },
          {
            title: '操作',
            fixed: 'right',
            width: 180,
            render: record => (
              <ActionsWrap>
                {record.status === '1' && permisstion.canUpdateComplants && (
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          useApi.complaintsUpdate.request({
                            id: record.id,
                            hisId: hisId,
                            status: '0'
                          }),
                        '忽略'
                      );
                    }}>
                    忽略
                  </LinkButton>
                )}
                {record.status === '0' && <span>已忽略</span>}
                {record.status === '2' && <span>已处理</span>}
                <LinkButton
                  onClick={() => {
                    history.push('/operate/complaint/list/' + record.id);
                  }}>
                  详情
                </LinkButton>
              </ActionsWrap>
            )
          }
        ],
        [hisId, history]
      )}
    />
  );
};
const Content = styled.div`
  display: flex;
  > .leftbox,
  .rightbox {
    width: 50%;
    overflow: hidden;
    word-break: break-all;
  }
`;
const Thumb = styled.div`
  width: 30px;
  height: 30px;
  margin: 5px;
  display: inline-block;
  cursor: pointer;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;
