import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiRequestParams,
  ListApiResponseData,
  ApiResponse
} from '@src/configs/apis';
import env from '@configs/env';
import QueryString from 'qs';

interface List {
  caseId: string;
  chargeFlag: string;
  choiceRecord: string;
  content: string;
  countNum: string;
  createDate: string;
  createTime: string;
  deptId: string;
  deptName: string;
  diagnosis: string;
  doctor: string;
  doctorEarnings: string;
  doctorId: string;
  doctorName: string;
  doctorReaded: string;
  doctorTimes: number;
  doctorType: number;
  endTime: string;
  finishTime: string;
  firstReplyTime: string;
  hisId: string;
  hisName: string;
  hospitalTradeno: string;
  hospitalVisitNo: string;
  id: number;
  interval: number;
  item: string;
  orderId: number;
  orderIdStr: string;
  orderStatusName: string;
  orderStr: string;
  patCardNo: string;
  patientAge: number;
  patientId: number;
  patientName: string;
  patientNo: string;
  patientTel: string;
  paySerialNumber: string;
  platformId: number;
  purpose: string;
  purposeType: string;
  refundSource: string;
  refundStatus: string;
  replyDirection: string;
  replyTime: string;
  sex: string;
  startTime: string;
  status: string;
  statusName: string;
  subscribeInfo: string;
  totalFee: number;
  type: string;
  typeName: string;
  updateTime: string;
  userId: number;
  userName: string;
  userReaded: string;
  userTimes: number;
  validFlag: string;
  weight: string;
}
export default {
  问诊监管列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        orderBy?: string;
        platformId?: string;
      }
    ) =>
      request.post<ListApiResponseData<List>>(
        '/mch/inquiry/inquiry/page/manage',
        data
      )
  ),
  退费: createApiHooks((data: { hisId: string; orderId: string }) =>
    request.post<ApiResponse<Record<string, unknown>>>(
      '/mch/order/order/refund',
      data
    )
  ),
  exportDoctor: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${
      env.apiHost
    }/mch/inquiry/inquiry/page/manage/export?${QueryString.stringify({
      ...p,
      isExport: '1'
    })}`;
    window.open(url);
  }
};
