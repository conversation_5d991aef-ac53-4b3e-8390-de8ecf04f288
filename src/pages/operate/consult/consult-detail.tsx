import React, { useEffect, useState } from 'react';
import { CardLayout, FormDescriptions, getPrice, Form } from 'parsec-admin';
import styled from 'styled-components';
import useApi from '../evaluate/api';
import { useParams } from 'react-router-dom';
import Message from '@components/ih-standard-message';

const editSate = false;

export default () => {
  const { id: orderId } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [doctorUrl, setDoctorUrl] = useState<string[]>([]);
  const [patientUrl, setPatientUrl] = useState<string[]>([]);

  const {
    data: { data },
    loading: detailLoading
  } = useApi.问诊详情({
    params: { orderId: orderId || '' },
    needInit: !!orderId,
    initValue: { data: {} }
  });

  useEffect(() => {
    if (data?.inquiry?.videoInfo) {
      const videoInfo: {
        stream_id: string;
        video_url: string;
      }[] = JSON.parse(data?.inquiry?.videoInfo || '') || [];

      // console.log('videoInfo', videoInfo);

      const doctorList: string[] = [];
      const patientList: string[] = [];
      videoInfo.forEach(item => {
        if (item.stream_id.split('-')[0] === 'doctor') {
          doctorList.push(item.video_url);
        } else {
          patientList.push(item.video_url);
        }
      }, []);

      setDoctorUrl(doctorList);
      setPatientUrl(patientList);
    }
  }, [data]);

  return (
    <Wrapper edit={false}>
      <CardLayout title={'患者信息'} loading={detailLoading}>
        <FormDescriptions
          data={data?.patient}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '用户名',
              name: 'name'
            },
            {
              label: '性别',
              name: 'patientSex'
            },
            {
              label: '年龄',
              name: 'patientAge'
            },
            {
              label: '体重',
              name: 'weight',
              render: v => v + ' kg'
            },
            {
              label: '联系方式',
              name: 'mobile'
            },
            {
              label: '身份证号',
              name: 'idNo'
            },
            {
              label: '问诊时间',
              name: 'createTime',
              render: () => data?.inquiry?.createDate
            },
            {
              label: '问诊科室',
              name: 'createTime',
              render: () => data?.inquiry?.deptName
            },
            {
              label: '问诊医生',
              name: 'createTime',
              render: () => data?.inquiry?.doctorName
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'订单信息'} loading={detailLoading}>
        <FormDescriptions
          data={data?.inquiry}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '支付状态',
              render: () => data?.order?.orderStatusName || ''
            },
            {
              label: '问诊状态',
              name: 'orderStatusName'
            },
            {
              label: '订单金额',
              name: 'totalFee',
              render: v => `¥ ${getPrice(v as number, 2, true)}`
            },
            {
              label: '办理渠道',
              name: 'orderStatusName',
              render: () => data?.order?.payChannelStr
            },
            {
              label: '业务类型',
              name: 'orderStatusName',
              render: () =>
                data?.order?.businessType === '1'
                  ? '咨询类'
                  : data?.order?.businessType === '2'
                  ? '诊疗类'
                  : ''
            },
            {
              label: '下单时间',
              name: 'createDate'
            },
            {
              label: '平台订单',
              name: 'orderIdStr'
            }
          ]}
        />
      </CardLayout>
      <CardLayout
        title={
          <CardTitleBox>
            <div>问诊详情</div>
            <StatusBox>
              <div className='status-item selectedItem'>
                {data?.inquiry?.statusName}
              </div>
            </StatusBox>
          </CardTitleBox>
        }
        loading={detailLoading}>
        {data && data.items && (
          <Message
            addNewChat={item => {}}
            doctorAvatar={data?.doctor.image}
            userAvatar={data?.patient?.patientImage || ''}
            goAppointDetail={() => {}}
            userName={data?.patient?.patientImage || data?.patient.name}
            data={{
              inquiry: data?.inquiry as any,
              items: data?.items as any
            }}
            direction={'TO_ALL'}
            goPrescriptionDetail={item => {}}
            goPrescriptionDetailPre={item => {}}
            onAction={(action, item) => {}}
          />
        )}
        {data?.order?.typeStr === '视频问诊' && (
          <VideoLinkBox>
            <p>患者视角：</p>
            {patientUrl.map((url, index) => {
              return (
                <p key={index}>
                  <a target='_blank' rel='noopener noreferrer' href={url}>
                    {url || '-'}
                  </a>
                </p>
              );
            })}
            <p>医生视角：</p>
            {doctorUrl.map((url, index) => {
              return (
                <p key={index}>
                  <a target='_blank' rel='noopener noreferrer' href={url}>
                    {url || '-'}
                  </a>
                </p>
              );
            })}
          </VideoLinkBox>
        )}
      </CardLayout>
    </Wrapper>
  );
};

const Wrapper = styled.div<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
  .ant-table {
    > .ant-table-container {
      > .ant-table-content {
        > table {
          > tbody {
            > tr {
              > td {
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
`;
const CardTitleBox = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
`;
const StatusBox = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-left: 40px;
  > .status-item {
    /* width: 60px; */
    padding: 0 10px;
    height: 24px;
    border-radius: 18px;
    text-align: center;
    line-height: 24px;
    font-size: 14px;
    background-color: #dcdcdc;
    color: #444444;
    margin-right: 20px;
    &.selectedItem {
      background-color: #c0ddef;
      color: #479ad0;
    }
  }
`;
const VideoLinkBox = styled.div`
  margin-top: 20px;
`;
