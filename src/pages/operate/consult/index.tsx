import React, { useEffect, useMemo, useRef } from 'react';
import {
  LinkButton,
  RouteComponentProps,
  DayRangePicker,
  getPrice,
  ArrSelect,
  renderState
} from 'parsec-admin';
import useApi from './api';
import MyTableList from '@components/myTableList';
import {
  orderTypeObj,
  purposeTypeObj,
  orderStatusObj,
  Channel,
  orderPayStatusObj
} from '@src/pages/order/api';
import permisstion from '@utils/permisstion';
import { Button, Form } from 'antd';
import qs from 'qs';
// const isDev = process.env.NODE_ENV !== 'production';

export default ({ history }: RouteComponentProps) => {
  const form = Form.useForm()[0];
  const searchRef = useRef<any>();
  const { beginTime, endTime } = qs.parse(
    window.location.href.split('?')[1]
  ) as {
    beginTime?: string;
    endTime?: string;
  };
  useEffect(() => {
    if (endTime && beginTime) {
      form.setFieldsValue({
        startDate: [beginTime, endTime]
      });
    }
  }, [endTime, beginTime, form]);
  return (
    <MyTableList
      form={form}
      tableTitle='问诊监管'
      action={
        <Button onClick={() => useApi.exportDoctor(searchRef.current)}>
          导出EXCEL
        </Button>
      }
      getList={({ params }) => {
        let searchParams = { ...params };
        const { startDate } = form.getFieldsValue();
        if (Array.isArray(startDate)) {
          searchParams = {
            ...searchParams,
            startDate: startDate[0],
            endDate: startDate[1]
          };
        }
        searchRef.current = searchParams;
        return useApi.问诊监管列表.request({
          ...searchParams,
          orderBy: 'createTime'
        });
      }}
      columns={useMemo(
        () => [
          {
            title: '问诊时间',
            width: 180,
            dataIndex: 'createDate',
            search: (
              <DayRangePicker
                placeholder={['开始日期', '结束日期']}
                valueFormat={'YYYY-MM-DD HH:mm:ss'}
              />
            ),
            searchIndex: ['startDate', 'endDate']
          },
          {
            title: '患者姓名 | ID',
            width: 160,
            dataIndex: 'patientName',
            render: (v, record: any) => {
              return `${v}|${record.patCardNo}`;
            }
          },
          {
            title: '业务类型 | 问诊目的',
            width: 180,
            dataIndex: 'typeName',
            render: (v, record: any) => {
              return `${v}|${record.purpose}`;
            }
          },
          {
            title: '办理渠道',
            width: 180,
            dataIndex: 'channel',
            search: <ArrSelect options={Channel} />,
            render: v => (v ? Channel[v] : '-')
          },
          {
            title: '平台单号',
            width: 180,
            dataIndex: 'orderIdStr'
          },
          {
            title: '问诊医生 | 问诊科室',
            width: 160,
            dataIndex: 'doctorName',
            render: (v, record: any) => {
              return `${v}|${record.deptName}`;
            }
          },
          {
            title: '支付状态',
            width: 100,
            dataIndex: 'orderStatusName',
            searchIndex: 'orderStatus',
            search: <ArrSelect options={orderPayStatusObj} />
          },
          {
            title: '问诊状态',
            width: 100,
            dataIndex: 'status',
            render: renderState(orderStatusObj)
            // render: v => {
            //   return orderStatusObj[v];
            // }
          },
          {
            title: '金额',
            width: 100,
            dataIndex: 'totalFee',
            render: v => `${getPrice(v, 2, true)}`
          },
          {
            title: '业务类型',
            width: 280,
            dataIndex: 'type',
            search: <ArrSelect options={orderTypeObj} />,
            render: false
          },
          {
            title: '问诊目的',
            width: 280,
            dataIndex: 'purposeType',
            search: <ArrSelect options={purposeTypeObj} />,
            render: false
          },
          {
            title: '问诊状态',
            dataIndex: 'status',
            search: <ArrSelect options={orderStatusObj} />,
            render: false
          },
          {
            title: '平台单号',
            dataIndex: 'orderId',
            search: true,
            render: false
          },
          {
            title: '问诊科室',
            dataIndex: 'deptName',
            search: true,
            render: false
          },
          {
            title: '问诊医生',
            dataIndex: 'doctorName',
            search: true,
            render: false
          },
          {
            title: '患者姓名',
            dataIndex: 'patientName',
            search: true,
            render: false
          },
          {
            title: '是否写病历',
            dataIndex: 'hasCaseId',
            search: (
              <ArrSelect
                options={{ '0': '否', '1': '是' }}
                placeholder='请选择是否写病历'
              />
            ),
            render: false
          },
          {
            title: '是否写病历',
            width: 120,
            align: 'center',
            dataIndex: 'caseId',
            render: v => (v ? '是' : '否'),
            fixed: 'right'
          },
          {
            title: '操作',
            fixed: 'right',
            width: 120,
            render: record =>
              permisstion.canQueryDetail && (
                <LinkButton
                  onClick={() => {
                    record.orderIdStr &&
                      history.push(
                        '/operate/consult/list/' + record.orderIdStr
                      );
                  }}>
                  详情
                </LinkButton>
              )
          }
        ],
        [history]
      )}
    />
  );
};
