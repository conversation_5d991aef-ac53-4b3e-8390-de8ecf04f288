import React, { useMemo } from 'react';
import {
  LinkButton,
  ArrSelect,
  ActionsWrap,
  DayRangePicker,
  useModal,
  handleSubmit,
  actionConfirm
} from 'parsec-admin';
import useApi from './api';
import MyTableList from '@components/myTableList';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Form, Input } from 'antd';
import moment from 'moment';
import permisstion from '@utils/permisstion';
import env from '@src/configs/env';

const { TextArea } = Input;
export default () => {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const typeList = [
    { id: 1, name: '启用' },
    { id: 2, name: '停用' }
  ];
  const hisId = env.hisId;
  // const { data: detail } = useApi.查询表扬信模版详情({
  //   params: hisId
  // });
  const [form] = Form.useForm();
  const switchModalVisible = useModal(({ id }) => {
    return {
      title: '模版',
      form,
      onSubmit: async values => {
        console.log('values', id);
        await handleSubmit(() =>
          id
            ? useApi.表扬信模版修改.request({ ...values, hisId: hisId, id })
            : useApi.表扬信模版新增.request({ ...values, hisId: hisId })
        );
      },
      items: [
        {
          label: '排序',
          name: 'sortNo',
          required: true
        },
        {
          label: '模版内容',
          name: 'commendContent',
          required: true,
          render: (
            <TextArea
              onChange={() => {}}
              rows={4}
              maxLength={100}
              placeholder='请输入模版内容（100字以内）'
            />
          )
        }
      ]
    };
  });
  return (
    <MyTableList
      tableTitle='表扬信内容模版'
      getList={({ params }) => useApi.表扬信模版.request(params)}
      scroll={{ x: 1600 }}
      action={
        permisstion.canAddReply && (
          <Button
            type={'default'}
            icon={<PlusOutlined />}
            onClick={() => {
              switchModalVisible();
            }}>
            新建模版
          </Button>
        )
      }
      columns={useMemo(
        () => [
          {
            title: '表扬内容',
            width: 200,
            dataIndex: 'commendContent'
          },
          {
            title: '排序',
            width: 220,
            dataIndex: 'sortNo'
          },
          {
            title: '创建人',
            dataIndex: 'createUser',
            width: 100,
            search: true
          },
          {
            title: '创建时间',
            width: 100,
            dataIndex: 'createTime',
            search: <DayRangePicker placeholder={['开始时间', '结束时间']} />,
            searchIndex: ['startDate', 'endDate'],
            render: val => moment(val).format('YYYY-MM-DD HH:mm:ss')
          },
          {
            title: '启用状态',
            dataIndex: 'status',
            width: 100,
            search: (
              <ArrSelect
                options={typeList.map(type => {
                  return {
                    value: type.id,
                    children: type.name
                  };
                })}
              />
            ),
            render: val => (val === 1 ? '启用' : '停用')
          },
          {
            title: '操作',
            fixed: 'right',
            width: 150,
            render: permisstion.canUpdateDept
              ? record => (
                  <ActionsWrap max={99}>
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () => {
                            return useApi.表扬信模版修改.request({
                              ...record,
                              status: record.status === 1 ? 2 : 1
                            });
                          },
                          record.status === 2 ? '启用' : '停用'
                        );
                      }}>
                      {record.status === 2 ? '启用' : '停用'}
                    </LinkButton>
                    <LinkButton
                      onClick={() => {
                        switchModalVisible(record);
                      }}>
                      编辑
                    </LinkButton>
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.删除表扬信模版.request({
                              id: record.id
                            }),
                          '删除'
                        );
                      }}>
                      删除
                    </LinkButton>
                  </ActionsWrap>
                )
              : false
          }
        ],
        [switchModalVisible, typeList]
      )}
    />
  );
};
