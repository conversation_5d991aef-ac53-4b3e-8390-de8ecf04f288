/*
 * @Author: hpeng<PERSON>i <EMAIL>
 * @Date: 2024-05-15 14:33:57
 * @LastEditors: hpengfei <EMAIL>
 * @LastEditTime: 2024-05-21 17:06:35
 * @FilePath: \ih-standard\src\pages\operate\templateLetter\api.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiRequestParams,
  ListApiResponseData,
  ApiResponse
} from '@src/configs/apis';

export default {
  删除表扬信模版: createApiHooks((params: { id: string }) =>
    request.delete(
      `/intelligent/mch/intelligent/commend/letter/template/${params.id}`
    )
  ),
  表扬信模版: createApiHooks((data: ListApiRequestParams) =>
    request.get<
      ListApiResponseData<{
        id: number;
        hisId: string;
        commendContent: string;
        sortNo: number;
        createUser: string;
        status: string;
        createTime: string;
      }>
    >('/intelligent/mch/intelligent/commend/letter/template', {
      params: data
    })
  ),
  表扬信模版新增: createApiHooks(
    (params: {
      hisId: string; // 医院ID
      commendContent: string; //表扬内容
      sortNo: number; //排序
    }) =>
      request.post<ApiResponse<any>>(
        '/intelligent/mch/intelligent/commend/letter/template',
        params,
        {
          headers: { 'Content-Type': 'application/json' }
        }
      )
  ),
  表扬信模版修改: createApiHooks(
    (params: {
      hisId: string; // 医院ID
      commendContent: string; //表扬内容
      sortNo: number; //排序
      status: number;
      id: string; // 症状ID
    }) =>
      request.put<ApiResponse<any>>(
        '/intelligent/mch/intelligent/commend/letter/template',
        params,
        {
          headers: { 'Content-Type': 'application/json' }
        }
      )
  ),
  查询表扬信模版详情: createApiHooks((id: string) =>
    request.post<ApiResponse<Record<string, unknown>>>(
      `/mch/intelligent/commend/letter/template/update/${id}`
    )
  )
};
