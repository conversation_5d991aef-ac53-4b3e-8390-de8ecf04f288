// 根据手机号查询出的所在的医院列表
export interface ComplaintList {
  complaintsCert?: string;
  complaintsContent?: string;
  complaintsReason?: string;
  createDate?: string;
  createTime?: string;
  deptId?: string;
  deptName?: string;
  doctorId?: string;
  doctorName?: string;
  endDate?: string;
  extfield?: string;
  hisId?: number;
  id?: number;
  numPerPage?: number;
  orderBy?: string;
  pageNum?: number;
  patientId?: number;
  patientName?: string;
  phone?: string;
  platformId?: number;
  replyContent?: string;
  replyList?: Array<Replay>;
  replyTime?: string;
  sort?: string;
  startDate?: string;
  startIndex?: number;
  status?: string;
  updateDate?: string;
  updateTime?: string;
  userAgent?: string;
  userId?: string;
  userName?: string;
}
export interface ComplaintDetail {
  complaintsCert?: string;
  complaintsContent?: string;
  complaintsReason?: string;
  createDate?: string;
  createTime?: string;
  deptId?: string;
  deptName?: string;
  doctorId?: string;
  doctorName?: string;
  endDate?: string;
  extfield?: string;
  hisId?: number;
  id?: number;
  ids?: string;
  numPerPage?: number;
  orderBy?: string;
  pageNum?: number;
  patientId?: number;
  patientName?: string;
  phone?: string;
  platformId?: number;
  replyContent?: string;
  replyList?: Array<Replay>;
  replyTime?: string;
  sort?: string;
  startDate?: string;
  startIndex?: number;
  status?: string;
  updateDate?: string;
  updateTime?: string;
  userAgent?: string;
  userId?: string;
  userName?: string;
  replyUrl?: string;
}
export interface Replay {
  adminId?: string;
  adminName?: string;
  complaintsId?: number;
  createDate?: string;
  createTime?: string;
  hisId?: number;
  id?: number;
  platformId?: number;
  replyContent?: string;
  replyName?: string;
  replyUrl?: string;
  status?: string;
  type?: string;
  updateDate?: string;
  updateTime?: string;
  userId?: number;
  userName?: string;
}
export interface MedicalRecordTemplate {
  id: '@natural'; //主键id
  hisId: '@integer'; //医院hisid
  createTime: '@datetime';
  updateTime: '@datetime';
  name: '@ctitle'; //模板名称
  templateId: '@natural'; //模板id
  templateType: "@pick('PUBLIC','DOCTOR')"; //模板类型：PUBLIC公共，DOCTOR医生自己创建的模板
  medicalTemplateType: '@pick(1,2)'; //病历类型：1西医病历 2中医病历
  deptList: [
    {
      id: '@natural'; //科室id
      name: '@cword(2)科室'; //科室名称
    }
  ]; //科室信息 当选择全部科室时参数如下{"id":0,"name":"全部科室"}
  chiefComplaint: '@csentence'; //主诉
  medicalHistory: '@csentence'; //病史症状
  anamnesis: '@csentence'; //既往史
  recommend: '@csentence'; //建议
  useCount: '@natural'; //使用量
}
export type Template = {
  name: '@ctitle'; //模板名称
  medicalTemplateType: '@pick(1,2)'; //病历类型：1西医病历 2中医病历
  deptList: [
    {
      id: '@natural'; //科室id
      name: '@cword(2)科室'; //科室名称
    }
  ];
  [key: string]: any;
};
