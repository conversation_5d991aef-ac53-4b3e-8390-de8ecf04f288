import React, { useRef, useState } from 'react';
import './index.less';
import { BlankLayout } from 'parsec-admin';
import { Button, Tabs } from 'antd';
import { WhereUse } from './api';
import EditTable from '@pages/operate/intelligentCustomerService/components/editTable';
import useApi from '@pages/operate/intelligentCustomerService/api';

export default () => {
  const [activeKey, setActiveKey] = useState<WhereUse>('GZH');
  const { loading: saveLoading, request: saveRequest } = useApi.配置信息修改({
    needInit: false
  });
  const EditTableRaf = useRef<any>();
  return (
    <BlankLayout className='box'>
      <div className='header'>
        <Tabs
          destroyInactiveTabPane
          activeKey={activeKey}
          onChange={key => {
            setActiveKey(key as WhereUse);
          }}>
          <Tabs.TabPane tab='微信公众号' key='GZH'>
            <EditTable
              ref={EditTableRaf}
              activeKey={'GZH'}
              saveRequest={saveRequest}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab='自助机' key='ZZJ'>
            <EditTable
              ref={EditTableRaf}
              activeKey={'ZZJ'}
              saveRequest={saveRequest}
            />
          </Tabs.TabPane>
        </Tabs>
        <Button
          className='subButton'
          type='primary'
          loading={saveLoading}
          onClick={() => {
            if (EditTableRaf.current) {
              EditTableRaf.current.submit();
            }
          }}>
          保存配置
        </Button>
      </div>
    </BlankLayout>
  );
};
