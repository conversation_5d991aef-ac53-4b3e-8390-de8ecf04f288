import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ApiResponse } from '@src/configs/apis';
export type WhereUse = 'ZZJ' | 'GZH';
export enum WhereShow {
  'SY-DB' = '首页-底部',
  'JFSB-YSJ' = '缴费失败-右上角',
  'JFYC-YSJ' = '缴费异常-右上角',
  'DDSX-YSJ' = '订单失效-右上角',
  'DDSB-YSJ' = '订单失败-右上角',
  'YMYDWT-ZJ' = '页面遇到问题-中间',
  'ZWSJ-ZJ' = '暂无数据=中间',
  'YJFK-DB' = '意见反馈-底部',
  'XJYJFK-DB' = '新建意见反馈-底部',
  'FKXQ-DB ' = '反馈详情-底部',
  'ZJHF-DB' = '追加回复-底部',
  'SY-YXJ' = '首页-右下角',
  'GHXQYC-YXJ' = '挂号详情异常-右下角',
  'JFXQYC-YXJ' = '缴费详情异常-右下角',
  'GHSBTC-ZJ' = '挂号失败弹窗-中间',
  'GHYCTC-ZJ ' = '挂号异常弹窗-中间',
  'JFSBTC-ZJ' = '缴费失败弹窗-中间',
  'JFYCTC-ZJ' = '缴费异常弹窗-中间'
}
export interface ConfigureListQueryData {
  hospitalId: ''; //第三方系统里的医院ID
  subHospitalId: ''; //第三方系统里的院区ID
  whereUse: WhereUse; //使用渠道（载体）枚举值（GZH-微信公众号；ZZJ-自助机）
  customerServiceList: ConfigureListQuery[];
}
export type ConfigureListQuery = {
  id: '@natural'; //主键ID
  configName: "@pick('小乔医助','意见反馈')"; //配置名称
  whereShowCode: ''; //显示界面/位置的编码
  whereShow: WhereShow; //显示界面/位置
  jumpType: 'CLICK' | 'SCAN'; //跳转方式（枚举：CLICK-点击跳转；SCAN-扫码）
  jumpTypeName: ''; // 跳转方式名称
  jumpUrl: 'https://httpbin.org/get?q=@word(8)'; //跳转链接
  status: 1 | 0; //启用状态
  statusName: ''; //启用状态名称
  creator: '@cname'; //创建人
  creatorId: ''; //创建人id
  createTime: ''; //创建时间
  updateTime: ''; //更新时间
};
export default {
  配置列表查询: createApiHooks((params: { whereUse: 'GZH' | 'ZZJ' }) => {
    return request.get<ApiResponse<ConfigureListQueryData>>(
      `/intelligent/mch/intelligent/customer_service`,
      { params }
    );
  }),
  配置信息修改: createApiHooks((data: ConfigureListQueryData) => {
    return request.put<ApiResponse<any>>(
      `/intelligent/mch/intelligent/customer_service`,
      data
    );
  })
};
