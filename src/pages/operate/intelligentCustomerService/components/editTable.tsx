import { Input, Select, Switch, Table, Form, message } from 'antd';
import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle
} from 'react';
import '../index.less';
import useApi, {
  ConfigureListQuery,
  WhereUse,
  ConfigureListQueryData
} from '@pages/operate/intelligentCustomerService/api';
import { ApiResponse } from '@configs/d';
const { Option } = Select;

const EditTable = forwardRef(
  (
    {
      activeKey,
      saveRequest
    }: {
      activeKey: WhereUse;
      saveRequest: (p?: ConfigureListQueryData) => Promise<ApiResponse<any>>;
    },
    ref
  ) => {
    const [showList, setShowList] = useState<any[]>([]);
    const [form] = Form.useForm();
    const {
      data: { data },
      loading,
      request
    } = useApi.配置列表查询({
      initValue: { data: {} },
      params: {
        whereUse: activeKey
      }
    });

    useEffect(() => {
      form.setFieldsValue({
        hospitalId: data?.hospitalId,
        subHospitalId: data?.subHospitalId
      });
      if (data?.customerServiceList && data?.customerServiceList.length) {
        setShowList(
          (data?.customerServiceList || []).map(item => {
            return { key: item.id, ...item };
          })
        );
      }
    }, [data, form]);
    const columns = [
      {
        dataIndex: 'whereShow',
        key: 'whereShow',
        width: 300,
        render: v => {
          return `位置名称：${v ? v : '-'}`;
        }
      },
      {
        dataIndex: 'configName',
        key: 'configName',
        width: 400,
        render: (v, item) => {
          return (
            <>
              配置名称：
              <Select
                defaultValue={v}
                className='select'
                onChange={e => {
                  showList.map(dataItem => {
                    if (dataItem.id === item.id) {
                      dataItem.configName = e;
                    }
                    return dataItem;
                  });
                }}>
                <Option value='意见反馈'>意见反馈</Option>
                <Option value='小桥医助'>小桥医助</Option>
              </Select>
            </>
          );
        }
      },
      {
        dataIndex: 'status',
        key: 'status',
        render: (v, item) => {
          return (
            <Switch
              checkedChildren='启用'
              defaultChecked={v === 1}
              unCheckedChildren='未启用'
              onChange={e => {
                showList.map(dataItem => {
                  if (dataItem.id === item.id) {
                    dataItem.status = e ? 1 : 0;
                  }
                  return dataItem;
                });
              }}
            />
          );
        }
      }
    ];
    const myReplace = (item, hospitalId, subHospitalId, dataList) => {
      let url = '';
      if (item?.jumpUrl) {
        const configName =
          item?.configName === '小桥医助' ? 'chat' : 'feedback';
        const beforeConfigName =
          dataList?.configName === '小桥医助' ? 'chat' : 'feedback';
        if (activeKey === 'ZZJ') {
          url = item?.jumpUrl
            .replace('{hospitalId}', hospitalId)
            .replace('{subHospitalId}', subHospitalId)
            .replace(beforeConfigName, configName)
            .replace(`/hospital/${data?.hospitalId}`, `/hospital/${hospitalId}`)
            .replace(
              `subhospitalId=${data?.subHospitalId}`,
              `subhospitalId=${subHospitalId}`
            );
        } else {
          // const urlArr = 'pages/${target}/index?subhospitalId=${subhospitalId}&hospitalId=${id}'.split(
          //   '/'
          // );

          url = item?.jumpUrl
            .replace('${target}', configName)
            .replace('${subhospitalId}', subHospitalId)
            .replace('${id}', hospitalId)
            .replace(beforeConfigName, configName)
            .replace(
              `subhospitalId=${data?.subHospitalId}`,
              `subhospitalId=${subHospitalId}`
            )
            .replace(
              `hospitalId=${data?.hospitalId}`,
              `hospitalId=${hospitalId}`
            );
        }
      }
      return url;
    };
    const save = () => {
      form.validateFields().then(({ hospitalId, subHospitalId }) => {
        const configurationInformationList: ConfigureListQuery[] = showList.map(
          (item, index) => {
            const url = myReplace(
              item,
              hospitalId,
              subHospitalId,
              data?.customerServiceList[index]
            );
            console.log(url);
            return { ...item, jumpUrl: url };
          }
        );
        if (configurationInformationList.length) {
          saveRequest({
            hospitalId: hospitalId || '',
            subHospitalId: subHospitalId || '',
            whereUse: activeKey,
            customerServiceList: configurationInformationList
          })
            .then(() => {
              message.success('保存成功！');
              request({ whereUse: activeKey });
            })
            .catch(e => {
              console.log(e);
            });
        } else {
          message.warn('当前没有可以保存的配置！');
        }
      });
    };
    useImperativeHandle(ref, () => ({
      submit: () => {
        save();
      }
    }));

    return (
      <>
        <div className='search'>
          <Form form={form} layout={'inline'}>
            <Form.Item
              label='医院ID'
              name='hospitalId'
              rules={[{ required: true, message: '请输入医院ID' }]}>
              <Input placeholder={'请输入医院ID'} />
            </Form.Item>
            <Form.Item
              label='院区ID'
              name='subHospitalId'
              rules={[{ required: true, message: '请输入院区ID' }]}>
              <Input placeholder={'请输入院区ID'} />
            </Form.Item>
          </Form>
        </div>
        <Table
          loading={loading}
          showHeader={false}
          pagination={false}
          dataSource={showList}
          columns={columns}
        />
      </>
    );
  }
);
export default EditTable;
