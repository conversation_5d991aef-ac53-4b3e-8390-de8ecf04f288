import { useMemo, useState } from 'react';
import {
  LinkButton,
  actionConfirm,
  ActionsWrap,
  handleSubmit
} from 'parsec-admin';
import useApi from './api';
import { Button, Modal } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import MyTableList from '@components/myTableList';
import Add from './components/add';

export default () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );
  const [modalVisible, setModalVisible] = useState(false);
  const [editId, setEditId] = useState<number>();

  const { request: reqAdd } = useApi.新增常用处方({
    needInit: false
  });

  const { request: reqEdit } = useApi.编辑常用处方({
    needInit: false
  });

  const handleSuccess = (data: any) => {
    handleSubmit(() => {
      return (data.id ? reqEdit : reqAdd)(data).then(() => {
        setModalVisible(false);
      });
    });
  };

  return (
    <div>
      <MyTableList
        tableTitle={'经典方管理'}
        getList={({ params }) =>
          useApi.经典方列表.request({
            ...params,
            templateType: 2
          })
        }
        action={
          <Button
            type={'default'}
            icon={<PlusOutlined />}
            onClick={() => {
              setModalVisible(true);
              setEditId(undefined);
            }}>
            添加
          </Button>
        }
        paginationExtra={
          <div>
            {!!selectedRowKeys.length && (
              <>
                <Button
                  type={'default'}
                  style={{ marginRight: '10px' }}
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.经典方删除.request(selectedRowKeys.join(',')),
                      '批量删除'
                    );
                  }}>
                  批量删除
                </Button>
                <span> 已选择{selectedRowKeys.length}条</span>
              </>
            )}
          </div>
        }
        rowSelection={{
          onChange: selectedRowKeys => setSelectedRowKeys(selectedRowKeys)
        }}
        columns={useMemo(
          () => [
            {
              title: '经典方名称',
              dataIndex: 'templateName',
              search: true
            },
            {
              title: '组成',
              dataIndex: 'drugList',
              width: '16%',
              render: i => {
                return i
                  .map(item => `${item.drugName}${item.chWeightPerDose}g`)
                  .join(',');
              }
            },
            {
              title: '功能',
              dataIndex: 'recipeFunction',
              width: '16%'
            },
            {
              title: '主治',
              dataIndex: 'majorFunction',
              width: '20%'
            },
            {
              title: '加减运用',
              dataIndex: 'useQuantity',
              width: '16%'
            },
            {
              title: '操作',
              fixed: 'right',
              width: 150,
              render: record => (
                <ActionsWrap>
                  <LinkButton
                    onClick={() => {
                      setModalVisible(true);
                      setEditId(record.id);
                    }}>
                    编辑
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () => useApi.经典方删除.request(record.id),
                        '删除'
                      );
                    }}>
                    删除
                  </LinkButton>
                </ActionsWrap>
              )
            }
          ],
          []
        )}
      />
      <Modal
        title={editId ? '编辑经典方' : '添加经典方'}
        width={800}
        visible={modalVisible}
        onCancel={() => {
          setModalVisible(false);
        }}
        footer={false}>
        <Add
          id={editId}
          onSuccess={handleSuccess}
          onCancel={() => {
            setModalVisible(false);
          }}
        />
      </Modal>
    </div>
  );
};
