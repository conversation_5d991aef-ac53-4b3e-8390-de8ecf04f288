import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ApiResponse,
  ListApiResponseData,
  ListApiRequestParams
} from '@src/configs/apis';
import env from '@src/configs/env';

export interface DicItem {
  dictGroupId: null;
  dictKey: 'B10.02.03.';
  dictValue: { value: '三度' };
  hisId: 0;
  id: 3429;
  sortNo: 2060;
}

export default {
  经典方列表: createApiHooks(
    (params: ListApiRequestParams & { templateType: number }) =>
      request.get<ListApiResponseData<OnlyDrugstoresType>>(
        '/mch/prescription/recipe-template/page',
        { params }
      )
  ),
  单个常用方详情: createApiHooks((id: number) => {
    return request.get<ApiResponse<RecipeTemplateData>>(
      `/mch/prescription/recipe-template/${id}`
    );
  }),
  经典方新增: createApiHooks(data =>
    request.post('/mch/prescription/recipe-template/add', data)
  ),
  经典方删除: createApiHooks((id: number | string) =>
    request.delete<ListApiResponseData<OnlyDrugstoresType>>(
      `/mch/prescription/recipe-template/delete/${id}`
    )
  ),
  //获取唯一药房，只有中医和西医各有一个,药房类型（1西药房，2中药房）
  获取唯一药房: createApiHooks((params: { storageType: number }) =>
    request.get<ApiResponse<OnlyDrugstoresType>>(
      '/mch/his/hospital/drugstore',
      { params }
    )
  ),
  //获取唯一药房，只有中医和西医各有一个,药房类型（1西药房，2中药房）
  获取药房列表: createApiHooks((params: { storageType: number }) =>
    request.get<ApiResponse<OnlyDrugstoresType[]>>(
      '/mch/his/hospitalDistrict/drugstore',
      { params }
    )
  ),
  监管上报对码字典: createApiHooks((type: HospiDic) => {
    return request
      .get<
        ApiResponse<
          {
            hisDictName: string;
            hisDictCode: string;
            dictType: string;
          }[]
        >
      >(`/mch/prescription/report-dict?type=${type}`)
      .then(res => {
        return {
          data: res.data?.data?.map(item => ({
            label: item.hisDictName,
            value: item.hisDictCode
          }))
        };
      })
      .catch(res => {
        return {
          data: administrationList
        };
      });
  }),
  药品列表: createApiHooks((params: GetDrugType) =>
    request.get<ReturnType<any>>('/mch/prescription/prescription/drugList', {
      params
    })
  ),
  处方详情: createApiHooks((orderId: number) => {
    return request.get<ReturnType<any>>(
      `/mch/prescription/prescription/v2/${orderId}`
    );
  }),
  查询字典列表: createApiHooks(
    ({
      groupCode,
      ...params
    }: {
      groupCode?: string;
      pageNum?: number;
      dictValue?: string;
      numPerPage?: number;
    }) =>
      request.get<{
        code: number;
        data: {
          totalCount: number;
          recordList: DicItem[];
        };
      }>(`/kaiqiao/his/dictItem/page/${groupCode}`, {
        params
      })
  ),
  查询格式化的字典列表: createApiHooks(
    ({
      groupCode,
      ...params
    }: {
      groupCode?: string;
      pageNum?: number;
      dictValue?: string;
      numPerPage?: number;
    }) =>
      request
        .get<{
          code: number;
          data: {
            totalCount: number;
            recordList: DicItem[];
          };
        }>(`/kaiqiao/his/dictItem/page/${groupCode}`, {
          params: {
            ...params,
            hisId: env.hisId
          }
        })
        .then(res => {
          return {
            data: res.data.data?.recordList?.map(item => ({
              label: item.dictValue?.value,
              value: item.dictKey
            }))
          };
        })
        .catch(() => {
          return {
            data: []
          };
        })
  ),
  新增常用处方: createApiHooks((params: RecipeTemplateData) => {
    return request.post('/mch/prescription/recipe-template/add', params, {
      headers: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  }),
  编辑常用处方: createApiHooks((params: RecipeTemplateData) => {
    return request.post('/mch/prescription/recipe-template/update', params, {
      headers: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  }),
  获取诊断: createApiHooks(
    (params: {
      diagnosisType: number;
      pageNo: number;
      pageSize: number;
      inputCode?: string;
    }) => {
      return request.get<ListApiResponseData<DiagnosisType>>(
        `/mch/inquiry/medicalRecord/diagnosisDict`,
        {
          headers: {
            'Content-Type': 'application/json;charset=utf-8'
          },
          params
        }
      );
    }
  ),
  中药饮片备注常量: createApiHooks(() => {
    return request.get<ApiResponse<Array<string>>>(
      '/mch/prescription/recipe-template/chRemark'
    );
  }),
  配伍禁忌检查: createApiHooks((params: PostPrescriptionType) => {
    return request.post<
      ApiResponse<{
        anti: Array<{ name: string; boycottList: Array<string> }>;
        fear: Array<{ name: string; boycottList: Array<string> }>;
      }>
    >('/mch/prescription/Incompatibility/check', params, {
      headers: {
        Accept: 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/json; charset=UTF-8'
      }
    });
  })
};

export type OnlyDrugstoresType = {
  hisId: '@natural(2200, 8900)'; // 医院ID
  districtCode: '@word(5)'; // 院区代码
  districtName: '@ctitle'; // 院区名称
  storageList: [
    // 药房列表
    {
      storageCode: '@word(4)'; // 药房编码
      storageName: '@ctitle'; // 药房名称
      storageType: "@natural('1','2')"; // 药房类型（1西药房，2中药房）
      companyId: '@natural(1, 9999)'; // 与处方流转平台交互的药企id
    }
  ];
};

export type HospiDic =
  | 'ADMINISTRATION'
  | 'FREQUENCY'
  | 'FORMULATION'
  | 'CN_ADMINISTRATION'
  | 'CN_FREQUENCY'
  | 'CN_FORMULATION'
  | 'CN_DIALECTICS';

export type GetDrugType = {
  drugType?: string;
  druName?: string;
  inputCode?: string;
  hospitalDistrictCode: string;
  pageNum: number;
  storageCode: string;
};

export type DrugType = {
  hospitalCode: string; //医院编码
  storage: string; //药房编码
  storageCode: string; //药房编码
  inputCode: string; //输入码
  storageName?: string; //药房名称
  itemClass: string; //类别
  drugCode: string; //药品代码
  drugName: string; //药品名称
  firmId: string; //公司名字
  firmName: string; //公司名字
  isAnesthesia?: 1 | null; //是否为筋麻类药品
  units: string; //位,指单个片剂的物理单位，比如片、粒、丸
  packageUnit?: string; //包装单位, 兼容旧处方
  dosage: string | number; //用量，指每次用量
  dosageUnit: string; //用量单位，是每一个片剂的药量单位，比如mg，ml
  drugSpec: string; //片剂规格，指单片药的规格
  packageSpec: string; //包装规格
  packagSpec: string; //包装规格
  administration: string; //途径
  medicationDays?: string; //用药天数
  administrationValue: string; //途径
  chWeightPerDose: number; //每次用量
  chRemark: string; //备注
  frequency: string; //药品服用频次
  frequencyName: string; //药品服用频次
  fydj: string; //费用等级
  stock?: number; //库存
  id?: number; // id
  unitPrice: number; //单价
  insureDrugType: string; //"医保药物类别（01甲类，02乙类）")
  amount: number; //数量
  retailPrice: string; //
  quantity: string; //
  doctorExplain: string; // 医生建议
};

export type PostPrescriptionType = {
  inquiryId: number | string; //问诊ID
  medicalRecordId?: number; //病历ID
  prescriptionType?: string; //处方类别（1西药，2中成药，3中草药）
  hospitalDistrictCode?: string; //医院编码
  storageCode?: string; //药房编码
  chTotalWeight?: string; //中医-药品总重量（g）（全药全付）
  chDose?: string; //中医-中药付数，疗程（单位：次，付）
  chAdministration?: string; //中医-给药途径
  chFrequency?: string; //中医-给药频率
  chFormulation?: string; //中医-剂型
  chDoctorDesc?: string; //中医-医生备注（禁忌等其他说明）
  drugList?: DrugType[];
};

export const administrationList = [
  { value: '1', label: '口服给药' },
  { value: '10', label: '眼部给药' },
  { value: '1001', label: '滴眼（左眼）' },
  { value: '1002', label: '滴眼（右眼）' },
  { value: '1003', label: '滴眼（双眼）' },
  { value: '1004', label: '涂眼睑用' },
  { value: '1005', label: '其他' },
  { value: '1006', label: '泪道冲洗' },
  { value: '101', label: '口服' },
  { value: '102', label: '舌下含服' }
];
export type DiagnosisType = {
  diagnosisCode: 'N20.000';
  diagnosisName: '肾结石';
  diagnosisType: number;
  inputCode: 'SJS';
};

export type RecipeTemplateData = {
  id?: number;
  prescriptionType: 1; //处方类别（1西药，2中成药，3中草药）
  templateName: '@cword(5)'; //药方名称
  cnDiagnosis: '@cword(5)'; //中医诊断
  diagnosis: '@cword(5)'; //西医诊断
  cnDialectics: '@cword(5)'; //中医辨证
  recipeFunction: '@cword(5)'; //功能
  majorFunction: '@cword(5)'; //主治
  useQuantity: '@cword(5)'; //加减运用
  useMethod: '@cword(5)'; //服法
  totalFee: 1; //总金额
  remark: '@cword(5)'; //备注
  chTotalWeight: '@cword(5)'; // 中医-药品总重量（g）（全药全付）
  chDose: '@cword(5)'; // 中医-中药付数，疗程（单位：次，付）
  chAdministration: '@cword(5)'; // 中医-给药途径编码
  chAdministrationName: '@cword(5)'; // 中医-给药途径中文描述
  chFrequency: '@cword(5)'; // 中医-给药频率编码
  chFrequencyName: '@cword(5)'; // 中医-给药频率中文描述
  chFormulation: '@cword(5)'; // 中医-剂型
  chDoctorDesc: '@cword(5)'; // 中医-医生备注（禁忌等其他说明）
  drugList: {
    inputCode: '@cword(5)'; // 拼音输入码
    drugType: '@cword(5)'; // 药品类别（西药01，中成药02，中草药03）
    drugCode: '@cword(5)'; // 药品代码
    drugName: '@cword(5)'; // 药品名称
    drugSpec: '@cword(5)'; // 药品规格
    packageSpec: '@cword(5)'; // 包装规格
    stock: '@cword(5)'; // 库存量
    dosage: '@cword(5)'; // 用量
    dosageUnit: '@cword(5)'; // 用量单位
    administration: '@cword(5)'; // 用药途径
    frequency: '@cword(5)'; // 使用频次
    formulation: '@cword(5)'; // 剂型
    isAnesthesia: '@cword(5)'; // 是否是精麻类药品（1是0否）
    doctorDesc: '@cword(5)'; // 医生备注
    hisItemCode: '@cword(5)'; // 医院收费项目编码
    hisItemName: '@cword(5)'; // 医院收费项目名称
    priceItemCode: '@cword(5)'; // 物价项目编码
    insureItemCode: '@cword(5)'; // 医保收费项目编码
    insureItemName: '@cword(5)'; // 医保收费项目名称
    hospitalDistrictCode: '@cword(5)'; // 院区编码
    storageCode: '@cword(5)'; // 药房编码
    firmName: '@cword(5)'; // 厂家
    unit: '@cword(5)'; // 单位
    packageUnit: '@cword(5)'; // 包装单位
    feeLevel: '@cword(5)'; // 费用等级
    unitPrice: '@integer(60, 100)'; // 单价
    insureDrugType: '@cword(5)'; // 医保药物类别（01甲类，02乙类）
    administrationName: '@cword(5)'; // 用药途径（描述）
    frequencyName: '@cword(5)'; // 使用频次（描述）
    amount: '@integer(60, 100)'; // 数量
    chWeightPerDose: '@integer(60, 100)'; // 中医-单药单付重量（g），单个药品在“单付药方”中的重量
    chWeight: '@integer(60, 100)'; // 中医-单药全付重量（g），单个药品在“整个疗程”中的重量，数值同amount
  }[]; // 药品列表
};
