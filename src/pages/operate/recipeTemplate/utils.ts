type DiagnosisObj = {
  name: string;
  code: string;
};

export const formatDiagnosis = (diagnosis?: DiagnosisObj[]): string => {
  if (!diagnosis || !Array.isArray(diagnosis)) {
    return '';
  }
  return diagnosis.map(item => `无|${item.name}|无|${item.code}`).join(';');
};

export const parseDiagnosis = (diagnosis: string): DiagnosisObj[] => {
  if (!diagnosis) {
    return [];
  }
  return diagnosis.split(';').map(item => {
    const [, name, , code] = item.split('|');
    return {
      name,
      code
    };
  });
};
