/** 中医处方组件 */
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Form,
  Input,
  Table,
  Button,
  Space,
  Spin,
  Modal,
  InputNumber,
  message
} from 'antd';
import useApi, { DrugType } from '../api';
import { ArrSelect, SelectFormItem } from 'parsec-admin';
import SearchMedicine from './searchMedicine';
import styled from 'styled-components';
import storage from '@src/utils/storage';
import React from 'react';
import SearchSelect from './searchSelect';
import { formatDiagnosis, parseDiagnosis } from '../utils';

export type SetDrugStorageType = { districtCode: string; storageCode: string };

export type storageType = {
  storageCode: string; // 药房编码
  storageName: string; // 药房名称
  storageType: string; // 药房类型（1西药房，2中药房）
  companyId: string; // 与处方流转平台交互的药企id
  parentCode: string;
  parentName: string;
};

export type districtType = {
  hisId: string; // 医院ID
  districtCode: string; // 院区代码
  districtName: string; // 院区名称
};

export default ({
  id,
  onSuccess,
  onCancel
}: {
  /** 常用方id */
  id?: number;
  onCancel?: () => void;
  onSuccess?: (data: any) => any;
}) => {
  const [moreVisible, setMoreVisible] = useState(false);
  const [drugStore, setDrugStore] = useState<SetDrugStorageType>({
    districtCode: '',
    storageCode: ''
  });

  const [drugList, setDrugList] = useState<DrugType[]>([]);
  const [form] = Form.useForm();
  const {
    data: templateDetailTailData,
    loading: templateDetailLoading
  } = useApi.单个常用方详情({
    needInit: !!id,
    params: id
  });

  const {
    data: { data: drugStores }
  } = useApi.获取药房列表({
    params: {
      storageType: 2
    },
    initValue: {
      data: []
    }
  });

  const categoryDrugStores: [districtType[], storageType[]] = useMemo(
    () =>
      (drugStores &&
        drugStores.length > 0 &&
        (drugStores || [])?.reduce(
          (store, item) => {
            store = [
              [
                ...(store?.[0] || []),
                {
                  hisId: item?.hisId, // 医院ID
                  districtCode: item?.districtCode, // 院区代码
                  districtName: item?.districtName // 院区名称
                }
              ],
              [
                ...(store?.[1] || []),
                ...((item?.storageList || [])?.map(innerItem => ({
                  ...innerItem,
                  parentName: item?.districtName,
                  parentCode: item?.districtCode
                })) || [])
              ]
            ];
            return store;
          },
          [[], []] as [districtType[], storageType[]]
        )) ||
      ([[], []] as [districtType[], storageType[]]),
    [drugStores]
  );

  /** 单剂量的总价 */
  const singleTotalPrice = useMemo(() => {
    return drugList.reduce((prev, item) => {
      return prev + item.unitPrice * (item.amount || 0);
    }, 0);
  }, [drugList]);

  /** 单剂量的总重量 */
  const totalWeight = useMemo(() => {
    return drugList.reduce((prev, item) => {
      return prev + (item.chWeightPerDose || 0);
    }, 0);
  }, [drugList]);

  const { data: wayData } = useApi.查询格式化的字典列表({
    params: { groupCode: 'cnAdministration' },
    needInit: true,
    initValue: []
  });

  const { data: frequencyData } = useApi.查询格式化的字典列表({
    params: { groupCode: 'cnFrequency' },
    needInit: true,
    initValue: []
  });

  const {
    data: { data: chRemarkArr }
  } = useApi.中药饮片备注常量({
    needInit: true,
    initValue: {
      data: []
    }
  });

  const { request: incompatibilityCheckRequest } = useApi.配伍禁忌检查({
    needInit: false
  });

  const checkIncompatibility = useCallback(
    (
      callback: () => void,
      data?: {
        anti: Array<{ name: string; boycottList: Array<string> }>;
        fear: Array<{ name: string; boycottList: Array<string> }>;
      }
    ) => {
      const doctor = storage.get('userInfo')
        ? storage.get('userInfo')
        : undefined;

      const InnerSpan = props => (
        <span style={{ color: 'red' }}>{props.children}</span>
      );

      const contentWrapper = (
        arr?: Array<{ name: string; boycottList: Array<string> }>
      ) => {
        return (
          (arr &&
            arr?.length > 0 &&
            arr?.map(({ name, boycottList }) => {
              return (
                <div>
                  <InnerSpan>{name}</InnerSpan>
                  <React.Fragment>
                    {boycottList?.map(item => (
                      <InnerSpan>
                        {'、'}
                        {item}
                      </InnerSpan>
                    ))}
                  </React.Fragment>
                </div>
              );
            })) || <div>{'暂无'}</div>
        );
      };

      if (data?.anti.length === 0 && data?.fear.length === 0) {
        callback();
      } else {
        Modal.confirm({
          title: '温馨提示',
          onOk: () => {
            callback();
          },
          onCancel: () => {},
          okText: '确认',
          cancelText: '取消',
          content: (
            <div>
              <div>当前处方种含有配伍禁忌</div>
              {data?.anti && data?.anti?.length > 0 && (
                <div>
                  <div>”十八反“配伍禁忌</div>
                  <div>{contentWrapper(data?.anti)}</div>
                </div>
              )}
              {data?.fear && data?.fear?.length > 0 && (
                <div>
                  <div>”十九畏“配伍禁忌</div>
                  <div>{contentWrapper(data?.fear)}</div>
                </div>
              )}
              <div>需要医生签名确认,请问是否继续开方?</div>
              <div style={{ fontWeight: 600 }}>
                医生确认签名：{(doctor as any)?.name}
              </div>
            </div>
          ) as any
        });
      }
    },
    []
  );

  const columns = [
    {
      title: '药品名称',
      dataIndex: 'drugName',
      width: 200
    },
    {
      title: '数量/单位',
      dataIndex: 'amount',
      width: 200,
      render: (v: number, record: DrugType) => {
        return (
          <InputNumber
            value={v}
            onChange={val => {
              if (val) {
                record.amount = val || 0;
                const packageSpec = Number(
                  record?.packageSpec?.replace('g', '') || 0
                );
                const weight = record.amount * packageSpec;
                record.chWeightPerDose = weight;
                record.dosage = weight;
                setDrugList(prev => [...prev]);
              }
            }}
            addonAfter={record.packageUnit}
          />
        );
      }
    },
    {
      title: '备注',
      dataIndex: 'chRemark',
      width: 200,
      render: (v: number, record: DrugType) => {
        return (
          <ArrSelect
            value={v}
            options={
              (chRemarkArr &&
                chRemarkArr?.length > 0 &&
                chRemarkArr?.map(item => ({
                  value: item,
                  children: item
                }))) ||
              []
            }
            onChange={v => {
              record.chRemark = v as string;
              setDrugList(prev => [...prev]);
            }}
          />
        );
      }
    },
    {
      title: '操作',
      dataIndex: 'drugCode',
      width: 60,
      render: (code: string) => (
        <Button
          type='link'
          danger
          onClick={() => {
            setDrugList(prev => [
              ...prev.filter(item => item.drugCode !== code)
            ]);
          }}>
          删除
        </Button>
      )
    }
  ];

  const handleAddMedicine = () => {
    setMoreVisible(true);
  };

  const handleSubmit = () => {
    form.validateFields().then(values => {
      if (drugList.length === 0) {
        message.error('请选择药品');
        return;
      }
      const { chDose } = values;
      const submitData = {
        id,
        hospitalDistrictCode: drugStore?.districtCode,
        storageCode: drugStore?.storageCode,
        chTotalWeight: totalWeight * chDose,
        prescriptionType: 3,
        ...values,
        diagnosis: formatDiagnosis(values.diagnosis),
        drugList
      };
      console.log('incompatibilityCheckRequest', incompatibilityCheckRequest);
      console.log('checkIncompatibility', checkIncompatibility);
      // incompatibilityCheckRequest(submitData).then(res => {
      //   const { code, data } = res || {};

      //   if (code === 0) {
      //     checkIncompatibility(() => {
      //       if (onSuccess) {
      //         onSuccess?.(submitData);
      //       }
      //     }, data);
      //   }
      // });
      setMoreVisible(false);
      onSuccess?.(submitData);
    });
  };

  useEffect(() => {
    // 如果有id，则是更新处方
    if (id && templateDetailTailData?.data) {
      const drugList = templateDetailTailData?.data?.drugList || [];
      form.setFieldsValue({
        ...(templateDetailTailData.data || {}),
        diagnosis: parseDiagnosis(templateDetailTailData?.data?.diagnosis || [])
      });
      setDrugList(drugList as any);
    } else {
      form.resetFields();
      setDrugList([]);
    }
  }, [form, id, templateDetailTailData.data]);

  useEffect(() => {
    if (drugStores?.length !== 0 && !id) {
      setDrugStore({
        districtCode: drugStores?.[0]?.districtCode || '',
        storageCode: drugStores?.[0]?.storageList?.[0]?.storageCode || ''
      });
    }
  }, [drugStores, id]);

  useEffect(() => {
    if (id && drugStores?.length !== 0) {
      const drugDistrictCode =
        templateDetailTailData?.data?.drugList?.[0]?.hospitalDistrictCode;
      const storageCode =
        templateDetailTailData?.data?.drugList?.[0]?.storageCode;
      if (drugDistrictCode && storageCode) {
        setDrugStore({ districtCode: drugDistrictCode, storageCode });
      }
    }
  }, [drugStores, id, setDrugStore, templateDetailTailData]);

  return (
    <Spin spinning={templateDetailLoading}>
      <Wrap>
        <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
          <div className='shadow'>
            <Form.Item
              name={'recipeFunction'}
              label='功能'
              rules={[{ required: true }]}>
              <Input placeholder='请输入功能' />
            </Form.Item>
            <Form.Item
              name={'majorFunction'}
              label='主治'
              rules={[{ required: true }]}>
              <Input placeholder='请输入主治' />
            </Form.Item>
            <Form.Item
              name={'useQuantity'}
              label='加减运用'
              rules={[{ required: true }]}>
              <Input placeholder='请输入加减运用' />
            </Form.Item>
          </div>
          <div className='shadow'>
            <Form.Item
              name={'templateName'}
              label='处方名称'
              rules={[{ required: true }]}>
              <Input placeholder='请输入处方名称' />
            </Form.Item>
            <Form.Item
              name={'diagnosis'}
              label='西医诊断'
              rules={[{ required: true }]}>
              <SearchSelect
                mode={'multiple'}
                groupCode={'mainDiagnosis'}
                placeholder={'请选择西医诊断'}
              />
            </Form.Item>
            <Form.Item
              name={'cnDiagnosis'}
              label='中医诊断'
              rules={[{ required: true }]}>
              <SearchSelect
                mode={'multiple'}
                groupCode={'cnDiagnosis'}
                placeholder={'请选择中医诊断'}
              />
            </Form.Item>
            <Form.Item
              name={'cnDialectics'}
              label='中医辨证'
              rules={[{ required: true }]}>
              <SearchSelect
                groupCode={'cnDialectics'}
                placeholder={'请选择中医辩证'}
              />
            </Form.Item>
            <Form.Item
              name={'chDose'}
              label='总剂数'
              rules={[{ required: true }]}>
              <InputNumber min={0} addonAfter='剂' placeholder='请输入总剂数' />
            </Form.Item>
            <SelectFormItem
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 16 }}
              label={'使用方式'}
              name={'chAdministration'}
              labelName={'chAdministrationName'}
              rules={[{ required: true }]}
              options={wayData}>
              <ArrSelect options={wayData || []} placeholder='请输入使用方式' />
            </SelectFormItem>
            <SelectFormItem
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 16 }}
              label={'用药频次'}
              name={'chFrequency'}
              labelName={'chFrequencyName'}
              rules={[{ required: true }]}
              options={frequencyData}>
              <ArrSelect
                options={frequencyData || []}
                placeholder='请输入用药频次'
              />
            </SelectFormItem>
            <Form.Item
              name={'chDoctorDesc'}
              label='医生说明'
              rules={[{ required: true }]}>
              <Input.TextArea placeholder='请填写医生说明' />
            </Form.Item>
            <Form.Item
              dependencies={['chDose']}
              labelCol={{ span: 0 }}
              wrapperCol={{ span: 24 }}>
              {({ getFieldValue }) => {
                const amount = getFieldValue('chDose') || 0;
                return (
                  <div className='summay'>
                    <div>
                      金额：¥{(amount * +(singleTotalPrice || 0)).toFixed(2)}
                    </div>
                    <div>
                      总重量： {(amount * (totalWeight || 0)).toFixed(0)}g
                    </div>
                  </div>
                );
              }}
            </Form.Item>
          </div>
          <div className='shadow'>
            <Table pagination={false} dataSource={drugList} columns={columns} />
            <div className='btns'>
              <Space align='end'>
                <Button onClick={handleAddMedicine}>新增药品</Button>
              </Space>
            </div>
          </div>
        </Form>
        <div className='submit-btns'>
          <Space>
            <Button onClick={onCancel}>取消</Button>
            <Button type='primary' onClick={handleSubmit}>
              确认
            </Button>
          </Space>
        </div>
        <Modal
          title='选择药品'
          width={800}
          visible={moreVisible}
          footer={
            <Button
              type='primary'
              onClick={() => {
                setMoreVisible(false);
              }}>
              关闭
            </Button>
          }
          onCancel={() => setMoreVisible(false)}>
          <SearchMedicine
            setDrugStore={setDrugStore}
            categoryDrugStores={categoryDrugStores}
            drugStore={drugStore}
            onSubmit={handleSubmit}
            selectDrugList={drugList}
            setSelectDrugList={setDrugList}
          />
        </Modal>
      </Wrap>
    </Spin>
  );
};

const Wrap = styled.div`
  padding: 0 16px 0px;

  .shadow {
    margin-top: 16px;
    padding: 12px;
    border-radius: 10px;
    box-shadow: 0px 0px 9px rgba(39, 128, 218, 0.24);
  }

  .total {
    color: #333;
    margin-bottom: 20px;
    font-size: 16px;
  }

  .btns {
    padding: 10px;
    display: flex;
  }

  .submit-btns {
    padding-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .summay {
    padding-right: 40px;
    display: flex;
    font-weight: bold;
    justify-content: flex-end;
    align-items: center;

    > div:nth-child(1) {
      margin-right: 20px;
    }
  }
`;
