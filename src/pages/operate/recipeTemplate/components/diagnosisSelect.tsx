import { useMemo, useState } from 'react';
import { Select, Spin, SelectProps } from 'antd';
import useApi from '../api';

interface DiagnosisSelectProps extends SelectProps<any> {
  value?: string;
  diagnosisType?: number;
  onChange?: (v: string) => void;
}

export default ({
  diagnosisType = 1,
  value,
  onChange,
  ...props
}: DiagnosisSelectProps) => {
  const [name, setName] = useState('');

  // 获取诊断列表
  const { data, loading } = useApi.获取诊断({
    params: {
      pageNo: 1,
      inputCode: name,
      pageSize: 10,
      diagnosisType
    }
  });
  const options = useMemo(() => {
    if (loading) {
      return [];
    }
    if (data?.data) {
      return data?.data?.recordList.map(item => ({
        label: item.diagnosisName,
        value: `无|${item.diagnosisName}|无|${item.diagnosisCode}`
      }));
    }
    return [];
  }, [data, loading]);
  return (
    <Select
      {...props}
      value={value ? value.split(';') : []}
      onChange={v => onChange?.(v.join(';'))}
      mode='multiple'
      options={options}
      onSelect={() => setName('')}
      onSearch={setName}
      notFoundContent={loading ? <Spin size='small' /> : null}
    />
  );
};
