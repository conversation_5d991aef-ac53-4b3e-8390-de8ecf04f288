import React, { useCallback, useMemo, useState } from 'react';
import { Table, Button, Modal, Form, message, InputNumber } from 'antd';
import useApi, { DrugType } from '../api';
import DrugSearchBar from './drugSearchBar';
import styled from 'styled-components';
import { ArrSelect } from 'parsec-admin';
import {
  districtType,
  SetDrugStorageType,
  storageType
} from '@pages/operate/recipeTemplate/components/add';

interface SearchMedicineProps {
  selectDrugList: DrugType[];
  setSelectDrugList: any;
  drugStore?: SetDrugStorageType;
  setDrugStore?: (v: SetDrugStorageType) => void;
  onSubmit?: () => void;
  categoryDrugStores: [districtType[], storageType[]];
}

/** 中医药品搜索组件 */
export default ({
  selectDrugList,
  setSelectDrugList,
  drugStore,
  setDrugStore,
  onSubmit,
  categoryDrugStores
}: SearchMedicineProps) => {
  const [drugName, setDrugName] = useState('');

  const [modalVisible, setModalVisible] = useState<boolean>(false);

  const [selectRecord, setSelectRecord] = useState<DrugType>();

  const [form] = Form.useForm();

  const {
    data: {
      data: { recordList: drugList }
    },
    loading: drugsLoading
  } = useApi.药品列表({
    needInit: !!drugStore,
    params: {
      hospitalDistrictCode: drugStore?.districtCode || '',
      storageCode: drugStore?.storageCode || '',
      inputCode: drugName,
      pageNum: 1,
      drugType: '03'
    },
    debounceInterval: 500,
    initValue: {
      data: {
        recordList: []
      }
    }
  });

  const {
    data: { data: chRemarkArr }
  } = useApi.中药饮片备注常量({
    needInit: true,
    initValue: {
      data: []
    }
  });

  const handleAddDrug = useCallback(
    (item: DrugType) => {
      setSelectDrugList((prev: DrugType[]) => [...prev, item]);
    },
    [setSelectDrugList]
  );

  const handleDrugStoreChange = (data: SetDrugStorageType) => {
    if (
      selectDrugList?.length !== 0 &&
      (data.districtCode !== drugStore?.districtCode ||
        data.storageCode !== drugStore?.storageCode)
    ) {
      Modal.confirm({
        title: '提示',
        content:
          '更换药房需要提交当前处方，请确保当前处方已完成开方，如不需要此处方，可提交后删除。',
        onOk() {
          onSubmit?.();
        },
        okText: '确认提交'
      });
    } else {
      setDrugStore?.(data);
    }
  };

  const selectedDrugCodes = useMemo(() => {
    return selectDrugList.map(item => item.drugCode);
  }, [selectDrugList]);

  const columns = useMemo(
    () => [
      {
        title: '药品名称',
        dataIndex: 'drugName',
        width: 200
      },
      {
        title: '规格',
        dataIndex: 'drugSpec',
        width: 100
      },
      {
        title: '库存',
        dataIndex: 'stock',
        width: 100
      },
      {
        title: '操作',
        dataIndex: 'drugCode',
        width: 60,
        render: (code: string, record: DrugType) => {
          const isSelected = selectedDrugCodes.includes(code);
          return (
            <Button
              disabled={isSelected}
              size='small'
              onClick={() => {
                setSelectRecord(record);
                setModalVisible(true);
              }}>
              {isSelected ? '已选' : '选择'}
            </Button>
          );
        }
      }
    ],
    [selectedDrugCodes]
  );

  return (
    <Wrap>
      <DrugSearchBar
        drugName={drugName}
        drugStore={drugStore}
        onDrugStoreChange={handleDrugStoreChange}
        setDrugName={setDrugName}
        categoryDrugStores={categoryDrugStores}
      />
      <div className='content'>
        <Table
          dataSource={drugList || []}
          columns={columns}
          loading={drugsLoading}
          pagination={false}
        />
        <Modal
          visible={modalVisible}
          onOk={() => {
            form.validateFields().then(values => {
              console.log(values);
              if (values.chWeightPerDose <= 0) {
                message.error('不能为负数或0g哦');
                return;
              }
              values.amount = Number(values.amount || 0);
              const packageSpec = Number(
                selectRecord?.packageSpec?.replace('g', '') || 0
              );
              const weight = values.amount * packageSpec;
              values.chWeightPerDose = weight;
              values.dosage = weight;
              handleAddDrug({ ...selectRecord, ...values });
              setModalVisible(false);
              form.resetFields();
            });
          }}
          onCancel={() => {
            setModalVisible(false);
          }}>
          <Form form={form}>
            <Form.Item name={'amount'} required={true} label={'单剂用量'}>
              <InputNumber
                min={0}
                style={{ width: '100%' }}
                placeholder={'请输入单剂用量'}
                addonAfter={selectRecord?.packageUnit}
              />
            </Form.Item>
            <Form.Item name={'chRemark'} required={true} label={'中药饮片备注'}>
              <ArrSelect
                style={{ width: '100%' }}
                defaultValue={'无'}
                options={
                  (chRemarkArr &&
                    chRemarkArr?.length > 0 &&
                    chRemarkArr?.map(item => ({
                      label: item,
                      value: item
                    }))) ||
                  []
                }
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </Wrap>
  );
};

const Wrap = styled.div`
  padding: 16px 10px;

  .content {
    padding: 16px 0;
  }
`;
