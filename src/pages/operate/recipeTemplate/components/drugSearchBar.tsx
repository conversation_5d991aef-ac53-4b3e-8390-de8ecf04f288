import { Input, Select } from 'antd';
import { districtType, SetDrugStorageType, storageType } from './add';

interface SearchMedicineProps {
  drugName: string;
  drugStore?: SetDrugStorageType;
  setDrugName: (value: string) => void;
  categoryDrugStores: [districtType[], storageType[]];
  onDrugStoreChange?: (v: SetDrugStorageType) => void;
}

export default ({
  drugName,
  setDrugName,
  drugStore,
  onDrugStoreChange,
  categoryDrugStores
}: SearchMedicineProps) => {
  return (
    <Input.Group compact>
      <Select
        style={{ width: '25%' }}
        value={drugStore?.districtCode}
        onChange={v =>
          onDrugStoreChange?.({
            districtCode: v,
            storageCode: ''
          })
        }>
        {categoryDrugStores?.[0]?.map(i => (
          <Select.Option value={i.districtCode} key={i.districtCode}>
            {i.districtName}
          </Select.Option>
        ))}
      </Select>
      <Select
        style={{ width: '25%' }}
        value={drugStore?.storageCode}
        onChange={v =>
          onDrugStoreChange?.({
            districtCode: drugStore?.districtCode || '',
            storageCode: v
          })
        }>
        {categoryDrugStores?.[1]
          ?.filter(i => i.parentCode === drugStore?.districtCode)
          ?.map(i => (
            <Select.Option value={i?.storageCode}>
              {i?.storageName}
            </Select.Option>
          ))}
      </Select>
      <Input.Search
        style={{ width: '50%' }}
        value={drugName}
        onChange={e => {
          setDrugName(e.target.value);
        }}
      />
    </Input.Group>
  );
};
