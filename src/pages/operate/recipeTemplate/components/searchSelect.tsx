import { useMemo, useState } from 'react';
import { Select, SelectProps, Spin } from 'antd';
import useApi from '../api';
import {
  formatCnList,
  formatCnObj,
  parseCnList,
  parseCnObj
} from '@src/utils/diagnosisFormat';
export default ({
  groupCode,
  value,
  mode,
  onChange,
  ...props
}: {
  groupCode?: string;
  value?: string | string[];
  onChange?: (v: any) => void;
} & SelectProps<any>) => {
  const [searchValue, setSearchValue] = useState('');

  const isMultiple = useMemo(() => mode === 'multiple', [mode]);

  const { data, loading } = useApi.查询字典列表({
    needInit: true,
    debounceInterval: 200,
    params: {
      groupCode,
      numPerPage: 30,
      dictValue: searchValue
    }
  });
  const options = useMemo(() => {
    if (loading) {
      return [];
    }
    if (data?.data) {
      const list = data?.data?.recordList.map(item => ({
        label: item.dictValue.value,
        value: JSON.stringify({
          name: item.dictValue.value,
          code: item.dictKey
        })
      }));

      if (Array.isArray(value)) {
        const stringValues = value.map(i => JSON.stringify(i));
        const optionValues = list.map(i => i.value);
        const newOptionsValues = [
          ...new Set([...optionValues, ...stringValues])
        ];
        return newOptionsValues
          .map(i => {
            try {
              const optionItem = JSON.parse(i);
              return {
                label: optionItem.name,
                value: i
              };
            } catch {
              return undefined;
            }
          })
          .filter(Boolean);
      }
      return list;
    }
    return [];
  }, [data, loading, value]);

  return (
    <Select
      value={
        (isMultiple ? formatCnList(value) : formatCnObj(value)) || undefined
      }
      mode={mode}
      showSearch
      onChange={v => onChange?.(isMultiple ? parseCnList(v) : parseCnObj(v))}
      options={options as any}
      onSelect={() => setSearchValue('')}
      onSearch={setSearchValue}
      notFoundContent={loading ? <Spin size='small' /> : null}
      {...props}
    />
  );
};
