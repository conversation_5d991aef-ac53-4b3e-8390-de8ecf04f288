import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  LinkButton,
  DayRangePicker,
  ArrSelect,
  ActionsWrap,
  actionConfirm,
  useModal,
  handleSubmit
} from 'parsec-admin';
import useApi from './api';
import './index.less';
import { Button, Form, Modal, Switch, Input, Select, Cascader } from 'antd';
import MyTableList from '@components/myTableList';
import { getTreeOptionsNo } from '@pages/hospital/doctorDetailSmart/utils';
import saveAs from 'file-saver';
import moment from 'moment';
import { BackgroundImg, TransferChange } from '@kqinfo/ui';
import html2canvas from 'html2canvas';
import Qrcode from '@components/qrcode';
const { TextArea } = Input;

export default () => {
  const {
    data: { data: depList }
  } = useApi.科室管理列表({
    initValue: [{ data: { recordList: [] } }]
  });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const checkStatusData = [
    { label: '审核通过', values: 1 },
    { label: '审核不通过', values: 2 }
  ];
  const [canvasImg, setCanvasImg] = useState('');
  const [commendationVisible, setCommendationVisible] = useState(false);
  const { request: handleExport, loading: exportLoading } = useApi.exportTypes({
    needInit: false
  });
  // const [deptNo, setDeptNo] = useState('');
  const [checkStatus, setCheckStatus] = useState('');
  const [deptListData, setDeptListData] = useState([]);
  const [recordData, setRecordData] = useState({} as any);
  const [queryParams, setQueryParams] = useState({} as any);
  //脱敏状态
  const [nameFullTransFlag1, setNameFullTransFlag] = useState(false);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  // const typeList = [
  //   { id: 1, name: '本院区' },
  //   { id: 2, name: '新院区' }
  // ];
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const reviewStatus = [
    { id: 0, name: '待审核' },
    { id: 1, name: '审核成功' },
    { id: 2, name: '审核失败' }
  ];
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const publicityStatus = [
    { id: 1, name: '已公示' },
    { id: 2, name: '未公示' }
  ];
  const [desensitizationVisible, setDesensitizationVisible] = useState(false);
  const [form] = Form.useForm();
  //审核
  const auditingModal = useModal(record => {
    return {
      onSubmit: async values => {
        await handleSubmit(() =>
          useApi.表扬信修改.request({
            ...record,
            ...values,
            checkStatus: values.checkStatus === '0' ? 1 : 2
          })
        );
      },
      title: '审核',
      items: [
        {
          name: 'checkStatus',
          label: '审核状态',
          required: true,
          render: (
            <ArrSelect
              options={checkStatusData}
              onChange={value => {
                console.log('value', value);
                setCheckStatus(value + '');
              }}
            />
          )
        },
        {
          name: 'remark',
          label: '审核备注',
          required: checkStatus === '1' ? true : false,
          render: (
            <TextArea
              rows={6}
              autoSize={true}
              maxLength={200}
              placeholder='请输入审核备注'
            />
          )
        }
      ]
    };
  });
  const wrapperRef = useRef(null);
  const handleConvertToImage = async () => {
    try {
      const node = wrapperRef.current;
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const canvas = await html2canvas(node!);
      const img = canvas.toDataURL('image/png');
      console.log('img', img);
      setCanvasImg(img);
      // 可以将img变量设置为图片标签的src属性来显示图片
    } catch (error) {
      console.error('Error generating image:', error);
    }
  };
  const base64ToBlob = code => {
    const raw = window.atob(code.substring(code.indexOf(',') + 1));
    const rawLength = raw.length;
    const uInt8Array = new Uint8Array(rawLength);
    for (let i = 0; i < rawLength; i++) {
      uInt8Array[i] = raw.charCodeAt(i);
    }
    return new Blob([uInt8Array]);
  };
  const getDoctorList = useCallback(params => {
    const nameFullTransFlag =
      sessionStorage.getItem('nameFullTransFlag') === 'true' ? 1 : 0;
    return useApi.表扬信列表.request({
      ...params,
      nameFullTransFlag,
      hospitalId: params.hospitalName,
      deptCode: params.deptName
    });
  }, []);
  return (
    <div>
      <Modal
        width={500}
        title='脱敏设置'
        visible={desensitizationVisible}
        destroyOnClose={true}
        maskClosable={false}
        onCancel={() => {
          setDesensitizationVisible(false);
        }}
        onOk={() => {
          form.validateFields().then(values => {
            const nameFullTransFlag = sessionStorage.getItem(
              'nameFullTransFlag'
            );
            if (values.desensitization === nameFullTransFlag) {
              return;
            }
            //保存脱敏状态
            setNameFullTransFlag(values.desensitization);
            sessionStorage.setItem('nameFullTransFlag', values.desensitization);
            handleSubmit(() => getDoctorList(queryParams));
          });
          setDesensitizationVisible(false);
        }}>
        <Form form={form}>
          <Form.Item name='desensitization' label='患者姓名脱敏'>
            <Switch
              checkedChildren='ON'
              unCheckedChildren='OFF'
              checked={nameFullTransFlag1}
              onClick={value => {
                setNameFullTransFlag(value);
                sessionStorage.setItem('nameFullTransFlag', value + '');
              }}
            />
          </Form.Item>
        </Form>
        <div>
          提示：
          <br />
          1.开启脱敏设置后，表扬信中的患者姓名进行脱敏展示，如“张*盛”，关闭后显示患者姓名全称；
          <br />
          2.开启脱敏后，全部表扬信将全进行脱敏展示；
        </div>
      </Modal>
      <MyTableList
        tableTitle='表扬信管理'
        getList={({ params }) => {
          setQueryParams({
            ...params
          });
          return getDoctorList(params);
        }}
        scroll={{ x: 1600 }}
        action={
          <ActionsWrap>
            <Button
              type={'default'}
              onClick={() => {
                setDesensitizationVisible(true);
                const nameFullTransFlag =
                  sessionStorage.getItem('nameFullTransFlag') || '0';
                setNameFullTransFlag(
                  nameFullTransFlag === 'true' ? true : false
                );
                form.setFieldValue(
                  'desensitization',
                  nameFullTransFlag === '1' ? true : false
                );
              }}>
              脱敏设置
            </Button>
            <Button
              type={'default'}
              loading={exportLoading}
              onClick={() => {
                handleExport({ ...queryParams, isExport: 1 }).then(data =>
                  saveAs(
                    data,
                    `表扬信数据统计 ${moment(queryParams.startDate).format(
                      'YYYY/MM/DD'
                    )} - ${moment(queryParams.endDate).format(
                      'YYYY/MM/DD'
                    )}.xls`
                  )
                );
              }}>
              导出
            </Button>
          </ActionsWrap>
        }
        columns={useMemo(
          () => [
            {
              title: '院区',
              width: 200,
              dataIndex: 'hospitalName',
              search: (
                <Select
                  style={{ width: '200px' }}
                  allowClear
                  options={depList?.recordList?.map(type => {
                    return {
                      value: type.id,
                      label: type.name
                    };
                  })}
                  onChange={item => {
                    console.log('values', item);
                    depList?.recordList.find(nav => {
                      if (nav.id === item) {
                        console.log('item', nav, item);
                        setDeptListData(nav?.children as any);
                      }
                    });
                  }}
                  placeholder={'请选择平台'}
                />
              )
            },
            {
              title: '科室',
              width: 150,
              dataIndex: 'deptName',
              search: (
                <TransferChange
                  mode={'cascade'}
                  data={getTreeOptionsNo(deptListData || []) as any}>
                  <Cascader
                    expandTrigger='hover'
                    placeholder='请选择科室'
                    options={getTreeOptionsNo(deptListData || []) as any}
                  />
                </TransferChange>
              )
            },
            {
              title: '医生',
              dataIndex: 'doctorName',
              width: 100,
              search: true
            },
            {
              title: '医生/护士/其他',
              width: 130,
              dataIndex: 'other'
            },
            {
              title: '表扬内容',
              dataIndex: 'commendContent',
              width: 100
            },
            {
              title: '患者姓名',
              width: 100,
              dataIndex: 'patientName'
            },
            {
              title: '手机号码',
              dataIndex: 'phone',
              width: 170,
              search: true
            },
            {
              title: '提交时间',
              dataIndex: 'createTime',
              width: 170,
              search: <DayRangePicker placeholder={['开始时间', '结束时间']} />,
              searchIndex: ['startDate', 'endDate']
            },
            {
              title: '审核状态',
              dataIndex: 'checkStatus',
              width: 100,
              search: (
                <ArrSelect
                  options={reviewStatus.map(type => {
                    return {
                      value: type.id,
                      children: type.name
                    };
                  })}
                />
              ),
              render: val =>
                val === 0 ? '待审核' : val === 1 ? '审核成功' : '审核失败'
            },
            {
              title: '审核备注',
              dataIndex: 'remark',
              width: 170
            },
            {
              title: '公示状态',
              dataIndex: 'publicStatus',
              width: 120,
              search: (
                <ArrSelect
                  options={publicityStatus.map(type => {
                    return {
                      value: type.id,
                      children: type.name
                    };
                  })}
                />
              ),
              render: val => (val === 1 ? '已公示' : '未公示')
            },
            {
              title: '操作',
              fixed: 'right',
              width: 200,
              render: record => (
                <ActionsWrap>
                  <LinkButton
                    onClick={() => {
                      const checkValue = checkStatusData.find(item => {
                        return item.values === record.checkStatus;
                      });
                      auditingModal({
                        ...record,
                        checkStatus: checkValue
                      });
                    }}>
                    {record.checkStatus === 0 ? '审核' : ''}
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () => {
                          return useApi.表扬信修改.request({
                            ...record,
                            publicStatus: record.publicStatus === 1 ? 2 : 1
                          });
                        },
                        record.publicStatus === 1 ? '取消公示' : '公示'
                      );
                    }}>
                    {record.publicStatus === 1 ? '取消公示' : '公示'}
                  </LinkButton>
                  {record.checkStatus === 1 && (
                    <LinkButton
                      onClick={() => {
                        setRecordData(record);
                        setCommendationVisible(true);
                        setTimeout(() => {
                          handleConvertToImage();
                        }, 500);
                      }}>
                      表扬信
                    </LinkButton>
                  )}

                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          useApi.删除表扬信.request({
                            id: record.id
                          }),
                        '删除'
                      );
                    }}>
                    删除
                  </LinkButton>
                </ActionsWrap>
              )
            }
          ],
          [
            auditingModal,
            checkStatusData,
            depList?.recordList,
            deptListData,
            publicityStatus,
            reviewStatus
          ]
        )}
      />
      <Modal
        width={400}
        title='表扬信'
        visible={commendationVisible}
        destroyOnClose={true}
        maskClosable={false}
        okText='下载'
        onCancel={() => {
          setCommendationVisible(false);
        }}
        onOk={() => {
          const data = base64ToBlob(canvasImg);
          const addElement = document.createElement('a');
          addElement.download = '表扬信.png';
          addElement.style.display = 'none';
          addElement.href = URL.createObjectURL(data);
          document.body.appendChild(addElement);
          addElement.click();
          URL.revokeObjectURL(addElement.href);
          document.body.removeChild(addElement);
        }}>
        <img src={canvasImg} />
        <div className='canvasCont' ref={wrapperRef}>
          <img
            src={require('../../../images/commend1.png')}
            alt=''
            className='canvasImgtop'
          />
          <div className='hosCont'>
            {recordData?.hospitalName}
            <br />
            科室：{recordData?.deptName}
          </div>
          <div className='jkk'>
            <div className='jkkCont'>
              您科室的 {recordData?.doctorName} （医生/护士/老师）
              {recordData?.commendContent}
              <div className='jkkContName'>
                您的患者：{recordData?.patientName}
              </div>
              <div className='jkkContName'>{recordData?.createTime}</div>
            </div>
          </div>
          <BackgroundImg
            img={require('../../../images/commend3.png')}
            className='jkkEwm'
            imgProps={{ className: 'pannel' }}>
            <div>
              {/* <img src={require('../../../images/commend3.png')} alt='' /> */}
              <div className='ewm'>
                <Qrcode
                  url={`https://ihs.cqkqinfo.com/patients/p40002-his/#/pages/home/<USER>
                  size={100}
                />
              </div>
              <div className='sbEwm'>
                识别二维码，也可加入
                <br />
                表扬沙区人民医院医务人员的队伍
              </div>
            </div>
          </BackgroundImg>
        </div>
      </Modal>
    </div>
  );
};
