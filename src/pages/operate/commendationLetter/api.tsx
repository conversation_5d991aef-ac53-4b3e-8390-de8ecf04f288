import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { DeptItem } from '../../hospital/departmentSmart/api';
import {
  ListApiRequestParams,
  ListApiResponseData,
  ApiResponse
} from '@src/configs/apis';

export default {
  表扬信列表: createApiHooks(
    (data: ListApiRequestParams & { nameFullTransFlag: number }) =>
      request.get<
        ListApiResponseData<{
          id: number;
          hisId: string;
          hospitalId: string;
          hospitalName: string;
          deptCode: string;
          deptName: string;
          doctorCode: string;
          doctorName: string;
          other: string;
          commendContent: string;
          patientName: string;
          phone: string;
          createTime: string;
          checkStatus: string;
          remark: string;
          publicStatus: string;
          updateTime: string;
        }>
      >('/intelligent/mch/intelligent/commend/letter', {
        params: data
      })
  ),
  表扬信列表分页: (data: ListApiRequestParams) =>
    request.get<
      ListApiResponseData<{
        id: number;
        hisId: string;
        hospitalId: string;
        hospitalName: string;
        deptCode: string;
        deptName: string;
        doctorCode: string;
        doctorName: string;
        other: string;
        commendContent: string;
        patientName: string;
        phone: string;
        createTime: string;
        checkStatus: string;
        remark: string;
        publicStatus: string;
        updateTime: string;
      }>
    >('/intelligent/mch/intelligent/commend/letter', {
      params: data
    }),
  表扬信修改: createApiHooks(
    (params: {
      hisId: string; // 医院ID
      checkStatus: number; //审核状态 0 待审核 1 审核通过 2 审核失败
      publicStatus: number; //公示状态 1 已公示 2 未公示
      id: string; // 症状ID
    }) =>
      request.put<ApiResponse<any>>(
        '/intelligent/mch/intelligent/commend/letter',
        params,
        {
          headers: { 'Content-Type': 'application/json' }
        }
      )
  ),
  exportTypes: createApiHooks(
    (params: {
      hisId?: string;
      deptName?: string;
      checkStatus?: string;
      publicStatus?: string;
      doctorName?: string;
      phone?: string;
      hospitalName?: string;
      startDate?: string;
      endDate?: string;
    }) =>
      request.get<Blob>('/intelligent/mch/intelligent/commend/letter/export', {
        responseType: 'blob',
        params
      })
  ),
  删除表扬信: createApiHooks((params: { id: string }) =>
    request.delete(`/intelligent/mch/intelligent/commend/letter/${params.id}`)
  ),
  科室管理列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        hisType?: string | number; // 所属业务平台 1互联网医院、2智慧医院、3互/智
      }
    ) =>
      request
        .get<ApiResponse<DeptItem[]>>('/mch/his/deptMain', { params: data })
        .then(res => {
          return {
            ...res,
            data: {
              code: res.data.code,
              msg: res.data.msg,
              data: {
                currentPage: 1,
                totalCount: 1,
                recordList: res.data.data || []
              }
            }
          };
        })
  )
};
