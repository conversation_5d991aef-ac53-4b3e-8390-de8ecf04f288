.hosCont {
  background-color: #fff;
  font-weight: bold;
  padding: 25px 20px;
  line-height: 26px;
  width: 325px;
  margin: auto;
}
.jkk {
  background-color: #ebe2c8;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 100%;
}
.jkkCont {
  background-color: #fff;
  font-size: 14px;
  width: 325px;
  margin: auto;
  text-indent: 2em;
  padding: 0 20px 10px;
  line-height: 26px;
}
.jkkContName {
  font-size: 12px;
  text-align: right;
  line-height: 20px;
}
.jkkContName:first-child {
  margin-top: 10px;
}
.jkkEwm {
  background-color: #fff;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}
.pannel {
  width: 100%;
  height: 244px;
  display: block;
}
.ewm {
  width: 100px;
  height: 100px;
  display: block;
  margin: 66px auto 0;
}
.sbEwm {
  text-align: center;
  font-size: 12px;
  line-height: 20px;
  margin-top: 8px;
}
.footTip {
  font-size: 24px;
  color: #9e9e9e;
  text-align: center;
  margin: 70px 0;
}
.item {
  padding: 29px 0px 19px;
  font-size: 30px;
  align-items: center;
  justify-content: space-between;
  &:last-child {
    border-bottom: none;
  }
  position: relative;
  margin-bottom: 20px;
  box-shadow: 2px 2px 20px rgba(39, 128, 217, 0.15);
  border-radius: 20px;
  margin-top: 20px;
  background: linear-gradient(to bottom, #e5f1ff 0%, #ffffff 100%);
}
.itemTop {
  padding: 0 40px 30px;
}
.left {
  width: 100px;
  height: 100px;
  float: left;
}
.right {
  padding-left: 120px;
}
// .arrow {
//   width: 100px;
//   height: 120px;
// }
.name {
  color: #333;
}
.praise {
  color: #666666;
  margin-top: 18px;
  font-size: 24px;
}
.praiseBtn {
  width: 200px;
  font-size: 24px;
  height: 68px;
  min-height: initial;
  border-radius: 10px;
  margin-top: 20px;
}
.position {
  color: #009db0;
}
.time {
  float: right;
}

.state {
  position: absolute;
  top: 0;
  right: 0;
  width: 130px;
  height: 50px;
  line-height: 50px;
  background-color: #ffc34c;
  text-align: center;
  font-size: 24px;
  color: #fff;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 20px;
}
.canvasCont {
  position: fixed;
  width: 350px;
  right: -10000px;
}
.canvasImgtop {
  display: block;
  margin: auto;
}
.canvasImg {
  width: 100%;
  height: 1100px;
}
.notPassed {
  background-color: #ff4a4a;
  width: 400px;
  padding: 18px 20px;
  color: #fff;
  font-size: 24px;
  margin: 250px auto 40px;
  text-align: center;
}
.delete {
  background-color: #fff;
  border: 1px solid #009db0 !important;
  color: #009db0 !important;
  width: 650px;
  margin: auto;
}
