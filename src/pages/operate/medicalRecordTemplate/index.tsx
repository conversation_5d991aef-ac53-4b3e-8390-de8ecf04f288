import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  DayRangePicker,
  LinkButton
} from 'parsec-admin';
import { Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import MyTableList from '../../../components/myTableList';
import { useHistory } from 'react-router';
import apis from '../api';
import permission from '@utils/permisstion';
import Organization from '@components/organization';
import React, { useState } from 'react';
import env from '@configs/env';
export const MedicalTemplateType = {
  '1': '西医病历',
  '2': '中医病历'
};
export default ({ isLocal = false }: { isLocal?: boolean }) => {
  const { push } = useHistory();
  const [targetHisId, setTargetHisId] = useState<any>(env.hisId);
  return (
    <MyTableList
      tableTitle={'病历模板列表'}
      showExpand={false}
      showHeaderExtra={isLocal}
      params={{ targetHisId }}
      action={
        isLocal ? (
          <Organization
            value={targetHisId}
            headerExtraText={'病历模板'}
            onChange={setTargetHisId}
          />
        ) : (
          permission.addMedicalRecordTemplate && (
            <Button
              icon={<PlusOutlined />}
              onClick={() => push('/operate/medicalRecordTemplate/add')}>
              添加
            </Button>
          )
        )
      }
      getList={({ pagination, params }) => {
        delete params.sort;
        return isLocal
          ? apis.本地电子病历模版列表.request(params as any)
          : apis.电子病历模版列表.request({
              ...(params as any)
            });
      }}
      showTool={false}
      columns={
        [
          { title: '标题', searchIndex: 'name', search: true, hidden: true },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            search: (
              <DayRangePicker
                valueFormat={'YYYY-MM-DD HH:mm:ss'}
                placeholder={['开始时间', '结束时间']}
              />
            ),
            searchIndex: ['createStartTime', 'createEndTime']
          },
          { title: '模板病历名称', dataIndex: 'name' },
          {
            title: '类型',
            dataIndex: 'medicalTemplateType',
            search: <ArrSelect options={MedicalTemplateType} />,
            render: v => (v ? MedicalTemplateType[v] : '-')
          },
          {
            title: '所属科室',
            dataIndex: 'deptList',
            render: v => {
              if (v?.length) {
                return v.map(v => v.name).join();
              }
              return '-';
            }
          },
          {
            title: '平台来源',
            dataIndex: 'institutionName',
            width: 150,
            hidden: !isLocal
          },
          { title: '内容详情', dataIndex: 'content' },
          { title: '使用量', dataIndex: 'useCount' },
          {
            title: '操作',
            fixed: 'right',
            width: 180,
            render: v =>
              isLocal ? (
                <LinkButton
                  onClick={() =>
                    push(
                      `/localAdmin/medicalRecordTemplate/detail/${v.id}/true/${targetHisId}`
                    )
                  }>
                  查看
                </LinkButton>
              ) : (
                <ActionsWrap max={3}>
                  {permission.updateMedicalRecordTemplate && (
                    <LinkButton
                      onClick={() =>
                        push(`/operate/medicalRecordTemplate/update/${v.id}`)
                      }>
                      编辑
                    </LinkButton>
                  )}
                  {permission.delMedicalRecordTemplate && (
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () => apis.病历模版删除.request(v.id),
                          '删除'
                        );
                      }}>
                      删除
                    </LinkButton>
                  )}
                  <LinkButton
                    onClick={() =>
                      push(`/operate/medicalRecordTemplate/detail/${v.id}/true`)
                    }>
                    详情
                  </LinkButton>
                </ActionsWrap>
              )
          }
        ].filter(item => !item?.hidden) as any[]
      }
    />
  );
};
