import { Pagination, Select, Spin } from 'antd';
import React, { useMemo, useState } from 'react';
import { SelectProps } from 'antd';
import useApi from '@pages/bedReservation/api';
import { Options } from '@pages/operate/medicalRecordTemplate/edit/index';
const { Option } = Select;
interface PageOptionProps {
  pageNum: number;
  numPerPage: number;
  total: number;
}
export default (
  props: SelectProps & {
    groupCode: string;
    isLocal?: boolean;
    targetHisId?: string;
  }
) => {
  const { groupCode, isLocal = false, targetHisId } = props;
  const [pageInfo, setPageInfo] = useState<PageOptionProps>({
    pageNum: 1,
    numPerPage: 10,
    total: 0
  });
  const [searchValue, setSearchValue] = useState('');
  const {
    data: { data: hisCodeData },
    loading
  } = useApi.查询字典列表({
    params: {
      pageNum: pageInfo.pageNum,
      numPerPage: pageInfo.numPerPage,
      groupCode: groupCode,
      dictValue: searchValue
    },
    needInit: !!groupCode && !isLocal,
    debounceInterval: 500
  });
  const {
    data: { data: codeLocalData },
    loading: localLoading
  } = useApi.本地查询字典列表({
    params: {
      pageNum: pageInfo.pageNum,
      numPerPage: pageInfo.numPerPage,
      groupCode: groupCode,
      dictValue: searchValue,
      targetHisId
    },
    needInit: !!groupCode && isLocal && !!targetHisId,
    debounceInterval: 500
  });
  const codeData = useMemo(() => (isLocal ? codeLocalData : hisCodeData), [
    codeLocalData,
    hisCodeData,
    isLocal
  ]);
  const code: Options[] = useMemo(() => {
    if (codeData?.recordList?.length) {
      setPageInfo(prevState => {
        return {
          ...prevState,
          total: codeData?.totalCount as number
        };
      });
      const arr: Options[] = [];
      codeData?.recordList.forEach(item => {
        arr.push({ label: item.dictValue.value, value: item.dictKey });
      });
      return arr;
    }
    return [];
  }, [codeData]);
  return (
    <Select
      {...props}
      mode='multiple'
      filterOption={false}
      showSearch
      searchValue={searchValue}
      onSearch={setSearchValue}
      style={{ width: '100%' }}
      options={code}
      dropdownRender={menu => (
        <Spin size='small' spinning={loading || localLoading}>
          {menu}
          <div style={{ margin: 8 }}>
            <Pagination
              total={pageInfo.total}
              pageSize={pageInfo.numPerPage}
              current={pageInfo.pageNum}
              onChange={(page, pageSize) =>
                setPageInfo({
                  pageNum: page,
                  numPerPage: pageSize,
                  total: pageInfo.total
                })
              }
            />
          </div>
        </Spin>
      )}>
      {code.map(v => {
        return (
          <Option key={v.value} value={v.value} label={v.label}>
            {v.label}
          </Option>
        );
      })}
    </Select>
  );
};
