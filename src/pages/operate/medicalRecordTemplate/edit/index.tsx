import {
  ArrSelect,
  FormDescriptions,
  handleSubmit,
  UploadImg
} from 'parsec-admin';
import styled from 'styled-components';
import { Button, Form, Input, InputNumber, Select } from 'antd';
import { useParams } from 'react-router-dom';
import { useEffect, useMemo, useState } from 'react';
import { MedicalTemplateType } from '@pages/operate/medicalRecordTemplate';
import TemplateApi from '@pages/organization/config/api';
import api from '../../api';
import env from '@configs/env';
import { ArrRadio } from 'parsec-admin/lib/components';
import { useHistory } from 'react-router';
import CodeSelect from '@pages/operate/medicalRecordTemplate/edit/codeSelect';
export type Options = {
  label: string;
  value: number | string;
};
const cn = ['cnDiagnosis', 'cnDiagnosis', 'cnTreatName', 'cnDialectics'];
export default ({ isLocal = false }: { isLocal?: boolean }) => {
  const [form] = Form.useForm();
  const { id, isDetail, targetHisId } = useParams<{
    id?: string;
    isDetail?: string;
    targetHisId?: string;
  }>();
  const isShow = useMemo(() => !(!!id && !!isDetail), [id, isDetail]);
  const [type, setType] = useState<number>(1);
  const { goBack } = useHistory();
  const {
    data: { data: medicalRecordContent },
    loading
  } = TemplateApi.电子病历模板详情({
    params: {
      sHisId: Number(env.hisId),
      type
    },
    needInit: !isLocal
  });
  const {
    data: { data: localMedicalRecordContent },
    loading: localLoading
  } = TemplateApi.本地电子病历模板详情({
    params: {
      sHisId: Number(env.hisId),
      type,
      targetHisId
    },
    needInit: isLocal && !!targetHisId
  });
  const data = useMemo(
    () => (isLocal ? localMedicalRecordContent : medicalRecordContent),
    [isLocal, localMedicalRecordContent, medicalRecordContent]
  );
  const {
    data: { data: HisDeptData }
  } = api.科室列表({
    params: {
      hisId: env.hisId,
      hisType: 1,
      lastStage: true,
      status: 1
    },
    needInit: !isLocal
  });
  const {
    data: { data: deptLocalData }
  } = api.本地科室列表({
    params: {
      hisId: env.hisId,
      hisType: 1,
      lastStage: true,
      status: 1,
      targetHisId
    },
    needInit: isLocal && !!targetHisId
  });
  const deptData = useMemo(() => (isLocal ? deptLocalData : HisDeptData), [
    HisDeptData,
    deptLocalData,
    isLocal
  ]);
  const deptLists = useMemo(() => {
    const arr: Options[] = [{ label: '全部科室', value: 0 }];
    if (deptData?.length) {
      deptData.forEach(v => {
        arr.push({ label: v.name, value: v.id });
      });
    }
    return arr;
  }, [deptData]);
  function diagnosisArr(value: string) {
    if (!value) {
      return [];
    }
    const list = value.split(';');
    const diagnosis = list
      .map(item => {
        return item.split('|', 4).map(item => {
          if (item === '无') {
            return '';
          } else {
            return item;
          }
        });
      })
      .filter(item => item[1] && item[3]);
    return diagnosis.map(item => {
      return {
        label: item[1],
        value: item[3]
      };
    });
  }
  const {
    loading: detailLoading,
    data: { data: detailHisData }
  } = api.病历模版详情({
    needInit: !!id && !isLocal,
    params: id,
    initValue: { data: {} }
  });
  const {
    loading: detailLocalLoading,
    data: { data: detailLocalData }
  } = api.本地病历模版详情({
    needInit: !!id && isLocal && !!targetHisId,
    params: { hisId: env.hisId, targetHisId, id: id || '' },
    initValue: { data: {} }
  });
  const detailData = useMemo(
    () => (isLocal ? detailLocalData : detailHisData),
    [detailHisData, detailLocalData, isLocal]
  );
  const { loading: updateLoading, request: update } = api.病历模版编辑({
    needInit: false
  });
  const { loading: addLoading, request: add } = api.病历模版新增({
    needInit: false
  });
  useEffect(() => {
    const dataKey = Object.keys(detailData);
    if (dataKey?.length) {
      dataKey?.forEach(v => {
        if (cn.includes(v)) {
          if (detailData[v]?.length) {
            detailData[v] = detailData[v].map((l: any) => {
              return { label: l?.name, value: l?.code };
            });
          }
        }
      });
      let mainDiagnosis = detailData?.mainDiagnosis;
      if (mainDiagnosis) {
        mainDiagnosis = diagnosisArr(mainDiagnosis);
      }
      console.log(mainDiagnosis);
      form.setFieldsValue({
        ...detailData,
        deptList: (detailData?.deptList || []).map(v => v.id),
        mainDiagnosis
      });
    }
    // eslint-disable-next-line
  }, [detailData]);
  return (
    <LayoutBox>
      <FormDescriptions
        edit={true}
        form={form}
        formProps={{
          requiredMark: true,
          layout: 'inline',
          wrapperCol: { span: 12 }
        }}
        items={useMemo(
          () => [
            {
              label: '类型',
              name: 'medicalTemplateType',
              required: true,
              formItemProps: {
                initialValue: 1,
                render: (
                  <ArrSelect
                    onChange={e => {
                      setType(e as number);
                    }}
                    disabled={!isShow}
                    allowClear={false}
                    options={MedicalTemplateType}
                  />
                )
              }
            },
            {
              label: '科室',
              name: 'deptList',
              required: true,
              formItemProps: {
                initialValue: 0,
                render: (
                  <Select
                    mode='multiple'
                    allowClear
                    disabled={!isShow}
                    style={{ width: '100%' }}
                    placeholder={'请选择科室'}
                    options={deptLists}
                  />
                )
              }
            },
            {
              label: '标题',
              name: 'name',
              required: true,
              formItemProps: {
                render: <Input disabled={!isShow} placeholder={'请输入标题'} />
              }
            }
          ],
          [isShow, deptLists]
        )}
      />
      <div>
        <span className={'labelTitle'}>病历内容：</span>
        <FormDescriptions
          edit={true}
          loading={
            loading || detailLoading || detailLocalLoading || localLoading
          }
          form={form}
          formProps={{
            requiredMark: false,
            layout: 'vertical'
          }}
          items={useMemo(
            () =>
              (data?.content || []).map(v => {
                return {
                  ...v,
                  label: <span>{v.hisColumnDesc}</span>,
                  name: v.hisColumn,
                  formItemProps: {
                    render: [...cn, 'mainDiagnosis'].includes(v.hisColumn) ? (
                      <CodeSelect
                        mode='multiple'
                        allowClear
                        labelInValue
                        isLocal={isLocal}
                        targetHisId={targetHisId}
                        disabled={!isShow}
                        groupCode={v.hisColumn}
                        placeholder={`请选择${v.hisColumnDesc}`}
                      />
                    ) : v.type === 'input' ? (
                      <Input.TextArea
                        placeholder={`请输入${v.hisColumnDesc}`}
                        disabled={!isShow}
                        maxLength={v?.len}
                      />
                    ) : v.type === 'uploadImg' ? (
                      <UploadImg disabled={!isShow} />
                    ) : v.type === 'picker' ? (
                      <ArrSelect
                        options={v.options}
                        disabled={!isShow}
                        placeholder={`请输入${v.hisColumnDesc}`}
                      />
                    ) : v.type === 'digit' ? (
                      <InputNumber
                        placeholder={`请输入${v.hisColumnDesc}`}
                        disabled={!isShow}
                        style={{ width: '100%' }}
                      />
                    ) : v.type === 'radio' ? (
                      <ArrRadio disabled={!isShow} radios={v.options} />
                    ) : (
                      undefined
                    ),
                    rules: [
                      {
                        required: v?.required,
                        message: v.hisColumnDesc + '是必填的'
                      }
                    ]
                  },
                  required: false,
                  span: 3
                };
              }),
            [isShow, data, isLocal, targetHisId]
          )}
        />
      </div>
      {isShow && (
        <Button
          loading={updateLoading || addLoading}
          type={'primary'}
          onClick={() => {
            form.validateFields().then(res => {
              console.log(res);
              let { deptList, mainDiagnosis } = res;
              if (deptList !== undefined && !Array.isArray(deptList)) {
                deptList = [deptList];
              }
              if (deptList?.length) {
                console.log(deptList);
                deptList = deptList.map(v => {
                  const dept = deptLists.find(l => l.value === v);
                  return {
                    id: dept?.value,
                    name: dept?.label
                  };
                });
                if (deptList.includes(0)) {
                  deptList = [{ id: 0, name: '全部科室' }, ...deptList];
                }
              }
              if (mainDiagnosis) {
                console.log(mainDiagnosis);
                if (mainDiagnosis?.length) {
                  mainDiagnosis = mainDiagnosis
                    .map(v => {
                      return `无|${v?.label}|无|${v?.value}`;
                    })
                    .join(';');
                }
              }
              Object.keys(res)?.forEach(v => {
                if (cn.includes(v)) {
                  if (res[v]?.length) {
                    res[v] = res[v].map((l: Options) => {
                      return { name: l?.label, code: l?.value };
                    });
                  }
                }
              });
              const params = {
                ...res,
                deptList,
                mainDiagnosis,
                medicalTemplateId: data?.id || ''
              };
              handleSubmit(() =>
                id
                  ? update({
                      id,
                      ...detailData,
                      ...params
                    })
                  : add({ ...params })
              ).then(() => goBack());
            });
          }}>
          提交
        </Button>
      )}
    </LayoutBox>
  );
};
const LayoutBox = styled.div`
  margin: 24px;
  padding: 24px;
  background-color: white;
  text-align: center;
  > div {
    text-align: left;
    &:nth-child(2) {
      display: flex;
      white-space: nowrap;
      margin-top: 30px;
      .labelTitle {
        margin-top: 10px;
        margin-right: 30px;
        :before {
          display: inline-block;
          margin-right: 4px;
          color: #ff4d4f;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: '*';
        }
      }
      > div {
        .ant-form-item-label {
          span {
            color: #2780d9;
            margin-left: 10px;
            :before {
              content: '';
              width: 5px;
              height: 13px;
              position: absolute;
              left: 0;
              top: 50%;
              -webkit-transform: translateY(-50%);
              -ms-transform: translateY(-50%);
              transform: translateY(-50%);
              background: #2780d9;
            }
          }
        }
      }
    }
  }
  > .ant-btn-primary {
    margin: 0 auto;
  }
`;
