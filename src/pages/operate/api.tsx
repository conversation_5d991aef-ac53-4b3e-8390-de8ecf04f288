import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import qs from 'qs';
import {
  ListApiResponseData,
  ListApiRequestParams,
  ApiResponse1
} from '@apiHooks';
import {
  ComplaintList,
  ComplaintDetail,
  MedicalRecordTemplate,
  Template
} from './d';
import { formatParamsWithMoment } from '@src/utils/common';
import env from '@src/configs/env';
export const StatusObj: any = {
  '': '全部',
  '1': '未处理',
  '2': '已处理',
  '0': '已忽略'
};
export default {
  list: createApiHooks(
    (
      data: ListApiRequestParams & {
        platformId?: string;
        userName?: string;
        doctorName?: string;
        status?: string;
        startDate?: string;
        endDate?: string;
      }
    ) =>
      request.post<ListApiResponseData<ComplaintList>>(
        '/mch/user/complaints/page',
        data
      )
  ),
  列表导出: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${env.apiHost}/mch/user/complaints/export?${qs.stringify(
      formatParamsWithMoment(p)
    )}`;
    window.open(url);
  },
  complaintsReplay: createApiHooks(
    (params: {
      complaintsId?: string;
      hisId: string;
      replyContent?: string;
      replyUrl?: string;
      type: string;
    }) => request.post('/mch/user/complaints/handleReply', params)
  ),
  complaintsDetail: createApiHooks((params: { id: string; hisId: string }) =>
    request.post<{
      code: number;
      data: ComplaintDetail;
      msg: string;
    }>('/mch/user/complaints/getBy', params)
  ),
  complaintsUpdate: createApiHooks(
    (params: { id: string; hisId: string; status: string }) =>
      request.post('/mch/user/complaints/update', params)
  ),
  complaintsUpdateBatch: createApiHooks(
    (params: { ids: string; hisId: string; status: string }) =>
      request.post('/mch/user/complaints/updateBatch', params)
  ),
  samplelist: createApiHooks((data: ListApiRequestParams) =>
    request.post<
      ListApiResponseData<{
        createTime: string;
        customizeType: string;
        deptId: string;
        doctorId: string;
        hisId: string;
        id: number;
        status: number;
        templateContent: string;
        templateName: string;
        type: number;
        updateTime: string;
      }>
    >('/mch/inquiry/replyTemplate/page', data)
  ),
  sampleDel: createApiHooks((params: { ids: string }) =>
    request.post('/mch/inquiry/replyTemplate/delete', params)
  ),
  sampleAdd: createApiHooks(
    (params: { hisId: string; templateContent: string }) =>
      request.post('/mch/inquiry/replyTemplate/save', params)
  ),
  sampleUpdate: createApiHooks(
    (params: { id: string; hisId: string; templateContent: string }) =>
      request.post('/mch/inquiry/replyTemplate/update', params)
  ),
  sampleTypeDel: createApiHooks((params: { hisId: string; id: string }) =>
    request.post(
      '/mch/inquiry/replyTemplateType/update',
      {
        ...params,
        status: '0'
      },
      {
        headers: {
          Accept: 'application/json, text/javascript, */*; q=0.01',
          'Content-Type': 'application/json; charset=UTF-8'
        }
      }
    )
  ),
  sampleTypeAdd: createApiHooks(
    (params: { hisId: string; name?: string; status?: string }) =>
      request.post<{
        code: number;
        data: Record<string, unknown>;
        msg: string;
      }>('/mch/inquiry/replyTemplateType/save', params, {
        headers: {
          Accept: 'application/json, text/javascript, */*; q=0.01',
          'Content-Type': 'application/json; charset=UTF-8'
        }
      })
  ),
  sampleType: createApiHooks((params: { hisId: string }) =>
    request.post<{
      code: number;
      data: {
        recordList: Array<{
          createTime: '2020-06-12 11:27:16';
          hisId: 2218;
          id: number;
          name: '默认分类';
          status: '1';
          updateTime: '2020-06-12 11:27:16';
        }>;
      };
      msg: string;
    }>('/mch/inquiry/replyTemplateType/listPage', params)
  ),
  // 查询常用处方列表
  recipeTemplateList: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId: string; //医院ID
        doctorId?: number; //医生ID
        templateType: number; //药方类型（1常用方，2经典方）
        templateName?: string; //药方名称
      }
    ) =>
      request.post<
        ListApiResponseData<{
          hisId: number; //医院ID
          hisName: string; //医院名称
          doctorId: number; //医生ID
          doctorCode: string; //医生编码
          doctorName: string; //医生名称
          deptId: number; //科室ID
          deptCode: string; //科室编码
          deptName: string; //科室名称
          prescriptionType: number; //处方类别（1西药，2中成药，3中草药）
          templateType: number; //药方类型（1常用方，2经典方）
          templateName: string; //药方名称
          cnDiagnosis: string; //中医诊断
          diagnosis: string; //西医诊断
          cnDialectics: string; //中医辨证
          recipeFunction: string; //功能
          majorFunction: string; //主治
          useQuantity: string; //加减运用
          useMethod: string; //服法
          totalFee: number; //总金额
          remark: string; //备注
          drugList: {
            drugName: string;
            amount: number;
          }[]; // 药品列表
        }>
      >('/mch/recipe-template/page', data)
  ),
  电子病历模版列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        createEndTime: string;
        name?: string;
        medicalTemplateType: string;
        createStartTime?: string;
      }
    ) =>
      request.get<ListApiResponseData<MedicalRecordTemplate[]>>(
        '/mch/inquiry/medical-record-content-template',
        {
          params
        }
      )
  ),
  本地电子病历模版列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        createEndTime: string;
        name?: string;
        medicalTemplateType: string;
        createStartTime?: string;
        targetHisId?: string | number;
      }
    ) =>
      request.get<ListApiResponseData<MedicalRecordTemplate[]>>(
        '/mch/inquiry/ls-main/getMedicalRecordList',
        {
          params
        }
      )
  ),
  科室列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        hisId: string;
        hisType: number;
        lastStage?: boolean;
        status?: 1 | 0;
      }
    ) =>
      request.get<
        ApiResponse1<
          {
            id: '@natural'; //主键id
            hisId: '2219'; //医院hisid
            name: '@cword(2)科室'; //科室名称
            no: '@word(4)'; //科室编码
          }[]
        >
      >('/mch/his/deptMain/list', {
        params
      })
  ),
  本地科室列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        hisId: string;
        targetHisId?: string;
        hisType: number;
        lastStage?: boolean;
        status?: 1 | 0;
      }
    ) =>
      request.get<
        ApiResponse1<
          {
            id: '@natural'; //主键id
            hisId: '2219'; //医院hisid
            name: '@cword(2)科室'; //科室名称
            no: '@word(4)'; //科室编码
          }[]
        >
      >('/mch/his/ls-main/deptMainList', {
        params
      })
  ),
  病历模版删除: createApiHooks((id: number) =>
    request.delete<ApiResponse1<any>>(
      `/mch/inquiry/medical-record-content-template/${id}`
    )
  ),
  病历模版新增: createApiHooks((data: Template) =>
    request.post<ApiResponse1<any>>(
      `/mch/inquiry/medical-record-content-template`,
      data,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  病历模版编辑: createApiHooks((data: Template & { id: number }) =>
    request.put<ApiResponse1<any>>(
      `/mch/inquiry/medical-record-content-template`,
      data,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  病历模版详情: createApiHooks((id: number | string) =>
    request.get<ApiResponse1<any>>(
      `/mch/inquiry/medical-record-content-template/${id}`
    )
  ),
  本地病历模版详情: createApiHooks(
    (params: { id: string; targetHisId?: string; hisId?: string }) =>
      request.get<ApiResponse1<any>>(
        `/mch/inquiry/ls-main/medicalRecordContent`,
        { params }
      )
  )
};
