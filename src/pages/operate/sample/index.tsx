import React, { useMemo, useState } from 'react';
import {
  LinkButton,
  actionConfirm,
  useModal,
  ActionsWrap,
  handleSubmit,
  ArrSelect,
  DayRangePicker,
  Form
} from 'parsec-admin';
import { useForceUpdate } from 'parsec-hooks';
import useApi from '../api';
import { Button, Input } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import MyTableList from '@components/myTableList';
import moment from 'moment';
import MyArrSelect from '@src/components/MyArrSelect';
import permisstion from '@utils/permisstion';
import styled from 'styled-components';
import env from '@src/configs/env';

const { TextArea } = Input;

export default () => {
  const hisId = env.hisId;
  const {
    data: {
      data: { recordList: typeList }
    },
    request: requestSampleType
  } = useApi.sampleType({
    params: {
      hisId: hisId
    },
    initValue: { data: { recordList: [] } }
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );
  const [form] = Form.useForm();
  const { forceUpdate } = useForceUpdate();
  const switchModalVisible = useModal(
    {
      title: '输入常用语',
      form,
      onSubmit: async values => {
        await handleSubmit(() =>
          values.id
            ? useApi.sampleUpdate.request({ ...values, hisId: hisId })
            : useApi.sampleAdd.request({ ...values, hisId: hisId })
        );
        requestSampleType();
      },
      items: [
        {
          name: 'id',
          render: false
        },
        {
          label: '标题',
          name: 'templateName',
          required: true
        },
        {
          label: '分类',
          name: 'customizeTypeId',
          formItemProps: {
            rules: [
              {
                required: true,
                message: '分类是必选的'
              }
            ]
          },
          render: () => (
            <MyArrSelect
              onUpdate={() => {
                requestSampleType();
              }}
              options={typeList.map(type => {
                return {
                  value: type.id,
                  title: type.id,
                  children: (
                    <SelectChildren>
                      <div>{type.name}</div>
                      <Button
                        danger={true}
                        onClick={e => {
                          e.stopPropagation();
                          e.preventDefault();
                          return useApi.sampleTypeDel
                            .request({
                              hisId,
                              id: type.id + ''
                            })
                            .then(() => {
                              requestSampleType();
                            });
                        }}>
                        删除
                      </Button>
                    </SelectChildren>
                  )
                };
              })}
            />
          )
        },
        {
          label: '常用语',
          name: 'templateContent',
          required: true,
          render: (
            <TextArea
              onChange={forceUpdate}
              rows={4}
              maxLength={500}
              placeholder='请输入常用语内容，方便您更快捷与患者沟通'
            />
          ),
          formItemProps: {
            extra: `${form.getFieldValue('templateContent')?.length ?? 0}/500`
          }
        }
      ]
    },
    [forceUpdate, hisId, typeList]
  );
  return (
    <MyTableList
      tableTitle={'常用语管理'}
      getList={({ params }) => useApi.samplelist.request(params)}
      action={
        permisstion.canAddReply && (
          <Button
            type={'default'}
            icon={<PlusOutlined />}
            onClick={() => switchModalVisible({})}>
            添加
          </Button>
        )
      }
      paginationExtra={
        <div>
          {!!selectedRowKeys.length && (
            <>
              <Button
                type={'default'}
                style={{ marginRight: '10px' }}
                onClick={() => {
                  actionConfirm(
                    () =>
                      useApi.sampleDel.request({
                        ids: selectedRowKeys.join(',')
                      }),
                    '批量删除'
                  );
                }}>
                批量删除
              </Button>
              <span> 已选择{selectedRowKeys.length}条</span>
            </>
          )}
        </div>
      }
      rowSelection={{
        onChange: selectedRowKeys => setSelectedRowKeys(selectedRowKeys)
      }}
      columns={useMemo(
        () => [
          {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 180,
            search: <DayRangePicker placeholder={['开始时间', '结束时间']} />,
            searchIndex: ['startDate', 'endDate'],
            render: val => moment(val).format('YYYY-MM-DD HH:mm:ss')
          },
          {
            title: '分类',
            dataIndex: 'customizeType',
            searchIndex: 'customizeTypeId',
            search: (
              <ArrSelect
                options={typeList.map(type => {
                  return {
                    value: type.id,
                    children: type.name
                  };
                })}
              />
            ),
            width: 100
          },
          {
            title: '标题',
            dataIndex: 'templateName',
            search: true,
            width: 300
          },
          {
            title: '内容',
            dataIndex: 'templateContent',
            width: 300,
            render: (v = '') => <Ellipsis row={2}>{v}</Ellipsis>
          },
          {
            title: '操作',
            fixed: 'right',
            width: 150,
            render: permisstion.showEditReply
              ? record => (
                  <ActionsWrap>
                    {permisstion.canUpdateReply && (
                      <LinkButton
                        onClick={() => {
                          switchModalVisible(record);
                        }}>
                        编辑
                      </LinkButton>
                    )}
                    {permisstion.canDeleteReply && (
                      <LinkButton
                        onClick={() => {
                          actionConfirm(
                            () => useApi.sampleDel.request({ ids: record.id }),
                            '删除'
                          );
                        }}>
                        删除
                      </LinkButton>
                    )}
                  </ActionsWrap>
                )
              : false
          }
        ],
        [switchModalVisible, typeList]
      )}
    />
  );
};
const SelectChildren = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Ellipsis = styled.div<{ row: number }>`
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: ${({ row }) => row};
  overflow: hidden;
`;
