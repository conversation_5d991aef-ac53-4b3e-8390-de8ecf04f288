import React from 'react';
import { CardLayout, FormDescriptions, getPrice, Form } from 'parsec-admin';
import styled from 'styled-components';
import useApi from './api';
import { Rate } from 'antd';
import { useParams } from 'react-router-dom';
import Message from '@components/ih-standard-message';

const editSate = false;

export default () => {
  const { id: orderId } = useParams<any>();
  const [form] = Form.useForm();

  const {
    data: { data },
    loading: detailLoading
  } = useApi.问诊详情({
    params: { orderId: orderId || '' },
    needInit: !!orderId,
    initValue: { data: {} }
  });

  const getAppraisal = (v: string, data: string) => {
    const label = (data || '')
      .split('-')
      .map((item: string) => {
        return item ? `#${item}#` : '';
      })
      .join(' ');
    return (
      <div>
        {label}
        <div>{v || ''}</div>
      </div>
    );
  };

  return (
    <Wrapper edit={false}>
      <CardLayout title={'患者信息'} loading={detailLoading}>
        <FormDescriptions
          data={data?.patient}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '用户名',
              name: 'name'
            },
            {
              label: '性别',
              name: 'patientSex'
            },
            {
              label: '年龄',
              name: 'patientAge'
            },
            {
              label: '体重',
              name: 'weight',
              render: v => v + ' kg'
            },
            {
              label: '联系方式',
              name: 'mobile'
            },
            {
              label: '身份证号',
              name: 'idNo'
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'就诊信息'} loading={detailLoading}>
        <FormDescriptions
          data={data?.patient}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '就诊院区',
              name: 'name'
            },
            {
              label: '就诊科室',
              name: 'patientSex'
            },
            {
              label: '就诊医生',
              name: 'name'
            },
            {
              label: '挂号类型',
              name: 'weight'
            },
            {
              label: '挂号时间',
              name: 'mobile'
            },
            {
              label: '预约时间',
              name: 'idNo'
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'订单信息'} loading={detailLoading}>
        <FormDescriptions
          data={data?.inquiry}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '订单号',
              name: 'orderIdStr'
            },
            {
              label: '订单状态',
              name: 'orderStatusName'
            },
            {
              label: '订单金额',
              name: 'totalFee',
              render: v => `${getPrice(v as number, 2, true)}`
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'医生信息'} loading={detailLoading}>
        <FormDescriptions
          data={data?.doctor}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '医生姓名',
              name: 'name'
            },
            {
              label: '医生职称',
              name: 'level'
            },
            {
              label: '所属科室',
              name: 'deptName'
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'评价详情信息'} loading={detailLoading}>
        <FormDescriptions
          data={data?.appraisal}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '评价时间',
              name: 'createTimeStr'
            },
            {
              label: '医生评价',
              name: 'score',
              render: v => {
                return (
                  <Rate
                    disabled
                    defaultValue={v as number}
                    style={{
                      position: 'relative',
                      top: '-10px'
                    }}
                  />
                );
              }
            },
            {
              label: '医生评价详情',
              name: 'appraisal',
              className: 'ant-descriptions-item-flex',
              render: v => {
                return getAppraisal(
                  v as string,
                  data?.appraisal?.appraisalLabel || ''
                );
              }
            },
            {
              label: '评价订单',
              name: 'id'
            },
            {
              label: '医院评价',
              name: 'hisScore',
              render: v => {
                return (
                  <Rate
                    disabled
                    defaultValue={v as number}
                    style={{
                      position: 'relative',
                      top: '-10px'
                    }}
                  />
                );
              }
            },
            {
              label: '医院评价详情',
              name: 'hisAppraisal',
              className: 'ant-descriptions-item-flex',
              render: v => {
                return getAppraisal(
                  v as string,
                  data?.appraisal?.hisAppraisalLabel || ''
                );
              }
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'问诊会话详情'} loading={detailLoading}>
        {data && data.items && (
          <Message
            addNewChat={item => {}}
            doctorAvatar={data?.doctor.image}
            userAvatar={data?.patient?.patientImage || ''}
            userName={data?.patient?.patientImage || data?.patient.name}
            data={{
              inquiry: data?.inquiry as any,
              items: data?.items as any
            }}
            direction={'TO_ALL'}
            goPrescriptionDetail={item => {}}
            goPrescriptionDetailPre={item => {}}
            goAppointDetail={item => {}}
            onAction={(action, item) => {}}
          />
        )}
      </CardLayout>
    </Wrapper>
  );
};

const Wrapper = styled.div<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
  .ant-table {
    > .ant-table-container {
      > .ant-table-content {
        > table {
          > tbody {
            > tr {
              > td {
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
`;
