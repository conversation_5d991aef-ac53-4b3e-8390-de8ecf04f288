import React, { useMemo, useState } from 'react';
import {
  LinkButton,
  RouteComponentProps,
  DayRangePicker,
  ArrSelect,
  ActionsWrap,
  actionConfirm
} from 'parsec-admin';
import useApi, { scoreListObj, scoreStates } from './api';
import MyTableList from '@components/myTableList';
import { Rate, Tooltip } from 'antd';
import permisstion from '@utils/permisstion';

//帮你渠道
const platformSource = {
  1: '微信',
  2: '支付宝'
};

//业务类型
const businessSource = {
  1: '互联网医院',
  2: '智慧医院'
};

//业务类型
const businessType1 = {
  1: '预约挂号',
  2: '今日挂号'
};

//业务类型
const businessType2 = {
  1: '图文问诊',
  2: '电话问诊',
  3: '视频问诊',
  4: 'mdt会诊'
};

export default ({ history }: RouteComponentProps) => {
  const [businessSourceId, setBusinessSourceId] = useState<number>(1);
  const getAppraisal = (v: string, data: string) => {
    console.log('data', data);
    const label = (data || '')
      .split('-')
      .map((item: string) => {
        return item ? `#${item}#` : '';
      })
      .join(' ');

    return (
      <Tooltip title={label + v || '暂无'}>
        <div
          style={{
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis'
          }}>
          {label + v || ''}
        </div>
      </Tooltip>
    );
  };

  const getBusinessType = (v: string, businessSource: string, type: string) => {
    // 根据业务平台选择对应的业务类型映射
    const businessTypeMap =
      businessSource === '2' ? businessType1 : businessType2;
    // 获取业务类型文本
    const businessTypeText = businessTypeMap[type] || '-';
    return (businessTypeText || '-') + ' | ' + v;
  };

  return (
    <MyTableList
      tableTitle='评价管理'
      getList={({ params }) => useApi.评价列表.request(params)}
      scroll={{ x: 1600 }}
      columns={useMemo(
        () => [
          {
            title: '评价时间',
            width: 200,
            dataIndex: 'createTimeStr',
            search: <DayRangePicker placeholder={['开始日期', '结束日期']} />,
            searchIndex: ['startDate', 'endDate']
          },
          {
            title: '类型 | 评价订单',
            width: 220,
            dataIndex: 'orderIdStr',
            render: (v, record: any) => {
              return getBusinessType(v, record.businessSource, record.type);
            }
          },
          {
            title: '问诊医生',
            dataIndex: 'doctorName',
            width: 180,
            search: true,
            render: false
          },
          {
            title: '用户姓名',
            dataIndex: 'name',
            width: 100,
            search: true
          },
          {
            title: '问诊医生｜问诊科室',
            dataIndex: 'doctorName',
            width: 200,
            render: (v, record: any) => {
              return v + '｜' + record.deptName;
            }
          },
          {
            title: '患者姓名｜ID',
            dataIndex: 'patientName',
            width: 200,
            render: (v, record: any) => {
              return v + '｜' + (record?.patCardNo || '-');
            }
          },
          {
            title: '联系方式',
            width: 150,
            dataIndex: 'mobile'
          },
          {
            title: '医生星级',
            dataIndex: 'score',
            width: 170,
            render: (v, record: any) => (
              <Rate
                disabled
                defaultValue={
                  record?.businessSource === '1'
                    ? v
                    : Math.ceil(record?.registerTotalScore / 6)
                }
              />
            ),
            search: <ArrSelect options={scoreListObj} />
          },
          {
            title: '医生评价详情',
            dataIndex: 'appraisal',
            width: 170,
            render: (v, record: any) => {
              return getAppraisal(
                v,
                record?.businessSource === '1'
                  ? record.appraisalLabel
                  : record.registerAppraisalLabel
              );
            }
          },
          {
            title: '医院星级',
            dataIndex: 'hisScore',
            width: 170,
            render: (v, record: any) => (
              <Rate
                disabled
                defaultValue={record?.businessSource === '1' ? v : ''}
              />
            )
          },
          {
            title: '医院评价详情',
            dataIndex: 'hisAppraisal',
            width: 170,
            render: (v, record: any) => {
              return (
                record?.businessSource === '1' &&
                getAppraisal(v, record.hisAppraisalLabel || '')
              );
            }
          },
          {
            title: '业务平台',
            dataIndex: 'businessSource',
            width: 170,
            render: v => {
              return businessSource[v] || '-';
            },
            search: (
              <ArrSelect
                options={businessSource}
                onChange={e => {
                  if (e !== undefined && !isNaN(Number(e))) {
                    setBusinessSourceId(Number(e));
                  }
                }}
              />
            )
          },
          {
            title: '业务类型',
            dataIndex: 'type',
            width: 170,
            search: (
              <ArrSelect
                options={businessSourceId === 2 ? businessType1 : businessType2}
              />
            ),
            render: false
          },
          {
            title: '患者姓名',
            dataIndex: 'patientName',
            width: 180,
            search: true,
            render: false
          },
          {
            title: '就诊卡号',
            dataIndex: 'patCardNo',
            width: 100,
            search: true,
            render: false
          },
          {
            title: '办理渠道',
            dataIndex: 'platformSource',
            width: 100,
            search: <ArrSelect options={platformSource} />,
            render: false
          },
          {
            title: '医生工号',
            dataIndex: 'doctorId',
            width: 100,
            search: true,
            render: false
          },
          {
            title: '状态',
            dataIndex: 'isShow',
            width: 170,
            search: <ArrSelect options={scoreStates} />,
            render: v => {
              return v === '0' ? '已下架' : '显示中';
            }
          },
          {
            title: '操作',
            fixed: 'right',
            width: 120,
            render: record => (
              <ActionsWrap>
                {record.isShow === '1' && permisstion.canUpadateAppraisal && (
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          useApi.修改评价.request({
                            id: record.id,
                            isShow: '0'
                          }),
                        '下架'
                      );
                    }}>
                    下架
                  </LinkButton>
                )}
                {record.isShow === '0' && permisstion.canUpadateAppraisal && (
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          useApi.修改评价.request({
                            id: record.id,
                            isShow: '1'
                          }),
                        '上架'
                      );
                    }}>
                    上架
                  </LinkButton>
                )}
                <LinkButton
                  onClick={() => {
                    if (record?.businessSource === '1') {
                      history.push('/operate/evaluate/' + record.orderIdStr);
                    } else {
                      history.push('/operate/evaluateZhyy/' + record.id);
                    }
                  }}>
                  详情
                </LinkButton>
              </ActionsWrap>
            )
          }
        ],
        [history, businessSourceId]
      )}
    />
  );
};
