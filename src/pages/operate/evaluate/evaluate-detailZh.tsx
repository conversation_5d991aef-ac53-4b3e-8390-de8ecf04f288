import React, { useState } from 'react';
import { CardLayout, FormDescriptions, getPrice, Form } from 'parsec-admin';
import styled from 'styled-components';
import useApi from './api';
import { Rate } from 'antd';
import { useParams } from 'react-router-dom';
import Message from '@components/ih-standard-message';
import { desenStr } from 'parsec-hooks';
import { Space } from '@kqinfo/ui';

const editSate = false;

export default () => {
  const { id: orderId } = useParams<any>();
  const [form] = Form.useForm();
  const [idNo, setIdNo] = useState(false);
  const [mobile, setMobile] = useState(false);
  const [mobile1, setMobile1] = useState(false);
  const [name, setName] = useState(false);
  const [name1, setName1] = useState(false);
  const {
    data: { data },
    loading: detailLoading
  } = useApi.智慧医院问诊详情({
    params: { id: orderId || '' },
    needInit: !!orderId,
    initValue: { data: {} }
  });

  const getAppraisal = (v: string, data: any) => {
    const label = v?.split(',');
    return (
      <div>
        {label?.map(item => {
          return (
            <label
              style={{
                padding: '6px 10px',
                background: '#efefef',
                borderRadius: '6px',
                marginRight: '10px'
              }}>
              {item}
            </label>
          );
        })}
        <div style={{ marginTop: '10px' }}>{data?.appraisal}</div>
      </div>
    );
  };

  return (
    <Wrapper edit={false}>
      <CardLayout title={'患者信息'} loading={detailLoading}>
        <FormDescriptions
          data={data}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '用户名',
              name: 'name',
              render: r => {
                if (!r) return '-';
                else
                  return (
                    <Space size={10}>
                      {name1 ? r : desenStr({ str: r as any, type: 'name' })}
                      <div
                        onClick={() => setName1(!name1)}
                        style={{
                          cursor: 'pointer',
                          textDecoration: 'underline',
                          color: '#027DB4'
                        }}>
                        {name1 ? '隐藏信息' : '展示完整信息'}
                      </div>
                    </Space>
                  );
              }
            },
            {
              label: '性别',
              name: 'sex'
            },
            {
              label: '年龄',
              name: 'age'
            },
            {
              label: '体重',
              name: 'weight',
              render: v => v + ' kg'
            },
            {
              label: '联系方式',
              name: 'mobile',
              render: r => {
                if (!r) return '-';
                else
                  return (
                    <Space size={10}>
                      {mobile ? r : desenStr({ str: r as any, type: 'phone' })}
                      <div
                        onClick={() => setMobile(!mobile)}
                        style={{
                          cursor: 'pointer',
                          textDecoration: 'underline',
                          color: '#027DB4'
                        }}>
                        {mobile ? '隐藏信息' : '展示完整信息'}
                      </div>
                    </Space>
                  );
              }
            },
            {
              label: '身份证号',
              name: 'idNumber',
              render: r => {
                if (!r) return '-';
                else
                  return (
                    <Space size={10}>
                      {idNo ? r : desenStr({ str: r as any, type: 'ID' })}
                      <div
                        onClick={() => setIdNo(!idNo)}
                        style={{
                          cursor: 'pointer',
                          textDecoration: 'underline',
                          color: '#027DB4'
                        }}>
                        {idNo ? '隐藏信息' : '展示完整信息'}
                      </div>
                    </Space>
                  );
              }
            },
            {
              label: '地址',
              name: 'patientAddress'
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'就诊信息'} loading={detailLoading}>
        <FormDescriptions
          data={data}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '就诊院区',
              name: 'areaName'
            },
            {
              label: '就诊科室',
              name: 'deptName'
            },
            {
              label: '就诊医生',
              name: 'doctorName'
            },
            {
              label: '医生职称',
              name: 'doctorLevel'
            },
            {
              label: '挂号类型',
              name: 'typeName'
            },
            {
              label: '挂号时间',
              name: 'createTimeStr'
            },
            {
              label: '预约时间',
              name: 'visitDate'
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'订单信息'} loading={detailLoading}>
        <FormDescriptions
          data={data}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '订单号',
              name: 'orderIdStr'
            },
            {
              label: '业务类型',
              name: 'typeName'
            },
            {
              label: '办理渠道',
              name: 'platformSource',
              render: v => (v === '1' ? '微信公众号' : '支付宝')
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'评价详情信息'} loading={detailLoading}>
        <FormDescriptions
          data={data}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '评价时间',
              name: 'createTimeStr'
            },
            {
              label: '评价用户',
              name: 'patientName',
              render: r => {
                if (!r) return '-';
                else
                  return (
                    <Space size={10}>
                      {name ? r : desenStr({ str: r as any, type: 'name' })}
                      <div
                        onClick={() => setName(!name)}
                        style={{
                          cursor: 'pointer',
                          textDecoration: 'underline',
                          color: '#027DB4'
                        }}>
                        {name ? '隐藏信息' : '展示完整信息'}
                      </div>
                    </Space>
                  );
              }
            },
            {
              label: '联系电话',
              name: 'mobile',
              render: r => {
                if (!r) return '-';
                else
                  return (
                    <Space size={5}>
                      {mobile1 ? r : desenStr({ str: r as any, type: 'phone' })}
                      <div
                        onClick={() => setMobile1(!mobile1)}
                        style={{
                          cursor: 'pointer',
                          textDecoration: 'underline',
                          color: '#027DB4'
                        }}>
                        {mobile1 ? '隐藏信息' : '展示完整信息'}
                      </div>
                    </Space>
                  );
              }
            },
            {
              label: '评价订单',
              name: 'id'
            }
          ]}
        />
        <FormDescriptions
          data={data}
          edit={editSate}
          form={form}
          column={1}
          loading={detailLoading}
          items={[
            {
              label: '评价内容',
              name: 'registerAppraisalLabel',
              render: v => {
                return getAppraisal(v as string, data as any);
              }
            }
          ]}
        />
        <FormDescriptions
          data={data}
          edit={editSate}
          form={form}
          column={2}
          loading={detailLoading}
          items={[
            {
              label: '综合评分',
              name: 'registerTotalScore',
              render: v => {
                return (
                  <Rate
                    disabled
                    defaultValue={(v as unknown) as number}
                    style={{
                      position: 'relative',
                      top: '-10px'
                    }}
                  />
                );
              }
            }
          ]}
        />
        <FormDescriptions
          data={data}
          edit={editSate}
          form={form}
          column={2}
          loading={detailLoading}
          items={[
            {
              label: '挂号体验',
              name: 'registerScore',
              render: v => {
                return (
                  <Rate
                    disabled
                    defaultValue={(v as unknown) as number}
                    style={{
                      position: 'relative',
                      top: '-10px'
                    }}
                  />
                );
              }
            },
            {
              label: '医生沟通',
              name: 'doctorChatScore',
              render: v => {
                return (
                  <Rate
                    disabled
                    defaultValue={(v as unknown) as number}
                    style={{
                      position: 'relative',
                      top: '-10px'
                    }}
                  />
                );
              }
            },
            {
              label: '护士沟通',
              name: 'nurseChatScore',
              render: v => {
                return (
                  <Rate
                    disabled
                    defaultValue={(v as unknown) as number}
                    style={{
                      position: 'relative',
                      top: '-10px'
                    }}
                  />
                );
              }
            },
            {
              label: '环境与标识',
              name: 'envScore',
              render: v => {
                return (
                  <Rate
                    disabled
                    defaultValue={(v as unknown) as number}
                    style={{
                      position: 'relative',
                      top: '-10px'
                    }}
                  />
                );
              }
            },
            {
              label: '隐私',
              name: 'privacyScore',
              render: v => {
                return (
                  <Rate
                    disabled
                    defaultValue={(v as unknown) as number}
                    style={{
                      position: 'relative',
                      top: '-10px'
                    }}
                  />
                );
              }
            },
            {
              label: '医生廉洁行医',
              name: 'doctorDishonestyScore',
              render: v => {
                return (
                  <Rate
                    disabled
                    defaultValue={(v as unknown) as number}
                    style={{
                      position: 'relative',
                      top: '-10px'
                    }}
                  />
                );
              }
            }
          ]}
        />
      </CardLayout>
    </Wrapper>
  );
};

const Wrapper = styled.div<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
  .ant-table {
    > .ant-table-container {
      > .ant-table-content {
        > table {
          > tbody {
            > tr {
              > td {
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
`;
