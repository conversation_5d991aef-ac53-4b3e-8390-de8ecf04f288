import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiRequestParams,
  ListApiResponseData,
  ApiResponse
} from '@src/configs/apis';

export const scoreListObj = {
  1: '一星评价',
  2: '二星评价',
  3: '三星评价',
  4: '四星评价',
  5: '五星评价'
};
export const scoreStates = {
  0: '已下架',
  1: '显示中'
};

export interface Appraisal {
  age: string;
  appraisal: 'ccccccccccccccccccccc';
  appraisalLabel: '态度好-及时回复-解答详细-很专业-非常感谢';
  createDate: '2020-04-17 10:40:35';
  createTime: '2020-04-17T02:40:35.000+0000';
  createTimeStr: '2020-04-17 10:40:35';
  deptId: '2018008';
  deptName: '皮ac';
  doctorId: '901';
  doctorName: '潘龙测试';
  hisAppraisal: 'sssssssssssssssssssssssssssssssssssss';
  hisAppraisalLabel: '服务完善-价格合理-制度规范-流程简单-系统稳定';
  hisId: 2218;
  hisName: '重庆医科大学附属儿童医院';
  hisScore: 5;
  id: 2004172000000002;
  idNumber: string;
  inquiryId: string;
  inquiryType: string;
  isShow: '1';
  mobile: '18883344574';
  name: 'cscsc';
  nameStr: 'c****';
  orderId: 2004030221800000000;
  orderIdStr: '2004030221800000013';
  patientId: string;
  patientName: string;
  replyAppraisal: string;
  score: number;
  sex: string;
  updateTime: '2020-04-17T02:40:35.000+0000';
  userId: 33323;
}
export interface Dcotor {
  ableType: null;
  auditTime: '2000-01-01 03:00:00';
  category: '4';
  circleImage: string;
  completed: 0;
  consultCount: null;
  consultationAuthority: '1';
  createTime: '2018-08-07 16:08:10';
  deptId: '2018008';
  deptName: '皮ac';
  deptmentId: 190829000000888;
  deptmentName: '下级医院职能部门';
  doctorId: '900';
  doctorInfoId: 1;
  doctorInfoVo: null;
  educationTitle: null;
  endValidDate: '2020-01-02 19:05:06';
  evaluated: 0;
  evaluationLabel: null;
  extFields: string;
  fans: 0;
  favoriteRate: null;
  favourTimes: 0;
  grade: '5';
  hisDoctorName: 'ZJR';
  hisId: 2218;
  hisName: '重庆医科大学附属儿童医院11';
  id: 1;
  idNumber: '420302197712110937';
  image: string;
  inquiryConfigParamList: null;
  inquirys: null;
  insuranceDueDate: null;
  insuranceEndDate: '2020-01-02 19:05:12';
  insuranceStartDate: '2020-01-02 19:05:08';
  introduction: '女，硕士，主治医师。2013年重庆医科大学儿科系毕业后留校工作，从事风湿免疫专业临床与研究工作。多次参加国际及全国学术会议，并大会发言，发表SCI论文2篇。擅长风湿免疫性疾病（川崎病，过敏性紫癜，原发性免疫缺陷病等）的诊治，对儿内科常见病（呼吸、消化方向）、儿童保健也有较深见解。4444444';
  isFull: '0';
  level: '医师';
  mobile: '18823051314';
  name: '潘龙测试';
  onDuty: '1';
  pdeptId: '2018008';
  pdeptName: '';
  policyNumber: 'PZEM201950010000000039';
  position: '2';
  practiceLevel: '执业医师';
  practiceMedicalInstitution: '重庆医科大学附属儿童医院';
  practiceNumber: 110500000011900;
  practiceScope: '外科专业';
  prescriptionQualification: '普通处方';
  qrContent: 'http://weixin.qq.com/q/02KPGAgeuQafP10000007G';
  qrTicket: 'gQE48TwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyS1BHQWdldVFhZlAxMDAwMDAwN0cAAgQDlGNcAwQAAAAA';
  qrUrl: string;
  recommend: null;
  remark: null;
  replyLabel: null;
  replyTime: null;
  reviewAdmin: null;
  reviewDoctor: null;
  score: 0;
  serviceTimes: 0;
  sex: 'M';
  signatureImg: 'data:image/jpg;base64,R0lGODlhoAA8APcAAP8CAv4BAf4BAf4CAv4EBP4NDf4mJv5vb/7z8/7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v////7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v////7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v////7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v////7+/v7+/v7+/v///yH5BAlkAPAALAAAAACgADwAAAj+AOEJHEiwoMGDCBMqXMiwocOHECNKnEixosWLGDNq3Mixo8ePIEOKHEmypMmTKFOqXMmypcuXMGPKnEmzps2bOHPq3Mmzp8+fQIMKHUq0qNGjSJMqXcq0qdOnNgMEADmVqsOqIadixbhVqtetHrFK7Tj261iCYMNq1XgWrcC2ZNGmvbh2INi5EuG6hae3oti3gK3uxUuxa2DCEfH+ZWuXr+ONZr/yNcuxal3LjCdHluzXruTNYQNDtowZ8+OslUVPPv3RdOrVjk0jzjg7MevLqE/3tXgWN+vCu3/Tbqy7dl7iwQs/9m28oVitXuPqlit9edzShzMTF85bc3TK1eujpsZuffhe7p1FL678PHTs7OiPn2/OMLr67ZDPs+1d3jVd+LBx9Rxor2lWEH0KGXYQgglO9158D623Hm2yHWheefp1N+Bb4gG33YR0uXYXgwa1BSKJCJXVl31+DRiZdiWCxxt01Bl4YULJTTQXiiWqNBuP6UEl5JBEFmnkkUgmqeSSTDbp5JNQRinllFRWaeWVWGap5ZZcdunll2CGKeaYZHYUEAA7';
  sortNo: 9;
  specialty: '肺炎、腹泻等儿内科常见病；免疫缺陷、过敏性紫癜、川崎病等疾病诊治肺炎、腹泻等儿内科常见病；免疫缺陷、过敏性紫癜、川崎病等疾病诊治222222222222';
  startValidDate: '2020-01-02 19:05:04';
  status: '1';
  timelyReply: '0%';
  title: null;
  type: '1';
  underwritingUnit: '中国人民财产保险股份有限公司';
  updateTime: '2020-04-24 11:31:37';
  workingLife: 10;
}
export interface Inquiry {
  caseId: string;
  chargeFlag: string;
  choiceRecord: string;
  content: '好喜欢夫妇超级超级超级高放假放假减肥放假';
  countNum: string;
  createDate: '2020-04-03 18:13:51';
  createTime: '2020-04-03 18:13:51';
  deptId: '2018008';
  deptName: '皮ac';
  diagnosis: '';
  doctor: string;
  doctorEarnings: string;
  doctorId: '901';
  doctorName: '潘龙测试';
  doctorReaded: '0';
  doctorTimes: 3;
  doctorType: 1;
  endTime: string;
  videoInfo: string;
  finishTime: '2020-04-13 17:47:49';
  firstReplyTime: '2020-04-09 10:28:45';
  hisId: 2218;
  hisName: '重庆医科大学附属儿童医院';
  hospitalTradeno: string;
  hospitalVisitNo: string;
  id: 2004033000000013;
  interval: 0;
  item: string;
  orderId: 2004030221800000000;
  orderIdStr: '2004030221800000013';
  orderStatusName: '已退款';
  orderStr: string;
  patCardNo: string;
  patientAge: 0;
  patientId: 59;
  patientName: 'ceshi';
  patientNo: string;
  patientTel: string;
  paySerialNumber: string;
  platformId: 221801;
  purpose: '咨询';
  purposeType: '1';
  refundSource: 'WXAPP';
  refundStatus: '1';
  replyDirection: 'TO_DOCTOR';
  replyTime: '2020-04-13 15:22:18';
  sex: string;
  startTime: string;
  status: string;
  statusName: string;
  subscribeInfo: string;
  totalFee: number;
  type: '1';
  typeName: string;
  updateTime: '2020-04-03 18:13:51';
  userId: 33323;
  userName: string;
  userReaded: '1';
  userTimes: 14;
  validFlag: string;
  weight: string;
}

export interface Items {
  action:
    | null
    | 'receiveChronic'
    | 'applyChronic'
    | 'payedChronic'
    | 'img'
    | 'replyContent'
    | 'text';
  actionTrigger: string;
  content: string;
  createDate: string;
  createTime: string;
  direction: 'TO_USER' | 'TO_DOCTOR';
  doctorIsShow: '0' | '1';
  id: number;
  inquiryId: number;
  orderId: number;
  sender: string;
  type: 'SYSTEM' | 'BIZ';
  updateDate: string;
  updateTime: string;
  url: string;
  userId: number;
  userIsShow: '0' | '1';
  voiceContent: string;
  voiceTime: number;
  userName: string;
}

export interface Order {
  appraisal: string;
  appraisalTime: string;
  autoCancelFlag: string;
  autoCancelTimeout: *************;
  businessType: '1';
  businessTypeStr: string;
  canCancelFlag: string;
  canPayFlag: string;
  createDate: string;
  createTime: '2020-04-03 18:13:52';
  deptId: '2018008';
  deptName: '皮ac';
  doctorId: '901';
  doctorName: '潘龙测试';
  extFields: string;
  hisId: 2218;
  hisName: '重庆医科大学附属儿童医院';
  id: 2004030221800000000;
  leftPayTime: string;
  operatorAccount: string;
  operatorId: string;
  operatorName: string;
  orderIdStr: string;
  orderStatus: 'S';
  orderStatusName: string;
  orderTotalFee: string;
  patCardNo: string;
  patientId: 59;
  patientName: 'ceshi';
  payChannel: 'weixin';
  payChannelStr: '微信';
  payDate: string;
  payFee: 1;
  payMethod: 'wap';
  payMethodStr: 'wap支付';
  paySerialNumber: '4200000552202004037048068958';
  payStatus: '1';
  payTime: '2020-04-03 18:14:02';
  refundDesc: string;
  refundStatus: 0;
  refundTime: string;
  remark: string;
  replyStatus: 0;
  score: number;
  sex: string;
  sourceType: string;
  status: '0';
  statusName: string;
  totalFee: number;
  type: '1';
  typeStr: string;
  unlockDatetime: string;
  unlockFlag: string;
  updateTime: '2020-04-03 18:13:51';
  userId: 33323;
  userName: string;
}

export interface Patient {
  accountId: 50;
  address: string;
  age: string;
  bindMedicareCard: 0;
  bindStatus: 0;
  birth: string;
  birthday: '2014-07-22';
  channelType: string;
  consumeType: string;
  createTime: '2018-07-09 15:16:33';
  height: string;
  hisId: 2218;
  hisName: string;
  id: 59;
  idImage: string;
  idNo: '******************';
  idType: 1;
  inquiryDept: string;
  inquiryDoctor: string;
  inquiryPurpose: string;
  inquiryTime: string;
  isDefalut: 0;
  isDefault: string;
  isSelf: string;
  married: string;
  mobile: '***********';
  name: '刘罡豪';
  openId: string;
  parentIdNo: string;
  parentIdType: 0;
  parentName: string;
  patCardNo: '**********';
  patCardType: 21;
  patHisId: string;
  patHisNo: '**********';
  patInNo: string;
  patientAddress: string;
  patientAge: string;
  patientId: string;
  patientImg: string;
  patientMobile: string;
  patientName: string;
  patientSex: string;
  patientType: 0;
  platformId: string;
  platformSource: string;
  realName: string;
  relationType: 5;
  sex: 'M';
  smoking: string;
  syncStatus: string;
  type: string;
  updateTime: '2020-04-13 15:12:57';
  userId: 33323;
  userName: string;
  weight: string;
  patientImage: string;
}

export default {
  评价列表: createApiHooks((data: ListApiRequestParams) =>
    request.post<
      ListApiResponseData<{
        id: number;
        userId: number;
        name: string;
        mobile: string;
        orderId: number;
        hisId: string;
        hisName: string;
        deptId: string;
        deptName: string;
        doctorId: string;
        doctorName: string;
        score: number;
        appraisal: string;
        isShow: string;
        createTime: string;
        updateTime: string;
        appraisalLabel: string;
        hisScore: number;
        hisAppraisal: string;
        hisAppraisalLabel: string;
        replyAppraisal: string;
        createDate: string;
        inquiryId: string;
        inquiryType: string;
        patientId: string;
        patientName: string;
        idNumber: string;
        sex: string;
        age: string;
        createTimeStr: string;
        orderIdStr: string;
        nameStr: string;
      }>
    >('/mch/inquiry/appraisal/page', data)
  ),
  问诊详情: createApiHooks((data: { orderId: string }) =>
    request.post<
      ApiResponse<{
        appraisal: Appraisal;
        doctor: Dcotor;
        hospitalOrderInfo: string;
        inquiry: Inquiry;
        items: Array<Items>;
        order: Order;
        patient: Patient;
      }>
    >('/mch/inquiry/inquiry/info', data)
  ),
  修改评价: createApiHooks((data: { id: number; isShow: string }) =>
    request.post<ApiResponse<Record<string, unknown>>>(
      '/mch/inquiry/appraisal/update',
      data
    )
  ),
  智慧医院问诊详情: createApiHooks((data: { id: string }) =>
    request.get<
      ApiResponse<{
        appraisal: string;
      }>
    >(`/mch/inquiry/appraisal/details/${data.id}`)
  )
};
