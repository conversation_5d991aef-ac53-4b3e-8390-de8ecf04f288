import { useMemo, useState } from 'react';
import { RouteComponentProps, DayRangePicker, getPrice } from 'parsec-admin';
import { Button, Table } from 'antd';
import useApi, { AbrTimeSlotTotal } from './api';
import MyTableList from '@components/myTableList';
import { ListApiResponseData } from '@src/configs/d';
import { ExportOutlined } from '@ant-design/icons';
import { saveAs } from 'file-saver';
import moment from 'moment';
// const isDev = process.env.NODE_ENV !== 'production';

export default ({ history }: RouteComponentProps) => {
  // const hisId = env.hisId;
  // // 获取医院列表
  // const {
  //   data: { data }
  // } = useApi1.getByHospit({
  //   initValue: {
  //     data: []
  //   },
  //   params: {
  //     hisId
  //   },
  //   needInit: !!hisId
  // });
  const [queryParams, setQueryParams] = useState({} as any);
  const { request: handleExport, loading: exportLoading } = useApi.exportOrder({
    needInit: false
  });
  const [footDatas, setFootDatas] = useState({} as AbrTimeSlotTotal);
  const SumPane = useMemo(() => {
    return (
      <>
        <Table.Summary.Row>
          <Table.Summary.Cell index={0} colSpan={2}>
            总计
          </Table.Summary.Cell>
          <Table.Summary.Cell index={1}>
            {footDatas?.abnormalCount}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={2}>
            {`${getPrice(+(footDatas?.abnormalFee || 0), 2, true)}`}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={3}>
            {footDatas?.dealCount}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={4}>
            {`${getPrice(+(footDatas?.dealFee || 0), 2, true)}`}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={5}>
            {footDatas?.unhandledCount}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={6}>
            {`${getPrice(+(footDatas?.unhandledFee || 0), 2, true)}`}
          </Table.Summary.Cell>
        </Table.Summary.Row>
      </>
    );
  }, [footDatas]) as any;
  return (
    <MyTableList
      className='statisticsTable'
      tableTitle='异常订单处理统计'
      action={
        <Button
          type={'default'}
          loading={exportLoading}
          icon={<ExportOutlined />}
          onClick={() =>
            handleExport({ ...queryParams, isExport: 1 }).then(data =>
              saveAs(
                data,
                `异常订单处理统计 ${moment(queryParams.startDate).format(
                  'YYYY/MM/DD'
                )} - ${moment(queryParams.endDate).format('YYYY/MM/DD')}.xls`
              )
            )
          }>
          导出
        </Button>
      }
      pagination={false}
      bordered
      scroll={{ x: 1600 }}
      getList={({ params }) => {
        setQueryParams({
          ...params
        });
        return useApi.异常订单处理统计查询
          .request({
            ...params
          })
          .then(res => {
            setFootDatas(res.data?.timeSlotTotal as any);
            return ({
              code: 0,
              msg: '',
              data: {
                currentPage: 1,
                totalCount: res.data?.recordList.length,
                recordList: res.data?.recordList
              }
            } as unknown) as ListApiResponseData<any>;
          });
      }}
      columns={useMemo(
        () => [
          {
            title: '日期',
            width: 180,
            dataIndex: 'statisticsDate',
            fixed: 'left',
            search: (
              <DayRangePicker
                placeholder={['开始日期', '结束日期']}
                valueFormat={'YYYY-MM-DD'}
              />
            ),
            searchIndex: ['startDate', 'endDate']
          },
          {
            title: '异常类别',
            width: 160,
            dataIndex: 'abnormalType'
          },
          // {
          //   title: '医院名称',
          //   width: 220,
          //   dataIndex: 'hisId',
          //   render: false,
          //   search: (
          //     <ArrSelect
          //       options={(data || []).map(x => ({
          //         value: x.hisId,
          //         children: x.hospitalName
          //       }))}
          //     />
          //   )
          // },
          {
            title: '异常情况',
            width: 360,
            children: [
              {
                title: '异常笔数',
                width: 180,
                dataIndex: 'abnormalCount'
              },
              {
                title: '异常金额',
                width: 180,
                dataIndex: 'abnormalFee',
                render: v => `${getPrice(v, 2, true)}`
              }
            ]
          },
          {
            title: '已处理情况',
            width: 360,
            children: [
              {
                title: '笔数',
                width: 180,
                dataIndex: 'dealCount'
              },
              {
                title: '金额',
                width: 180,
                dataIndex: 'dealFee',
                render: v => `${getPrice(v, 2, true)}`
              }
            ]
          },
          {
            title: '未处理情况',
            width: 360,
            children: [
              {
                title: '笔数',
                width: 180,
                dataIndex: 'unhandledCount'
              },
              {
                title: '金额',
                width: 180,
                dataIndex: 'unhandledFee',
                render: v => `${getPrice(v, 2, true)}`
              }
            ]
          }
        ],
        []
      )}
      summary={() => SumPane}
    />
  );
};
