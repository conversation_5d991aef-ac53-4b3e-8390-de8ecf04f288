import { useMemo, useState } from 'react';
import { RouteComponentProps, DayRangePicker, getPrice } from 'parsec-admin';
import { Button, Table } from 'antd';
import useApi, { TimeSlotTotal } from './api';
import MyTableList from '@components/myTableList';
import { ListApiResponseData } from '@src/configs/d';
import { ExportOutlined } from '@ant-design/icons';
import saveAs from 'file-saver';
import moment from 'moment';
// const isDev = process.env.NODE_ENV !== 'production';

export default ({ history }: RouteComponentProps) => {
  const [queryParams, setQueryParams] = useState({} as any);
  const { request: handleExport, loading: exportLoading } = useApi.exportTypes({
    needInit: false
  });
  const [footDatas, setFootDatas] = useState({} as TimeSlotTotal);
  const SumPane = useMemo(() => {
    return (
      <>
        <Table.Summary.Row>
          <Table.Summary.Cell index={0}>总计</Table.Summary.Cell>
          <Table.Summary.Cell index={3}>
            {footDatas.todayRegCount}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={4}>
            {`${getPrice(+(footDatas.todayRegFee || 0), 2, true)}`}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={5}>
            {footDatas.regCount}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={6}>
            {`${getPrice(+(footDatas.regFee || 0), 2, true)}`}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={7}>
            {footDatas.opPayCount}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={8}>
            {`${getPrice(+(footDatas.opPayFee || 0), 2, true)}`}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={9}>
            {footDatas.ihHosPayCount}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={10}>
            {`${getPrice(+(footDatas.ihHosPayFee || 0), 2, true)}`}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={11}>
            {footDatas.otherPayCount}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={12}>
            {`${getPrice(+(footDatas.otherPayFee || 0), 2, true)}`}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={13}>
            {footDatas.totalCount}
          </Table.Summary.Cell>
          <Table.Summary.Cell index={14}>
            {`${getPrice(+(footDatas.totalFee || 0), 2, true)}`}
          </Table.Summary.Cell>
        </Table.Summary.Row>
      </>
    );
  }, [footDatas]) as any;
  return (
    <MyTableList
      tableTitle='自助机业务分类数据统计'
      className='statisticsTable'
      action={
        <Button
          type={'default'}
          loading={exportLoading}
          icon={<ExportOutlined />}
          onClick={() =>
            handleExport({ ...queryParams, isExport: 1 }).then(data =>
              saveAs(
                data,
                `自助机业务分类数据统计 ${moment(queryParams.startDate).format(
                  'YYYY/MM/DD'
                )} - ${moment(queryParams.endDate).format('YYYY/MM/DD')}.xls`
              )
            )
          }>
          导出
        </Button>
      }
      pagination={false}
      bordered
      scroll={{ x: 1600 }}
      getList={({ params }) => {
        setQueryParams({
          ...params,
          businessType: 'self'
        });
        return useApi.业务分类使用报表
          .request({
            ...params,
            businessType: 'self'
          })
          .then(res => {
            setFootDatas(res.data?.timeSlotTotal as any);
            return ({
              code: 0,
              msg: '',
              data: {
                currentPage: 1,
                totalCount: res.data?.recordList.length,
                recordList: res.data?.recordList
              }
            } as unknown) as ListApiResponseData<any>;
          });
      }}
      columns={useMemo(
        () => [
          {
            title: '日期',
            width: 100,
            dataIndex: 'statisticsDate',
            fixed: 'left',
            search: (
              <DayRangePicker
                placeholder={['开始日期', '结束日期']}
                valueFormat={'YYYY-MM-DD'}
              />
            ),
            searchIndex: ['startDate', 'endDate']
          },
          {
            title: '当日挂号',
            width: 240,
            children: [
              {
                title: '笔数',
                width: 120,
                dataIndex: 'todayRegCount'
              },
              {
                title: '金额',
                width: 120,
                dataIndex: 'todayRegFee',
                render: v => `${getPrice(+(v || 0), 2, true)}`
              }
            ]
          },
          {
            title: '预约挂号',
            width: 240,
            children: [
              {
                title: '笔数',
                width: 120,
                dataIndex: 'regCount'
              },
              {
                title: '金额',
                width: 120,
                dataIndex: 'regFee',
                render: v => `${getPrice(+(v || 0), 2, true)}`
              }
            ]
          },
          {
            title: '门诊缴费',
            width: 240,
            children: [
              {
                title: '笔数',
                width: 120,
                dataIndex: 'opPayCount'
              },
              {
                title: '金额',
                width: 120,
                dataIndex: 'opPayFee',
                render: v => `${getPrice(+(v || 0), 2, true)}`
              }
            ]
          },
          {
            title: '住院预存',
            width: 240,
            children: [
              {
                title: '笔数',
                width: 120,
                dataIndex: 'ihHosPayCount'
              },
              {
                title: '金额',
                width: 120,
                dataIndex: 'ihHosPayFee',
                render: v => `${getPrice(+(v || 0), 2, true)}`
              }
            ]
          },
          {
            title: '其他',
            width: 240,
            children: [
              {
                title: '笔数',
                width: 120,
                dataIndex: 'otherPayCount'
              },
              {
                title: '金额',
                width: 120,
                dataIndex: 'otherPayFee',
                render: v => `${getPrice(+(v || 0), 2, true)}`
              }
            ]
          },
          {
            title: '总计',
            width: 240,
            children: [
              {
                title: '笔数',
                width: 120,
                dataIndex: 'totalCount'
              },
              {
                title: '金额',
                width: 120,
                dataIndex: 'totalFee',
                render: v => `${getPrice(+(v || 0), 2, true)}`
              }
            ]
          }
        ],
        []
      )}
      summary={() => SumPane}
    />
  );
};
