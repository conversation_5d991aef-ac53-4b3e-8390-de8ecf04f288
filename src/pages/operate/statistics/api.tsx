import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiRequestParams,
  ListApiResponseData,
  ApiResponse
} from '@src/configs/apis';
export interface FunctionTag {
  functionTag: string;
  usedCount: string;
  percentage: string;
}
export interface TimeSlotTotal {
  totalFollowedUser: string;
  totalAccountUser: string;
  todayRegCount: string;
  todayRegFee: string;
  regCount: string;
  regFee: string;
  opPayCount: string;
  opPayFee: string;
  ihHosPayCount: string;
  ihHosPayFee: string;
  otherPayCount: string;
  otherPayFee: string;
  totalCount: string;
  totalFee: string;
}
export interface AbrTimeSlotTotal {
  abnormalCount: string;
  abnormalFee: string;
  dealCount: string;
  dealFee: string;
  unhandledCount: string;
  unhandledFee: string;
}
interface Typelist {
  //记录数据列表
  id: string;
  hisId: string;
  hisName: string;
  businessType: string;
  totalFollowedUser: string;
  totalAccountUser: string;
  todayRegCount: string;
  todayRegFee: string;
  regCount: string;
  regFee: string;
  opPayCount: string;
  opPayFee: string;
  ihHosPayCount: string;
  ihHosPayFee: string;
  otherPayCount: string;
  otherPayFee: string;
  totalCount: string;
  totalFee: string;
  statisticsDate: string;
}
interface AbrList {
  id: string;
  hisId: string;
  hisName: string;
  abnormalType: string;
  abnormalCount: string;
  abnormalFee: string;
  dealCount: string;
  dealFee: string;
  unhandledCount: string;
  unhandledFee: string;
  statisticsDate: string;
}

export default {
  问诊监管列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        orderBy?: string;
        platformId?: string;
      }
    ) =>
      request.post<
        ListApiResponseData<{
          caseId: string;
          chargeFlag: string;
          choiceRecord: string;
          content: string;
          countNum: string;
          createDate: string;
          createTime: string;
          deptId: string;
          deptName: string;
          diagnosis: string;
          doctor: string;
          doctorEarnings: string;
          doctorId: string;
          doctorName: string;
          doctorReaded: string;
          doctorTimes: number;
          doctorType: number;
          endTime: string;
          finishTime: string;
          firstReplyTime: string;
          hisId: string;
          hisName: string;
          hospitalTradeno: string;
          hospitalVisitNo: string;
          id: number;
          interval: number;
          item: string;
          orderId: number;
          orderIdStr: string;
          orderStatusName: string;
          orderStr: string;
          patCardNo: string;
          patientAge: number;
          patientId: number;
          patientName: string;
          patientNo: string;
          patientTel: string;
          paySerialNumber: string;
          platformId: number;
          purpose: string;
          purposeType: string;
          refundSource: string;
          refundStatus: string;
          replyDirection: string;
          replyTime: string;
          sex: string;
          startTime: string;
          status: string;
          statusName: string;
          subscribeInfo: string;
          totalFee: number;
          type: string;
          typeName: string;
          updateTime: string;
          userId: number;
          userName: string;
          userReaded: string;
          userTimes: number;
          validFlag: string;
          weight: string;
        }>
      >('/mch/inquiry/inquiry/page/manage', data)
  ),
  退费: createApiHooks((data: { hisId: string; orderId: string }) =>
    request.post<ApiResponse<Record<string, unknown>>>(
      '/mch/order/order/refund',
      data
    )
  ),
  自助机功能使用率查询: createApiHooks(
    (
      params: ListApiRequestParams & {
        hisId?: string;
        subHisId?: string;
        machineLocation?: string;
        machineType?: string;
        machineNo?: string;
        startDate?: string;
        endDate?: string;
        page?: number;
        limit: number;
      }
    ) =>
      request.get<
        ListApiResponseData<{
          id: string;
          hisId: string;
          hisName: string;
          subHisId: string;
          subHisName: string;
          machineNo: string;
          machineName: string;
          machineType: string;
          machineBuilding: string;
          machineLocation: string;
          totalUsedCount: string;
          functionTags: FunctionTag[];
        }>
      >('/mch/statistics/machine', { params })
  ),
  业务分类使用报表: createApiHooks(
    (params: {
      hisId?: string;
      businessType?: string;
      startDate?: string;
      endDate?: string;
    }) =>
      request.get<
        ApiResponse<{
          recordList: Typelist[];
          timeSlotTotal: TimeSlotTotal;
        }>
      >('/mch/statistics/business', { params })
  ),
  异常订单处理统计查询: createApiHooks(
    (params: { hisId?: string; startDate?: string; endDate?: string }) =>
      request.get<
        ApiResponse<{
          recordList: AbrList[];
          timeSlotTotal: AbrTimeSlotTotal;
        }>
      >('/mch/statistics/abnormal', { params })
  ),
  exportOrder: createApiHooks(
    (params: { hisId?: string; startDate?: string; endDate?: string }) =>
      request.get<Blob>('/mch/statistics/abnormal/export', {
        responseType: 'blob',
        params
      })
  ),
  exportTypes: createApiHooks(
    (params: {
      hisId?: string;
      businessType?: string;
      startDate?: string;
      endDate?: string;
    }) =>
      request.get<Blob>('/mch/statistics/business/export', {
        responseType: 'blob',
        params
      })
  )
};
