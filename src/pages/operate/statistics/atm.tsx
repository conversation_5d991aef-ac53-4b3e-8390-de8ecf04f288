import { useMemo, useState } from 'react';
import { RouteComponentProps, DayRangePicker } from 'parsec-admin';
import useApi, { FunctionTag } from './api';
import MyTableList from '@components/myTableList';
import { ListApiResponseData } from '@src/configs/d';
// const isDev = process.env.NODE_ENV !== 'production';

export default ({ history }: RouteComponentProps) => {
  const [funcList, setFuncList] = useState([] as any[]);
  const funColumn = useMemo(() => {
    return funcList.map((item: FunctionTag, index) => ({
      title: item.functionTag,
      dataIndex: item.functionTag,
      width: 180
    }));
  }, [funcList]);
  const newColumns = useMemo(() => {
    const arr = [
      ...[
        {
          title: '医院名称',
          width: 100,
          dataIndex: 'hisName',
          fixed: 'left'
        },
        {
          title: '自助终端编号',
          width: 100,
          dataIndex: 'machineNo',
          fixed: 'left',
          search: true
        },
        {
          title: '生产厂家',
          width: 100,
          fixed: 'left',
          dataIndex: 'machineName'
        },

        {
          title: '位置',
          width: 100,
          dataIndex: 'machineLocation',
          fixed: 'left',
          search: true,
          render: (v, r) => `${r.machineBuilding}${r.machineLocation}`
        },
        {
          title: '自助终端类别',
          width: 100,
          dataIndex: 'machineType',
          search: true
        },
        {
          title: '使用次数',
          width: 80,
          dataIndex: 'totalUsedCount'
        },
        {
          title: '查询时间',
          dataIndex: 'createDate',
          render: false,
          search: (
            <DayRangePicker
              placeholder={['开始日期', '结束日期']}
              valueFormat={'YYYY-MM-DD'}
            />
          ),
          searchIndex: ['startDate', 'endDate']
        }
      ],
      ...funColumn
    ];
    return arr;
  }, [funColumn]) as any;
  return (
    <MyTableList
      tableTitle='自助终端功能使用率统计表'
      className='statisticsTable'
      bordered
      exportExcelButton
      getList={({ params }) => {
        console.log('params', params);
        return useApi.自助机功能使用率查询
          .request({ ...params, page: params.pageNum || 1, limit: 10 })
          .then(res => {
            if (res?.data?.currentPage === 1) {
              const Tclomn: any[] = res.data?.recordList[0]?.functionTags || [];
              console.log('Tclomn', Tclomn);
              setFuncList(Tclomn);
            }
            const newrecordList = (res.data?.recordList || []).map(item => {
              (item.functionTags || []).forEach((x, i) => {
                item[x.functionTag] = `${x.usedCount} | ${(
                  +x.percentage * 100
                ).toFixed(2)}%`;
              });
              return item;
            });
            return ({
              code: 0,
              msg: '',
              data: {
                currentPage: res.data?.currentPage,
                totalCount: res.data?.totalCount,
                recordList: newrecordList
              }
            } as unknown) as ListApiResponseData<any>;
          });
      }}
      scroll={{ x: 3000 }}
      columns={newColumns}
    />
  );
};
