import React from 'react';
import { RouteComponentProps } from 'react-router';
import {
  ActionsWrap,
  ArrSelect,
  DateShow,
  LinkButton,
  useModal,
  actionConfirm,
  useReloadTableList
} from 'parsec-admin';
import { PlusOutlined } from '@ant-design/icons';
import { Button, InputNumber, message, Form } from 'antd';
import apis from './apis';

import MyTableList from '@src/components/myTableList';

export const types = [
  {
    label: '西药',
    value: 'WESTERN_MEDICINE'
  },
  {
    label: '中药方剂',
    value: 'CM_FORMULA'
  },
  {
    label: '妆品及其耗材',
    value: 'MAKEUP_CONSUMABLES'
  }
];

const ManageCates: React.FC<RouteComponentProps> = () => {
  const [form] = Form.useForm();
  const reload = useReloadTableList();
  const showEditModal = useModal(({ id }) => ({
    title: `${id ? '编辑' : '新增'}类型`,
    width: 400,
    form,
    onSubmit: async values => {
      const api = id ? apis.adit.request : apis.add.request;
      await api({
        ...values
      });
      message.success(id ? '更新成功' : '新增成功');
      reload();
    },
    items: [
      { name: 'id', render: false },
      {
        name: 'name',
        required: true,
        label: '类型名称'
      },
      {
        name: 'prescriptionDrugsType',
        label: '便捷购药分类',
        required: true,
        render: () => {
          return <ArrSelect options={types} placeholder='请选择' />;
        }
      },
      {
        name: 'sort',
        label: '排序',
        required: true,
        render: () => <InputNumber placeholder='请输入' min={0} />
      }
    ]
  }));

  return (
    <MyTableList
      tableTitle='类型管理'
      action={
        <Button
          type={'primary'}
          icon={<PlusOutlined />}
          onClick={() => showEditModal()}>
          新增类型
        </Button>
      }
      getList={({ params }: { params: any }) => {
        const { sort, ...p } = params;
        return apis.queryList.request({
          ...p,
          pageNum: params.pageNum,
          numPerPage: 10
        });
      }}
      columns={[
        {
          title: '类型名称',
          width: 180,
          dataIndex: 'name'
        },
        {
          title: '便捷购药分类',
          width: 180,
          dataIndex: 'prescriptionDrugsType',
          render: v => {
            return v ? types.find(item => item.value === v)?.label : '-';
          }
        },
        {
          title: '排序',
          width: 100,
          dataIndex: 'sort'
        },
        {
          title: '药方数量',
          width: 100,
          dataIndex: 'medicalRecipeNum',
          render: v => {
            return v ?? 0;
          }
        },
        {
          title: '操作时间',
          width: 180,
          dataIndex: 'updateTime',
          render: v => <DateShow>{v}</DateShow>
        },
        {
          title: '操作人',
          width: 120,
          dataIndex: 'operator'
        },
        {
          title: '操作',
          fixed: 'right',
          align: 'center',
          width: 100,
          render: record => (
            <ActionsWrap max={8}>
              <LinkButton
                onClick={() => {
                  form.setFieldsValue({ ...record });
                  showEditModal({ id: record?.id });
                }}>
                编辑
              </LinkButton>
              <LinkButton
                type='danger'
                onClick={() => {
                  actionConfirm(async () => {
                    return apis.del.request(record.id);
                  }, '删除');
                }}>
                删除
              </LinkButton>
            </ActionsWrap>
          )
        }
      ]}
    />
  );
};

export default ManageCates;
