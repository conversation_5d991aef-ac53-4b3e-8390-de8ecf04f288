import createApiHooks from 'create-api-hooks';
import {
  ApiResponse,
  ListApiResponseData,
  ListApiRequestParams
} from '@apiHooks';
import { request } from 'parsec-admin';

interface List {
  id: '@natural'; //主键
  name: '@cname'; //名称
  prescriptionDrugsType: '@word(5)'; //快捷购药药品类别(西药(WESTERN_MEDICINE),中药方剂(CM_FORMULA) , 妆品及其耗材(MAKEUP_CONSUMABLES)
  sort: '@natural'; //排序
  medicalRecipeNum: '@natural'; //药方数量
  updateTime: '@datetime'; //操作时间
  operator: '@word(5)'; //操作人
}

interface Data {
  id?: number;
  prescriptionDrugsType: '@word(5)'; //快捷购药药品类别(西药(WESTERN_MEDICINE),中药方剂(CM_FORMULA) , 妆品及其耗材(MAKEUP_CONSUMABLES)
  name: '@cname'; //名称
  sort: '@natural'; //排序
}

export default {
  queryList: createApiHooks(
    (params: ListApiRequestParams & { prescriptionDrugsType?: string }) =>
      request.get<ListApiResponseData<List>>(
        '/mch/prescription/medicalRecipeType/page',
        { params }
      )
  ),
  add: createApiHooks((params: Data) =>
    request.post<ApiResponse<any>>(
      `/mch/prescription/medicalRecipeType/add`,
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  adit: createApiHooks((params: Data) =>
    request.put<ApiResponse<any>>(
      `/mch/prescription/medicalRecipeType/update`,
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  del: createApiHooks((id: number) =>
    request.delete<ApiResponse<any>>(
      `/mch/prescription/medicalRecipeType/delete/${id}`
    )
  )
};
