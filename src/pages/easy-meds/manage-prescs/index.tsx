// import { useMemo } from 'react';
import { RouteComponentProps, useHistory } from 'react-router';
import {
  ActionsWrap,
  ArrSelect,
  DateShow,
  LinkButton,
  actionConfirm,
  getPrice
} from 'parsec-admin';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Tooltip, Switch } from 'antd';
import apis from './apis';

import MyTableList from '@src/components/myTableList';

export const types = [
  {
    label: '西药',
    value: 'WESTERN_MEDICINE'
  },
  {
    label: '中药方剂',
    value: 'CM_FORMULA'
  }
  // {
  //   label: '妆品及其耗材',
  //   value: 'MAKEUP_CONSUMABLES'
  // }
];

const ManagePrescs: React.FC<RouteComponentProps> = () => {
  const { push } = useHistory();

  return (
    <MyTableList
      tableTitle='便捷购药列表'
      action={[
        <Button
          icon={<PlusOutlined />}
          onClick={() => push(`/easy-meds/manage-prescs/edit/add`)}>
          新建
        </Button>
        // <Button type={'default'} onClick={() => void 0}>
        //   批量导入
        // </Button>
      ]}
      getList={({ params }: { params: any }) => {
        const { sort, ...p } = params;
        return apis.queryList.request({
          ...p,
          pageNum: params.pageNum,
          numPerPage: 10
        });
      }}
      size='small'
      scroll={{ x: 1000 }}
      exportExcelButton
      columns={[
        {
          title: '药方名称',
          dataIndex: 'medicalRecipeName',
          search: true,
          width: 120
        },
        {
          title: '便捷购药分类',
          dataIndex: 'prescriptionDrugsType',
          width: 120,
          search: (
            <ArrSelect options={[{ label: '全部', value: '' }, ...types]} />
          ),
          render: v => {
            return v ? types.find(item => item.value === v)?.label : '-';
          }
        },
        {
          title: '药方分类',
          dataIndex: 'medicalRecipeTypeName',
          width: 180
        },
        {
          title: '西医诊断',
          dataIndex: 'diagnosis',
          width: 180,
          render: (_, record: any) => {
            return (
              record?.diagnosis?.map(v => v.name)?.join(',') ??
              record?.cnDiagnosis?.map(v => v.name)?.join(',') ??
              '-'
            );
          }
        },
        {
          title: '简介',
          dataIndex: 'briefIntroduction',
          width: 180,
          excelRender: v => v,
          render: v => <Tooltip title={v}>{v}</Tooltip>
        },
        {
          title: '价格',
          dataIndex: 'totalFee',
          render: v => getPrice(v),
          width: 100
        },
        {
          title: '推荐',
          dataIndex: 'recommended',
          align: 'center',
          width: 80,
          excelRender: v => (v === 1 ? '推荐' : '不推荐'),
          render: (v, record) => (
            <Switch
              defaultChecked={v === 1}
              onChange={() => {
                apis.changeRecommand.request({
                  medicalRecipeId: record?.id,
                  recommend: v === 1 ? 0 : 1
                });
              }}
            />
          )
        },
        {
          title: '状态',
          dataIndex: 'status',
          align: 'center',
          width: 100,
          render: v => (v ? '启用' : '停用')
        },
        {
          title: '操作时间',
          dataIndex: 'updateTime',
          excelRender: v => v,
          render: v => <DateShow>{v}</DateShow>,
          width: 180
        },
        { title: '操作人', dataIndex: 'operator', width: 100 },
        {
          title: '操作',
          fixed: 'right',
          align: 'center',
          width: 180,
          render: (_, record) => (
            <ActionsWrap max={3}>
              <LinkButton
                onClick={() =>
                  push(`/easy-meds/manage-prescs/edit/${record.id}`)
                }>
                编辑
              </LinkButton>
              <LinkButton
                onClick={() => {
                  actionConfirm(
                    async () => {
                      await apis.changeStatus.request({
                        medicalRecipeId: record?.id,
                        status: record.status === 1 ? 0 : 1
                      });
                    },
                    record.status === 1 ? '停用' : '启用'
                  );
                }}>
                {record.status === 1 ? '停用' : '启用'}
              </LinkButton>
              <LinkButton
                type='danger'
                onClick={() => {
                  actionConfirm(async () => {
                    await apis.del.request(record.id);
                  }, '删除');
                }}>
                删除
              </LinkButton>
            </ActionsWrap>
          )
        }
      ]}
    />
  );
};

export default ManagePrescs;
