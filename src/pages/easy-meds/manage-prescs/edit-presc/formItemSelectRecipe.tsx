import React from 'react';
import MyTableList from '@src/components/myTableList';
import { Input, Modal, Space } from 'antd';
import apis from '../apis';

interface Obj {
  id: string;
  name: string;
}

interface Props {
  value?: Obj[];
  onChange?: (v: Obj[]) => void;
}
const FormItemArea = (props: Props) => {
  const { value, onChange = () => {} } = props;
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState('');
  const [selectRow, setSelectRow] = React.useState<any[]>();
  React.useEffect(() => {
    if (value) {
      setSelectRow(value);
      setInputValue(value?.map(v => v.name)?.join('、') as string);
    }
  }, [value]);
  return (
    <>
      <Input
        value={inputValue}
        placeholder='请选择常用方'
        onClick={() => {
          setOpen(true);
        }}
      />
      {open && (
        <Modal
          visible={true}
          width={1000}
          onCancel={() => setOpen(false)}
          title='选择医生'
          onOk={() => {
            onChange?.(selectRow as Obj[]);
            setInputValue(selectRow?.map(v => v.name)?.join('、') as string);
            setOpen(false);
          }}>
          <MyTableList<any, any>
            rowKey='id'
            size='small'
            tableTitle='医生列表'
            getList={async ({ params }: { params: any }) => {
              const res = { ...params };
              return apis.便捷购药分页查询经典处方.request(res);
            }}
            rowSelection={{
              type: 'radio',
              selectedRowKeys: selectRow?.map(v => v.id),
              onChange: (_, selectedRows) => {
                setSelectRow(selectedRows);
              }
            }}
            columns={[
              {
                title: '医生名称',
                dataIndex: 'doctorName',
                search: true
              },
              {
                title: '医院',
                dataIndex: 'hospitalName'
              },
              {
                title: '手机号码',
                dataIndex: 'phone'
              },
              {
                title: '处方名称',
                dataIndex: 'templateName',
                search: true
              },
              {
                title: '西医诊断',
                dataIndex: 'diagnosis'
              },
              {
                title: '处方信息',
                dataIndex: 'drugFrontVos',
                render: v => {
                  return (
                    <Space direction='vertical'>
                      {v
                        ? v?.map(item => {
                            return <span key={item.id}>{item?.drugName}</span>;
                          })
                        : '-'}
                    </Space>
                  );
                }
              }
            ]}></MyTableList>
        </Modal>
      )}
    </>
  );
};

export default FormItemArea;
