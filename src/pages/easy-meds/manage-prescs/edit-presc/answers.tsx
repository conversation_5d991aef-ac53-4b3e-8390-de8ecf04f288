import React, { forwardRef, useImperativeHandle } from 'react';
import { Space, Input, Checkbox, Button } from 'antd';
import { DeleteFilled } from '@ant-design/icons';

interface Answer {
  answerContent: string;
  allow: 1 | 2;
}

interface Question {
  questionContent: string;
  answerReqs: Answer[];
}

const defaultAnswer: Answer = {
  answerContent: '',
  allow: 1
};

const defaultQuestion: Question = {
  questionContent: '',
  answerReqs: [defaultAnswer]
};

const validIndex = (index?: number) => index || index === 0;

const Answers = (props, ref) => {
  const [list, setList] = React.useState<Question[]>([defaultQuestion]);
  const del = (
    type: 'question' | 'answer',
    outIndex: number,
    inIndex?: number
  ) => {
    if (type === 'question') {
      setList(pre => {
        return pre.filter((_, index) => index !== outIndex);
      });
    } else {
      setList(pre => {
        return pre.map((v, index) => {
          return index === outIndex
            ? {
                ...v,
                answerReqs: v.answerReqs.filter((_, index) => index !== inIndex)
              }
            : v;
        });
      });
    }
  };

  useImperativeHandle(ref, () => {
    return {
      getAnswers() {
        return list
          .filter(v => {
            return v.questionContent;
          })
          .map(v => {
            return {
              ...v,
              answerReqs: v.answerReqs.filter(v => v.answerContent)
            };
          });
      },
      setContent(value: Question[]) {
        if (value.length === 0) {
          setList([defaultQuestion]);
          return;
        }
        const list = value
          .filter(v => v.questionContent)
          .map(v => {
            return v?.answerReqs ? v : { ...v, answerReqs: [defaultAnswer] };
          });
        setList(list);
      }
    };
  });

  const changeContent = (value: string, outIndex: number, inIndex?: number) => {
    if (validIndex(outIndex) && !validIndex(inIndex)) {
      setList(pre => {
        return pre.map((v, i) => {
          return i === outIndex
            ? {
                ...v,
                questionContent: value
              }
            : v;
        });
      });
    }
    if (validIndex(outIndex) && validIndex(inIndex)) {
      setList(pre => {
        return pre.map((v, index) => {
          return index === outIndex
            ? {
                ...v,
                answerReqs: v.answerReqs.map((item, i) => {
                  return i === inIndex
                    ? {
                        ...item,
                        answerContent: value
                      }
                    : item;
                })
              }
            : v;
        });
      });
    }
  };
  const changeCheckout = (value: 1 | 2, outIndex: number, inIndex?: number) => {
    if (value && validIndex(outIndex) && validIndex(inIndex)) {
      setList(pre => {
        return pre.map((v, index) => {
          return index === outIndex
            ? {
                ...v,
                answerReqs: v.answerReqs.map((item, i) => {
                  return i === inIndex
                    ? {
                        ...item,
                        allow: value
                      }
                    : item;
                })
              }
            : v;
        });
      });
    }
  };
  return (
    <Space direction='vertical'>
      {list.map((v, i) => {
        return (
          <Space key={i} direction='vertical'>
            <Space style={{ marginTop: 20 }}>
              <span>题目{i + 1}：</span>
              <Input
                style={{ width: '500px' }}
                value={v.questionContent}
                placeholder='请输入题目'
                onChange={e => changeContent(e.target.value, i)}
              />
              {i !== 0 && (
                <Button
                  type='link'
                  onClick={() => del('question', i)}
                  icon={<DeleteFilled style={{ color: '#ff3141' }} />}
                />
              )}
              {i === list.length - 1 && (
                <Button
                  type='link'
                  onClick={() => {
                    setList(pre => {
                      return [...pre, defaultQuestion];
                    });
                  }}>
                  新增题目
                </Button>
              )}
            </Space>
            <Space style={{ marginLeft: 20 }} align='start'>
              <span>答案：</span>
              <Space direction='vertical'>
                {v.answerReqs.map((item, index) => {
                  return (
                    <Space key={index}>
                      <Input
                        value={item.answerContent}
                        placeholder='请输入答案'
                        style={{ width: '200px' }}
                        onChange={e => changeContent(e.target.value, i, index)}
                      />
                      <span>
                        <Checkbox
                          checked={item.allow === 2}
                          onChange={e =>
                            changeCheckout(e.target.checked ? 2 : 1, i, index)
                          }
                        />{' '}
                        拒绝开方
                      </span>
                      {index !== 0 && (
                        <Button
                          onClick={() => del('answer', i, index)}
                          type='link'
                          icon={<DeleteFilled style={{ color: '#ff3141' }} />}
                        />
                      )}
                      {index === v.answerReqs.length - 1 && (
                        <Button
                          type='link'
                          onClick={() => {
                            setList(pre => {
                              return pre.map((ss, ii) => {
                                return ii === i
                                  ? {
                                      ...ss,
                                      answerReqs: [
                                        ...v.answerReqs,
                                        defaultAnswer
                                      ]
                                    }
                                  : ss;
                              });
                            });
                          }}>
                          新增答案
                        </Button>
                      )}
                    </Space>
                  );
                })}
              </Space>
            </Space>
          </Space>
        );
      })}
    </Space>
  );
};

export default forwardRef(Answers);
