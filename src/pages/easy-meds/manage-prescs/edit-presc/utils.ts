export enum PrescType {
  /** 全平台医生 */
  'ALL' = 'PLATFORM',
  /** 所属大区 */
  'REGION' = 'REGION',
  /** 自定义 */
  'CUSTOM' = 'REGIONAL_DOCTOR'
}

export const sutiRange = [
  {
    value: PrescType.ALL,
    label: '全平台医生'
  },
  // {
  //   value: PrescType.REGION,
  //   label: '医生分区'
  // },
  {
    value: PrescType.CUSTOM,
    label: '自定义选择'
  }
];

export enum Type {
  /** 西药 */
  WESTERN_MEDICINE = 'WESTERN_MEDICINE',
  /** 中药方剂 */
  CM_FORMULA = 'CM_FORMULA',
  /** 妆品及其耗材 */
  MAKEUP_CONSUMABLES = 'MAKEUP_CONSUMABLES'
}

export const types = [
  {
    label: '西药',
    value: Type.WESTERN_MEDICINE,
    num: '01',
    storageType: 1
  },
  {
    label: '中药方剂',
    value: Type.CM_FORMULA,
    num: '03',
    storageType: 2
  }
  // {
  //   label: '妆品及其耗材',
  //   value: Type.MAKEUP_CONSUMABLES,
  //   num: '05',
  //   storageType: 3
  // }
];

export const dosageUnitData = [
  { label: '片', value: '片' },
  { label: '粒', value: '粒' },
  { label: '支', value: '支' },
  { label: 'mg', value: 'mg' },
  { label: 'ml', value: 'ml' },
  { label: 'g', value: 'g' },
  { label: '包', value: '包' },
  { label: '滴', value: '滴' },
  { label: '喷', value: '喷' },
  { label: '丸', value: '丸' },
  { label: '瓶', value: '瓶' },
  { label: '枚', value: '枚' },
  { label: 'u', value: 'u' },
  { label: 'ug', value: 'ug' },
  { label: 'iu', value: 'iu' },
  { label: '次', value: '次' },
  { label: '条', value: '条' },
  { label: '贴', value: '贴' },
  { label: '揿', value: '揿' }
];
