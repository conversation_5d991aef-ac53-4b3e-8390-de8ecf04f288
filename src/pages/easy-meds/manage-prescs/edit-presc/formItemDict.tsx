import React from 'react';
import MyTableList from '@src/components/myTableList';
import { Input, Modal } from 'antd';
import apis from '../apis';

interface Obj {
  code: string;
  name: string;
}

interface Props {
  value?: Obj[];
  onChange?: (v: Obj[]) => void;
  groupCode: string;
}
const FormItemDict = (props: Props) => {
  const { value, onChange = () => {}, groupCode } = props;
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState('');
  const [selectRow, setSelectRow] = React.useState<any[]>();
  React.useEffect(() => {
    if (value) {
      setSelectRow(value);
      setInputValue(value?.map(v => v.name)?.join(','));
    }
  }, [value]);
  return (
    <>
      <Input
        value={inputValue}
        placeholder='请选择'
        onClick={() => {
          setOpen(true);
        }}
      />
      {open && (
        <Modal
          visible={true}
          width={1000}
          onCancel={() => setOpen(false)}
          title='选择诊断'
          onOk={() => {
            onChange?.(selectRow as Obj[]);
            setInputValue(selectRow?.map(v => v.name)?.join(',') as string);
            setOpen(false);
          }}>
          <MyTableList<any, any>
            rowKey='dictKey'
            tableTitle='选择诊断'
            getList={({ params }) => {
              const p = { ...params };
              if (p?.dict) {
                p.dictValue = p.dict;
                delete p.dict;
              }
              return apis.查询字典列表.request({
                ...p,
                groupCode
              });
            }}
            rowSelection={{
              type: 'radio',
              selectedRowKeys: selectRow?.map(v => v.code),
              onChange: (_, selectedRows) => {
                setSelectRow([
                  {
                    code: selectedRows[0]?.dictKey,
                    name: selectedRows[0]?.dictValue?.value
                  }
                ]);
              }
            }}
            size='small'
            columns={[
              { title: '诊断编码', dataIndex: 'dictKey' },
              {
                title: '诊断名称',
                dataIndex: 'dictValue',
                render: v => {
                  return v?.value;
                }
              },
              {
                title: '诊断信息',
                dataIndex: 'dict',
                render: false,
                search: (
                  <Input
                    style={{ width: 300 }}
                    placeholder='请输入诊断编号或名称'
                  />
                )
              }
            ]}></MyTableList>
        </Modal>
      )}
    </>
  );
};

export default FormItemDict;
