import React from 'react';
import MyTableList from '@src/components/myTableList';
import { Input, Modal } from 'antd';
import apis from '@pages/hospital/doctorSmart/api';

interface Obj {
  doctorCode: string;
  doctorName: string;
}

interface Props {
  value?: Obj[];
  onChange?: (v: Obj[]) => void;
}
const FormItemArea = (props: Props) => {
  const { value, onChange = () => {} } = props;
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState('');
  const [selectRow, setSelectRow] = React.useState<any[]>();
  React.useEffect(() => {
    if (value) {
      setSelectRow(
        value.map(v => ({ doctorId: v.doctorCode, name: v.doctorName }))
      );
      setInputValue(value?.map(v => v.doctorName)?.join('、') as string);
    }
  }, [value]);
  return (
    <>
      <Input
        value={inputValue}
        onClick={() => {
          setOpen(true);
        }}
      />
      {open && (
        <Modal
          visible={true}
          width={1000}
          onCancel={() => setOpen(false)}
          title='选择医生'
          onOk={() => {
            onChange?.(
              selectRow?.map(v => {
                return {
                  doctorCode: v.doctorId,
                  doctorName: v.name
                };
              }) as Obj[]
            );
            // setInputValue(selectRow?.map(v => v.name)?.join('、') as string);
            setOpen(false);
          }}>
          <MyTableList<any, any>
            rowKey='doctorId'
            size='small'
            tableTitle='医生列表'
            getList={async ({ params }: { params: any }) => {
              return apis.医生列表.request({ ...params, type: 1 });
            }}
            rowSelection={{
              preserveSelectedRowKeys: true,
              selectedRowKeys: selectRow?.map(v => v.doctorId),
              onChange: (_, selectedRows) => {
                setSelectRow(selectedRows);
              }
            }}
            columns={[
              {
                title: '医生名称',
                dataIndex: 'name',
                search: true
              },
              // {
              //   title: '医院',
              //   dataIndex: 'practiceMedicalInstitution'
              // },
              {
                title: '手机号码',
                dataIndex: 'mobile'
              },
              {
                title: '职称',
                dataIndex: 'level'
              }
              // {
              //   title: '处方名称',
              //   dataIndex: 'templateName',
              //   search: true
              // },
              // {
              //   title: '西医诊断',
              //   dataIndex: 'diagnosis'
              // },
              // {
              //   title: '处方信息',
              //   dataIndex: 'drugFrontVos',
              //   render: v => {
              //     return 11;
              //   }
              // }
            ]}></MyTableList>
        </Modal>
      )}
    </>
  );
};

export default FormItemArea;
