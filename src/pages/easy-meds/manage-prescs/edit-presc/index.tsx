import {
  Checkbox,
  Form,
  Input,
  Space,
  Button,
  Radio,
  InputNumber,
  message
} from 'antd';
import {
  DetailLayout,
  ArrSelect,
  UploadImg,
  Editor,
  getPrice
} from 'parsec-admin';
import { useParams } from 'react-router';
import { useHistory } from 'react-router-dom';
import { sutiRange, PrescType, Type, types } from './utils';
import FormItemSelectDoctor from './formItemSelectDoctor';
import FormItemPrescription from './formItemPrescription';
import FormItemWestPrescription from './formItemWestPrescription';
import FormItemSelectRecipe from './formItemSelectRecipe';
import styles from './index.module.less';
import Answers from './answers';
import React, { useEffect, useRef, useState } from 'react';
import { useSelectDoctor } from '@src/customHooks/useSelectDoctor';
import cateApis from '../../manage-cates/apis';
import FormItemDict from './formItemDict';
import apis, { MedicalRecipeReqDrugs } from '../apis';
import env from '@src/configs/env';
import { calcGetPrice } from '@src/utils/tools';

const { Item } = Form;

interface Options {
  value: string | number;
  label: string;
}

interface Pharmacy {
  storageCode: string;
  storageName: string;
  districtCode: string;
  districtName: string;
}

export interface DictInfo {
  administrationList: Options[];
  rateInfo: Options[];
}
const EditPresc: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { doctorList, pharmacistList } = useSelectDoctor();
  const answerRef = useRef<any>();
  const [form] = Form.useForm();
  const history = useHistory();
  const [hideAnswer] = React.useState(false);
  const [cates, setCates] = useState<Options[]>([]);
  const [pharmacyList, setPharmacyList] = useState<Pharmacy[]>([]);
  const [pharmacyInfo, setPharmacyInfo] = useState<Pharmacy>();

  const [dictInfo, setDictInfo] = useState<{
    administrationList: Options[];
    rateInfo: Options[];
  }>({
    administrationList: [],
    rateInfo: []
  });
  // 是否是新增
  const isAdd = id === 'add';

  const {
    data: { data: detail }
  } = apis.detail({
    needInit: id !== 'add',
    params: {
      id
    }
  });

  const queryDictInfo = async (type: 'cn' | 'en') => {
    setDictInfo({
      administrationList: [],
      rateInfo: []
    });
    const arr =
      type === 'cn'
        ? ['cnAdministration', 'cnFrequency']
        : ['administration', 'frequency'];
    const [adList, freList] = await Promise.all(
      arr.map(v => apis.getDictInfo.request({ dictGroup: v }))
    );
    setDictInfo({
      administrationList: adList.data?.recordList.map((v: any) => {
        return {
          value: v.dictValue?.svCode,
          label: v.dictValue.value
        };
      }) as Options[],
      rateInfo: freList.data?.recordList.map((v: any) => {
        return {
          value: v.dictValue?.svCode,
          label: v.dictValue.value
        };
      }) as Options[]
    });
  };

  useEffect(() => {
    if (!detail) return;
    const formData: any = {
      ...detail,
      medicalRecipeImage: [detail?.medicalRecipeImage],
      recommended: detail?.recommended === 1 ? true : false
    };
    setPharmacyInfo({
      districtCode: detail?.districtCode,
      districtName: detail?.districtName,
      storageCode: detail?.storageCode,
      storageName: detail?.storageName
    });
    if (detail?.prescriptionDrugsType) {
      const storageType = types.find(
        v => v.value === detail?.prescriptionDrugsType
      )?.storageType;
      queryStorageType(storageType as number);
      queryCates(detail?.prescriptionDrugsType);
      queryDictInfo(
        detail?.prescriptionDrugsType === Type.CM_FORMULA ? 'cn' : 'en'
      );
      // // 如果是妆品及耗材
      // detail?.prescriptionDrugsType === Type.MAKEUP_CONSUMABLES &&
      //   setHideAnswer(true);
    }
    if (detail?.regionIds) {
      formData.regionIds = detail.regionIds?.[0];
    }
    /** 费用 */
    if (formData?.agentFryingFee) {
      formData.agentFryingFee = formData.agentFryingFee / 100;
      formData.agentFryingStatus = true;
    } else {
      formData.agentFryingStatus = false;
    }
    if (formData?.inquiryFee) {
      formData.inquiryFee = formData.inquiryFee / 100;
      formData.inquiryStatus = true;
    } else {
      formData.inquiryStatus = false;
    }
    // 问答
    if (
      detail?.medicalRecipeQuestionResList &&
      detail?.prescriptionDrugsType !== Type.MAKEUP_CONSUMABLES
    ) {
      answerRef.current.setContent(
        detail.medicalRecipeQuestionResList as any[]
      );
    }
    form.setFieldsValue(formData);
  }, [detail, form]);

  /** 药方类型 */
  const queryCates = (key: string) => {
    cateApis.queryList
      .request({
        prescriptionDrugsType: key,
        pageNum: 1,
        numPerPage: 999
      })
      .then(res => {
        const list = res?.data?.recordList?.map(v => {
          return {
            value: v.id,
            label: v.name
          };
        });
        setCates(list as Options[]);
      });
  };

  /** 药方类型 */
  const queryStorageType = (key: number) => {
    apis.选择药房.request(key).then(res => {
      const arr: Pharmacy[] = [];
      (res?.data ?? []).map(v => {
        (v?.storageList ?? []).map(item => {
          arr.push({
            storageCode: item.storageCode,
            storageName: item.storageName,
            districtCode: v.districtCode,
            districtName: v.districtName
          });
        });
      });
      setPharmacyList(arr);
    });
  };

  const submit = async () => {
    const res = await form.validateFields();
    const params = {
      ...res,
      medicalRecipeImage: res?.medicalRecipeImage?.[0],
      hisId: env.hisId,
      medicalRecipeTypeName: cates.find(
        v => v.value === res.medicalRecipeTypeId
      )?.label,
      recommended: res?.recommended ? 1 : 0,
      ...pharmacyInfo
    };
    if (params?.agentFryingFee) {
      params.agentFryingFee = params.agentFryingFee * 100;
    }
    if (params?.inquiryFee) {
      params.inquiryFee = params.inquiryFee * 100;
    }
    if (params?.approvedPharmacistId) {
      params.approvedPharmacistName = pharmacistList.find(
        v => v.value === params.approvedPharmacistId
      )?.label;
    }
    if (params?.approvedDoctorId) {
      params.approvedDoctorName = doctorList.find(
        v => v.value === params.approvedDoctorId
      )?.label;
    }
    // 选择医生
    if (params?.doctorCodes) {
      params.doctorCodes = params.doctorCodes.map(v => v.doctorCode);
    }
    if (!hideAnswer) {
      params.medicalRecipeQuestionReqs = answerRef.current.getAnswers();
    }
    if (id !== 'add') {
      params.id = id;
    }
    if (params?._search) {
      delete params._search;
    }
    const api = isAdd ? apis.add.request : apis.adit.request;
    await api(params);
    message.success(isAdd ? '新增成功' : '更新成功');
    history.goBack();
  };

  /** 计算价格 price 单位为 分 */
  const calcPrice = (list: MedicalRecipeReqDrugs[]) => {
    const total = list?.reduce((pre, cur) => pre + cur?.price || 0, 0);
    return total;
  };

  return (
    <DetailLayout
      cardsProps={[
        {
          title: '药方基本信息',
          children: (
            <Form
              form={form}
              labelCol={{ span: 4 }}
              style={{
                width: '1000px',
                margin: '0 auto'
              }}
              onValuesChange={(changeValues, values) => {
                if (values?.medicalRecipeReqDrugs) {
                  form.setFieldsValue({
                    totalPrice: getPrice(
                      calcPrice(values.medicalRecipeReqDrugs)
                    )
                  });
                }
                if (changeValues._search) {
                  const drugFrontVos =
                    changeValues._search?.[0]?.drugFrontVos ?? [];
                  const drugs = drugFrontVos.map(v => {
                    return {
                      drugCode: v.drugCode,
                      drugName: v.drugName,
                      price:
                        Number(
                          calcGetPrice((v?.amount ?? 0) * (v.unitPrice || 0))
                        ) * 100,
                      medicalRecipeUsage: v?.administrationName,
                      medicalRecipeDosage: v?.dosage,
                      medicalRecipeDosageUnit: v?.dosageUnit,
                      frequency: v?.frequency,
                      quantity: v?.amount
                    };
                  });
                  form.setFieldsValue({
                    medicalRecipeReqDrugs: drugs,
                    totalPrice: getPrice(calcPrice(drugs))
                  });
                }
              }}
              initialValues={{
                scopeAction: PrescType.ALL
              }}>
              <Item label='药方名称' required style={{ marginBottom: 0 }}>
                <Space>
                  <Item name='medicalRecipeName' required>
                    <Input placeholder='请输入' />
                  </Item>
                  <Item name='recommended' valuePropName='checked'>
                    <Checkbox>推荐药方</Checkbox>
                  </Item>
                </Space>
              </Item>
              <Item name='prescriptionDrugsType' label='便捷购药分类' required>
                <ArrSelect
                  options={types}
                  style={{ width: '200px' }}
                  onChange={v => {
                    queryCates(v as string);
                    form.setFieldsValue({ storageCode: undefined });
                    const storageType = types.find(item => item.value === v)
                      ?.storageType;
                    queryStorageType(storageType as number);
                    queryDictInfo(v === Type.CM_FORMULA ? 'cn' : 'en');
                    // setHideAnswer(v === Type.MAKEUP_CONSUMABLES);
                  }}
                />
              </Item>
              <Item name='storageCode' label='药房' required>
                <ArrSelect
                  options={pharmacyList.map(v => {
                    return {
                      ...v,
                      value: v.storageCode,
                      label: v.storageName
                    };
                  })}
                  style={{ width: '200px' }}
                  onChange={v => {
                    const obj = pharmacyList.find(item => {
                      return item.storageCode === v;
                    });
                    setPharmacyInfo(obj);
                  }}
                />
              </Item>
              <Item label='药方类型' style={{ marginBottom: 0 }}>
                <Space>
                  <Item name='medicalRecipeTypeId'>
                    <ArrSelect
                      options={cates}
                      style={{
                        width: '200px',
                        maxHeight: '300px'
                      }}
                    />
                  </Item>
                  <Item>
                    <Button
                      type='link'
                      onClick={() => {
                        history.push('/easy-meds/manage-cates');
                      }}>
                      维护分类
                    </Button>
                  </Item>
                </Space>
              </Item>
              <Item name='scopeAction' label='适用范围'>
                <Radio.Group options={sutiRange} />
              </Item>
              <Item noStyle dependencies={['scopeAction']} required>
                {({ getFieldValue }) => {
                  const res = getFieldValue('scopeAction');
                  return res === PrescType.CUSTOM ? (
                    <Item name='doctorCodes' label='选择医生'>
                      <FormItemSelectDoctor />
                    </Item>
                  ) : null;
                }}
              </Item>
              <Item
                name='medicalRecipeImage'
                label='药方图片'
                required
                extra='支持图片格式：jpg、png、jpeg，大小不超过2M'>
                <UploadImg
                  length={1}
                  maxSize={2 * 1024 * 1024}
                  accept='.jpg,.png,.jpeg'
                />
              </Item>
              <Item name='briefIntroduction' required label='药方简介'>
                <Input.TextArea />
              </Item>
              <Item name='diagnosis' label='西药诊断' required>
                <FormItemDict groupCode='mainDiagnosis' />
              </Item>
              <Item noStyle dependencies={['prescriptionDrugsType']}>
                {({ getFieldValue }) => {
                  const res = getFieldValue('prescriptionDrugsType');
                  return [Type.CM_FORMULA].includes(res) ? (
                    <Item name='cnDiagnosis' label='中药诊断' required>
                      <FormItemDict groupCode='cnDiagnosis' />
                    </Item>
                  ) : null;
                }}
              </Item>
              <Item noStyle dependencies={['prescriptionDrugsType']}>
                {({ getFieldValue }) => {
                  const res = getFieldValue('prescriptionDrugsType');
                  return [Type.CM_FORMULA].includes(res) ? (
                    <Item name='cnDialectics' label='中医辩证' required>
                      <FormItemDict groupCode='cnDialectics' />
                    </Item>
                  ) : null;
                }}
              </Item>
              {/* <Item noStyle dependencies={['prescriptionDrugsType']}>
                {({ getFieldValue }) => {
                  const res = getFieldValue('prescriptionDrugsType');
                  return [Type.WESTERN_MEDICINE].includes(res) ? (
                    <Item
                      name='searchStatue'
                      label='是否使用常用方'
                      extra='选择常用方，可直接选取医生常用方并配置用药问卷'>
                      <Radio.Group>
                        <Radio value={false}>否</Radio>
                        <Radio value={true}>是</Radio>
                      </Radio.Group>
                    </Item>
                  ) : null;
                }}
              </Item> */}
              <Item noStyle dependencies={['searchStatue']}>
                {({ getFieldValue }) => {
                  const res = getFieldValue('searchStatue');
                  return res ? (
                    <Item name='_search' label='搜索常用方'>
                      <FormItemSelectRecipe />
                    </Item>
                  ) : null;
                }}
              </Item>
              <Item noStyle dependencies={['prescriptionDrugsType']}>
                {({ getFieldValue }) => {
                  const res = getFieldValue('prescriptionDrugsType');
                  const labelName =
                    res === Type.MAKEUP_CONSUMABLES ? '商品名称' : '药品名称';
                  return [Type.CM_FORMULA].includes(res) ? (
                    <Item
                      name='medicalRecipeReqDrugs'
                      required
                      label={labelName}>
                      <FormItemPrescription
                        districtCode={pharmacyInfo?.districtCode}
                        storageCode={pharmacyInfo?.storageCode}
                        type={types.find(v => v.value === res)?.num}
                      />
                    </Item>
                  ) : (
                    <Item
                      name='medicalRecipeReqDrugs'
                      required
                      label={labelName}>
                      <FormItemWestPrescription
                        districtCode={pharmacyInfo?.districtCode}
                        storageCode={pharmacyInfo?.storageCode}
                        dictInfo={dictInfo}
                        type={types.find(v => v.value === res)?.num}
                        name={labelName}
                      />
                    </Item>
                  );
                }}
              </Item>
              <Item
                name='totalPrice'
                extra='注：药方费用由药方药品计算生成，保留小数点后两位。'
                required
                label='药品费用'>
                <Input disabled placeholder='药品费用' />
              </Item>
              <Item name='maxBuyQuantity' required label='购买上限'>
                <InputNumber placeholder='请输入' />
              </Item>
              <Item name='approvedDoctorId' label='开方医生'>
                <ArrSelect options={doctorList} />
              </Item>
              <Item name='approvedPharmacistId' label='审方药师'>
                <ArrSelect options={pharmacistList} />
              </Item>
              {/* 中药 -> 费用 */}
              <Item noStyle dependencies={['prescriptionDrugsType']}>
                {({ getFieldValue }) => {
                  const res = getFieldValue('prescriptionDrugsType');
                  return [Type.CM_FORMULA].includes(res) ? (
                    <>
                      <Item
                        name='inquiryStatus'
                        label='是否有辩证论治费'
                        required>
                        <Radio.Group>
                          <Radio value={false}>否</Radio>
                          <Radio value={true}>是</Radio>
                        </Radio.Group>
                      </Item>
                      <Item noStyle dependencies={['inquiryStatus']}>
                        {({ getFieldValue }) => {
                          const res = getFieldValue('inquiryStatus');
                          return res ? (
                            <Item
                              required
                              name='inquiryFee'
                              label='辩证论治费'
                              extra='注：设置后，患者选择本药方时需要支付医生问诊费用'>
                              <InputNumber
                                placeholder='请输入'
                                addonAfter='元'
                              />
                            </Item>
                          ) : null;
                        }}
                      </Item>
                      <Item
                        name='agentFryingStatus'
                        label='是否可代煎'
                        required>
                        <Radio.Group>
                          <Radio value={false}>否</Radio>
                          <Radio value={true}>是</Radio>
                        </Radio.Group>
                      </Item>
                      <Item noStyle dependencies={['agentFryingStatus']}>
                        {({ getFieldValue }) => {
                          const res = getFieldValue('agentFryingStatus');
                          return res ? (
                            <Item
                              required
                              name='agentFryingFee'
                              label='代煎费'
                              extra='注：代煎费由医院自主定价，为每副药代煎的单价'>
                              <InputNumber
                                placeholder='请输入'
                                addonAfter='元/副'
                              />
                            </Item>
                          ) : null;
                        }}
                      </Item>
                      <Item label='用法用量' required>
                        <Space>
                          <Item name='medicalRecipeUsage' noStyle>
                            <ArrSelect
                              style={{
                                width: 200
                              }}
                              options={dictInfo.administrationList.map(v => {
                                return {
                                  ...v,
                                  value: v.label
                                };
                              })}
                              placeholder='请选择用法'
                            />
                          </Item>
                          <Item name='frequency' noStyle>
                            <ArrSelect
                              style={{
                                width: 200
                              }}
                              options={dictInfo.rateInfo.map(v => {
                                return {
                                  ...v,
                                  value: v.label
                                };
                              })}
                              placeholder='请选择用量'
                            />
                          </Item>
                        </Space>
                      </Item>
                    </>
                  ) : null;
                }}
              </Item>
              {/* 西药 -> 问诊费 */}
              <Item dependencies={['prescriptionDrugsType']} noStyle>
                {({ getFieldValue }) => {
                  const res = getFieldValue('prescriptionDrugsType');
                  return [
                    Type.WESTERN_MEDICINE,
                    Type.MAKEUP_CONSUMABLES
                  ].includes(res) ? (
                    <>
                      <Item name='inquiryStatus' label='是否有问诊费' required>
                        <Radio.Group>
                          <Radio value={false}>否</Radio>
                          <Radio value={true}>是</Radio>
                        </Radio.Group>
                      </Item>
                      <Item noStyle dependencies={['inquiryStatus']}>
                        {({ getFieldValue }) => {
                          const res = getFieldValue('inquiryStatus');
                          return res ? (
                            <Item
                              required
                              name='inquiryFee'
                              label='问诊费'
                              extra='注：设置后，患者选择本药方时需要支付医生问诊费用'>
                              <InputNumber
                                placeholder='请输入问诊费'
                                addonAfter='元'
                              />
                            </Item>
                          ) : null;
                        }}
                      </Item>
                    </>
                  ) : null;
                }}
              </Item>
              <Item name='status' label='药方状态' required>
                <Radio.Group>
                  <Radio value={1}>启用</Radio>
                  <Radio value={0}>停用</Radio>
                </Radio.Group>
              </Item>
              <Item name='introduction' label='药方介绍' required>
                <Editor className={styles.editor} />
              </Item>
            </Form>
          )
        },
        {
          title: '问答问题（单选）',
          hidden: hideAnswer,
          children: (
            <Form
              style={{
                width: '800px',
                margin: '0 auto'
              }}>
              <Item>
                <Answers ref={answerRef} />
              </Item>
            </Form>
          )
        },
        {
          title: null,
          children: (
            <Space>
              <Button
                onClick={() => {
                  history.goBack();
                }}>
                取消
              </Button>
              <Button type='primary' onClick={submit}>
                保存
              </Button>
            </Space>
          )
        }
      ]}
    />
  );
};

export default EditPresc;
