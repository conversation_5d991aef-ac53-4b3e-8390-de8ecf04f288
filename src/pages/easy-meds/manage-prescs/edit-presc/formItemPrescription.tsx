import React, { useState } from 'react';
import { Space, Row, Col, Button, InputNumber, Form, message } from 'antd';
import { DeleteOutlined, FormOutlined } from '@ant-design/icons';
import { ArrSelect, useModal, getPrice } from 'parsec-admin';
import apis from '../apis';
import { calcGetPrice } from '@src/utils/tools';

interface Value {
  drugCode?: string; //对应处方流转的drugCode
  drugName?: string; //药品名
  units?: string; //单位
  drugSpec?: string; //药品规格
  usage?: string; //用法
  dosage?: string; //用量
  quantity?: number; //数量
  price?: number;
}

interface Props {
  value?: Value[];
  onChange?: (value?: Value[]) => void;
  type?: string;
  storageCode?: string;
  districtCode?: string;
}

const FormItemPrescription = (props: Props) => {
  const { value, onChange, type, storageCode, districtCode } = props;
  const [list, setList] = useState<Value[]>([]);
  const [form] = Form.useForm();
  const [drugs, setDrugs] = useState<any[]>([]);

  React.useEffect(() => {
    if (value) {
      setList(value);
    }
  }, [value]);
  React.useEffect(() => {
    if (!type || !storageCode || !districtCode) return;
    apis.getDrugList
      .request({
        numPerPage: 999,
        pageNum: 1,
        drugType: type,
        storageCode,
        districtCode
      })
      .then(res => {
        const list = res?.data?.recordList?.map(v => {
          return {
            value: v.drugName,
            label: v.drugName,
            code: v.drugCode,
            unitPrice: v.unitPrice
          };
        });
        setDrugs(list as any[]);
      });
  }, [type, storageCode, districtCode]);
  const openModal = useModal(
    (params: { type: 'add' | 'edit'; index?: number }) => {
      return {
        title: '选择药品',
        form,
        onSubmit: async () => {
          const res = await form.validateFields();
          const newRes = {
            ...res,
            price: Number(calcGetPrice(res?.price * 100))
          };
          const newList =
            params.type === 'edit'
              ? list.map((v, i) => {
                  if (i === params.index) {
                    return newRes;
                  } else {
                    return v;
                  }
                })
              : [...list, newRes];
          onChange?.(newList);
        },
        myFormProps: {
          formProps: {
            onValuesChange(_, values) {
              if (values?.drugCode && values?.quantity) {
                const unitPrice = drugs.find(
                  item => item.code === values.drugCode
                )?.unitPrice;
                form.setFieldsValue({
                  price: Number(
                    calcGetPrice((unitPrice || 0) * values.quantity)
                  )
                });
              }
            }
          }
        },
        items: [
          {
            name: 'drugCode',
            render: false
          },
          {
            name: 'drugName',
            label: '药品名称',
            required: true,
            render: () => {
              return (
                <ArrSelect
                  options={drugs}
                  onChange={v => {
                    const obj = drugs.find(item => item.value === v);
                    form.setFieldsValue({
                      drugCode: obj?.code
                    });
                  }}
                />
              );
            }
          },
          {
            name: 'quantity',
            label: '数量',
            required: true,
            render: () => {
              return (
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  placeholder='请输入数量'
                />
              );
            }
          },
          {
            name: 'price',
            label: '价格',
            required: true,
            render: () => {
              return (
                <InputNumber
                  disabled
                  style={{ width: '100%' }}
                  placeholder='价格'
                  precision={2}
                />
              );
            }
          }
        ]
      };
    },
    [list]
  );

  const handleDelete = (code?: string) => {
    if (!code) return;
    const newList = list.filter(v => v.drugCode !== code);
    setList(newList);
    onChange?.(newList);
  };

  return (
    <Space direction='vertical' style={{ width: '100%' }}>
      {list.map((v, i) => {
        return (
          <Row key={i}>
            <Col span={6}>药品名称：{`${v?.drugName}`}</Col>
            <Col span={3}>数量：{`${v?.quantity}`}</Col>
            <Col span={5}>价格（元）：{`${getPrice(v?.price)}`}</Col>
            <Col span={4}>
              <Space>
                <DeleteOutlined
                  onClick={() => handleDelete(v.drugCode)}
                  style={{ color: 'red' }}
                />
                <FormOutlined
                  style={{ color: '#2780da' }}
                  onClick={() => {
                    form.setFieldsValue({ ...v });
                    openModal({ type: 'edit', index: i });
                  }}
                />
              </Space>
            </Col>
          </Row>
        );
      })}
      <Button
        onClick={() => {
          if (!districtCode && !storageCode) {
            message.warn('请选择药房');
            return;
          }
          openModal({ type: 'add' });
        }}
        type='primary'>
        添加药品
      </Button>
    </Space>
  );
};

export default FormItemPrescription;
