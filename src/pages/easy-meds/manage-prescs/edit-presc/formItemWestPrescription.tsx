import React, { useState } from 'react';
import { Space, Button, InputNumber, Form, Table, message, Select } from 'antd';
import { DeleteOutlined, FormOutlined } from '@ant-design/icons';
import { ArrSelect, useModal, getPrice } from 'parsec-admin';
import apis from '../apis';
import { dosageUnitData } from './utils';
import { DictInfo } from './index';

interface Value {
  drugCode?: string; //对应处方流转的drugCode
  drugName?: string; //药品名
  units?: string; //单位
  drugSpec?: string; //药品规格
  medicalRecipeUsage?: string; //用法
  medicalRecipeDosage?: string; //用量
  medicalRecipeDosageUnit?: string;
  quantity?: number; //数量
  price?: number;
  unitPrice?: number;
  frequency?: string;
}

interface Props {
  value?: Value[];
  name: string;
  onChange?: (value?: Value[]) => void;
  type?: string;
  storageCode?: string;
  districtCode?: string;
  dictInfo: DictInfo;
}
const { Item } = Form;
const FormItemWestPrescription = (props: Props) => {
  const {
    value,
    onChange,
    name,
    type,
    storageCode,
    districtCode,
    dictInfo
  } = props;
  const [list, setList] = useState<Value[]>([]);
  const [form] = Form.useForm();
  const [drugs, setDrugs] = useState<any[]>([]);

  React.useEffect(() => {
    if (value) {
      setList(value);
    }
  }, [value]);
  React.useEffect(() => {
    if (!type || !storageCode || !districtCode) return;
    apis.getDrugList
      .request({
        numPerPage: 999,
        pageNum: 1,
        drugType: type,
        storageCode,
        districtCode
      })
      .then(res => {
        const list = res?.data?.recordList?.map(v => {
          return {
            value: v.drugName,
            label: v.drugName,
            code: v.drugCode,
            unitPrice: v.unitPrice,
            administration: v?.administration,
            frequency: v?.frequency,
            dosageUnit: v?.dosageUnit
          };
        });
        setDrugs(list as any[]);
      });
  }, [type, storageCode, districtCode]);
  const openModal = useModal(
    (params: { type: 'add' | 'edit'; index?: number }) => {
      return {
        title: `选择${name.slice(0, 2)}`,
        form,
        myFormProps: {
          formProps: {
            onValuesChange(_, values) {
              if (values?.drugCode && values?.quantity) {
                let unitPrice: number | undefined = 0;
                let search = false;
                const listItem = list.find(
                  item => item.drugCode === values.drugCode
                );
                // 这里的 unitPrice 的单位是**元**
                // 先找原来的
                if (listItem) {
                  // 看是否存在单价，如果没有，就自己计算
                  unitPrice = listItem?.unitPrice;
                  search = true;
                }
                // 若没有找到
                if (!search) {
                  unitPrice = drugs.find(item => item.code === values.drugCode)
                    ?.unitPrice;
                }
                form.setFieldsValue({
                  price: getPrice((unitPrice || 0) * values.quantity * 100)
                });
              }
            }
          }
        },
        onSubmit: async () => {
          const res = await form.validateFields();
          const newRes = { ...res, price: res?.price * 100 };
          const newList =
            params.type === 'edit'
              ? list.map((v, i) => {
                  if (i === params.index) {
                    return newRes;
                  } else {
                    return v;
                  }
                })
              : [...list, newRes];
          onChange?.(newList);
        },
        items: [
          {
            name: 'drugCode',
            render: false
          },
          {
            name: 'drugName',
            label: name,
            required: true,
            render: () => {
              return (
                <ArrSelect
                  options={drugs}
                  onChange={v => {
                    const obj = drugs.find(item => item.value === v);
                    form.setFieldsValue({
                      drugCode: obj?.code,
                      medicalRecipeUsage: obj?.administration,
                      frequency: obj?.frequency,
                      medicalRecipeDosageUnit: obj?.dosageUnit
                    });
                  }}
                />
              );
            }
          },
          {
            name: 'medicalRecipeUsage',
            label: '用法',
            required: true,
            render: () => {
              return (
                <ArrSelect
                  options={dictInfo.administrationList.map(v => {
                    return {
                      ...v,
                      value: v.label
                    };
                  })}
                />
              );
            }
          },
          {
            label: (
              <Space>
                <span style={{ color: '#ff4d4f', fontSize: '16px' }}>*</span>
                用量
              </Space>
            ),
            formItemProps: {
              style: { marginBottom: 0 }
            },
            render: () => {
              return (
                <Space>
                  <Item
                    name='medicalRecipeDosage'
                    rules={[{ required: true, message: '请输入' }]}>
                    <InputNumber addonBefore='每次' placeholder='请输入' />
                  </Item>
                  <Item
                    name='medicalRecipeDosageUnit'
                    rules={[{ required: true, message: '请选择' }]}>
                    <Select
                      style={{ width: 100 }}
                      placeholder='请选择'
                      options={dosageUnitData.map(v => {
                        return {
                          ...v,
                          value: v.label
                        };
                      })}
                    />
                  </Item>
                </Space>
              );
            }
          },
          {
            name: 'frequency',
            label: '频次',
            required: true,
            render: () => {
              return (
                <ArrSelect
                  options={dictInfo.rateInfo.map(v => {
                    return {
                      ...v,
                      value: v.label
                    };
                  })}
                />
              );
            }
          },
          {
            name: 'quantity',
            label: '数量',
            required: true,
            render: () => {
              return (
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  placeholder='请输入数量'
                />
              );
            }
          },
          {
            name: 'price',
            label: '价格',
            required: true,
            render: () => {
              return (
                <InputNumber
                  disabled
                  style={{ width: '100%' }}
                  placeholder='价格'
                />
              );
            }
          }
        ]
      };
    },
    [name]
  );

  const handleDelete = (code?: string) => {
    if (!code) return;
    const newList = list.filter(v => v.drugCode !== code);
    setList(newList);
    onChange?.(newList);
  };

  return (
    <Space direction='vertical' style={{ width: '100%' }}>
      {list.length ? (
        <Table
          dataSource={list}
          pagination={false}
          rowKey='drugCode'
          size='small'
          columns={[
            {
              title: '药品名称：',
              dataIndex: 'drugName'
            },
            {
              title: '用法',
              dataIndex: 'medicalRecipeUsage'
            },
            {
              title: '用量',
              dataIndex: 'medicalRecipeDosage',
              render: (_, record) => {
                return (
                  <span>
                    每次{record?.medicalRecipeDosage}
                    {record?.medicalRecipeDosageUnit}
                  </span>
                );
              }
            },
            {
              title: '频次',
              dataIndex: 'frequency'
            },
            {
              title: '数量',
              dataIndex: 'quantity'
            },
            {
              title: '价格',
              dataIndex: 'price',
              render: v => {
                return v / 100;
              }
            },
            {
              title: '操作',
              dataIndex: 'action',
              render: (_, record: any, index) => {
                return (
                  <Space>
                    <DeleteOutlined
                      onClick={() => handleDelete(record.drugCode)}
                      style={{ color: 'red' }}
                    />
                    <FormOutlined
                      style={{ color: '#2780da' }}
                      onClick={() => {
                        form.setFieldsValue({
                          ...record,
                          price: record?.price / 100
                        });
                        openModal({ type: 'edit', index });
                      }}
                    />
                  </Space>
                );
              }
            }
          ]}></Table>
      ) : null}
      <Button
        onClick={() => {
          if (!districtCode && !storageCode) {
            message.warn('请选择药房');
            return;
          }
          openModal({ type: 'add' });
        }}
        type='primary'>
        添加{name.slice(0, 2)}
      </Button>
    </Space>
  );
};

export default FormItemWestPrescription;
