import createApiHooks from 'create-api-hooks';
import {
  ApiResponse,
  ListApiResponseData,
  ListApiRequestParams
} from '@apiHooks';
import { request } from 'parsec-admin';

interface CnDiagnosis {
  name: '@cname'; //name
  code: '@word(5)'; //code
}

export interface MedicalRecipeReqDrugs {
  drugCode: '@natural'; //对应处方流转的drugCode
  drugName: '@cname'; //药品名
  units: '@word(5)'; //单位
  drugSpec: '@word(5)'; //药品规格
  usage: '@word(5)'; //用法
  dosage: '@word(5)'; //用量
  quantity: '@natural'; //数量
  price: number;
}

interface Answers {
  id: '@natural'; //主键
  answerContent: '@word(5)'; //答案内容
  allow: '@natural'; //是否允许开方 1:允许 2: 不允许
}

interface MedicalRecipeQuestionReqs {
  id: '@natural'; //主键
  questionContent: '@word(5)'; //问题内容
  answerReqs: Answers[]; //问题答案
}

interface List {
  id: number; //id
  hisId: '@natural'; //医院名id
  hisName: '@cname'; //医院名称
  medicalRecipeName: '@cname'; //药方名
  recommended: '@natural'; //推荐方, 0:不是 1:是
  prescriptionDrugsType: '@word(5)'; //快捷购药药品类别(西药(WESTERN_MEDICINE),中药方剂(CM_FORMULA) , 妆品及其耗材(MAKEUP_CONSUMABLES)
  medicalRecipeTypeName: '@cname'; //药方类型名,便于做分组查询
  cnDiagnosis: CnDiagnosis[];
  briefIntroduction: '@word(5)'; //简介
  totalFee: '@natural'; //总费用
  status: number; //启停状态
  introduction: '@word(5)'; //药方介绍
  updateTime: '@datetime'; //操作时间
  operator: '@word(5)'; //操作人
}

interface Data {
  id: '@natural'; //id
  hisId: '@natural'; //医院名id
  hisName: '@cname'; //医院名称
  medicalRecipeName: '@cname'; //药方名
  recommended: number; //推荐方, 0:不是 1:是
  sort: '@natural'; //sort
  prescriptionDrugsType: any; //快捷购药药品类别(西药(WESTERN_MEDICINE),中药方剂(CM_FORMULA) , 妆品及其耗材(MAKEUP_CONSUMABLES)
  medicalRecipeTypeId: '@natural'; //药方类型id
  medicalRecipeTypeName: '@cname'; //药方类型名,便于做分组查询
  scopeAction: ''; //作用范围, 平台(PLATFORM), 大区(REGION),医生(REGIONAL_DOCTOR)
  //作用范围区域
  regionIds: string[];
  doctorCodes: { doctorName: string; doctorCode: string }[];
  medicalRecipeImage: '@word(5)'; //药方图片
  briefIntroduction: '@word(5)'; //简介
  diagnosis: CnDiagnosis[];
  cnDiagnosis: CnDiagnosis[];
  cnDialectics: CnDiagnosis[];
  medicalRecipeReqDrugs: MedicalRecipeReqDrugs[];
  maxBuyQuantity: '@natural'; //maxBuyQuantity
  medicalRecipeUsage: '@word(5)'; //用法
  medicalRecipeDosage: '@word(5)'; //用量
  approvedDoctorId: '@natural'; //approvedDoctorId
  approvedDoctorName: '@natural'; //approvedDoctorName
  approvedPharmacistId: '@natural'; //approvedPharmacistId
  approvedPharmacistName: '@natural'; //approvedPharmacistName
  inquiryFee: number; //辩证论治费(单位:分)
  agentFryingFee: number; //待煎费用
  status: number; //启停状态
  introduction: '@word(5)'; //introduction
  medicalRecipeQuestionResList: MedicalRecipeQuestionReqs[];
  districtCode: string;
  districtName: string;
  storageCode: string;
  storageName: string;
}

export default {
  queryList: createApiHooks(
    (
      params: ListApiRequestParams & {
        medicalRecipeName?: string;
        prescriptionDrugsType?: string;
      }
    ) =>
      request.get<ListApiResponseData<List>>(
        '/mch/prescription/medicalRecipe/page',
        { params }
      )
  ),
  add: createApiHooks((params: Data) =>
    request.post<ApiResponse<any>>(
      `/mch/prescription/medicalRecipe/add`,
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  adit: createApiHooks((params: Data) =>
    request.put<ApiResponse<any>>(
      `/mch/prescription/medicalRecipe/update`,
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  del: createApiHooks((id: number) =>
    request.delete<ApiResponse<any>>(
      `/mch/prescription/medicalRecipe/delete/${id}`
    )
  ),
  detail: createApiHooks((params: { id: string }) =>
    request.get<ApiResponse<Data>>(
      `/mch/prescription/medicalRecipe/queryMedicalRecipeDetails/${params.id}`
    )
  ),
  changeStatus: createApiHooks(
    (params: { medicalRecipeId: number; status: 1 | 0 }) =>
      request.post<ApiResponse<any>>(
        `/mch/prescription/medicalRecipe/modifyState`,
        params,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
  ),
  changeRecommand: createApiHooks(
    (params: { medicalRecipeId: number; recommend: 1 | 0 }) =>
      request.post<ApiResponse<any>>(
        `/mch/prescription/medicalRecipe/modifyRecommend`,
        params,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
  ),
  getDrugList: createApiHooks(
    (
      params: ListApiRequestParams & {
        drugType?: string;
        storageCode?: string;
        districtCode?: string;
      }
    ) =>
      request.get<ListApiResponseData<any>>(
        `/mch/prescription/medicalRecipe/drugList`,
        {
          params
        }
      )
  ),
  查询字典列表: createApiHooks(
    (
      params: { groupCode: string; dictValue?: string } & ListApiRequestParams
    ) =>
      request.get<
        ListApiResponseData<{
          id: 1; //Id
          hisId: 'query.hisId'; // 医院id
          dictGroupCode: 'query.groupCode'; // 字典组code
          dictKey: '@word'; // 字典键
          dictValue: {
            value: '@word';
          }; // 字典值
          sortNo: '@natural(1, 100)'; // 排序号 数值越大越靠前
        }>
      >(`/kaiqiao/his/dictItem/page/${params.groupCode}`, { params })
  ),
  便捷购药分页查询经典处方: createApiHooks((params: ListApiRequestParams) =>
    request.get<
      ListApiResponseData<{
        id: '@natural'; //id
        doctorName: '@cname'; //医生姓名
        hospitalName: '@cname'; //医院名称
        title: '@word(5)'; //标题
        phone: '@word(5)'; //手机号码
        templateName: '@cname'; //处方名称
        diagnosis: '@word(5)'; //诊断
      }>
    >(`/mch/prescription/recipe-template/quickBuyMcRecipePage`, { params })
  ),
  选择药房: createApiHooks((storageType: number) =>
    request.get<
      ApiResponse<
        {
          districtName: string;
          districtCode: string;
          storageList: {
            storageCode: string;
            storageName: string;
          }[];
        }[]
      >
    >(`/mch/his/hospitalDistrict/drugstore?storageType=${storageType}`)
  ),
  apiGetMchHisRegionList: createApiHooks((params: ListApiRequestParams) =>
    request.get<ListApiResponseData<any[]>>('/mch/his/region/list', {
      params
    })
  ),
  getDictInfo: createApiHooks((params: { dictGroup: string }) =>
    request.get<ListApiResponseData<any[]>>(
      `/kaiqiao/his/dictItem/page/${params.dictGroup}`,
      {
        params: {
          pageNum: 1,
          numPerPage: 1000
        }
      }
    )
  )
};
