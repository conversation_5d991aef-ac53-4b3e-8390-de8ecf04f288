import createApiHooks from 'create-api-hooks';
import { ApiResponse } from '@apiHooks';
import { request } from 'parsec-admin';
import env from '@src/configs/env';

export interface Data {
  medicalPersonnelId: string; //medicalPersonnelId
  type: number; //类型 1:医生 2:医师
  name: string; //医生名称
}

export default {
  配置人员: createApiHooks((params: { recipePersonList: Data[] }) =>
    request.post<ApiResponse<any>>(
      '/mch/prescription/medicalRecipeConfig/person',
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  获取人员配置: createApiHooks(() =>
    request.get<ApiResponse<{ recipePersonList: Data[] }>>(
      `/mch/prescription/medicalRecipeConfig/getMedicalRecipePerson/${env.hisId}`
    )
  ),
  配置知情同意书: createApiHooks((params: { content: string }) =>
    request.post<ApiResponse<any>>(
      `/mch/prescription/medicalRecipeConfig/informedConsentForm`,
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  获取知情同意书: createApiHooks(() =>
    request.get<ApiResponse<{ content: string }>>(
      `/mch/prescription/medicalRecipeConfig/getMedicalRecipeInformedConsentForm/${env.hisId}`
    )
  ),
  查询医生列表: createApiHooks((type: number) =>
    request.get<ApiResponse<{ id: number; name: string; doctorId: string }[]>>(
      `/mch/his/doctorMain/list?type=${type}`
    )
  )
};
