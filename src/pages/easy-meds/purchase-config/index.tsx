import { Button, Form, Tabs, message } from 'antd';
import { <PERSON><PERSON><PERSON><PERSON>, Editor, ArrSelect } from 'parsec-admin';
import apis from './apis';
import React, { useEffect } from 'react';
import { useSelectDoctor } from '@src/customHooks/useSelectDoctor';

const { Item } = Form;

const PurchaseConfig: React.FC = () => {
  return (
    <CardLayout title={'购药配置'}>
      <ConfTabs />
    </CardLayout>
  );
};

const ConfTabs: React.FC = () => {
  const [form1] = Form.useForm();
  const [form2] = Form.useForm();
  const [tab, setTab] = React.useState('1');
  const { doctorList, pharmacistList } = useSelectDoctor();
  const success = () => {
    message.success('保存成功');
  };

  const query = React.useCallback(
    async (tab: string) => {
      if (tab === '1') {
        const res = await apis.获取人员配置.request();
        const data = res?.data?.recipePersonList;
        const name1Obj = data?.find(v => v.type === 1);
        const name2Obj = data?.find(v => v.type === 2);
        form1.setFieldsValue({
          name1: { value: name1Obj?.medicalPersonnelId, label: name1Obj?.name },
          name2: { value: name2Obj?.medicalPersonnelId, label: name2Obj?.name }
        });
      }
      if (tab === '2') {
        const res = await apis.获取知情同意书.request();
        form2.setFieldsValue({ content: res?.data?.content });
      }
    },
    [form1, form2]
  );

  useEffect(() => {
    query(tab);
  }, [tab, query]);

  return (
    <Tabs
      defaultActiveKey='1'
      activeKey={tab}
      onChange={v => {
        setTab(v);
      }}>
      <Tabs.TabPane tab='人员设置' key='1'>
        <Form
          form={form1}
          onFinish={async () => {
            const res = await form1.validateFields();
            const params = {
              recipePersonList: [
                {
                  medicalPersonnelId: res?.name1?.value,
                  name: res?.name1?.label,
                  type: 1
                },
                {
                  medicalPersonnelId: res?.name2?.value,
                  name: res?.name2?.label,
                  type: 2
                }
              ]
            };
            await apis.配置人员.request(params);
            success();
          }}>
          <Item name='name1' label='开方医生'>
            <ArrSelect
              style={{ width: 180 }}
              labelInValue
              options={doctorList}
            />
          </Item>
          <Item name='name2' label='审方药师'>
            <ArrSelect
              style={{ width: 180 }}
              labelInValue
              options={pharmacistList}
            />
          </Item>
          <Item>
            <Button type='primary' htmlType='submit'>
              保存
            </Button>
          </Item>
        </Form>
      </Tabs.TabPane>
      <Tabs.TabPane tab='知情同意书' key='2'>
        <Form
          form={form2}
          onFinish={async () => {
            const res = await form2.validateFields();
            await apis.配置知情同意书.request({ ...res });
            success();
          }}>
          <Item name='content'>
            <Editor />
          </Item>
          <Form.Item style={{ textAlign: 'right' }}>
            <Button type='primary' htmlType='submit'>
              提交
            </Button>
          </Form.Item>
        </Form>
      </Tabs.TabPane>
    </Tabs>
  );
};

export default PurchaseConfig;
