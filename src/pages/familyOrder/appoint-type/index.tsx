import React, { useMemo } from 'react';
import {
  ActionsWrap,
  ArrSelect,
  handleSubmit,
  LinkButton,
  useModal
} from 'parsec-admin';
import useApi from '@pages/order/api';
import MyTableList from '@components/myTableList';
import { Button, Input } from 'antd';
import dayjs from 'dayjs';
import { PlusOutlined } from '@ant-design/icons';

export default () => {
  const switchModal = useModal(({ id }) => {
    return {
      onSubmit: values => {
        return handleSubmit(() => {
          return id
            ? useApi.更新预约类型.request(values)
            : useApi.添加预约类型.request(values);
        });
      },
      title: id ? '编辑' : '添加',
      items: [
        { name: 'id', render: false },
        {
          name: 'serviceName',
          label: '预约类型',
          render: <Input placeholder='请输入预约类型' />
        },
        {
          name: 'status',
          label: '状态',
          render: (
            <ArrSelect
              options={[
                { label: '禁用', value: 0 },
                { label: '启用', value: 1 }
              ]}
              placeholder='请选择状态'
            />
          )
        }
      ]
    };
  });
  return (
    <MyTableList
      tableTitle={'预约类型管理'}
      action={
        <Button
          type={'default'}
          icon={<PlusOutlined />}
          onClick={() => switchModal()}>
          添加
        </Button>
      }
      getList={({ params }: { params: any }) => {
        return useApi.预约类型分页.request(params);
      }}
      columns={useMemo(
        () => [
          {
            title: '序号',
            width: 100,
            dataIndex: 'id'
          },
          {
            title: '预约类型',
            width: 100,
            dataIndex: 'serviceName',
            search: true
          },
          {
            title: '创建人',
            width: 100,
            dataIndex: 'createUser'
          },
          {
            title: '创建时间',
            width: 200,
            dataIndex: 'createTime',
            render: v => dayjs(v).format('YYYY/MM/DD HH:mm')
          },
          {
            title: '启用状态',
            width: 200,
            dataIndex: 'status',
            render: v => (v ? '启用' : '禁用'),
            search: (
              <ArrSelect
                options={[
                  { label: '禁用', value: 0 },
                  { label: '启用', value: 1 }
                ]}
              />
            )
          },

          {
            title: '操作',
            width: 150,
            fixed: 'right',
            render: (v, record: any) => {
              return (
                <ActionsWrap>
                  <LinkButton
                    onClick={() => {
                      switchModal({
                        ...record
                      });
                    }}>
                    编辑
                  </LinkButton>
                </ActionsWrap>
              );
            }
          }
        ],
        [switchModal]
      )}
    />
  );
};
