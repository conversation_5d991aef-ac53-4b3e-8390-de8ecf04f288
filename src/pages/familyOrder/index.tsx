import useApi from '@pages/order/api';
import React, { useEffect, useMemo, useState } from 'react';
import {
  actionConfirm,
  ArrSelect,
  DayRangePicker,
  LinkButton,
  useModal
} from 'parsec-admin';
import MyTableList from '@components/myTableList';
import { getAddressOptions, Space, TransferChange } from '@kqinfo/ui';
import { Cascader, Row } from 'antd';
// import { saveAs } from 'file-saver';

const statusEnum = [
  { value: -1, label: '全部' },
  { value: 1, label: '已处理' },
  { value: 0, label: '未处理' }
];

export default () => {
  const [addressOptions, setAddressOptions] = useState<any[]>([]);

  // const params = useRef<Record<string, any>>({});

  const switchModalVisible = useModal((record: any) => {
    return {
      title: '查看详情',
      children: (
        <div>
          <Row style={{ color: 'red', fontSize: '20px', fontWeight: 'bold' }}>
            状态:{statusEnum?.find(i => i.value === record.status)?.label}
          </Row>
          <Row style={{ margin: '10px 0' }}>
            <div style={{ width: '50%' }}>客户姓名:{record.clientName}</div>
            <div style={{ width: '50%' }}>联系电话:{record.phone}</div>
          </Row>
          <Row style={{ margin: '10px 0' }}>
            <div style={{ width: '50%' }}>提交表单时间:{record.createTime}</div>
            <div style={{ width: '50%' }}>服务区域:{record.area || '暂无'}</div>
          </Row>
          <Row style={{ margin: '10px 0' }}>
            <div>
              预约类型:
              {record.serviceType}
            </div>
          </Row>
          <Row style={{ margin: '10px 0' }}>
            <div>表单备注:{record.remark}</div>
          </Row>
        </div>
      )
    };
  }, []);

  useEffect(() => {
    getAddressOptions().then(options => {
      setAddressOptions(options);
    });
  }, []);

  return (
    <MyTableList
      tableTitle='预约列表'
      getList={({ params }) => {
        return useApi.用户预约记录分页.request({
          ...params,
          area: (params as any).area?.map(i => i.label)?.join('-')
        });
      }}
      exportExcelButton={true}
      // 预留的导出
      // action={
      //   <Button
      //     onClick={() => {
      //       useApi.预约管理导出.request(params).then(r => {
      //         saveAs(r, '用户预约记录.xlsx');
      //       });
      //     }}>
      //     导出
      //   </Button>
      // }
      columns={
        useMemo(
          () => [
            {
              title: '提交表单时间',
              width: 180,
              dataIndex: 'createTime'
            },
            {
              title: '预约类型',
              width: 180,
              dataIndex: 'serviceType',
              searchIndex: 'serviceType',
              search: true
            },
            {
              title: '客户姓名',
              width: 140,
              dataIndex: 'clientName',
              searchIndex: 'clientName',
              search: true
            },
            {
              title: '联系电话',
              width: 120,
              dataIndex: 'phone',
              searchIndex: 'phone',
              search: true
            },
            {
              title: '表单备注',
              dataIndex: 'remark'
            },
            {
              title: '服务区域',
              dataIndex: 'area',
              searchIndex: 'area',
              search: (
                <TransferChange>
                  {(onChange, value) => {
                    return (
                      <Cascader
                        style={{ width: '100%' }}
                        value={value?.map(i => i.value)}
                        onChange={(_, options) => {
                          onChange(options);
                        }}
                        placeholder={'请选择服务区域'}
                        options={addressOptions}
                      />
                    );
                  }}
                </TransferChange>
              )
            },
            {
              title: '状态',
              dataIndex: 'status',
              searchIndex: 'status',
              search: <ArrSelect options={statusEnum} />,
              excelRender: v => statusEnum?.find(i => i.value === v)?.label,
              render: v => statusEnum?.find(i => i.value === v)?.label
            },
            {
              title: '提交时间',
              render: false,
              search: (
                <DayRangePicker
                  placeholder={['开始时间', '结束时间']}
                  valueFormat={'YYYY-MM-DD HH:mm:ss'}
                  disabledDate={current => {
                    return current && current.valueOf() > Date.now();
                  }}
                />
              ),
              searchIndex: ['startTime', 'endTime']
            },
            {
              title: '操作',
              fixed: 'right',
              width: 120,
              excelRender: false,
              render: record => (
                <Space vertical size={20}>
                  <LinkButton
                    onClick={() => {
                      switchModalVisible(record);
                    }}>
                    查看详情
                  </LinkButton>
                  {record.status === 0 && (
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.标记为已读.request({
                              id: record.id,
                              status: record.status === 0 ? 1 : 0,
                              handleRemark: ''
                            }),
                          '确认标记为已读'
                        );
                      }}>
                      标记为已读
                    </LinkButton>
                  )}
                </Space>
              )
            }
          ],
          [addressOptions, switchModalVisible]
        ) as any
      }
    />
  );
};
