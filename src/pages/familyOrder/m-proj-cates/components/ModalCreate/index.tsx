import { Button, Form, Input, Modal, Space, Upload } from 'antd';
import { UploadImg } from 'parsec-admin';
import API from '../../api';
import { useCallback, useEffect } from 'react';

interface ModalCreateProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
}

export function ModalCreate({ visible, onOk, onCancel }: ModalCreateProps) {
  const [form] = Form.useForm();

  const createAPI = API.useCreate({ needInit: false });

  useEffect(() => {
    visible && form.resetFields();
  }, [visible]);

  const handleConfirm = useCallback(() => {
    form.validateFields().then(async values => {
      console.log('CREATE CATE: ', values);
      await createAPI.request(values);
      onOk();
    });
  }, [form, createAPI, onOk]);

  return (
    <Modal
      title='新建分类'
      visible={visible}
      onOk={handleConfirm}
      onCancel={onCancel}>
      <Form form={form}>
        <Form.Item label='分类名称' name='name'>
          <Input />
        </Form.Item>
        {/* Logo Image */}
        <Form.Item
          label='Logo图片'
          name='logoUrl'
          extra='支持图片格式：jpg、png、jpeg，大小不超过2M，建议尺寸：64px * 64px'>
          <UploadImg
            arrValue={false}
            showUploadList={{
              showPreviewIcon: true,
              showRemoveIcon: true,
              showDownloadIcon: false
            }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}
