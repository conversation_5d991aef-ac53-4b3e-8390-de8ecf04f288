import MyTableList from '@src/components/myTableList';
import { Button, Image, Modal } from 'antd';
import { useState } from 'react';
import { ActionsWrap, LinkButton, useReloadTableList } from 'parsec-admin';
import { ModalCreate } from './components/ModalCreate';
import PageSuspense from '@src/layouts/PageSuspense';

import API from './api';

export default function ManageProjectCates() {
  const [createShow, setCreateShow] = useState(false);
  const deleteAPI = API.useDelete({ needInit: false });

  const reload = useReloadTableList();

  const handleCreate = () => {
    setCreateShow(true);
  };

  return (
    <PageSuspense>
      <MyTableList
        tableTitle='项目分类'
        action={
          <Button type='primary' onClick={() => handleCreate()}>
            新建分类
          </Button>
        }
        getList={({ params }: { params: any }) => {
          const { sort, ...p } = params;

          return API.useList.request({
            ...p,
            pageNum: params.pageNum,
            numPerPage: 10
          });
        }}
        columns={[
          {
            title: '分类图片',
            width: 180,
            dataIndex: 'logoUrl',
            render: (_, record) => <Image src={record.logoUrl} width={60} />
          },
          {
            title: '分类名称',
            width: 180,
            dataIndex: 'name'
          },
          {
            title: '项目数量',
            width: 180,
            dataIndex: 'projectNum'
          },
          {
            title: '操作时间',
            width: 180,
            dataIndex: 'createTime'
          },
          {
            title: '操作人',
            width: 180,
            dataIndex: 'operatorName'
          },
          {
            title: '操作',
            width: 180,
            dataIndex: 'action',
            render: (_, record) => (
              <ActionsWrap max={8}>
                {/* <LinkButton onClick={() => setViewOne(record.id)}>
                  查&nbsp;看
                </LinkButton> */}
                <LinkButton
                  type='danger'
                  onClick={() =>
                    Modal.confirm({
                      title: '删除项目分类',
                      content: '确定要删除项目分类吗？',
                      onOk: async () => {
                        await deleteAPI.request([record.id]);
                        reload();
                      }
                    })
                  }>
                  删&nbsp;除
                </LinkButton>
              </ActionsWrap>
            )
          }
        ]}
      />
      <ModalCreate
        visible={createShow}
        onOk={() => {
          setCreateShow(false);
          reload();
        }}
        onCancel={() => setCreateShow(false)}
      />
    </PageSuspense>
  );
}
