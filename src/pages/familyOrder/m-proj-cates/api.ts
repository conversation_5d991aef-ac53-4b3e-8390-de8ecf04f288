import createApiHooks from 'create-api-hooks';
import {
  ApiResponse,
  ListApiRequestParams,
  ListApiResponseData
} from '@apiHooks';

import { request } from 'parsec-admin';

export type ProjectCate = any;

export default {
  useList: createApiHooks(
    (params: ListApiRequestParams & { prescriptionDrugsType?: string }) =>
      request.get<ListApiResponseData<ProjectCate>>(
        '/mch/inquiry/nursing/category/list',
        { params }
      )
  ),
  useOne: createApiHooks((id: string) =>
    request.get<ApiResponse<ProjectCate>>('/mch/inquiry/nursing/category/' + id)
  ),
  useCreate: createApiHooks((data: ProjectCate) =>
    request.post<ApiResponse<any>>(
      '/mch/inquiry/nursing/category/add',
      { ...data },
      { headers: { 'Content-Type': 'application/json' } }
    )
  ),
  useUpdate: createApiHooks((data: ProjectCate) =>
    request.put<ApiResponse<any>>('/mch/inquiry/nursing/category/update', {
      data
    })
  ),
  useDelete: createApiHooks((ids: string[]) =>
    request.delete<ApiResponse<any>>('/mch/inquiry/nursing/category/delete', {
      // TODO: batch delete
      data: {
        id: ids[0]
      }
    })
  )
};
