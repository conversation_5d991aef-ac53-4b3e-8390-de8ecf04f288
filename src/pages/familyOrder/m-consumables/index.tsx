import MyTableList from '@src/components/myTableList';
import { Button, Input, message, Modal, Radio, Space, Tag } from 'antd';
import { useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  handleSubmit,
  LinkButton,
  useModal,
  useReloadTableList,
  InputMoney
} from 'parsec-admin';
import { ModalCreate } from './components/ModalCreate';

import API from './api';
import PageSuspense from '@src/layouts/PageSuspense';

export default function ManageConsumables() {
  const [createShow, setCreateShow] = useState(false);
  const [selectRowKeys, setSelectRowKeys] = useState<any[]>([]);
  const reload = useReloadTableList();

  const listAPI = API.useList({ needInit: false });
  const updateStatusAPI = API.useUpdateStatus({ needInit: false });
  const deleteAPI = API.useDelete({ needInit: false });

  const switchModal = useModal(({ id }) => {
    return {
      onSubmit: values => {
        return handleSubmit(() => {
          return id
            ? API.useUpdate.request(values)
            : API.useCreate.request(values);
        });
      },
      title: id ? '编辑' : '添加',
      items: [
        { name: 'id', render: false },
        {
          name: 'name',
          label: '耗材名称',
          required: true,
          render: <Input placeholder='请输入耗材名称' />
        },
        {
          name: 'brandName',
          label: '品牌/厂家',
          render: <Input placeholder='请输入品牌/厂家' />
        },
        {
          name: 'spec',
          label: '规格',
          required: true,
          render: <Input placeholder='请输入规格' />
        },
        {
          name: 'fee',
          label: '金额',
          required: true,
          render: <InputMoney />
        },
        {
          name: 'status',
          label: '状态',
          required: true,
          render: (
            <Radio.Group>
              <Radio value={1}>启用</Radio>
              <Radio value={0}>禁用</Radio>
            </Radio.Group>
          )
        }
      ]
    };
  });
  const handleImport = () => {
    const el = document.createElement('input');
    el.setAttribute('type', 'file');
    el.addEventListener('change', () => {
      const file = el.files?.[0];
      if (file) {
        message.info('批量导入接口未实现');
        console.log(file);
      }
    });
    el.click();
  };

  return (
    <PageSuspense>
      <MyTableList
        tableTitle='耗材管理'
        action={
          <Space>
            <Button
              type='primary'
              onClick={() => {
                actionConfirm(
                  () =>
                    updateStatusAPI.request({
                      ids: selectRowKeys,
                      status: 1
                    }),
                  '批量启用'
                );
              }}>
              批量启用
            </Button>
            <Button
              type='primary'
              onClick={() => {
                actionConfirm(
                  () =>
                    updateStatusAPI.request({
                      ids: selectRowKeys,
                      status: 0
                    }),
                  '',
                  {
                    template:
                      '停用后项目将不计算该耗材费用，且不在患者端展示，你还要继续吗？',
                    props: { okText: '继续' }
                  }
                );
              }}>
              批量停用
            </Button>
            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={() => switchModal()}>
              新建耗材
            </Button>
            <Button type='primary' onClick={() => handleImport()}>
              批量导入
            </Button>
          </Space>
        }
        exportExcelButton={true}
        rowSelection={{
          onChange: selectedRowKeys => setSelectRowKeys(selectedRowKeys),
          selectedRowKeys: selectRowKeys
        }}
        getList={({ params }: { params: any }) => {
          const { sort, ...p } = params;

          return listAPI.request({
            ...p,
            pageNum: params.pageNum,
            numPerPage: 10
          });
        }}
        columns={[
          {
            title: '耗材名称',
            width: 180,
            dataIndex: 'name',
            search: true
          },
          {
            title: '品牌/厂家',
            width: 180,
            dataIndex: 'brandName'
          },
          {
            title: '规格',
            width: 180,
            dataIndex: 'spec'
          },
          {
            title: '金额',
            width: 180,
            dataIndex: 'fee',
            render: v => v && `${Number(v) / 100}元`
          },
          {
            title: '状态',
            width: 180,
            dataIndex: 'status',
            render: v =>
              v === 1 ? (
                <Tag color='success'>启用</Tag>
              ) : (
                <Tag color='error'>停用</Tag>
              ),
            search: (
              <ArrSelect
                options={[
                  { label: '启用', value: 1 },
                  { label: '停用', value: 0 }
                ]}
              />
            )
          },
          {
            title: '操作时间',
            width: 180,
            dataIndex: 'updateTime'
          },
          {
            title: '操作人',
            width: 180,
            dataIndex: 'operatorName'
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 180,
            align: 'center',
            render: (_, record) => (
              <ActionsWrap max={8}>
                {!record?.status && (
                  <LinkButton
                    type='warning'
                    onClick={() => switchModal({ ...record })}>
                    编辑
                  </LinkButton>
                )}
                <LinkButton
                  type='warning'
                  onClick={async () => {
                    record.status === 1
                      ? actionConfirm(
                          () =>
                            updateStatusAPI.request({
                              ids: [record.id],
                              status: 0
                            }),
                          '',
                          {
                            template:
                              '停用后项目将不计算该耗材费用，且不在患者端展示，你还要继续吗？',
                            props: { okText: '继续' }
                          }
                        )
                      : handleSubmit(() => {
                          return updateStatusAPI.request({
                            ids: [record.id],
                            status: 1
                          });
                        });
                  }}>
                  {record.status === 1 ? '停用' : '启用'}
                </LinkButton>
                <LinkButton
                  type='danger'
                  onClick={() => {
                    Modal.confirm({
                      title: '删除耗材',
                      content: '确定要删除该耗材吗？',
                      onOk: async () => {
                        await deleteAPI.request([record.id]);
                        reload();
                      }
                    });
                  }}>
                  删&nbsp;除
                </LinkButton>
              </ActionsWrap>
            )
          }
        ]}
      />
      {/* Modal */}
      <ModalCreate
        visible={createShow}
        onOk={() => {
          setCreateShow(false);
          reload();
        }}
        onCancel={() => setCreateShow(false)}
      />
    </PageSuspense>
  );
}
