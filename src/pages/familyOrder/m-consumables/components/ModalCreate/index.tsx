import { useCallback } from 'react';
import { Form, Input, InputNumber, message, Modal, Radio } from 'antd';
import API from '../../api';

interface ModalCreateProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
}

export function ModalCreate({ visible, onOk, onCancel }: ModalCreateProps) {
  const [form] = Form.useForm();

  const createAPI = API.useCreate({ needInit: false });

  const handleConfirm = useCallback(() => {
    form.validateFields().then(async values => {
      console.log('CREATE CATE: ', values);
      const temp = { ...values, fee: values.fee * 100 };

      await createAPI.request(temp);
      message.success('新建成功');
      onOk();
    });
  }, [form, createAPI, onOk]);

  return (
    <Modal
      title='新建耗材'
      visible={visible}
      onOk={handleConfirm}
      onCancel={onCancel}>
      <Form
        disabled={false}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        form={form}>
        <Form.Item
          label='耗材名称'
          name='name'
          rules={[{ required: true, message: '请输入耗材名称' }]}>
          <Input />
        </Form.Item>
        <Form.Item label='品牌/厂家' name='brandName'>
          <Input />
        </Form.Item>
        <Form.Item label='规格' name='spec'>
          <Input />
        </Form.Item>
        <Form.Item
          label='金额'
          name='fee'
          rules={[{ required: true, message: '请输入金额' }]}>
          <InputNumber addonAfter='元' precision={2} />
        </Form.Item>
        <Form.Item label='状态' name='status' rules={[{ required: true }]}>
          <Radio.Group>
            <Radio value='1'>启用</Radio>
            <Radio value='0'>禁用</Radio>
          </Radio.Group>
        </Form.Item>
      </Form>
    </Modal>
  );
}
