import createApiHooks from 'create-api-hooks';
import {
  ApiResponse,
  ListApiRequestParams,
  ListApiResponseData
} from '@apiHooks';

import { request } from 'parsec-admin';

export type Consumable = any;

export type UpdateParams = {
  ids: string[];
  data: Partial<Consumable>;
};

export type UpdateStatus = {
  ids: string[];
  status: 0 | 1;
};

export default {
  useList: createApiHooks((params: ListApiRequestParams) =>
    request.get<ListApiResponseData<Consumable>>(
      '/mch/inquiry/nursing/consumables/list',
      { params }
    )
  ),
  useOne: createApiHooks((id: string) =>
    request.get<ApiResponse<Consumable>>(
      '/mch/inquiry/nursing/consumables/' + id
    )
  ),
  useCreate: createApiHooks((data: Consumable) =>
    request.post<ApiResponse<any>>(
      '/mch/inquiry/nursing/consumables/add',
      { ...data },
      { headers: { 'Content-Type': 'application/json' } }
    )
  ),
  useUpdate: createApiHooks((data: UpdateParams) =>
    request.post<ApiResponse<any>>(
      '/mch/inquiry/nursing/consumables/update',
      { ...data },
      { headers: { 'Content-Type': 'application/json' } }
    )
  ),
  useDelete: createApiHooks((ids: string[]) =>
    request.post<ApiResponse<any>>(
      '/mch/inquiry/nursing/consumables/delete',
      { ids },
      { headers: { 'Content-Type': 'application/json' } }
    )
  ),
  useUpdateStatus: createApiHooks((data: UpdateStatus) =>
    request.post<ApiResponse<any>>(
      '/mch/inquiry/nursing/consumables/change-status',
      { ...data },
      { headers: { 'Content-Type': 'application/json' } }
    )
  )
};
