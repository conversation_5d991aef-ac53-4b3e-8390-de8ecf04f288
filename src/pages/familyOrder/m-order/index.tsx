import MyTableList from '@src/components/myTableList';
import { Form, Input, InputNumber, message, Modal, Select, Tag } from 'antd';
import { useMemo } from 'react';
import {
  ActionsWrap,
  ArrSelect,
  DayRangePicker,
  handleSubmit,
  LinkButton,
  RouteComponentProps,
  useModal,
  useReloadTableList,
  InputMoney
} from 'parsec-admin';

import API, { OrderStatus, STATUS_MAP } from './api';
import SettingsAPI from '../m-settings/api';
import PageSuspense from '@src/layouts/PageSuspense';

const { confirm } = Modal;

export default function ManageOrder({ history }: RouteComponentProps) {
  const settingsAPI = SettingsAPI.useSettings();

  const reload = useReloadTableList();

  const agreeAPI = API.useAgree({ needInit: false });
  const assignAPI = API.useAssign({ needInit: false });
  const cancelAPI = API.useCancel({ needInit: false });
  const rejectAPI = API.useReject({ needInit: false });
  const refundAPI = API.useRefund({ needInit: false });
  const staffListAPI = API.useStaffList({ needInit: true });

  const isHospital = useMemo(() => settingsAPI.data?.data === 'HOSPITAL', [
    settingsAPI.data?.data
  ]);

  const staffOptions = useMemo(() => {
    console.log(staffListAPI.data);
    return staffListAPI.data?.data?.recordList
      ?.filter(item => ['5'].includes(item.userFlag))
      .map(item => ({
        label: item.name,
        value: item.id
      }));
  }, [staffListAPI.data?.data?.recordList]);

  const showRefundModal = useModal(({ id }) => ({
    onSubmit: values => {
      return handleSubmit(async () => {
        await refundAPI.request({ ...values, id });
        message.success('操作成功');
        reload();
      });
    },
    title: (
      <div className='space-x-2'>
        <span>操作退款</span>
        <span className='text-xs opacity-50'>
          请仔细核实该订单信息，以便准确操作退款
        </span>
      </div>
    ),
    items: [
      {
        label: '退款金额',
        name: 'amount',
        formLayout: 'vertical',
        formItemProps: {
          help: '退款金额=订单金额-已退款金额'
        },
        wrapperCol: { span: 17 },
        render: _ => <InputMoney addonAfter='元' />,
        required: true
      },
      {
        label: '退款原因',
        name: 'refundReason',
        required: true
      }
    ]
  }));

  const showAssignModal = useModal(({ id }) => ({
    onSubmit: values => {
      return handleSubmit(async () => {
        await assignAPI.request({ ...values, id });
        message.success('操作成功');
        reload();
      });
    },
    title: '分配护理人员',
    items: [
      {
        label: '护理人员',
        name: 'staffId',
        required: true,
        render: _ => (
          <Select placeholder='请选择护理人员' options={staffOptions} />
        )
      }
    ]
  }));

  const showCancelModal = useModal(({ id }) => ({
    onSubmit: values => {
      return handleSubmit(async () => {
        await cancelAPI.request({ ...values, id });
        message.success('操作成功');
        reload();
      });
    },
    title: '取消订单',
    items: [
      {
        label: '取消原因',
        name: 'reason',
        required: true
      }
    ]
  }));

  const showRejectModal = useModal(({ id }) => ({
    onSubmit: values => {
      return handleSubmit(async () => {
        await rejectAPI.request({ ...values, id });
        message.success('操作成功');
        reload();
      });
    },
    title: '拒绝',
    items: [
      {
        label: '拒绝原因',
        name: 'reason',
        required: true
      }
    ]
  }));

  const handleRefund = ({ id }: { id: string }) => {
    showRefundModal({ id });
  };

  const handleAudit = (id: string, type: 'agree' | 'reject') => {
    confirm({
      title: `确定要${type === 'agree' ? '同意' : '拒绝'}吗？`,
      onOk: () =>
        type === 'agree'
          ? handleSubmit(async () => {
              await agreeAPI.request({ id });
              message.success('操作成功');
              reload();
            })
          : showRejectModal({ id })
    });
  };

  const renderAction = (status: OrderStatus, record: any) => {
    const actions = {
      view: (
        <LinkButton
          onClick={() => history.push(`/familyOrder/m-order/${record.id}`)}>
          查&nbsp;看
        </LinkButton>
      ),
      assign: (
        <LinkButton
          type='info'
          onClick={() => showAssignModal({ id: record.id })}>
          分&nbsp;配
        </LinkButton>
      ),
      agree: (
        <LinkButton
          type='success'
          onClick={() => handleAudit(record.id, 'agree')}>
          同&nbsp;意
        </LinkButton>
      ),
      reject: (
        <LinkButton
          type='danger'
          onClick={() => showRejectModal({ id: record.id })}>
          拒&nbsp;绝
        </LinkButton>
      ),
      cancel: (
        <LinkButton
          type='danger'
          onClick={() => showCancelModal({ id: record.id })}>
          取&nbsp;消
        </LinkButton>
      ),
      refund: (
        <LinkButton
          type='danger'
          onClick={() => handleRefund({ id: record.id })}>
          退&nbsp;费
        </LinkButton>
      )
    };

    // 定义每个操作对应的状态列表
    const actionStatusMap = [
      { action: actions.agree, statuses: [OrderStatus.AUDITING] },
      { action: actions.reject, statuses: [OrderStatus.AUDITING] },
      { action: actions.assign, statuses: [OrderStatus.UN_ASSIGN] },
      {
        action: actions.refund,
        statuses: [
          OrderStatus.UN_ASSIGN,
          OrderStatus.PENDING,
          OrderStatus.UN_SERVICE,
          OrderStatus.WALKING,
          OrderStatus.IN_SERVICE,
          OrderStatus.FINISHED_SERVICE,
          OrderStatus.FINISHED_ORDER
        ]
      }
    ];

    return [
      ...actionStatusMap
        .filter(({ statuses }) => statuses.includes(status))
        .map(({ action }) => action),
      actions.view
    ];
  };

  return (
    <PageSuspense>
      <MyTableList
        tableTitle='护理上门订单管理'
        exportExcelButton
        getList={({ params }: { params: any }) => {
          const { sort, ...p } = params;

          return API.useList.request({
            ...p,
            pageNum: params.pageNum,
            numPerPage: 10
          });
        }}
        columns={[
          {
            title: '下单时间',
            width: 180,
            dataIndex: 'createTime',
            fixed: 'left'
          },
          {
            title: '项目名称',
            width: 120,
            dataIndex: 'projectName',
            search: true,
            render: v => v,
            fixed: 'left'
          },
          {
            title: '服务时间',
            width: 240,
            dataIndex: 'serviceTime',
            search: <DayRangePicker placeholder={['开始时间', '结束时间']} />,
            searchIndex: ['serviceStartTime', 'serviceEndTime'],
            render: v => v
          },
          {
            title: '服务地址',
            width: 180,
            dataIndex: 'addressDetails'
          },
          {
            title: '患者信息',
            width: 180,
            dataIndex: 'patientName',
            search: <Input placeholder='患者姓名' />,
            render: (_, record) => {
              const sexMap = { M: '男', F: '女' };
              const [name, patientSex, patientAge] = [
                // 姓名两个字 中间加空格
                record.patientName?.replace(
                  /^([\u4e00-\u9fa5])([\u4e00-\u9fa5])$/,
                  '$1\u2003$2'
                ) || '',
                sexMap[record.patientSex],
                record.patientAge
              ];
              return `${name} | ${patientSex} | ${patientAge}岁`;
            }
          },
          {
            title: '订单金额(元)',
            width: 200,
            dataIndex: 'payFee',
            render: v => v && `${Number(v) / 100}元`
          },
          {
            title: '订单状态',
            width: 180,
            dataIndex: 'status',
            align: 'center',
            search: <ArrSelect options={STATUS_MAP} />,
            searchIndex: 'orderStatus',
            render: v => <Tag>{STATUS_MAP[v]}</Tag>
          },
          {
            title: '护理人员',
            width: 180,
            dataIndex: 'staffName',
            render: v => v || '-',
            search: <Input placeholder='护理人员' />
          },
          {
            title: '完成时间',
            width: 240,
            dataIndex: 'finishedTime',
            render: v => v || '-',
            search: <DayRangePicker placeholder={['开始时间', '结束时间']} />,
            searchIndex: ['finishedStartTime', 'finishedEndTime']
          },
          {
            title: '拒绝/取消原因',
            width: 180,
            dataIndex: 'reason',
            render: v => v || '-'
          },
          {
            title: '备注',
            width: 180,
            dataIndex: 'remark',
            render: v => v || '-'
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 180,
            align: 'center',
            fixed: 'right',
            render: (_, record) => (
              <ActionsWrap max={8}>
                {renderAction(record.status, record)}
              </ActionsWrap>
            )
          }
        ]}
      />
    </PageSuspense>
  );
}
