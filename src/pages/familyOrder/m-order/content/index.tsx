import { Space, Image } from 'antd';
import { CardLayout, FormDescriptions } from 'parsec-admin';
import { useParams } from 'react-router-dom';

import PageSuspense from '@src/layouts/PageSuspense';

import API, { STATUS_MAP } from '../api';

export default function Content() {
  const { id } = useParams<{ id: string }>();

  console.log('ID: ', id);
  const {
    data: { data: detail }
  } = API.useOne({ params: id });

  return (
    <PageSuspense>
      <div className='space-y-4 divide-y'>
        {Object.entries(sections)?.map(([title, items]) => (
          <CardLayout title={title} loading={false}>
            <FormDescriptions
              data={detail}
              edit={false}
              column={4}
              loading={false}
              items={items}
            />
          </CardLayout>
        ))}
      </div>
    </PageSuspense>
  );
}

const sections = {
  预约信息: [
    {
      label: '预约项目',
      name: 'projectName'
    },
    {
      label: '项目耗材',
      name: 'consumables',
      render: (_, record) =>
        record?.orderConsumables
          ?.map(item => `${item.consumablesName} \u2003 ${item.num}`)
          .join('\u2002|\u2002') || '-',
      column: 2
    },
    {
      label: '耗材费用',
      name: 'consumablesCost',
      render: (_, record) => Number(record?.consumablesFee) / 100 || '-'
    },
    {
      label: '项目服务价格',
      name: 'serviceFee',
      render: (_, record) => Number(record?.serviceFee) / 100 || '-'
    },
    {
      label: '上门服务费',
      name: 'walkServiceFee',
      render: (_, record) => Number(record?.walkServiceFee) / 100 || '-'
    },
    {
      label: '路程费',
      name: 'distanceFee',
      render: (_, record) => Number(record?.distanceFee) / 100 || '-'
    },
    {
      label: '路程费说明',
      name: 'distanceNote',
      column: 2,
      render: (_, record) => record?.distanceNote || '-'
    },
    {
      label: '联 系 人',
      name: 'patientName'
    },
    {
      label: '联系电话',
      name: 'mobile'
    },
    {
      label: '上门地址',
      name: 'addressDetails'
    },
    {
      label: '服务时间',
      name: 'serviceTime'
    }
  ],
  患者信息: [
    {
      label: '姓名',
      name: 'patientName'
    },
    {
      label: '性别',
      name: 'patientSex',
      render: v => ({ F: '女', M: '男' }[v])
    },
    {
      label: '年龄',
      name: 'patientAge',
      render: v => `${v}岁`
    },
    {
      label: '身份证号',
      name: 'idNo'
    },
    {
      label: '联系电话',
      name: 'mobile'
    },
    {
      label: '补充说明',
      name: 'addRemark'
    },
    {
      label: '就诊证明',
      name: 'visitRecordUrls',
      render: (_, record) => (
        <Space>
          {record?.visitRecordUrls?.split(',').map((url, i) => (
            <Image key={i} src={url} width={100} />
          ))}
        </Space>
      )
    }
  ],
  订单信息: [
    {
      label: '订单状态',
      name: 'status',
      render: (_, record) => STATUS_MAP[record?.status]
    },
    {
      label: '护理人员',
      name: 'staffName'
    },
    {
      label: '订单金额',
      name: 'payFee',
      render: v => `${Number(v ?? 0) / 100}元`
    },
    {
      label: '下单时间',
      name: 'createTime'
    },
    {
      label: '支付时间',
      name: 'payedTime'
    },
    {
      label: '平台单号',
      name: 'payOrderId'
    },
    {
      label: '交易单号',
      name: 'trandNo',
      column: 4
    }
  ],
  服务信息: [
    {
      label: '开始服务',
      name: 'serviceUrl',
      render: (v, _) => (
        <div style={{ display: 'flex', flexWrap: 'wrap' }}>
          {v?.split(',')?.map((url, i) => (
            <Image key={i} src={url} width={150} />
          ))}
        </div>
      )
    },
    {
      label: '开始服务时间',
      name: 'serviceTime'
    }
  ],
  处理信息: [
    {
      label: '医疗垃圾处理',
      name: 'medicalRubbishHandleUrl',
      render: (v, _) => (
        <Space>
          {v?.split(',')?.map((url, i) => (
            <Image key={i} src={url} width={200} />
          ))}
        </Space>
      )
    },
    {
      label: '护理记录',
      name: 'patientMaintenanceContent'
    }
  ]
};
