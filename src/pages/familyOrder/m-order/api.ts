import createApiHooks from 'create-api-hooks';
import {
  ApiResponse,
  ListApiRequestParams,
  ListApiResponseData
} from '@apiHooks';

import { request } from 'parsec-admin';

export type Order = any;

export enum OrderStatus {
  UN_PAY = 'UN_PAY',
  AUDITING = 'AUDITING',
  REJECTED = 'REJECTED',
  UN_ASSIGN = 'UN_ASSIGN',
  PENDING = 'PENDING',
  UN_SERVICE = 'UN_SERVICE',
  WALKING = 'WALKING',
  IN_SERVICE = 'IN_SERVICE',
  FINISHED_SERVICE = 'FINISHED_SERVICE',
  FINISHED_ORDER = 'FINISHED_ORDER',
  REFUNDED = 'REFUNDED',
  PATIENT_CANCELED = 'PATIENT_CANCELED',
  MCH_CANCELED = 'MCH_CANCELED',
  FINISHED_DISPOSAL = 'FINISHED_DISPOSAL',
  EVALUATED = 'EVALUATED'
}

export const STATUS_MAP = {
  [OrderStatus.UN_PAY]: '待支付',
  [OrderStatus.AUDITING]: '审核中',
  [OrderStatus.REJECTED]: '已拒绝',
  [OrderStatus.UN_ASSIGN]: '待分配',
  [OrderStatus.PENDING]: '待接单',
  [OrderStatus.UN_SERVICE]: '待服务',
  [OrderStatus.WALKING]: '上门中',
  [OrderStatus.IN_SERVICE]: '服务中',
  [OrderStatus.FINISHED_SERVICE]: '服务完成',
  [OrderStatus.FINISHED_ORDER]: '订单完成',
  [OrderStatus.REFUNDED]: '已退款',
  [OrderStatus.PATIENT_CANCELED]: '患者已取消',
  [OrderStatus.MCH_CANCELED]: '管理端已取消',
  [OrderStatus.FINISHED_DISPOSAL]: '完成处置',
  [OrderStatus.EVALUATED]: '已评价'
};

export type Review = {
  id: string;
  op: 'AGREE' | 'REJECT' | 'CANCEL';
  reason?: string;
};

export type Assign = {
  id: string;
  staffId: string;
};

export type Refund = {
  id: string;
  amount: string;
  refundReason: string;
};

export type Agree = {
  id: string;
};

export type Cancel = {
  id: string;
  reason: string;
};

export type Reject = {
  id: string;
  reason: string;
};

export default {
  useList: createApiHooks((params: ListApiRequestParams) =>
    request.get<ListApiResponseData<Order>>('/mch/inquiry/nursing/order/list', {
      params
    })
  ),
  useOne: createApiHooks<string, any>(id =>
    request.get<ApiResponse<Order>>('/mch/inquiry/nursing/order/' + id)
  ),
  useCreate: createApiHooks((data: Order) =>
    request.post<ApiResponse<any>>('/mch/inquiry/nursing/order/add', {
      data
    })
  ),
  useUpdate: createApiHooks((data: Order) =>
    request.put<ApiResponse<any>>('/mch/inquiry/nursing/order/update', {
      data
    })
  ),
  useDelete: createApiHooks((ids: string[]) =>
    request.delete<ApiResponse<any>>('/mch/inquiry/nursing/order/delete', {
      params: { ids }
    })
  ),
  useReview: createApiHooks((data: Review) =>
    request.put<ApiResponse<any>>('/mch/inquiry/nursing/order/review', {
      data
    })
  ),
  useAssign: createApiHooks((data: Assign) =>
    request.put<ApiResponse<any>>('/mch/inquiry/nursing/order/assign', {
      ...data
    })
  ),
  useCancel: createApiHooks((data: Cancel) =>
    request.put<ApiResponse<any>>('/mch/inquiry/nursing/order/cancel', {
      ...data
    })
  ),
  useRefund: createApiHooks((data: Refund) =>
    request.put<ApiResponse<any>>('/mch/inquiry/nursing/order/refund', {
      ...data
    })
  ),
  useAgree: createApiHooks((data: Agree) =>
    request.put<ApiResponse<any>>('/mch/inquiry/nursing/order/agree', {
      ...data
    })
  ),
  useReject: createApiHooks((data: Reject) =>
    request.put<ApiResponse<any>>('/mch/inquiry/nursing/order/refuse', {
      ...data
    })
  ),
  // 护理人员列表
  useStaffList: createApiHooks(() =>
    request.post<ListApiResponseData<any>>(
      '/mch/user/doctorAccount/listPage?pageNum=1&numPerPage=100'
    )
  )
};
