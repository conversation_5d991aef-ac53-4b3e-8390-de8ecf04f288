import React, { useEffect } from 'react';
import { Button, Form, Radio, Tabs, message } from 'antd';
import { CardLayout, Editor } from 'parsec-admin';
import PageSuspense from '@src/layouts/PageSuspense';

import API, { NursingLevel } from './api';

const { Item } = Form;

export default function ManageAppointType() {
  return (
    <PageSuspense>
      <CardLayout title={'护理预约上门设置'}>
        <ConfTabs />
      </CardLayout>
    </PageSuspense>
  );
}

const ConfTabs: React.FC = () => {
  const [tab, setTab] = React.useState('1');

  const [consentForm] = Form.useForm();

  const [audit, setAudit] = React.useState<NursingLevel>();

  const { data } = API.useSettings();

  const cfAPI = API.useConsentForm();
  const updateCFAPI = API.useUpdateConsentForm({ needInit: false });

  useEffect(() => {
    setAudit(data.data as NursingLevel);
  }, [data]);

  useEffect(() => {
    consentForm.setFieldsValue({ remark: cfAPI.data?.data?.remark || '' });
  }, [cfAPI.data]);

  return (
    <Tabs
      defaultActiveKey='1'
      activeKey={tab}
      onChange={v => {
        setTab(v);
      }}>
      <Tabs.TabPane tab='基础设置' key='1'>
        <div className='flex items-center'>
          <label className='text-sm mr-2'>订单审核</label>
          <Radio.Group
            value={audit}
            onChange={async e => {
              await API.useUpdate.request({ nursingLevel: e.target.value });
              setAudit(e.target.value);
              message.success('设置成功！');
            }}>
            <Radio value={NursingLevel.NURSE}>护理人员审核</Radio>
            <Radio value={NursingLevel.HOSPITAL}>院级审核</Radio>
          </Radio.Group>
        </div>
      </Tabs.TabPane>
      <Tabs.TabPane tab='知情同意书' key='2'>
        <Form
          form={consentForm}
          onFinish={async () => {
            const res = await consentForm.validateFields();
            await updateCFAPI.request({ ...res, nursingLevel: audit });
            message.success('设置成功！');
          }}>
          <Item name='remark'>
            <Editor />
          </Item>
          <Form.Item style={{ textAlign: 'right' }}>
            <Button type='primary' htmlType='submit'>
              提交
            </Button>
          </Form.Item>
        </Form>
      </Tabs.TabPane>
    </Tabs>
  );
};
