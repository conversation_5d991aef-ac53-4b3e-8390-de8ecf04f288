import createApiHooks from 'create-api-hooks';
import { ApiResponse } from '@apiHooks';
import { request } from 'parsec-admin';
import env from '@src/configs/env';

export interface Data {
  medicalPersonnelId: string; //medicalPersonnelId
  type: number; //类型 1:医生 2:医师
  name: string; //医生名称
}

export enum NursingLevel {
  NURSE = 'NURSE',
  HOSPITAL = 'HOSPITAL'
}

export type UpdateSettings = {
  nursingLevel: NursingLevel;
};

export default {
  useSettings: createApiHooks(() =>
    request.get<ApiResponse<string>>('/mch/inquiry/nursing/level', {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  ),
  useConsentForm: createApiHooks(() =>
    request.get<ApiResponse<any>>(
      `/mch/inquiry/nursing/level/getHisNoticeInfo`,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  useUpdateConsentForm: createApiHooks((data: { content: string }) =>
    request.post('/mch/inquiry/nursing/level/setHisNoticeInfo', data, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  ),
  useUpdate: createApiHooks((data: UpdateSettings) =>
    request.post('/mch/inquiry/nursing/level', data, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  ),
  // Remove old
  配置人员: createApiHooks((params: { recipePersonList: Data[] }) =>
    request.post<ApiResponse<any>>(
      '/mch/prescription/medicalRecipeConfig/person',
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  获取人员配置: createApiHooks(() =>
    request.get<ApiResponse<{ recipePersonList: Data[] }>>(
      `/mch/prescription/medicalRecipeConfig/getMedicalRecipePerson/${env.hisId}`
    )
  ),
  配置知情同意书: createApiHooks((params: { content: string }) =>
    request.post<ApiResponse<any>>(
      `/mch/prescription/medicalRecipeConfig/informedConsentForm`,
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  获取知情同意书: createApiHooks(() =>
    request.get<ApiResponse<{ content: string }>>(
      `/mch/prescription/medicalRecipeConfig/getMedicalRecipeInformedConsentForm/${env.hisId}`,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  查询医生列表: createApiHooks((type: number) =>
    request.get<ApiResponse<{ id: number; name: string; doctorId: string }[]>>(
      `/mch/his/doctorMain/list?type=${type}`,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  )
};
