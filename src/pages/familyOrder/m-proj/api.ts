import createApiHooks from 'create-api-hooks';
import {
  ApiResponse,
  ListApiRequestParams,
  ListApiResponseData
} from '@apiHooks';

import { request } from 'parsec-admin';

export type Project = any;

export default {
  useList: createApiHooks(
    (params: ListApiRequestParams & { prescriptionDrugsType?: string }) =>
      request.get<ListApiResponseData<Project>>(
        '/mch/inquiry/nursing/project/list',
        { params }
      )
  ),
  useOne: createApiHooks(({ id }: { id: string }) =>
    request.get<ApiResponse<Project>>('/mch/inquiry/nursing/project/' + id, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  ),
  useCreate: createApiHooks((data: Project) =>
    request.post<ApiResponse<any>>(
      '/mch/inquiry/nursing/project/add',
      { ...data },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  useUpdate: createApiHooks((data: Project) =>
    request.put<ApiResponse<any>>(
      '/mch/inquiry/nursing/project/update',
      { ...data },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  useDelete: createApiHooks((ids: string[]) =>
    request.post<ApiResponse<any>>(
      '/mch/inquiry/nursing/project/delete',
      { ids },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  useEdit: createApiHooks((data: Project) =>
    request.post<ApiResponse<any>>(
      '/mch/inquiry/nursing/project/edit',
      { ...data },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  useUpdateStatus: createApiHooks((data: { ids: string[]; status: number }) =>
    request.post<ApiResponse<any>>(
      '/mch/inquiry/nursing/project/change-status',
      { ...data },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  )
};
