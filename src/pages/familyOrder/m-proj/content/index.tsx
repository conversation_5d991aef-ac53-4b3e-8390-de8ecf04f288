import { MinusCircleOutlined } from '@ant-design/icons';
import {
  Button,
  Form,
  Input,
  InputNumber,
  message,
  Radio,
  RadioChangeEvent,
  Select,
  Space,
  TimePicker
} from 'antd';
import {
  DetailLayout,
  Editor,
  handleSubmit,
  InputMoney,
  UploadImg
} from 'parsec-admin';
import { useCallback, useEffect, useMemo, useState } from 'react';
import moment from 'moment';
import styles from './index.module.less';
import API from '../api';
import CateAPI from '../../m-proj-cates/api';
import MaterialAPI from '../../m-consumables/api';
import { RouteComponentProps, useParams } from 'react-router-dom';
import PageSuspense from '@src/layouts/PageSuspense';

enum ServiceTime {
  WORKDAY = '09:00:00-18:00:00',
  ALL_DAY = '00:00:00-23:59:59',
  OTHER = 'other'
}

const init = {
  name: null,
  categoryId: null,
  categoryName: null,
  url: null,
  serviceTime: '',
  status: 'enable',
  consumables: [{ consumablesId: null, num: null, consumablesName: null }],
  consumableFee: null,
  serviceFee: 0,
  walkServiceFee: 0,
  distanceFee: 0,
  fee: null,
  introduce: null,
  serviceContent: null,
  notice: null
};

export default function Content({ history }: RouteComponentProps) {
  const { id } = useParams<{ id: string }>();

  const [form] = Form.useForm();

  const [otherDisabled, setOtherDisabled] = useState(true);

  const createAPI = API.useCreate({ needInit: false });
  const editAPI = API.useEdit({ needInit: false });
  const contentAPI = API.useOne({
    params: { id },
    needInit: id !== 'create'
  });

  const catesAPI = CateAPI.useList({ params: { numPerPage: 100 } });
  const materialsAPI = MaterialAPI.useList({ params: { numPerPage: 100 } });

  const cateOptions = useMemo(() => {
    return catesAPI.data?.data?.recordList.map(v => ({
      label: v.name,
      value: v.id
    }));
  }, [catesAPI.data?.data]);

  const materialOptions = useMemo(() => {
    console.log('MATERIALS: ', materialsAPI.data?.data?.recordList);
    return materialsAPI.data?.data?.recordList
      .filter(v => v.status === 1) // 过滤掉 status === 1 的记录
      .map(v => ({
        label: v.name,
        value: v.id,
        fee: Number(v.fee)
      }));
  }, [materialsAPI.data?.data]);

  useEffect(() => {
    if (contentAPI.data?.data) {
      const formData = contentAPI.data.data;

      const isOther =
        formData.serviceTime !== ServiceTime.WORKDAY &&
        formData.serviceTime !== ServiceTime.ALL_DAY;

      const serviceTimeCustom = isOther
        ? formData.serviceTime.split('-').map(time => moment(time, 'HH:mm:ss'))
        : null;

      if (isOther) {
        setOtherDisabled(false);
      }

      form.setFieldsValue({
        ...formData,
        serviceTime: isOther ? ServiceTime.OTHER : formData.serviceTime,
        serviceTimeCustom,
        consumablesFee: formData.consumablesFee, // 转换为元
        serviceFee: formData.serviceFee, // 转换为元
        fee:
          formData.consumablesFee +
          formData.serviceFee +
          formData.distanceFee +
          formData.walkServiceFee, // 转换为元
        distanceFee: formData.distanceFee,
        walkServiceFee: formData.walkServiceFee
      });
    }
  }, [contentAPI.data, form]);

  const handleServTimeChange = (e: RadioChangeEvent) => {
    if (e.target.value === ServiceTime.OTHER) {
      setOtherDisabled(false);
    } else {
      form.setFieldsValue({
        serviceTimeCustom: null
      });
      setOtherDisabled(true);
    }
  };

  const handleConfirm = () => {
    const objs = form.getFieldsValue();
    if (!objs.name) {
      message.error('请输入项目名称');
      return;
    }
    if (!objs.categoryId) {
      message.error('请选择项目分类');
      return;
    }
    if (!objs.serviceTime) {
      message.error('请选择项目时间');
      return;
    }
    if (objs.status === undefined || objs.status === null) {
      message.error('请选择项目状态');
      return;
    }
    if (objs.serviceFee === undefined || objs.serviceFee === null) {
      message.error('请输入项目服务价格');
      return;
    }
    if (!objs.fee) {
      message.error('请输入项目总计');
      return;
    }
    if (!objs.introduce) {
      message.error('请输入项目介绍');
      return;
    }
    if (!objs.serviceContent) {
      message.error('请输入服务内容');
      return;
    }
    if (!objs.notice) {
      message.error('请输入预约须知');
      return;
    }

    form.validateFields().then(async values => {
      const consumables = values.consumables.map(v => {
        return {
          ...v,
          consumablesName: materialOptions?.find(
            o => o.value === v.consumablesId
          )?.label
        };
      });

      const serviceTimeCustom = values.serviceTimeCustom
        ?.map(v => moment(v).format('HH:mm:ss'))
        .join('-');

      const payload = {
        ...values,
        categoryName: cateOptions?.find(v => v.value === values.categoryId)
          ?.label,
        serviceTime:
          values.serviceTime === ServiceTime.OTHER
            ? serviceTimeCustom
            : values.serviceTime,
        consumables,
        consumablesFee: calculateConsumablesFee(values.consumables), // 转换为分
        serviceFee: values.serviceFee, // 转换为分
        distanceFee: values.distanceFee,
        walkServiceFee: values.walkServiceFee,
        fee:
          calculateConsumablesFee(values.consumables) +
          values.serviceFee +
          values.walkServiceFee +
          values.distanceFee // 转换为分
      };
      handleSubmit(() => {
        if (id !== 'create') {
          return editAPI.request({ id, ...payload }).then(() => {
            message.success('编辑成功');
            history.goBack();
          });
        } else {
          return createAPI.request(payload).then(() => {
            message.success('新增成功');
            history.goBack();
          });
        }
      });
    });
  };

  const calculateConsumablesFee = useCallback(
    consumables => {
      return consumables.reduce((total, item) => {
        const materialFee =
          materialOptions?.find(m => m.value === item.consumablesId)?.fee || 0;
        return total + materialFee * (item.num || 0);
      }, 0);
    },
    [materialOptions]
  );

  return (
    <PageSuspense>
      <DetailLayout
        cardsProps={[
          {
            title: '项目详情',
            children: (
              <div className='flex flex-col gap-4 bg-white p-4'>
                <Form
                  form={form}
                  initialValues={init}
                  disabled={false}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 18 }}>
                  <Form.Item
                    label={
                      <>
                        <span
                          style={{
                            color: 'red',
                            marginRight: 5,
                            fontSize: 16
                          }}>
                          *
                        </span>
                        项目名称
                      </>
                    }
                    name='name'
                    wrapperCol={{ span: 10 }}>
                    <Input />
                  </Form.Item>
                  <Form.Item
                    label={
                      <>
                        <span
                          style={{
                            color: 'red',
                            marginRight: 5,
                            fontSize: 16
                          }}>
                          *
                        </span>
                        项目分类
                      </>
                    }
                    className=''
                    wrapperCol={{ span: 10 }}>
                    <div className='flex gap-4'>
                      <Form.Item name='categoryId' className='flex-1'>
                        <Select
                          className='flex-1'
                          options={cateOptions}
                          onChange={value => {
                            const cate = cateOptions?.find(
                              v => v.value === value
                            );
                            form.setFieldsValue({ categoryName: cate?.label });
                          }}
                        />
                      </Form.Item>
                      <Button
                        type='ghost'
                        onClick={() =>
                          history.push('/familyOrder/m-proj-cates')
                        }>
                        维护分类
                      </Button>
                    </div>
                  </Form.Item>
                  <Form.Item
                    label='项目图片'
                    name='url'
                    extra='支持图片格式：jpg、png、jpeg，大小不超过2M，建议尺寸：144px * 144px'>
                    <UploadImg
                      arrValue={false}
                      showUploadList={{
                        showPreviewIcon: true,
                        showRemoveIcon: true,
                        showDownloadIcon: false
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    label={
                      <>
                        <span
                          style={{
                            color: 'red',
                            marginRight: 5,
                            fontSize: 16
                          }}>
                          *
                        </span>
                        服务时间
                      </>
                    }>
                    <Space align='baseline' size={0}>
                      <Form.Item name='serviceTime'>
                        <Radio.Group onChange={handleServTimeChange}>
                          <Radio value={ServiceTime.WORKDAY}>
                            工作日 (9:00-18:00)
                          </Radio>
                          <Radio value={ServiceTime.ALL_DAY}>24小时</Radio>
                          <Radio value={ServiceTime.OTHER}>其他</Radio>
                        </Radio.Group>
                      </Form.Item>
                      <Form.Item name='serviceTimeCustom' className='w-fit'>
                        <TimePicker.RangePicker disabled={otherDisabled} />
                      </Form.Item>
                    </Space>
                  </Form.Item>

                  <Form.Item
                    label={
                      <>
                        <span
                          style={{
                            color: 'red',
                            marginRight: 5,
                            fontSize: 16
                          }}>
                          *
                        </span>
                        服务状态
                      </>
                    }
                    name='status'>
                    <Radio.Group>
                      <Radio value={1}>启用</Radio>
                      <Radio value={0}>停用</Radio>
                    </Radio.Group>
                  </Form.Item>
                  <Form.List name='consumables'>
                    {(fields, { add, remove }) => (
                      <Form.Item
                        label={
                          <>
                            <span
                              style={{
                                color: 'red',
                                marginRight: 5,
                                fontSize: 16
                              }}>
                              *
                            </span>
                            项目耗材
                          </>
                        }>
                        <div className='flex flex-col gap-4'>
                          {fields.map((field, i) => (
                            <Space
                              key={field.fieldKey}
                              align='baseline'
                              size={20}
                              className='children:w-full'>
                              <Form.Item
                                {...field}
                                name={[field.name, 'consumablesId']}
                                className='w-64'>
                                <Select
                                  placeholder='请选择耗材'
                                  suffixIcon={field.fieldKey}
                                  options={materialOptions}
                                  onChange={() => {
                                    const values = form.getFieldValue(
                                      'consumables'
                                    );
                                    const totalFee = calculateConsumablesFee(
                                      values
                                    );
                                    form.setFieldsValue({
                                      consumablesFee: totalFee,
                                      fee:
                                        Number(totalFee) +
                                        Number(
                                          form.getFieldValue('serviceFee') || 0
                                        ) +
                                        Number(
                                          form.getFieldValue('distanceFee') || 0
                                        ) +
                                        Number(
                                          form.getFieldValue(
                                            'walkServiceFee'
                                          ) || 0
                                        )
                                    });
                                  }}
                                />
                              </Form.Item>
                              <Form.Item
                                {...field}
                                name={[field.name, 'num']}
                                label='数量'
                                required>
                                <InputNumber
                                  placeholder='请输入数量'
                                  onChange={() => {
                                    const values = form.getFieldValue(
                                      'consumables'
                                    );
                                    const totalFee = calculateConsumablesFee(
                                      values
                                    );
                                    form.setFieldsValue({
                                      consumablesFee: totalFee,
                                      fee:
                                        Number(totalFee) +
                                        Number(
                                          form.getFieldValue('serviceFee') || 0
                                        ) +
                                        Number(
                                          form.getFieldValue('distanceFee') || 0
                                        ) +
                                        Number(
                                          form.getFieldValue(
                                            'walkServiceFee'
                                          ) || 0
                                        )
                                    });
                                  }}
                                />
                              </Form.Item>
                              <MinusCircleOutlined
                                onClick={() => {
                                  remove(field.name);
                                  const values = form.getFieldValue(
                                    'consumables'
                                  );
                                  const totalFee = calculateConsumablesFee(
                                    values
                                  );
                                  form.setFieldsValue({
                                    consumablesFee: totalFee,
                                    fee:
                                      Number(totalFee) +
                                      Number(
                                        form.getFieldValue('serviceFee') || 0
                                      ) +
                                      Number(
                                        form.getFieldValue('distanceFee') || 0
                                      ) +
                                      Number(
                                        form.getFieldValue('walkServiceFee') ||
                                          0
                                      )
                                  });
                                }}
                              />
                            </Space>
                          ))}
                          <Form.Item>
                            <Button type='dashed' onClick={() => add()}>
                              添加耗材
                            </Button>
                          </Form.Item>
                        </div>
                      </Form.Item>
                    )}
                  </Form.List>
                  <Form.Item
                    label={
                      <>
                        <span
                          style={{
                            color: 'red',
                            marginRight: 5,
                            fontSize: 16
                          }}>
                          *
                        </span>
                        耗材费用
                      </>
                    }
                    name='consumablesFee'>
                    <InputMoney
                      onChange={value => {
                        const serviceFee =
                          form.getFieldValue('serviceFee') || 0;
                        const distanceFee =
                          form.getFieldValue('distanceFee') || 0;
                        const walkServiceFee =
                          form.getFieldValue('walkServiceFee') || 0;
                        form.setFieldsValue({
                          fee:
                            Number(value || 0) +
                            Number(serviceFee) +
                            Number(distanceFee) +
                            Number(walkServiceFee)
                        });
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    label={
                      <>
                        <span
                          style={{
                            color: 'red',
                            marginRight: 5,
                            fontSize: 16
                          }}>
                          *
                        </span>
                        项目服务价格
                      </>
                    }
                    name='serviceFee'>
                    <InputMoney
                      onChange={value => {
                        const consumablesFee =
                          form.getFieldValue('consumablesFee') || 0;
                        const distanceFee =
                          form.getFieldValue('distanceFee') || 0;
                        const walkServiceFee =
                          form.getFieldValue('walkServiceFee') || 0;
                        form.setFieldsValue({
                          fee:
                            Number(value || 0) +
                            Number(consumablesFee) +
                            Number(distanceFee) +
                            Number(walkServiceFee)
                        });
                      }}
                    />
                  </Form.Item>
                  <Form.Item label='上门服务费' name='walkServiceFee'>
                    <InputMoney
                      defaultValue={0}
                      onChange={value => {
                        const consumablesFee =
                          form.getFieldValue('consumablesFee') || 0;
                        const serviceFee =
                          form.getFieldValue('serviceFee') || 0;
                        const distanceFee =
                          form.getFieldValue('distanceFee') || 0;
                        form.setFieldsValue({
                          fee:
                            Number(value || 0) +
                            Number(consumablesFee) +
                            Number(distanceFee) +
                            Number(serviceFee)
                        });
                      }}
                    />
                  </Form.Item>
                  <Form.Item label='路程费' name='distanceFee'>
                    <InputMoney
                      onChange={value => {
                        const consumablesFee =
                          form.getFieldValue('consumablesFee') || 0;
                        const serviceFee =
                          form.getFieldValue('serviceFee') || 0;
                        const walkServiceFee =
                          form.getFieldValue('walkServiceFee') || 0;
                        form.setFieldsValue({
                          fee:
                            Number(value || 0) +
                            Number(consumablesFee) +
                            Number(serviceFee) +
                            Number(walkServiceFee)
                        });
                      }}
                    />
                  </Form.Item>
                  <Form.Item label='路程费说明' name='distanceNote'>
                    <Input.TextArea rows={4} maxLength={2000} showCount />
                  </Form.Item>
                  <Form.Item
                    label={
                      <>
                        <span
                          style={{
                            color: 'red',
                            marginRight: 5,
                            fontSize: 16
                          }}>
                          *
                        </span>
                        费用总计
                      </>
                    }>
                    <Space align='baseline'>
                      <Form.Item name='fee'>
                        <InputMoney />
                      </Form.Item>
                      <p className='mb-0 text-xs text-gray-500'>
                        (备注：费用总计=耗材费用+服务费用+上门服务费+路程费)
                      </p>
                    </Space>
                  </Form.Item>

                  <Form.Item
                    label={
                      <>
                        <span
                          style={{
                            color: 'red',
                            marginRight: 5,
                            fontSize: 16
                          }}>
                          *
                        </span>
                        项目介绍
                      </>
                    }
                    name='introduce'>
                    <Editor className={styles.editor} />
                  </Form.Item>
                  <Form.Item
                    label={
                      <>
                        <span
                          style={{
                            color: 'red',
                            marginRight: 5,
                            fontSize: 16
                          }}>
                          *
                        </span>
                        服务内容
                      </>
                    }
                    name='serviceContent'>
                    <Editor className={styles.editor} />
                  </Form.Item>
                  <Form.Item
                    label={
                      <>
                        <span
                          style={{
                            color: 'red',
                            marginRight: 5,
                            fontSize: 16
                          }}>
                          *
                        </span>
                        预约须知
                      </>
                    }
                    name='notice'>
                    <Editor className={styles.editor} />
                  </Form.Item>
                  <div className='flex justify-center gap-4 w-full'>
                    <Button
                      type='primary'
                      ghost
                      onClick={() => history.goBack()}>
                      取&nbsp;消
                    </Button>
                    <Button type='primary' onClick={() => handleConfirm()}>
                      保&nbsp;存
                    </Button>
                  </div>
                </Form>
              </div>
            )
          }
        ]}
      />
    </PageSuspense>
  );
}
