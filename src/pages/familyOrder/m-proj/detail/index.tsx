import { Form, Table } from 'antd';
import {
  DetailLayout,
  FormDescriptions,
  PreviewImg,
  getPrice
} from 'parsec-admin';
import { useMemo } from 'react';

import API from '../api';
import CateAPI from '../../m-proj-cates/api';
import { RouteComponentProps, useParams } from 'react-router-dom';
import PageSuspense from '@src/layouts/PageSuspense';

export default function Content({ history }: RouteComponentProps) {
  const { id } = useParams<{ id: string }>();

  const [form] = Form.useForm();

  const { data: detail, loading } = API.useOne({
    params: { id },
    needInit: !!id
  });

  const catesAPI = CateAPI.useList({ params: { numPerPage: 100 } });

  const cateOptions = useMemo(() => {
    return catesAPI.data?.data?.recordList.map(v => ({
      label: v.name,
      value: v.id
    }));
  }, [catesAPI.data?.data]);

  console.log(detail?.data);
  const details = useMemo(() => {
    if (detail.data) {
      const list = (detail?.data?.consumables || []).map((item, index) => {
        return {
          key: item.id,
          name: item.consumablesName,
          num: item.num,
          price: item.fee,
          totalPrice: item.totalFee
        };
      });
      const arr = [
        ...list,
        {
          key: 'xmfwjg',
          name: '项目服务价格',
          num: 1,
          price: detail?.data?.serviceFee,
          totalPrice: detail?.data?.serviceFee
        },
        {
          key: 'smfwf',
          name: '上门服务费',
          num: 1,
          price: detail?.data?.walkServiceFee,
          totalPrice: detail?.data?.walkServiceFee
        },
        {
          key: 'lcf',
          name: '路程费',
          num: 1,
          price: detail?.data?.distanceFee,
          totalPrice: detail?.data?.distanceFee
        }
      ];

      return { ...detail.data, anyfree: arr };
    }
  }, [detail]);
  const columns: any = [
    {
      title: '费用名称',
      dataIndex: 'name'
    },
    {
      title: '数量',
      dataIndex: 'num'
    },
    {
      title: '单价/元',
      dataIndex: 'price',
      render: (v: number) => getPrice(v)
    },
    {
      title: '合计价格/元',
      dataIndex: 'totalPrice',
      render: (v: number) => getPrice(v)
    }
  ];

  return (
    <PageSuspense>
      <DetailLayout
        cardsProps={[
          {
            title: '项目详情',
            children: (
              <FormDescriptions
                data={details}
                edit={false}
                form={form}
                loading={loading}
                items={[
                  {
                    label: '项目名称',
                    name: 'name'
                  },
                  {
                    label: '项目分类',
                    name: 'categoryId',
                    render: val =>
                      (cateOptions || []).find(item => item.value === val)
                        ?.label
                  },
                  {
                    label: '项目状态',
                    name: 'status',
                    render: val => (val ? '启用' : '禁用')
                  },
                  {
                    label: '项目图片',
                    name: 'url',
                    render: v => <PreviewImg src={v} width={80} height={80} />
                  },
                  {
                    label: '服务时间',
                    name: 'serviceTime',
                    span: 3
                  },
                  {
                    label: '项目费用',
                    name: 'anyfree',
                    span: 3,
                    render: (v, record) => (
                      <Table
                        columns={columns}
                        dataSource={v}
                        bordered
                        pagination={false}
                        summary={() => (
                          <Table.Summary fixed>
                            <Table.Summary.Row>
                              <Table.Summary.Cell index={0}>
                                <span
                                  style={{ color: 'red', fontWeight: 'bold' }}>
                                  费用总计（元）
                                </span>
                              </Table.Summary.Cell>
                              <Table.Summary.Cell
                                index={1}></Table.Summary.Cell>
                              <Table.Summary.Cell
                                index={2}></Table.Summary.Cell>
                              <Table.Summary.Cell index={3}>
                                <span
                                  style={{ color: 'red', fontWeight: 'bold' }}>
                                  {getPrice(record?.fee || 0)}
                                </span>
                              </Table.Summary.Cell>
                            </Table.Summary.Row>
                          </Table.Summary>
                        )}
                      />
                    )
                  },
                  {
                    label: '路程费说明',
                    name: 'distanceNote',
                    span: 3,
                    render: v => <div dangerouslySetInnerHTML={{ __html: v }} />
                  },
                  {
                    label: '项目介绍',
                    name: 'introduce',
                    span: 3,
                    render: v => <div dangerouslySetInnerHTML={{ __html: v }} />
                  },
                  {
                    label: '服务内容',
                    name: 'serviceContent',
                    span: 3,
                    render: v => <div dangerouslySetInnerHTML={{ __html: v }} />
                  },
                  {
                    label: '预约须知',
                    name: 'notice',
                    span: 3,
                    render: v => <div dangerouslySetInnerHTML={{ __html: v }} />
                  }
                ]}
              />
            )
          }
        ]}
      />
    </PageSuspense>
  );
}
