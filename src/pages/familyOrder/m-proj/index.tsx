import MyTableList from '@src/components/myTableList';
import { Button, message, Modal, Space, Tag, Tooltip } from 'antd';
import { useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  DayRangePicker,
  handleSubmit,
  LinkButton,
  RouteComponentProps
} from 'parsec-admin';

import API from './api';
import PageSuspense from '@src/layouts/PageSuspense';

export default function ManageProject({ history }: RouteComponentProps) {
  const deleteAPI = API.useDelete({ needInit: false });

  const updateStatusAPI = API.useUpdateStatus({ needInit: false });
  const [selectRowKeys, setSelectRowKeys] = useState<any[]>([]);

  const handleImport = () => {
    const el = document.createElement('input');
    el.setAttribute('type', 'file');
    el.addEventListener('change', () => {
      const file = el.files?.[0];
      if (file) {
        message.info('批量导入接口未实现');
        console.log(file);
      }
    });
    el.click();
  };

  return (
    <PageSuspense>
      <MyTableList
        tableTitle='项目管理'
        action={
          <Space>
            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={() => history.push('/familyOrder/m-proj/create')}>
              新建项目
            </Button>
            <Button
              type='primary'
              onClick={() => {
                handleSubmit(() =>
                  updateStatusAPI.request({
                    ids: selectRowKeys,
                    status: 1
                  })
                );
              }}>
              批量启用
            </Button>
            <Button
              type='primary'
              onClick={() => {
                actionConfirm(
                  () =>
                    updateStatusAPI.request({
                      ids: selectRowKeys,
                      status: 0
                    }),
                  '',
                  {
                    template: '停用后用户无法看到这些项目，你还要继续吗？',
                    props: { okText: '继续' }
                  }
                );
              }}>
              批量停用
            </Button>
            <Button type='primary' onClick={handleImport}>
              批量导入
            </Button>
          </Space>
        }
        exportExcelButton
        getList={({ params }: { params: any }) => {
          const { sort, ...p } = params;

          return API.useList.request({
            ...p,
            pageNum: params.pageNum,
            numPerPage: 10
          });
        }}
        rowSelection={{
          onChange: selectedRowKeys => setSelectRowKeys(selectedRowKeys),
          selectedRowKeys: selectRowKeys
        }}
        columns={[
          {
            title: '项目名称',
            width: 180,
            dataIndex: 'name',
            fixed: 'left',
            search: true
          },
          {
            title: '项目分类',
            width: 180,
            dataIndex: 'categoryName',
            search: true
          },
          {
            title: '项目时间',
            width: 280,
            dataIndex: 'createTime',
            searchIndex: ['createStartTime', 'createEndTime'],
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                disabledDate={current => {
                  return current && current.valueOf() > Date.now();
                }}
              />
            )
          },
          {
            title: '项目介绍',
            width: 180,
            dataIndex: 'introduce',
            render: v => (
              <Tooltip
                placement='bottom'
                trigger='hover'
                overlayStyle={{ maxHeight: '200px', overflow: 'auto' }}
                title={<div dangerouslySetInnerHTML={{ __html: v }} />}>
                <div style={{ width: '160px', position: 'relative' }}>
                  <div
                    className='truncate'
                    dangerouslySetInnerHTML={{
                      __html: v?.replace(/<[^>]+>/g, '') // 移除HTML标签，只保留文本
                    }}
                  />
                </div>
              </Tooltip>
            )
          },
          {
            title: '项目费用',
            width: 180,
            dataIndex: 'fee',
            render: v => v && `${Number(v) / 100}元`
          },
          {
            title: '状态',
            width: 180,
            dataIndex: 'status',
            render: value => (
              <Tag color={value === 1 ? 'success' : 'default'}>
                {value === 1 ? '启用' : '停用'}
              </Tag>
            ),
            search: (
              <ArrSelect
                options={[
                  { label: '启用', value: 1 },
                  { label: '停用', value: 0 }
                ]}
              />
            )
          },
          {
            title: '操作时间',
            width: 180,
            dataIndex: 'updateTime'
          },
          {
            title: '操作人',
            width: 180,
            dataIndex: 'operatorName'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 200,
            fixed: 'right',
            render: (_, record) => (
              <ActionsWrap max={8}>
                <LinkButton
                  onClick={() => {
                    history.push(`/familyOrder/m-proj-detail/${record.id}`);
                  }}>
                  查&nbsp;看
                </LinkButton>
                {!record?.status && (
                  <LinkButton
                    onClick={() => {
                      history.push(`/familyOrder/m-proj/${record.id}`);
                    }}>
                    编&nbsp;辑
                  </LinkButton>
                )}
                <LinkButton
                  onClick={() => {
                    record.status === 1
                      ? actionConfirm(
                          () =>
                            updateStatusAPI.request({
                              ids: [record.id],
                              status: 0
                            }),
                          '',
                          {
                            template:
                              '停用后用户无法看到该项目，你还要继续吗？',
                            props: { okText: '继续' }
                          }
                        )
                      : handleSubmit(() => {
                          return updateStatusAPI.request({
                            ids: [record.id],
                            status: 1
                          });
                        });
                  }}>
                  {record.status === 1 ? '停\u2002用' : '启\u2002用'}
                </LinkButton>
                <LinkButton
                  type='danger'
                  onClick={() => {
                    actionConfirm(() => deleteAPI.request([record.id]), '删除');
                  }}>
                  删&nbsp;除
                </LinkButton>
              </ActionsWrap>
            )
          }
        ]}
      />
    </PageSuspense>
  );
}
