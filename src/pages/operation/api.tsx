import createApiHooks from 'create-api-hooks';
import {
  request,
  ListApiRequestParams,
  ListApiResponseData
} from 'parsec-admin';
import {
  ListApiRequestParams as ListApiRequestParams1,
  ListApiResponseData as ListApiResponseData1
} from '@src/configs/apis';

export const operationType: any = {
  DAY: '日间手术'
  // CHOSEN: '择期手术'
};
export const stateType: any = {
  '0': '预约中',
  '-1': '已取消'
};

export const sexType1: any = {
  F: '女',
  M: '男'
};
export const timeValType: any = {
  AM: '上午',
  PM: '下午'
};

export const operationAddress = ['VIP3楼5诊室'];

export const cancelReason: any = {
  OPT_NOT_ENOUGH: '术前准备不充分',
  IDICATIO_NOT_ENOUGH: '诊断改变/手术指征不足',
  DOCTOR_NOT_IN: '收治医生手术日不在',
  PATIENT_CANCEL_OPT: '患者要求取消手术',
  NO_OPT_ROOM: '没有手术间',
  ARRANGE_ERROR: '安排错误',
  OTHER: '其他原因'
};

export default {
  手术订单列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        type?: 'DAY' | 'CHOSEN';
        state?: 0 | -1;
        optName?: string;
        patientName?: string;
        bookBeginTime?: string;
        bookEndTime?: string;
        createBeginTime?: string;
        createEndTime?: string;
      }
    ) => {
      return request.get<
        ListApiResponseData<{
          id: number;
          createdAt: string;
          type: string;
          optName: string;
          bookedAt: string;
          address: string;
          patientName: string;
          patientAge: number;
          patientSex: number;
          state: number;
          timeVal: number;
        }>
      >('/mch/prescription/v1/opt-booking/operation', {
        params
      });
    }
  ),
  取消手术: createApiHooks(
    (params: { id: number; remark?: string; reason: string }) =>
      request.put('/mch/prescription/v1/opt-booking/operation/state', params)
  ),
  手术订单详情: createApiHooks((params: { id: number }) =>
    request.get<{
      id: number;
      type: string;
      createdAt: string;
      updatedAt: string;
      optName: string;
      bookedAt: string;
      address: string;
      patientName: string;
      patientAge: number;
      patientSex: string;
      status: number;
      appointmentTime: string;
      hisId: string;
      operator: number;
      remark: string;
      remarkType: number;
      remarkTypeStr: string;
      remarkAt: string;
      timeVal: number;
    }>('/mch/prescription/v1/opt-booking/operation/' + params.id)
  ),
  就诊人列表: createApiHooks(
    (
      data: ListApiRequestParams1 & {
        name?: string;
        sort?: string;
        hisId: string;
      }
    ) =>
      request.post<
        ListApiResponseData1<{
          id: number;
          name: string;
          hisId: string;
          hisName: string;
          userId: number;
          channelType: string;
          patientType: number;
          relationType: number;
          idType: number;
          idNo: string;
          sex: string;
          birthday: string;
          mobile: string;
          address: string;
          bindStatus: number;
          parentName: string;
          parentIdType: number;
          parentIdNo: string;
          patCardType: number;
          patCardNo: string;
          consumeType: string;
          isDefalut: number;
          isSelf: string;
          syncStatus: string;
          type: number;
          idImage: string;
          patInNo: string;
          bindMedicareCard: number;
          height: string;
          weight: string;
          married: string;
          smoking: string;
          patHisId: string;
          patHisNo: string;
          createTime: string;
          updateTime: string;
          accountId: string;
          patientId: string;
          patientName: string;
          patientMobile: string;
          patientSex: string;
          isDefault: string;
          patientImg: string;
          patientAge: string;
          platformId: string;
          patientAddress: string;
          openId: string;
          realName: string;
          birth: string;
          platformSource: string;
          age: string;
          inquiryTime: string;
          inquiryDept: string;
          inquiryDoctor: string;
          inquiryPurpose: string;
          userName: string;
        }>
      >('/mch/user/patient/page', data)
  ),
  手术预约添加: createApiHooks(
    (data: {
      id?: number;
      type: string;
      optName: string;
      optBookedAt: string;
      optAddress: string;
      patientId: string;
      patientName: string;
      patientAge: string;
      patientSex: string;
      patientPhone: string;
      patientCard: string;
      timeVal: number;
    }) => {
      if (data.id) {
        return request.put('/mch/prescription/v1/opt-booking/operation', data, {
          headers: { 'Content-Type': 'application/json; charset=UTF-8' }
        });
      } else {
        return request.post(
          '/mch/prescription/v1/opt-booking/operation',
          data,
          {
            headers: { 'Content-Type': 'application/json; charset=UTF-8' }
          }
        );
      }
    }
  )
};
