import React, { useState } from 'react';
import { Steps } from 'antd';
import Picture1 from '@src/images/refundStep1.png';
import Picture2 from '@src/images/refundStep2.png';
import Picture3 from '@src/images/refundStep3.png';
import Picture21 from '@src/images/refundStep21.png';
import Picture31 from '@src/images/refundStep31.png';
import { CardLayout, handleSubmit } from 'parsec-admin';
import ChoosePatient from '@components/choosePatient';
import FillInInfomation from '@components/choosePatient/fillInInfomation';
import ApplicationOK from '@components/choosePatient/applicationOK';
import useApi from '../api';
import { useHistory } from 'react-router-dom';

const { Step } = Steps;

export default () => {
  const history = useHistory();
  const [stepIndex, setStepIndex] = useState(0);
  const [patient, setPatient] = useState<any>({});
  const [result, setResult] = useState<any>({});

  return (
    <CardLayout>
      <Steps current={stepIndex}>
        <Step title='选择就诊人' icon={<img src={Picture1} alt='' />} />
        <Step
          title='填写预约资料'
          icon={<img src={stepIndex === 0 ? Picture21 : Picture2} alt='' />}
        />
        <Step
          title={'提交预约申请'}
          icon={<img src={stepIndex === 2 ? Picture3 : Picture31} alt='' />}
        />
      </Steps>
      {stepIndex === 0 && (
        <ChoosePatient
          onNext={() => setStepIndex(1)}
          onChange={value => {
            setPatient(value);
          }}
        />
      )}
      {stepIndex === 1 && (
        <FillInInfomation
          goBack={() => {
            setStepIndex(0);
            setResult({});
          }}
          onSubmit={value => {
            const newData = { ...value };
            newData.patientId = patient.id;
            newData.patientName = patient.name;
            newData.patientAge = patient.patientAge;
            newData.patientSex = patient.sex;
            newData.patientPhone = patient.mobile;
            newData.patientCard = patient.idNo;

            handleSubmit(() => {
              return useApi.手术预约添加
                .request({ ...newData })
                .then(response => {
                  setStepIndex(2);
                  setResult(response);
                });
            });
          }}
        />
      )}
      {stepIndex === 2 && (
        <ApplicationOK
          value={result}
          goBack={() => {
            history.goBack();
          }}
          goOn={() => {
            setStepIndex(0);
            setPatient({});
            setResult({});
          }}
        />
      )}
    </CardLayout>
  );
};
