import React from 'react';
import { CardLayout, FormDescriptions, Form, DateShow } from 'parsec-admin';
import styled from 'styled-components';
import useApi, {
  operationType,
  stateType,
  cancelReason,
  timeValType
} from '../api';
import { useParams } from 'react-router-dom';
import moment from 'moment';
import { timeFormat } from '../list';

const editSate = false;

export default () => {
  const { id: orderId } = useParams<any>();
  const [form] = Form.useForm();

  const { data, loading: detailLoading } = useApi.手术订单详情({
    params: { id: orderId || '' },
    needInit: !!orderId,
    initValue: { data: {} }
  });

  return (
    <Wrapper edit={false}>
      <CardLayout title={'患者信息'} loading={detailLoading}>
        <FormDescriptions
          data={data}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '姓名',
              name: 'patientName'
            },
            {
              label: '性别',
              name: 'patientSex',
              render: v => (v === 'M' ? '男' : '女')
            },
            {
              label: '年龄',
              name: 'patientAgeStr'
            },
            {
              label: '患者ID',
              name: 'patientId'
            },
            {
              label: '联系电话',
              name: 'patientPhone'
            },
            {
              label: '身份证号',
              name: 'patientCard'
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'手术信息'} loading={detailLoading}>
        <FormDescriptions
          data={data}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '手术类型',
              name: 'type',
              render: v => (v ? operationType[v] : '-')
            },
            {
              label: '手术名称',
              name: 'optName'
            },
            {
              label: '预约手术时间',
              name: 'optBookedAt',
              render: v =>
                moment(v).format(timeFormat) + ' ' + timeValType[data.timeVal]
            },
            {
              label: '地址',
              name: 'optAddress'
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'订单信息'} loading={detailLoading}>
        <FormDescriptions
          data={data}
          edit={editSate}
          form={form}
          loading={detailLoading}
          items={[
            {
              label: '预约状态',
              name: 'status',
              render: v => (v || v === 0 ? stateType[v] : '-')
            },
            {
              label: '申请时间',
              name: 'createAt',
              render: (v: any) => (v ? <DateShow>{v}</DateShow> : '')
            },
            {
              label: '申请单号',
              name: 'optBookingSn'
            },
            {
              label: '取消原因',
              name: 'remarkType',
              hidden: data.remarkType ? false : true,
              render: v => {
                return (
                  <span>
                    {v ? cancelReason[v] : '-'}
                    {data.remark ? '/' + data.remark : ''}
                  </span>
                );
              }
            },
            {
              label: '取消时间',
              name: 'remarkAt',
              hidden: data.remarkAt ? false : true,
              render: (v: any) => (v ? <DateShow>{v}</DateShow> : '')
            }
          ]}
        />
      </CardLayout>
    </Wrapper>
  );
};

const Wrapper = styled.div<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
`;
