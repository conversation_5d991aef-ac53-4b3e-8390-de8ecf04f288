import React, { useMemo, useState } from 'react';
import {
  LinkButton,
  ActionsWrap,
  DayRangePicker,
  ArrSelect,
  useModal,
  handleSubmit,
  Form,
  DateShow
} from 'parsec-admin';
import useApi, {
  operationType,
  stateType,
  cancelReason,
  timeValType,
  sexType1
} from '../api';
import OldTableList from '@components/oldTableList';
import { Input, Button } from 'antd';
import { useHistory } from 'react-router-dom';
import moment from 'moment';

const { TextArea } = Input;
export const timeFormat = 'YYYY-MM-DD';

export default () => {
  const [form] = Form.useForm();
  const history = useHistory();
  const [reasonType, setReasonType] = useState<any>(0);

  const switchModalVisible = useModal(() => ({
    title: '取消预约',
    form,
    onSubmit: values =>
      handleSubmit(() => {
        return useApi.取消手术.request({ ...values });
      }, '取消'),
    items: [
      {
        name: 'id',
        render: false
      },
      {
        label: '取消原因',
        name: 'remarkType',
        required: true,
        render: (
          <ArrSelect
            options={cancelReason}
            onChange={value => {
              setReasonType(value);
              form.resetFields(['remark']);
            }}
          />
        )
      },
      {
        label: '其他原因',
        name: 'remark',
        required: reasonType === 'OTHER' ? true : false,
        render: <TextArea rows={6} placeholder='请输入其他原因' />
      }
    ]
  }));

  return (
    <OldTableList
      tableTitle='订单列表'
      action={
        <Button
          type={'default'}
          onClick={() => {
            history.push('/operation/list/add');
          }}>
          添加预约
        </Button>
      }
      getList={({ pagination: { current }, params }) => {
        return useApi.手术订单列表.request({
          page: current,
          limit: 10,
          ...params
        });
      }}
      scroll={{ x: 1500 }}
      columns={useMemo(
        () => [
          {
            title: '申请日期',
            width: 200,
            dataIndex: 'createAt',
            searchIndex: ['createBeginAt', 'createEndAt'],
            search: <DayRangePicker />,
            render: v => <DateShow>{v}</DateShow>
          },
          {
            title: '手术类型',
            width: 100,
            dataIndex: 'type',
            render: val => operationType[val],
            search: <ArrSelect options={operationType} />
          },
          {
            title: '手术名称',
            width: 180,
            dataIndex: 'optName',
            search: true
          },
          {
            title: '预约手术时间',
            width: 200,
            dataIndex: 'optBookedAt',
            searchIndex: ['bookBeginAt', 'bookEndAt'],
            search: <DayRangePicker />,
            render: (v, record: any) => {
              return (
                moment(v).format(timeFormat) + ' ' + timeValType[record.timeVal]
              );
            }
          },
          {
            title: '地址',
            width: 200,
            dataIndex: 'optAddress'
          },
          {
            title: '就诊人信息',
            width: 200,
            dataIndex: 'patientName',
            render: (v, record: any) => {
              return (
                record.patientName +
                '|' +
                record.patientAgeStr +
                '|' +
                sexType1[record.patientSex]
              );
            }
          },
          {
            title: '就诊人姓名',
            dataIndex: 'patientName',
            render: false,
            search: true
          },
          {
            title: '预约状态',
            width: 100,
            dataIndex: 'status',
            render: val => stateType[val],
            search: <ArrSelect options={stateType} />
          },
          {
            title: '操作',
            fixed: 'right',
            width: 110,
            render: (record: any) => (
              <ActionsWrap>
                <LinkButton
                  onClick={() => {
                    history.push('/operation/list/operation/' + record.id);
                  }}>
                  查看
                </LinkButton>
                {record.status === 0 && (
                  <LinkButton
                    onClick={() => {
                      switchModalVisible({ ...record });
                    }}>
                    取消
                  </LinkButton>
                )}
              </ActionsWrap>
            )
          }
        ],
        [history, switchModalVisible]
      )}
    />
  );
};
