import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiRequestParams,
  ListApiResponseData,
  ApiResponse
} from '@src/configs/apis';

export default {
  科室管理列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
      }
    ) =>
      request.post<
        ListApiResponseData<{
          id: number;
          hisId: string;
          hisName: string;
          no: string;
          name: string;
          hasChild: number;
          pid: string;
          tel: string;
          sortNo: number;
          level: number;
          img: string;
          initials: string;
          status: string;
          skill: string;
          createTime: string;
          updateTime: string;
          summary: string;
          searchName: string;
          qrTicket: string;
          qrContent: string;
          qrUrl: string;
          qrTicketFollow: string;
          qrContentFollow: string;
          qrUrlFollow: string;
          employeeCount: number;
          medicalDepartment: string;
          menuLink: string;
          hospitalDeptNo: string;
          hospitalDeptName: string;
          standardDeptNo: string;
          standardDeptName: string;
          parnetDept: string;
          qrcodeType: string;
        }>
      >('/mch/his/dept/listPage', data)
  ),
  标准科室列表: createApiHooks((data: { hisId?: string }) =>
    request.post<ApiResponse<{ [value: string]: string }>>(
      '/mch/his/dept/getStandardDept',
      data
    )
  ),
  新增科室: createApiHooks(
    (data: {
      hisId?: string;
      hisName?: string;
      no?: string;
      name?: string;
      summary?: string;
      hasChild?: number;
      pid?: string;
      tel?: string;
      sortNo?: string;
      level?: number;
      img?: string;
      initials?: string;
      status?: string;
      skill?: string;
      employeeCount?: number;
      medicalDepartment?: string;
      menuLink?: string;
      hospitalDeptNo?: string;
      hospitalDeptName?: string;
      standardDeptNo?: string;
      standardDeptName: string;
    }) =>
      request.post<ApiResponse<Record<string, unknown>>>(
        '/mch/his/dept/add',
        data
      )
  ),
  修改科室: createApiHooks(
    (data: {
      id?: string;
      hisId?: string;
      hisName?: string;
      no?: string;
      name?: string;
      summary?: string;
      hasChild?: number;
      pid?: string;
      tel?: string;
      sortNo?: string;
      level?: number;
      img?: string;
      initials?: string;
      status?: string;
      skill?: string;
      employeeCount?: number;
      medicalDepartment?: string;
      menuLink?: string;
      hospitalDeptNo?: string;
      hospitalDeptName?: string;
      standardDeptNo?: string;
      standardDeptName?: string;
    }) =>
      request.post<ApiResponse<Record<string, unknown>>>(
        '/mch/his/dept/update',
        data
      )
  )
};
