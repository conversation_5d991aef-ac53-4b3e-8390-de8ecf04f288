import React, { useMemo } from 'react';
import {
  ArrSelect,
  useModal,
  UploadImg,
  ActionsWrap,
  LinkButton,
  actionConfirm,
  handleSubmit
} from 'parsec-admin';
import useApi from './api';
import MyTableList from '@components/myTableList';
import { Button, Input, InputNumber } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import permisstion from '@utils/permisstion';
import env from '@configs/env';

const { TextArea } = Input;

export default () => {
  const hisId = env.hisId;
  const {
    data: { data: standardDeptList }
  } = useApi.标准科室列表({
    params: { hisId },
    needInit: !!hisId,
    initValue: { standardDeptList: {} }
  });

  const switchPrizeModalVisible = useModal(
    ({ id }) => ({
      title: '新增/编辑科室',
      onSubmit: ({ ...params }: any) => {
        params.hisId = hisId;
        params.hisName = 'null';
        if (!params.id) {
          params.status = '1'; //新增科室时，默认可用
        }
        if (!params.hisId) {
          return Promise.reject();
        }
        return handleSubmit(
          () => (params.id ? useApi.修改科室 : useApi.新增科室).request(params),
          `${params.id ? '修改' : '新增'}`
        );
      },
      items: [
        {
          name: 'id',
          render: false
        },
        {
          label: '科室编码',
          name: 'no',
          required: true,
          render: () => <Input disabled={!!id} placeholder='请输入科室编码' />
        },
        {
          label: '科室名称',
          name: 'name',
          required: true
        },
        {
          label: '标准科室',
          name: 'standardDeptNo',
          render: <ArrSelect options={standardDeptList || {}} />
        },
        {
          label: '对应诊疗科目',
          name: 'medicalDepartment',
          render: <TextArea autoSize={{ minRows: 3, maxRows: 5 }} />,
          required: true
        },
        {
          label: '职工人数',
          name: 'employeeCount',
          render: <InputNumber min={0} style={{ width: '100%' }} />,
          required: true
        },
        {
          label: '科室排序号',
          name: 'sortNo',
          render: (
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              placeholder='输入的数值越小排名越靠前'
            />
          )
        },
        {
          label: '科室图标',
          name: 'img',
          render: (
            <UploadImg
              showUploadList={{
                showPreviewIcon: true,
                showRemoveIcon: true,
                showDownloadIcon: false
              }}
            />
          ),
          formItemProps: {
            extra: <span>建议上传正方形图标</span>
          }
        },
        {
          label: '联系电话',
          name: 'tel'
        },
        {
          label: '科室介绍',
          name: 'summary',
          render: <TextArea autoSize={{ minRows: 3, maxRows: 5 }} />,
          required: true
        }
      ]
    }),
    [hisId, standardDeptList]
  );

  return (
    <MyTableList
      tableTitle='科室管理'
      action={
        permisstion.canAddDept && (
          <Button
            type={'default'}
            icon={<PlusOutlined />}
            onClick={() => switchPrizeModalVisible()}>
            添加
          </Button>
        )
      }
      getList={({ params }) => useApi.科室管理列表.request(params)}
      showTool={false}
      columns={useMemo(
        () => [
          {
            title: '科室编码',
            dataIndex: 'no',
            width: 100
          },
          {
            title: '科室名称',
            dataIndex: 'name',
            width: 100,
            search: true
          },
          {
            title: '职工人数',
            dataIndex: 'employeeCount',
            width: 100,
            render: v => v || 0
          },
          {
            title: '科室排序',
            dataIndex: 'sortNo',
            width: 100
          },
          // {
          //   title: '科室介绍',
          //   dataIndex: 'summary',
          //   width: 100
          // },
          {
            title: '联系电话',
            dataIndex: 'tel',
            width: 100
          },
          {
            title: '状态',
            dataIndex: 'status',
            width: 100,
            render: v => {
              if (v === '1') {
                return '正常';
              } else if (v === '2') {
                return '停用';
              } else {
                return '-';
              }
            }
          },
          {
            title: '科室状态',
            dataIndex: 'status',
            width: 100,
            render: false,
            search: <ArrSelect options={{ '1': '正常', '2': '停用' }} />
          },
          {
            title: '创建时间',
            width: 150,
            dataIndex: 'createTime'
          },
          {
            title: '操作',
            fixed: 'right',
            width: 150,
            render: permisstion.canUpdateDept
              ? record => (
                  <ActionsWrap max={99}>
                    <LinkButton
                      onClick={() => {
                        switchPrizeModalVisible({
                          ...record,
                          standardDeptNo: Number(record.standardDeptNo || '')
                        });
                      }}>
                      编辑
                    </LinkButton>
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.修改科室.request({
                              hisId,
                              id: record.id,
                              status: record.status === '1' ? '2' : '1'
                            }),
                          record.status === '1' ? '停用' : '启用'
                        );
                      }}>
                      {record.status === '1' ? '停用' : '启用'}
                    </LinkButton>
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.修改科室.request({
                              hisId,
                              id: record.id,
                              status: '0'
                            }),
                          '删除'
                        );
                      }}>
                      删除
                    </LinkButton>
                    {/* <LinkButton
                  onClick={() => {
                    record.qrUrlFollow && window.open(record.qrUrlFollow);
                  }}>
                  随访二维码
                </LinkButton> */}
                    {/* <LinkButton
                  onClick={() => {
                    record.qrUrl && window.open(record.qrUrl);
                  }}>
                  二维码
                </LinkButton> */}
                  </ActionsWrap>
                )
              : false
          }
        ],
        [hisId, switchPrizeModalVisible]
      )}
    />
  );
};
