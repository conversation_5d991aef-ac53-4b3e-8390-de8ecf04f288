import React, { useEffect, useState } from 'react';
import { Switch, Col, Row, Form } from 'antd';

interface Iprops {
  onChange?: (v?: string) => void;
  value?: string;
}

export default (props: Iprops) => {
  const { value, onChange } = props;
  // 配置项，可以通过添加这个来动态添加权限
  const labelList = [
    '门诊加号',
    '报告解读',
    '在线处方',
    '检验检查',
    '远程会诊',
    '双向转诊'
  ];
  // 默认初始值
  const initialValue = labelList.map(label => `${label}|0`);
  const [valueList, setValueList] = useState<string[]>(
    value ? decodeURI(value).split(',') : initialValue
  );

  function handleSwitchChange(value: boolean, label: string) {
    const index = labelList.findIndex(item => item === label);
    const res = [...valueList];
    res[index] = value ? `${labelList[index]}|1` : `${labelList[index]}|0`;
    setValueList(res);
  }

  //更新回显值
  useEffect(() => {
    if (value && value !== encodeURI(valueList.join(','))) {
      setValueList(value.split(','));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  useEffect(() => {
    if (onChange) {
      onChange(encodeURI(valueList.join(',')));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [valueList]);

  return (
    <Row justify='space-between'>
      {labelList.map(label => {
        const checked =
          valueList.find(item => item.includes(label))?.split('|')[1] === '1';
        return (
          <Col span={4} key={label}>
            <Form.Item label={label}>
              <Switch
                checked={checked}
                onChange={value => handleSwitchChange(value, label)}
              />
            </Form.Item>
          </Col>
        );
      })}
    </Row>
  );
};
