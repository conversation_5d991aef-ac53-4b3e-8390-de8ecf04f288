import React, { useState, useEffect, useMemo } from 'react';
import {
  RouteComponentProps,
  CardLayout,
  UploadImg,
  handleSubmit
} from 'parsec-admin';
import { Spin, message, Cascader } from 'antd';
import styled from 'styled-components';
import useApi from '../doctor/api';
import {
  Form,
  Input,
  Col,
  Row,
  Button,
  Switch,
  Space,
  Select,
  DatePicker,
  InputNumber
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import MoNameSwitch from './moNameSwitch';
import { HospitalData } from '../info/apis';
import { useSessionStorage } from 'react-use';
import {
  doctorlevels,
  druglevels,
  nurselevels,
  categories,
  prescriptionQualifications,
  docType
} from '../doctor/d';
import moment from 'moment';
import { useLocation } from 'react-router-dom';
import env from '@configs/env';
const options = [
  {
    value: 'zhejiang',
    label: '门诊',
    disabled: false,
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake'
          },
          {
            value: 'xiasha',
            label: 'Xia Sha'
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: '住院',
    disabled: false,
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua men'
          }
        ]
      }
    ]
  }
];
const { RangePicker } = DatePicker;
const isDev = process.env.NODE_ENV !== 'production';
const MySwitch: React.FC<{
  disabled?: boolean;
  value?: string;
  onChange?: (v: string) => void;
}> = props => (
  <Switch
    disabled={props.disabled}
    checked={props.value === '1'}
    onChange={v => {
      props.onChange && props.onChange(v ? '1' : '0');
    }}
  />
);
const initInquirys = [
  { type: '1', isOnDuty: '0' },
  { type: '2', isOnDuty: '0' },
  { type: '3', isOnDuty: '0' },
  { type: '11', isOnDuty: '0' }
];
export default ({
  history,
  match: {
    params: { id, doctorId }
  }
}: RouteComponentProps) => {
  const locat = useLocation();
  // 1医生 2护士 3职员 4药师
  const type: docType = useMemo(() => {
    if (locat.pathname.includes('/doctor')) {
      return '1';
    }
    if (locat.pathname.includes('/nurse')) {
      return '2';
    }
    if (locat.pathname.includes('/drug')) {
      return '4';
    }
    return '1';
  }, [locat]);

  const name = useMemo(() => {
    if (type === '1') {
      return '医生';
    }
    if (type === '2') {
      return '护士';
    }
    if (type === '4') {
      return '药师';
    }
    return '医生';
  }, [type]);
  const levels = useMemo(() => {
    if (type === '1') {
      return doctorlevels;
    }
    if (type === '2') {
      return nurselevels;
    }
    if (type === '4') {
      return druglevels;
    }
    return doctorlevels;
  }, [type]);

  const hisId = env.hisId;
  const {
    data,
    request: requestDetail,
    loading: loadingDetail
  } = useApi.医生详情({
    params: {
      id,
      hisId,
      doctorId
    },
    needInit: !!id && !!hisId && !!doctorId
  });
  const { request: requestUpdate, loading: loadingUpdate } = useApi.医生更新({
    needInit: false
  });
  const { request: requestSave, loading: loadingSave } = useApi.医生新增({
    needInit: false
  });
  const {
    data: { data: deptList }
  } = useApi.科室列表({
    params: {
      hisId
    },
    needInit: !!hisId
  });
  const [form] = useForm();
  useEffect(() => {
    // 新建时 初始化
    form.setFieldsValue({
      inquirys: initInquirys,
      recommend: '0'
    });
    if (isDev || window.location.href.includes('debugger')) {
      form.setFieldsValue({
        image:
          'https://ihoss.oss-cn-beijing.aliyuncs.com/PIC/1562224854971-docMng.jpg',
        name: '测试',
        doctorId: Date.now() + '',
        mobile: '18696797630',
        idNumber: '500242199306302233',
        workingLife: 5,

        level: '1',
        position: '教学职称',
        practiceNumber: Date.now() + '',
        practiceMedicalInstitution: 'practiceMedicalInstitution',
        category: '1',
        policyNumber: Date.now() + '',
        insuranceDate: [moment(), moment()],

        deptId: '015',
        auditTime: moment(),
        practiceScope: 'practiceScope',
        practiceLevel: 'practiceLevel',
        prescriptionQualification: '0',
        underwritingUnit: 'underwritingUnit',
        ValidDate: [moment(), moment()],

        inquirys: [{ type: '1' }, { type: '2' }, { type: '3' }],
        // recommend: '0',
        // sortNo: 9,

        signatureImg:
          'https://ihoss.oss-cn-beijing.aliyuncs.com/PIC/1562224854971-docMng.jpg',
        specialty: 'specialty',
        introduction: 'introduction'
      });
    }
  }, [form]);

  useEffect(() => {
    if (
      data &&
      data?.data &&
      data?.data?.doctorVoList &&
      data?.data?.doctorVoList[0]
    ) {
      const detail = data?.data?.doctorVoList[0];
      const inquirys: Array<{
        type: string;
        isOnDuty: string;
        remune?: number;
        price?: number;
        amount?: number;
      }> = initInquirys;
      (detail.inquirys || []).forEach(item => {
        const inquiry = inquirys.find(x => x.type === item.type);
        if (inquiry) {
          inquiry.isOnDuty = item.isOnDuty;
          inquiry.remune = item.remune / 100;
          inquiry.price = item.price / 100;
          if (item.amount) {
            inquiry.amount = item.amount / 100;
          } else {
            inquiry.amount = undefined;
          }
        }
      });

      form.setFieldsValue({
        image: detail.image,
        name: detail.name,
        doctorId: detail.doctorId,
        mobile: detail.mobile,
        idNumber: detail.idNumber,
        workingLife: detail.workingLife,

        // level: detail.level, // level要取grade ，是的，你没看错
        level: detail.grade,
        position: detail.position,
        practiceNumber: detail.practiceNumber,
        practiceMedicalInstitution: detail.practiceMedicalInstitution,
        category: detail.category,
        policyNumber: detail.policyNumber,
        insuranceDate: detail.insuranceStartDate
          ? [moment(detail.insuranceStartDate), moment(detail.insuranceEndDate)]
          : null,

        deptId: detail.deptId,
        auditTime: detail.auditTime ? moment(detail.auditTime) : null,
        practiceScope: detail.practiceScope,
        practiceLevel: detail.practiceLevel,
        moNames: detail.moNames,
        prescriptionQualification: detail.prescriptionQualification,
        underwritingUnit: detail.underwritingUnit,
        ValidDate: detail.startValidDate
          ? [moment(detail.startValidDate), moment(detail.endValidDate)]
          : null,

        inquirys,
        recommend: detail.recommend || '0',
        sortNo:
          detail.sortNo !== undefined && detail.sortNo !== null
            ? detail.sortNo
            : 999,

        signatureImg: detail.signatureImg,
        specialty: detail.specialty,
        introduction: detail.introduction
      });
    }
  }, [data, form]);
  const [values, setValues] = useState<HospitalData>();
  const onFinish = (values: any) => {
    console.log('Success:', values);
    //保险时间
    if (values.insuranceDate) {
      values.insuranceStartDate = values.insuranceDate[0];
      values.insuranceEndDate = values.insuranceDate[1];
    }
    delete values.insuranceDate;
    // 有效时间
    if (values.ValidDate) {
      values.startValidDate = values.ValidDate[0];
      values.endValidDate = values.ValidDate[1];
    }

    // 要传中文
    values.level = levels.find(x => values.level === x.value)?.label;

    delete values.ValidDate;
    //服务设置
    values.inquirys.forEach((item: any) => {
      item.remune *= 100;
      item.price *= 100;
      item.amount *= 100;
    });
    values.inquirys = JSON.stringify(values.inquirys);
    values.type = type;
    console.log('Success:', values);
    if (id) {
      return handleSubmit(
        () =>
          requestUpdate({
            id,
            hisId,
            // doctorId,
            ...values
          }).then(() => {
            requestDetail();
          }),
        '保存'
      );
    } else {
      return handleSubmit(
        () =>
          requestSave({
            hisId,
            ...values
          }).then(res => {
            if (res.code === 0) {
              history.goBack();
            } else {
              message.error(res.msg);
            }
          }),
        '创建'
      );
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  const onValuesChange = (changedValues: any, values: any) => {
    if (values['hospitalIntroduction']) {
      values['hospitalIntroduction'] = values['hospitalIntroduction'].toHTML();
    }
    setValues(values);
  };
  function filter(inputValue: string, path: any) {
    return path.some(
      (option: any) =>
        option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1
    );
  }
  return (
    <Spin spinning={loadingDetail || loadingUpdate || loadingSave}>
      <Form
        name='hospitalInfoForm'
        size='large'
        form={form}
        initialValues={{ ...values }}
        scrollToFirstError={true}
        onValuesChange={onValuesChange}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}>
        <Wrapper edit={false}>
          <CardLayout title={'个人信息'}>
            <Row gutter={[16, 0]}>
              <Col span={8}>
                <Form.Item
                  label={`${name}头像`}
                  name='image'
                  rules={[
                    {
                      required: true,
                      message: `请上传${name}头像`
                    }
                  ]}
                  extra='建议尺寸为 126px * 168px'>
                  <UploadImg
                    showUploadList={{
                      showPreviewIcon: true,
                      showRemoveIcon: true,
                      showDownloadIcon: false
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label={`智慧医院科室`} name='name1'>
                  <Cascader
                    options={options}
                    placeholder='Please select'
                    showSearch={{ filter }}
                    tagRender={(v: any) => {
                      console.log(v);
                      return <div>fdafd</div>;
                    }}
                    multiple
                    maxTagCount='responsive'
                  />
                </Form.Item>
                <Form.Item
                  label={`${name}姓名`}
                  name='name'
                  rules={[
                    {
                      required: true,
                      message: `请输入${name}姓名`
                    }
                  ]}>
                  <Input placeholder={`请输入${name}姓名`} />
                </Form.Item>
                <Form.Item
                  label={`${name}编号`}
                  name='doctorId'
                  rules={[
                    {
                      required: true,
                      message: `请输入${name}编号`
                    }
                  ]}>
                  <Input placeholder={`请输入${name}编号`} />
                </Form.Item>
                <Form.Item
                  label='联系电话'
                  name='mobile'
                  rules={[
                    {
                      required: true,
                      message: '请输入联系电话'
                    }
                  ]}>
                  <Input placeholder='请输入联系电话' />
                </Form.Item>
                <Form.Item
                  label='身份证号'
                  name='idNumber'
                  rules={[
                    {
                      required: true,
                      // validator: (rule, value) => {
                      //   // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
                      //   // @ts-ignore
                      //   return import('idcard').then(({ default: idCard }) =>
                      //     idCard.verify(value)
                      //       ? Promise.resolve()
                      //       : Promise.reject('请输入正确的身份证号码')
                      //   );
                      // },
                      message: '请输入正确的身份证号码'
                    }
                  ]}>
                  <Input placeholder='请输入身份证号' />
                </Form.Item>
                <Form.Item label='从业年限'>
                  <Form.Item name='workingLife' noStyle>
                    {/* <InputNumber
                      placeholder='请输入从业年限'
                      style={{ width: '80%' }}
                    /> */}
                    <Input style={{ width: '80%' }} />
                  </Form.Item>
                  <span>&nbsp;&nbsp;&nbsp;&nbsp;年</span>
                </Form.Item>
              </Col>
            </Row>
          </CardLayout>
          <CardLayout title={'职业信息'}>
            <Row gutter={[16, 0]}>
              <Col span={12}>
                <Form.Item
                  label={`${name}职称`}
                  name='level'
                  rules={[
                    {
                      required: true,
                      message: `请选择${name}职称`
                    }
                  ]}>
                  <Select options={levels} placeholder={`请选择${name}职称`} />
                </Form.Item>

                <Form.Item
                  label='教学职称'
                  name='position'
                  rules={[
                    {
                      required: true,
                      message: `请输入教学职称`
                    }
                  ]}>
                  <Input placeholder='请输入教学职称' />
                </Form.Item>
                <Form.Item
                  label='执业证号'
                  name='practiceNumber'
                  rules={[
                    {
                      required: true,
                      message: `请输入执业证号`
                    }
                  ]}>
                  <Input placeholder='请输入执业证号' />
                </Form.Item>
                <Form.Item
                  label='执业医疗'
                  name='practiceMedicalInstitution'
                  rules={[
                    {
                      required: true,
                      message: `请输入执业医疗机构名称`
                    }
                  ]}>
                  <Input placeholder='请输入执业医疗机构名称' />
                </Form.Item>
                <Form.Item
                  label={`${name}类别`}
                  name='category'
                  rules={[
                    {
                      required: true,
                      message: `请选择${name}类别`
                    }
                  ]}>
                  <Select
                    options={categories}
                    placeholder={`请选择${name}类别`}
                  />
                </Form.Item>
                <Form.Item
                  label='保险单号'
                  name='policyNumber'
                  rules={[
                    {
                      required: true,
                      message: `请输入保险单号`
                    }
                  ]}>
                  <Input placeholder='请输入保险单号' />
                </Form.Item>
                <Form.Item
                  label='保险期限'
                  name='insuranceDate'
                  rules={[
                    {
                      required: true,
                      message: `请选择保险期限`
                    }
                  ]}>
                  <RangePicker placeholder={['请选择开始日期', '请选择结束']} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label='所属科室'
                  name='deptId'
                  rules={[
                    {
                      required: true,
                      message: '请选择所属科室'
                    }
                  ]}>
                  <Select
                    placeholder='请选择所属科室'
                    options={(deptList || []).map(dept => {
                      return {
                        label: dept.name,
                        value: dept.no + ''
                      };
                    })}
                  />
                </Form.Item>

                <Form.Item
                  label='首次获证'
                  name='auditTime'
                  rules={[
                    {
                      required: true,
                      message: `请选择首次获证日期`
                    }
                  ]}>
                  <DatePicker
                    placeholder='请选择首次获证日期'
                    style={{ width: '100%' }}
                  />
                </Form.Item>
                <Form.Item
                  label='执业范围'
                  name='practiceScope'
                  rules={[
                    {
                      required: true,
                      message: `请输入执业范围`
                    }
                  ]}>
                  <Input placeholder='请输入执业范围' />
                </Form.Item>
                <Form.Item
                  label='执业级别'
                  name='practiceLevel'
                  rules={[
                    {
                      required: true,
                      message: `请输入执业级别`
                    }
                  ]}>
                  <Input placeholder='请输入执业级别' />
                </Form.Item>
                <Form.Item
                  label='处方资格'
                  name='prescriptionQualification'
                  rules={[
                    {
                      required: true,
                      message: `请选择处方资格`
                    }
                  ]}>
                  <Select
                    options={prescriptionQualifications}
                    placeholder='请选择处方资格'
                  />
                </Form.Item>
                <Form.Item
                  label='承保单位'
                  name='underwritingUnit'
                  rules={[
                    {
                      required: true,
                      message: '请输承保单位'
                    }
                  ]}>
                  <Input placeholder='请输入承保单位' />
                </Form.Item>
                <Form.Item
                  label='有效时间'
                  name='ValidDate'
                  rules={[
                    {
                      required: true,
                      message: '请选择有效时间'
                    }
                  ]}>
                  <RangePicker placeholder={['请选择开始日期', '请选择结束']} />
                </Form.Item>
              </Col>
            </Row>
          </CardLayout>
          <CardLayout title={'服务设置'}>
            <Form.List name={'inquirys'}>
              {fields => {
                return (fields || []).map((field, index) => {
                  return (
                    <Row gutter={[16, 0]} key={field.fieldKey}>
                      <Col span={4}>
                        <Form.Item
                          label={
                            ['图文问诊', '电话问诊', '视频问诊', '健康咨询'][
                              index
                            ]
                          }
                          name={[field.name, 'isOnDuty']}>
                          <MySwitch />
                        </Form.Item>
                      </Col>
                      <Col span={7}>
                        <Form.Item
                          label='执行价格'
                          name={[field.name, 'remune']}>
                          <InputNumber
                            disabled={index !== 3}
                            placeholder='请输入（元）'
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={7}>
                        <Form.Item
                          label='展示原价'
                          name={[field.name, 'amount']}>
                          <InputNumber
                            disabled={index !== 3}
                            placeholder='请输入（元）'
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                      {/* {index === 2 && !!id && !!doctorId && (
                        <Col span={6}>
                          <span>排班号数：</span>
                          <InputNumber
                            disabled
                            value={sumResourceNumFromNowOn}
                            formatter={value => `${value}个号`}
                            // parser={value => (value || '').replace('%个号', '')}
                          />
                        </Col>
                      )} */}
                      {index === 2 && !!id && !!doctorId && (
                        <Col span={6}>
                          <Form.Item
                            label='排班号数'
                            name={[field.name, 'sumResourceNumFromNowOn']}>
                            <InputNumber
                              disabled
                              placeholder='排班号数'
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        </Col>
                      )}
                    </Row>
                  );
                });
              }}
            </Form.List>

            <Row gutter={[16, 0]}>
              <Col span={4}>
                <Form.Item
                  label='首页推荐'
                  name='recommend'
                  rules={[
                    {
                      required: false,
                      message: '请选择首页推荐'
                    }
                  ]}>
                  <MySwitch />
                </Form.Item>
              </Col>
              <Col span={7}>
                <Form.Item
                  label='排列序号'
                  name='sortNo'
                  required={false}
                  rules={[
                    {
                      required: true,
                      message: '请输入排列序号'
                    }
                  ]}>
                  <InputNumber
                    placeholder='请输入排列序号'
                    style={{ width: 200 }}
                  />
                </Form.Item>
              </Col>
              <Col span={8}></Col>
            </Row>
            {type === '1' && (
              <Form.Item name='moNames'>
                <MoNameSwitch />
              </Form.Item>
            )}
          </CardLayout>
          <CardLayout title={'电子签名'}>
            <Form.Item label='' name='signatureImg'>
              <UploadImg
                showUploadList={{
                  showPreviewIcon: true,
                  showRemoveIcon: true,
                  showDownloadIcon: false
                }}
              />
            </Form.Item>
          </CardLayout>
          <CardLayout title={'介绍信息'}>
            <Row gutter={[16, 0]}>
              <Col span={24}>
                <Form.Item
                  label='擅长领域'
                  name='specialty'
                  rules={[
                    {
                      required: true,
                      message: '请输入擅长领域'
                    }
                  ]}>
                  <Input.TextArea rows={5} placeholder='请输入擅长领域' />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label={`${name}介绍`}
                  name='introduction'
                  rules={[
                    {
                      required: true,
                      message: `请输入${name}介绍`
                    }
                  ]}>
                  <Input.TextArea rows={5} placeholder={`请输入${name}介绍`} />
                </Form.Item>
              </Col>
            </Row>
          </CardLayout>
          <CardLayout>
            <Form.Item style={{ textAlign: 'right' }}>
              <Space size={22}>
                <Button
                  type='ghost'
                  onClick={() => {
                    history.goBack();
                  }}>
                  取消
                </Button>
                <Button
                  loading={loadingDetail || loadingUpdate || loadingSave}
                  type='primary'
                  htmlType='submit'>
                  保存
                </Button>
              </Space>
            </Form.Item>
          </CardLayout>
        </Wrapper>
      </Form>
    </Spin>
  );
};

const Wrapper = styled.div<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
`;
