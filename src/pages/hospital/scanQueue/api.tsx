import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiResponseData,
  ListApiRequestParams,
  ApiResponse
} from '@apiHooks';

export default {
  扫码签到列表: createApiHooks((params: ListApiRequestParams) =>
    request.get<
      ListApiResponseData<{
        createTime: string;
        customizeType: string;
        deptId: string;
        doctorId: string;
        hisId: string;
        id: number;
        status: number;
        templateContent: string;
        templateName: string;
        type: number;
        updateTime: string;
      }>
    >('/intelligent/mch/intelligent/scan-queue/page', { params })
  ),
  新增扫码签到: createApiHooks((data: any) =>
    request.post<ApiResponse<Record<string, unknown>>>(
      '/intelligent/mch/intelligent/scan-queue/addScanQueue',
      data,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  // 停用启用
  putOnOff: createApiHooks((params: { id: string; status: number }) =>
    request.put<ApiResponse<undefined>>(
      `/intelligent/mch/intelligent/scan-queue/status/${params.id}`,
      params
    )
  ),
  编辑扫码签到: createApiHooks((params: any) =>
    request.post(
      '/intelligent/mch/intelligent/scan-queue/updateScanQueue',
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  )
};
