import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  ArrSelect,
  useModal,
  ActionsWrap,
  LinkButton,
  actionConfirm,
  handleSubmit,
  Form,
  CheckboxGroup
} from 'parsec-admin';
import useApi from '@src/pages/hospital/departmentSmart/api';
import api from './api';
import MyTableList from '@components/myTableList';
import { Button, Cascader, Input, Modal, Radio, Select, Switch } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useHistory } from 'react-router';
import env from '@configs/env';
import html2canvas from 'html2canvas';
import { TransferChange } from '@kqinfo/ui';
import { getTreeOptions } from '@pages/hospital/doctorDetailSmart/utils';
import Qrcode from '@components/qrcode';
import './index.less';
// import BraftEditor from 'braft-editor';

export default () => {
  const hisId = env.hisId;
  const [pageSize, setPageSize] = useState(1);
  const {
    data: { data: depList }
  } = useApi.科室管理列表({
    initValue: { data: { recordList: [] } }
  });
  const [form] = Form.useForm();
  const [districtData, setDistrictData] = useState([] as any);
  const [districtId, setdistrictId] = useState('');
  const [districtName, setdistrictName] = useState('');
  const [deptName, setdeptName] = useState('');
  const [deptCode, setdeptCode] = useState('');
  const filterArray = useCallback(
    (arr: Array<any>): any => {
      return arr
        ?.filter(item => {
          return (
            item.hisType === form.getFieldValue('hisType') ||
            item.hisType === (3 as any)
          );
        })
        ?.map(x => {
          return {
            label: x.name,
            value: x.id,
            children: x?.children ? filterArray(x?.children) : undefined
          };
        });
    },
    [form]
  );
  const [commendationVisible, setCommendationVisible] = useState(false);
  const [canvasImg, setCanvasImg] = useState('');
  const [recordData, setRecordData] = useState({} as any);
  const switchPrizeModalVisible = useModal(
    (record: any) => {
      const isEdit = !!record?.id;
      return {
        title: isEdit ? '编辑签到' : '添加签到',
        width: 800,
        form,
        onSubmit: ({ ...params }: any) => {
          params.hisId = hisId;
          params.districtId = districtId;
          params.districtName = districtName;
          params.deptName = deptName;
          params.deptCode = deptCode;
          if (isEdit) {
            return handleSubmit(
              () => api.编辑扫码签到.request({ ...params, id: record?.id }),
              `编辑`
            );
          } else {
            return handleSubmit(() => api.新增扫码签到.request(params), `新增`);
          }
        },
        myFormProps: {
          initValues: {
            record
          }
        } as any,
        items: [
          {
            label: '科室名称',
            required: true,
            name: 'deptName',
            render: (
              <TransferChange
                mode={'cascade'}
                data={getTreeOptions(depList?.recordList) as any}>
                <Cascader
                  expandTrigger='hover'
                  placeholder='请选择上级科室'
                  options={getTreeOptions(depList?.recordList) as any}
                  onChange={(value, selectedOptions) => {
                    setdistrictId(value[0] + '');
                    setdistrictName(selectedOptions[0]?.label + '');
                    setdeptName(
                      selectedOptions[selectedOptions.length - 1]?.label + ''
                    );
                    setdeptCode(value[value.length - 1] + '');
                    // setDistrictData(selectedOptions);
                  }}
                />
              </TransferChange>
            )
          },
          {
            label: '科室位置',
            name: 'address',
            required: true
          },
          {
            label: '诊区编码',
            name: 'areaId',
            required: true,
            render: (
              <Select
                options={[{ label: '109', value: 109 }]}
                placeholder={`请选择诊区编码`}
                disabled={isEdit}
              />
            )
          },
          {
            label: '科室二维码适配场景',
            name: 'scenario',
            required: true
          },
          {
            label: 'ip地址',
            name: 'ip',
            required: true,
            render: <Input disabled={isEdit} placeholder={`请输入ip地址`} />
          },
          {
            label: '状态',
            name: 'status',
            required: true,
            render: () => (
              <Radio.Group disabled={isEdit}>
                <Radio value={0}>停用</Radio>
                <Radio value={1}>启用</Radio>
              </Radio.Group>
            )
          }
        ]
      };
    },
    [hisId]
  );
  const wrapperRef = useRef(null);
  const handleConvertToImage = async () => {
    try {
      const node = wrapperRef.current;
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const canvas = await html2canvas(node!);
      const img = canvas.toDataURL('image/png');
      console.log('img', img);
      setCanvasImg(img);
      // 可以将img变量设置为图片标签的src属性来显示图片
    } catch (error) {
      console.error('Error generating image:', error);
    }
  };
  const base64ToBlob = code => {
    const raw = window.atob(code.substring(code.indexOf(',') + 1));
    const rawLength = raw.length;
    const uInt8Array = new Uint8Array(rawLength);
    for (let i = 0; i < rawLength; i++) {
      uInt8Array[i] = raw.charCodeAt(i);
    }
    return new Blob([uInt8Array]);
  };
  return (
    <div>
      <MyTableList
        tableTitle='院区列表'
        action={
          <ActionsWrap>
            <Button
              type={'primary'}
              icon={<PlusOutlined />}
              onClick={() => switchPrizeModalVisible()}>
              添加二维码
            </Button>
          </ActionsWrap>
        }
        getList={({ params }) => {
          setPageSize(params?.pageNum);
          return api.扫码签到列表.request(params);
        }}
        showTool={false}
        columns={useMemo(
          () => [
            {
              title: '序号',
              dataIndex: 'no',
              width: 100,
              render: (val, record: any, index) => {
                return (pageSize - 1) * 10 + index + 1;
              }
            },
            {
              title: '科室名称',
              dataIndex: 'deptName',
              width: 150,
              search: true
            },
            {
              title: '适配场景',
              width: 180,
              dataIndex: 'scenario'
            },
            {
              title: '院区',
              width: 100,
              dataIndex: 'districtName'
            },
            {
              title: '科室位置信息',
              width: 180,
              dataIndex: 'address'
            },
            {
              title: '排队序列号',
              width: 180,
              dataIndex: 'createTime'
            },
            {
              title: 'ip',
              width: 180,
              dataIndex: 'ip'
            },
            {
              title: '状态',
              width: 100,
              dataIndex: 'status',
              search: (
                <ArrSelect
                  options={[
                    { value: '1', children: '启用' },
                    { value: '0', children: '停用' }
                  ]}
                />
              ),
              render: (val, record: any) => {
                return (
                  <Switch
                    checkedChildren='ON'
                    unCheckedChildren='OFF'
                    checked={record.status === 0 ? false : true}
                    onClick={() => {
                      actionConfirm(
                        () => {
                          return api.putOnOff.request({
                            id: record.id,
                            status: record.state ? 0 : 1
                          });
                        },
                        record.state ? '停用' : '启用'
                      );
                    }}
                  />
                );
              }
            },
            {
              title: '操作',
              fixed: 'right',
              width: 280,
              render: record => {
                return (
                  <ActionsWrap max={99}>
                    {record?.status === 1 && (
                      <LinkButton
                        onClick={() => {
                          setRecordData(record);
                          setCommendationVisible(true);
                          setTimeout(() => {
                            handleConvertToImage();
                          }, 500);
                        }}>
                        查询签到二维码
                      </LinkButton>
                    )}
                    <LinkButton
                      onClick={() => {
                        setdistrictId(record?.districtId);
                        setdistrictName(record?.districtName);
                        setdeptName(record?.deptName);
                        setdeptCode(record?.deptCode);
                        switchPrizeModalVisible({
                          ...record
                        });
                      }}>
                      编辑
                    </LinkButton>
                  </ActionsWrap>
                );
              }
            }
          ],
          [switchPrizeModalVisible]
        )}
      />
      <Modal
        width={400}
        title='签到二维码'
        visible={commendationVisible}
        destroyOnClose={true}
        maskClosable={false}
        okText='下载'
        onCancel={() => {
          setCommendationVisible(false);
        }}
        onOk={() => {
          const data = base64ToBlob(canvasImg);
          const addElement = document.createElement('a');
          addElement.download = '签到二维码.png';
          addElement.style.display = 'none';
          addElement.href = URL.createObjectURL(data);
          document.body.appendChild(addElement);
          addElement.click();
          URL.revokeObjectURL(addElement.href);
          document.body.removeChild(addElement);
        }}>
        <img src={canvasImg} className='canvasContImg' />
        <div className='canvasCont' ref={wrapperRef}>
          <Qrcode
            url={`${window.location.origin}/patients/p${hisId}-his/#/pages2/usercenter/select-user/index?pageRoute=/pages2/signin/detail-sm/index&deptName=${recordData?.deptName}&queueId=${recordData?.id}&address=${recordData?.address}`}
            size={200}
          />
        </div>
      </Modal>
    </div>
  );
};
