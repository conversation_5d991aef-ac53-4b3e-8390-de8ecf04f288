import React, { useEffect, useState } from 'react';
import { actionConfirm, CardLayout } from 'parsec-admin';
import styled from 'styled-components';
import useApi, { Comment } from './api';
import { useParams } from 'react-router-dom';
import { Avatar, Image } from 'antd';
import { formatNumber } from '@src/utils/tools';
import { DeleteOutlined } from '@ant-design/icons';

export default () => {
  const { id } = useParams<any>();
  const [commentsList, setCommentsList] = useState<
    Array<Comment & { hide?: boolean }>
  >([]);
  const [page, setPage] = useState(1);

  const { data, loading: detailLoading } = useApi.文章详情({
    params: { id },
    needInit: !!id,
    initValue: {}
  });
  const {
    request,
    data: { list, pages },
    loading: commentsLoading
  } = useApi.文章评论({
    params: { id, limit: 10, page },
    needInit: !!id,
    initValue: []
  });

  useEffect(() => {
    if (list) {
      setCommentsList(list2 => {
        return list2.concat(list);
      });
    }
  }, [list, pages]);

  useEffect(() => {
    setCommentsList([]);
  }, []);

  return (
    <Wrapper edit={false}>
      <CardLayout title={''} loading={detailLoading}>
        <ArticleContent>
          <div className='article-title'>{data?.title}</div>
          <div className='article-otherInfo'>
            <div className='article-otherInfo-left'>
              <Avatar size={34} src={data.authorImageUrl} />
              <div className='article-otherInfo-left-doctor'>
                <span>{data.authorName} | </span>
                {data.authorLevel}
              </div>
              <div className='article-otherInfo-left-createTime'>
                {data.createTime}
              </div>
            </div>
            <div className='article-otherInfo-right'>
              <span>
                <i>{formatNumber(data.views)}</i> 浏览量
              </span>
              <span>
                <i>{formatNumber(data.likes)}</i> 点赞量
              </span>
              <span>
                <i>{formatNumber(data.comments)}</i> 评论量
              </span>
            </div>
          </div>
          <div className='article-content'>{data.content}</div>
          {data.imageUrls &&
            JSON.parse(data.imageUrls).map(
              (item: string | undefined, i: number) => {
                return <Image key={i} src={item} />;
              }
            )}
        </ArticleContent>
      </CardLayout>
      {commentsList && commentsList.length !== 0 && (
        <CardLayout title={'精彩评论'} loading={commentsLoading}>
          {(commentsList || []).map((item, i) => {
            return (
              <ArticleComments key={i}>
                {i !== 0 && <div className='article-comments-line'></div>}
                <div className='article-comments-title'>
                  <div className='article-comments-title-left'>
                    <Avatar size={30} src={item?.authorImageUrl} />
                    <span>{item.authorName}</span>
                  </div>
                  <div className='article-comments-title-right'>
                    {item.createTime}
                    <DeleteOutlined
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.删除评论.request({
                              id: item.id + ''
                            }),
                          '删除'
                        ).then(() => {
                          setCommentsList([]);
                          request();
                        });
                      }}
                    />
                  </div>
                </div>
                <div className='article-comments-content'>
                  <pre>{item.content}</pre>
                </div>
                {item.replies && item.replies.length !== 0 && (
                  <div
                    className={
                      item.hide
                        ? 'article-comments-replies article-comments-replies-hidden'
                        : 'article-comments-replies'
                    }>
                    {(item.replies || []).map((item1, i1) => {
                      return (
                        <div className='article-comments-reply' key={i1}>
                          <div className='article-comments-reply-left'>
                            <span>{item1.authorName}: </span>
                            {item1.content}
                          </div>
                          <div className='article-comments-reply-right'>
                            {item1.createTime}
                            {item1.deleteTime ? (
                              '已删除'
                            ) : (
                              <DeleteOutlined
                                onClick={() => {
                                  actionConfirm(
                                    () =>
                                      useApi.删除评论.request({
                                        id: item1.id
                                      }),
                                    '删除'
                                  ).then(() => {
                                    request();
                                  });
                                }}
                              />
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
                {item.replies && item.replies.length !== 0 && (
                  <div
                    className='article-comments-replyButton'
                    onClick={() => {
                      item.hide = !item.hide;
                      setCommentsList([...commentsList]);
                    }}>
                    {item.hide ? '展开' : '收起'}全部{item.replies.length}条回复
                  </div>
                )}
              </ArticleComments>
            );
          })}
          {pages && pages >= page ? (
            <div className='noMore-box'>没有更多评论了。。。</div>
          ) : (
            <div className='noMore-box' onClick={() => setPage(page + 1)}>
              加载更多
            </div>
          )}
        </CardLayout>
      )}
    </Wrapper>
  );
};

const Wrapper = styled.div<{ edit: boolean }>`
  .noMore-box {
    margin-top: 84px;
    margin-bottom: 40px;
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
  }
`;

const ArticleContent = styled.div`
  width: 100%;
  height: auto;
  padding: 0 100px;
  > .article-title {
    font-size: 20px;
    font-weight: bold;
    color: #333333;
    margin-top: 20px;
    margin-bottom: 30px;
  }
  > .article-otherInfo {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    > .article-otherInfo-left {
      display: flex;
      justify-content: space-between;
      align-items: center;
      > .article-otherInfo-left-doctor {
        margin: 0 58px 0 16px;
        font-size: 16px;
        color: #666666;
        > span {
          color: #333333;
          font-weight: bold;
        }
      }
      > .article-otherInfo-left-createTime {
        font-size: 16px;
        font-weight: 400;
        color: #999999;
      }
    }
    > .article-otherInfo-right {
      > span {
        margin-left: 40px;
        color: #666666;
        > i {
          font-style: normal;
          color: #2780d9;
        }
      }
    }
  }
  > .article-content {
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 32px;
    margin-bottom: 30px;
    white-space: pre-line;
  }
  > .ant-image {
    width: 100%;
    margin-bottom: 10px;
  }
`;
const ArticleComments = styled.div`
  width: 100%;
  height: auto;
  padding: 0 100px;
  > .article-comments-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;

    > .article-comments-title-left {
      display: flex;
      justify-content: space-between;
      align-items: center;
      > span {
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        margin-right: 10px;
      }
    }

    > .article-comments-title-right {
      font-size: 16px;
      font-weight: 400;
      color: #333333;
      > span {
        color: #ff9d46;
        font-size: 18px;
        margin-left: 28px;
        cursor: pointer;
      }
    }
  }
  > .article-comments-content {
    font-size: 16px;
    font-weight: 400;
    color: #333333;
    margin-bottom: 30px;
  }
  > .article-comments-line {
    width: 100%;
    height: 1px;
    background: #e3e6e8;
    opacity: 0.3;
    margin-bottom: 28px;
  }

  > .article-comments-replies {
    background: #f5f9fc;
    border-radius: 2px;
    padding: 0 18px;
    box-sizing: border-box;
    height: auto;
    > .article-comments-reply {
      width: 100%;
      min-height: 60px;
      box-sizing: border-box;
      border-bottom: 1px solid #e3e6e8;
      font-size: 16px;
      font-weight: 400;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      > .article-comments-reply-left {
        flex: 1;
        > span {
          color: #2780d9;
        }
      }
      > .article-comments-reply-right {
        width: 198px;
        margin-left: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        > span {
          color: #ff9d46;
          font-size: 18px;
          margin-left: 28px;
          cursor: pointer;
        }
      }
    }
    &.article-comments-replies-hidden {
      height: 0;
      opacity: 0;
      display: none;
    }
  }
  > .article-comments-replyButton {
    width: 100%;
    height: 60px;
    box-sizing: border-box;
    font-size: 16px;
    font-weight: 400;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #2780d9;
    cursor: pointer;
    background: #f5f9fc;
    margin-bottom: 36px;
    padding: 0 18px;
  }
`;
