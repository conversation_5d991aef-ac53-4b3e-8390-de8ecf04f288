import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ListApiResponseData } from 'parsec-admin';

export interface ArticleParams {
  hisId?: string;
  createTimeFrom?: string;
  createTimeTo?: string;
  limit?: number;
  page?: number;
  status?: string;
  title?: string;
  authorName?: string;
}
export interface ArticleCommentsParams {
  id?: string;
  limit?: number;
  page?: number;
}

interface ArticleDetail {
  allowReply?: boolean;
  authorDeptName?: string;
  authorImageUrl?: string;
  authorLevel?: string;
  authorName?: string;
  comments?: number;
  content?: string;
  createTime?: string;
  deleteTime?: string;
  hisId?: number;
  id?: number;
  imageUrls?: string;
  likes?: number;
  modifyTime?: string;
  title?: string;
  top?: string;
  userId?: number;
  views?: number;
}

export interface Comment {
  allowReply: boolean;
  authorImageUrl: string;
  authorName: string;
  comments: number;
  content: string;
  createTime: string;
  deleteTime: string;
  hisId: string;
  id: number;
  imageUrls: string;
  likes: number;
  modifyTime: string;
  title: string;
  top: string;
  userId: number;
  views: number;
  replies: any[];
}

export default {
  文章列表: createApiHooks((data: ArticleParams & { hisId?: string }) =>
    request.get<
      ListApiResponseData<{
        allowReply: boolean;
        authorImageUrl: string;
        authorName: string;
        comments: number;
        content: string;
        createTime: string;
        deleteTime: string;
        hisId: string;
        id: number;
        imageUrls: string;
        likes: number;
        modifyTime: string;
        title: string;
        top: string;
        userId: number;
        views: number;
      }>
    >('/mch/blog/article', {
      params: data
    })
  ),
  删除文章: createApiHooks((params: { id: string }) =>
    request.delete(`/mch/blog/article/${params.id}`)
  ),
  批量删除文章: createApiHooks((params: { ids: string }) =>
    request.delete('/mch/blog/article/-/batch', { params })
  ),
  文章详情: createApiHooks((params: { id: string }) =>
    request.get<ArticleDetail>('/mch/blog/article/' + params.id)
  ),
  文章评论: createApiHooks((data: ArticleCommentsParams) =>
    request.get<{
      list: Comment[];
      total: number;
      pageNum: number;
      pages?: number;
    }>(`/mch/blog/article/${data.id}/comments`, {
      params: {
        limit: data.limit,
        page: data.page
      }
    })
  ),
  删除评论: createApiHooks((params: { id: string }) =>
    request.delete(`/mch/blog/article/-/comments/${params.id}`)
  )
};
