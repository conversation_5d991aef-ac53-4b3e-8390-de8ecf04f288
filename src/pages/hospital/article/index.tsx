import React, { useMemo, useState } from 'react';
import {
  ActionsWrap,
  LinkButton,
  actionConfirm,
  DayRangePicker,
  ArrSelect
} from 'parsec-admin';
import useApi, { ArticleParams } from './api';
import OldTableList from '@components/oldTableList';
import { Button, message } from 'antd';
import { useHistory } from 'react-router-dom';
import { formatNumber } from '@src/utils/tools';
import moment from 'moment';
import env from '@configs/env';

export default () => {
  const history = useHistory();
  const hisId = env.hisId;
  const status = useMemo(
    () => [
      { value: 'ALL', children: '全部' },
      { value: 'PUBLISHED', children: '已发布' },
      { value: 'DELETED', children: '已删除' }
    ],
    []
  );

  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );

  return (
    <OldTableList
      tableTitle='文章列表'
      rowKey='id'
      action={
        <Button
          onClick={() => {
            if (selectedRowKeys.length === 0) {
              message.info('请选择需要删除的文章');
              return;
            }
            actionConfirm(
              () =>
                useApi.批量删除文章.request({
                  ids: selectedRowKeys.join(',')
                }),
              '删除'
            );
          }}>
          批量删除
        </Button>
      }
      rowSelection={{
        onChange: selectedRowKeys => setSelectedRowKeys(selectedRowKeys)
      }}
      getList={({ pagination: { current }, params }) => {
        const newParams: ArticleParams = {
          hisId,
          limit: 10,
          page: current
        };

        if (params.title) {
          newParams.title = params.title;
        }
        if (params.authorName) {
          newParams.authorName = params.authorName;
        }
        if (params.status) {
          newParams.status = params.status;
        }
        if (params.createTimeFrom) {
          newParams.createTimeFrom = moment(params.createTimeFrom).format(
            'YYYY-MM-DD'
          );
        }
        if (params.createTimeTo) {
          newParams.createTimeTo = moment(params.createTimeTo).format(
            'YYYY-MM-DD'
          );
        }

        return useApi.文章列表.request({ ...newParams });
      }}
      showTool={false}
      columns={useMemo(
        () => [
          {
            title: '文章标题',
            dataIndex: 'title',
            width: 300,
            search: true
          },
          {
            title: '医师姓名｜职称',
            dataIndex: 'authorName',
            width: 200,
            align: 'center',
            render: (v, record: any) =>
              `${record.authorName}｜${record.authorLevel || ''}`
          },
          {
            title: '医师姓名',
            searchIndex: 'authorName',
            search: true
          },
          {
            title: '科室',
            dataIndex: 'authorDeptName',
            width: 120,
            align: 'center'
          },
          {
            title: '发布时间',
            dataIndex: 'createTime',
            width: 180,
            align: 'center',
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                disabledDate={current => {
                  return current && current > moment().endOf('day');
                }}
              />
            ),
            searchIndex: ['createTimeFrom', 'createTimeTo']
          },
          {
            title: '浏览量（次）',
            dataIndex: 'views',
            width: 150,
            align: 'center',
            render: v => formatNumber(v)
          },
          {
            title: '点赞数',
            dataIndex: 'likes',
            width: 120,
            align: 'center',
            render: v => formatNumber(v)
          },
          {
            title: '评论数',
            dataIndex: 'comments',
            width: 120,
            align: 'center',
            render: v => formatNumber(v)
          },
          {
            title: '状态',
            width: 120,
            align: 'center',
            render: (v, record: any) => {
              if (record.deleteTime) {
                return '已删除';
              } else {
                return '已发布';
              }
            }
          },
          {
            title: '状态',
            search: <ArrSelect options={status} />,
            searchIndex: 'status'
          },
          {
            title: '操作',
            fixed: 'right',
            width: 150,
            align: 'center',
            render: (v, record: any) => (
              <ActionsWrap max={99}>
                <LinkButton
                  onClick={() => {
                    record.id && history.push('/content/article/' + record.id);
                  }}>
                  查看
                </LinkButton>
                {!record.deleteTime && (
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          useApi.删除文章.request({
                            id: record.id
                          }),
                        '删除'
                      );
                    }}>
                    删除
                  </LinkButton>
                )}
              </ActionsWrap>
            )
          }
        ],
        [history, status]
      )}
    />
  );
};
