import MyTableList from '@src/components/myTableList';
import { ArrSelect, useModal } from 'parsec-admin';
import { useEffect, useMemo, useState } from 'react';
import useApi, { Symptom } from '../api';
export type UseEditModalCallbackParams = any;

export const operationType1: any = {
  C: '检验',
  D: '检查',
  E: '治疗',
  F: '手术',
  G: '麻醉',
  H: '护理',
  I: '膳食',
  K: '输血',
  L: '输氧',
  M: '材料',
  Z: '其他'
  // CHOSEN: '择期手术'
};
const useSelectModal = () => {
  const { data } = useApi.查询症状项目信息({
    needInit: true
  });
  useEffect(() => {
    // console.log('data123', data);
  }, [data]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const columns = useMemo(
    () => [
      //选择项目
      {
        title: '名称',
        dataIndex: 'itemName',
        search: true
      },
      {
        title: '编码',
        dataIndex: 'itemCode'
      },
      {
        title: '类别',
        dataIndex: 'itemType',
        search: (
          <ArrSelect options={operationType1} placeholder='请输入手术类型' />
        ),
        render: (text: string) => operationType1[text]
      },
      {
        title: '单位',
        dataIndex: 'unit'
      }
    ],
    []
  );
  const modal = useModal(
    ({ resolve }) => {
      return {
        destroyOnClose: true,
        width: 1000,
        onSubmit: async () => {
          resolve(selectedRows);
        },
        children: (
          <MyTableList
            tableTitle={'选择症状'}
            getList={
              (async ({ params }) => {
                const res = await useApi.查询症状项目信息.request({
                  ...params,
                  hisType: '2'
                });
                return {
                  data: {
                    recordList: res.data,
                    totalCount: res?.data?.length
                  }
                };
              }) as any
            }
            columns={columns as any}
            rowSelection={{
              preserveSelectedRowKeys: true,
              onChange: (selectedRowKeys, selectedRows) => {
                setSelectedRowKeys(selectedRowKeys);
                setSelectedRows(selectedRows);
              }
            }}
            paginationExtra={
              <div>
                {!!selectedRowKeys.length && (
                  <span> 已选择{selectedRowKeys.length}条</span>
                )}
              </div>
            }
          />
        )
      };
    },
    [selectedRowKeys]
  );
  return () =>
    new Promise((resolve: (v: Symptom[]) => void) => {
      modal({
        resolve
      });
    });
};
export default useSelectModal;
