/*
 * @Author: hpengfei <EMAIL>
 * @Date: 2024-04-25 15:58:47
 * @LastEditors: hpengfei <EMAIL>
 * @LastEditTime: 2024-05-10 09:45:46
 * @FilePath: \ih-standard\src\pages\hospital\preConsultation\components\SelectSymptomsModal.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import MyTableList from '@src/components/myTableList';
import { useModal } from 'parsec-admin';
import { useMemo, useState } from 'react';
import useApi, { SymptomItem } from '../api';
export type UseEditModalCallbackParams = any;

const useSelectModal = (deptId: string) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const columns = useMemo(
    () => [
      //选择症状
      {
        title: '症状名称',
        dataIndex: 'symptomName',
        search: true
      },
      {
        title: '诊断编码',
        dataIndex: 'diagnosisCode'
      },
      {
        title: '诊断名称',
        dataIndex: 'diagnosisName'
      }
    ],
    []
  );
  const modal = useModal(
    ({ resolve }) => {
      return {
        destroyOnClose: true,
        width: 1000,
        onSubmit: async () => {
          resolve(selectedRows);
        },
        children: (
          <MyTableList
            tableTitle={'选择症状'}
            getList={({ params }) => {
              return useApi.症状列表分页.request({
                ...params,
                hisType: '2',
                deptId
              });
            }}
            columns={columns as any}
            rowSelection={{
              preserveSelectedRowKeys: true,
              onChange: (selectedRowKeys, selectedRows) => {
                setSelectedRowKeys(selectedRowKeys);
                setSelectedRows(selectedRows);
                console.log('改变');
              }
            }}
            paginationExtra={
              <div>
                {!!selectedRowKeys.length && (
                  <span> 已选择{selectedRowKeys.length}条</span>
                )}
              </div>
            }
          />
        )
      };
    },
    [selectedRowKeys]
  );
  return () =>
    new Promise((resolve: (v: SymptomItem[]) => void) => {
      modal({
        resolve
      });
    });
};
export default useSelectModal;
