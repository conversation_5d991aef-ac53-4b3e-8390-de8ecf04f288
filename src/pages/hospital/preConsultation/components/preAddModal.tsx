import MyTableList from '@src/components/myTableList';
import { useModal } from 'parsec-admin';
import { useMemo, useState } from 'react';
import useApi from '../api';
import { ListApiResponseData } from '@src/configs/d';
import { Input } from 'antd';
import env from '@configs/env';
export type UseEditModalCallbackParams = any;

const useAddModal = () => {
  const hisId = env.hisId;
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const columns = useMemo(
    () => [
      {
        title: '科室编号',
        width: 180,
        dataIndex: 'no'
      },
      {
        title: '科室名称',
        width: 100,
        dataIndex: 'name',
        searchIndex: 'deptName',
        search: (
          <Input
            placeholder={'请输入科室名'}
            onChange={() => {
              console.log('xxx');
            }}
          />
        )
      }
    ],
    []
  );
  const modal = useModal(
    ({ resolve }) => {
      return {
        destroyOnClose: true,
        width: 1000,
        onSubmit: async () => {
          return resolve(selectedRows);
        },
        children: (
          <MyTableList
            tableTitle='添加诊前开单科室'
            getList={async (params: any) => {
              const deptName = params?.params?.deptName;
              const queryParams = {
                hisType: 2,
                hisId,
                lastStage: true
              };
              if (deptName) {
                queryParams['deptName'] = deptName;
              }
              const data = await useApi.科室列表.request(queryParams);
              return ({
                code: 0,
                msg: '',
                data: {
                  currentPage: 1,
                  totalCount: (data.data as any)?.length,
                  recordList: data.data
                }
              } as unknown) as ListApiResponseData<any>;
            }}
            columns={columns}
            rowSelection={{
              preserveSelectedRowKeys: true,
              onChange: (selectedRowKeys, selectedRows) => {
                setSelectedRowKeys(selectedRowKeys);
                setSelectedRows(selectedRows);
              }
            }}
            paginationExtra={
              <div>
                {!!selectedRowKeys.length && (
                  <span> 已选择{selectedRowKeys.length}条</span>
                )}
              </div>
            }
          />
        )
      };
    },
    [selectedRowKeys]
  );
  return () =>
    new Promise((resolve: (v: Record<string, string>) => void) => {
      modal({
        resolve
      });
    });
};
export default useAddModal;
