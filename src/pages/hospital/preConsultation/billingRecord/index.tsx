import { useMemo, useRef, useState } from 'react';
import { ActionsWrap, DayRangePicker, RouteComponentProps } from 'parsec-admin';
import api from '../api';
import { Text } from 'remax/one';
import { Button } from 'antd';
import saveAs from 'file-saver';
import moment from 'moment';
import MyTableList from '@components/myTableList';

export default ({ history }: RouteComponentProps) => {
  const paramsRef = useRef<any>();
  const [pageSize, setPageSize] = useState(0);
  const { request: handleExport, loading: exportLoading } = api.exportTypes({
    needInit: false
  });
  const [queryParams, setQueryParams] = useState({} as any);
  return (
    <MyTableList
      tableTitle='开单记录列表'
      getList={({ params }) => {
        const { sort, ...p } = params;
        setQueryParams({
          ...params
        });
        setPageSize(params?.pageNum);
        return api.查询开单记录列表.request({
          ...p,
          pageNum: params.pageNum,
          numPerPage: 10
        });
      }}
      action={
        <ActionsWrap>
          <Button
            type={'default'}
            loading={exportLoading}
            onClick={() => {
              handleExport({ ...queryParams, isExport: 1 }).then(data =>
                saveAs(data, `开单记录统计.xls`)
              );
            }}>
            导出
          </Button>
        </ActionsWrap>
      }
      columns={[
        {
          title: '序号',
          width: 60,
          dataIndex: 'id',
          render: (_value, _row, index) => {
            return (pageSize - 1) * 10 + index + 1;
          }
        },
        {
          title: '来源机构',
          width: 140,
          dataIndex: 'cooperHospitalName',
          render: val => val || '-'
        },
        {
          title: '开单时间',
          width: 180,
          search: (
            <DayRangePicker
              placeholder={['开始时间', '结束时间']}
              valueFormat={'YYYY-MM-DD'}
            />
          ),
          searchIndex: ['startDate', 'endDate'],
          dataIndex: 'createTime'
        },
        {
          title: '开单科室',
          width: 100,
          dataIndex: 'deptName'
        },
        {
          title: '开单医生',
          width: 100,
          dataIndex: 'doctorName'
        },
        {
          title: '患者',
          width: 100,
          dataIndex: 'patientName',
          search: true
        },
        {
          title: '医技项目',
          width: 200,
          search: true,
          dataIndex: 'itemName'
        },
        {
          title: '开单状态',
          width: 100,
          dataIndex: 'status',
          render(text, record, index) {
            return Number(text) === 1 ? (
              <Text style={{ color: 'green' }}>成功</Text>
            ) : (
              <Text style={{ color: 'red' }}>失败</Text>
            );
          }
        },
        {
          title: '关联订单',
          width: 140,
          dataIndex: 'agtOrdNum'
        }
      ]}
    />
  );
};
