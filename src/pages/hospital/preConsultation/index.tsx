import React, { useState } from 'react';
import {
  ActionsWrap,
  LinkButton,
  actionConfirm,
  handleSubmit
} from 'parsec-admin';
import useApi from '@src/pages/hospital/departmentSmart/api';
import api from './api';
import MyTableList from '@components/myTableList';
import { Button, message } from 'antd';
import { useHistory } from 'react-router';
import env from '@configs/env';
import useAddModal from './components/preAddModal';
import { statusTypes } from './types';

export default () => {
  const history = useHistory();
  const hisId = env.hisId;
  const [pageSize, setPageSize] = useState(0);
  const addPreModal = useAddModal();

  //   const { run: addRun } = useRequest(async data => {
  //     const res = await api.添加开单科室(data);
  //     return res;
  //   });
  return (
    <>
      <MyTableList
        tableTitle='科室列表'
        action={
          <ActionsWrap>
            <Button
              type={'default'}
              onClick={() => {
                addPreModal().then((params: any) => {
                  handleSubmit(
                    () =>
                      api.添加开单科室.request({
                        hisId,
                        hisType: 2,
                        deptIds: params.map((item: any) => item?.no).toString()
                      }),
                    `增加`
                  );
                });
              }}>
              +添加开单科室
            </Button>
            <Button
              type={'primary'}
              onClick={() =>
                history.push('/hospital/preConsultation/billingRecord/index')
              }>
              开单记录
            </Button>
            <Button
              type={'primary'}
              onClick={() =>
                history.push('/hospital/preConsultation/symptomManage')
              }>
              症状管理
            </Button>
          </ActionsWrap>
        }
        getList={({ params }) => {
          setPageSize(params?.pageNum);
          return api.查询开单科室列表.request({
            ...params,
            hisType: '2',
            numPerPage: 10
          }) as any;
        }}
        showTool={false}
        columns={[
          {
            title: '序号',
            width: 100,
            dataIndex: 'id',
            render: (_value, _row, index) => {
              return (pageSize - 1) * 10 + index + 1;
            }
          },
          {
            title: '科室名称',
            dataIndex: 'deptName',
            width: 250,
            search: true
          },
          {
            title: '科室编码',
            dataIndex: 'deptId',
            width: 250
          },
          {
            title: '上级科室',
            dataIndex: 'pdeptName',
            width: 250
          },
          {
            title: '院区',
            dataIndex: 'district',
            width: 200
          },
          {
            title: '所属医疗机构',
            dataIndex: 'hospitalName',
            width: 200
          },
          {
            title: '症状',
            dataIndex: 'symptoms',
            width: 200,
            render: v => v?.map((item: any) => item?.name).join(';')
          },
          {
            title: '项目',
            dataIndex: 'items',
            width: 200,
            render: v => {
              return v?.map((item: any) => item?.itemName).join(',');
            }
          },
          {
            title: '状态',
            dataIndex: 'status',
            width: 200,
            render: v => statusTypes[v]
          },
          {
            title: '操作',
            fixed: 'right',
            width: 280,
            render: record => {
              return (
                <ActionsWrap max={99}>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          useApi.开单科室启停.request({
                            id: record.id,
                            status: record.status === '1' ? '0' : '1'
                          }),
                        record.status === '1' ? '停用' : '启用'
                      );
                    }}>
                    {record.status === '1' ? '停用' : '启用'}
                  </LinkButton>
                  <LinkButton
                    onClick={() =>
                      history.push(
                        `/hospital/preConsultation/billingSettings/${encodeURIComponent(
                          record.deptName
                        )}/${record.id}`
                      )
                    }>
                    开单设置
                  </LinkButton>
                  <LinkButton
                    onClick={() =>
                      history.push(
                        `/hospital/preConsultation/billingSettings/detail/${encodeURIComponent(
                          record.deptName
                        )}/${record.id}`
                      )
                    }>
                    详情
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      // 启用状态的科室不能删除
                      if (record.status === '1') {
                        message.warning('该科室已启用无法删除');
                        return;
                      }
                      actionConfirm(
                        () =>
                          useApi.删除开单科室.request({
                            id: record.id
                          }),
                        '删除'
                      );
                    }}>
                    <span style={{ color: 'red' }}>删除</span>
                  </LinkButton>
                </ActionsWrap>
              );
            }
          }
        ]}
      />
    </>
  );
};
