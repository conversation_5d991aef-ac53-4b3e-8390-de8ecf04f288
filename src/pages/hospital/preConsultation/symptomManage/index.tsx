/*
 * @Author: hpengfei <EMAIL>
 * @Date: 2024-05-13 10:47:18
 * @LastEditors: hpengfei <EMAIL>
 * @LastEditTime: 2024-06-17 15:43:52
 * @FilePath: \ih-standard\src\pages\hospital\preConsultation\symptomManage\index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useCallback, useEffect, useState } from 'react';
import {
  ActionsWrap,
  LinkButton,
  actionConfirm,
  Form,
  useModal,
  handleSubmit,
  ArrSelect
} from 'parsec-admin';
import api from '../api';
import MyTableList from '@components/myTableList';
// import { useHistory } from 'react-router';
import { But<PERSON>, Select } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useDebounce } from 'ahooks';
import env from '@src/configs/env';
// import env from '@configs/env';
// import styles from './index.module.less';

const formatData = (data: any) => {
  return data?.map(x => {
    return {
      label: x?.dictValue?.value,
      value: x?.dictKey
    };
  });
};

export default () => {
  //   const history = useHistory();
  const hisId = env.hisId;
  const [form] = Form.useForm();
  const { data, request: reqMainDict } = api.查询字典列表({
    params: {
      groupCode: 'mainDiagnosis',
      dictValue: '',
      numPerPage: 999
    },
    needInit: false
  });
  const { data: deptDataList } = api.科室列表({
    params: {
      hisType: 2,
      hisId,
      lastStage: false
    },
    needInit: true
  });
  useEffect(() => {
    console.log('datatest', deptDataList);
    if (data) {
      console.log('data', data);
    }
  }, [data, deptDataList]);
  const [filterData, setFilterData] = useState<
    {
      label: string;
      value: string;
    }[]
  >();
  const [searchKey, setSearchKey] = useState<string>('');
  const [diagnosisCode, setDiagnosisCode] = useState<string>('');
  const [pageSize, setPageSize] = useState(0);

  const keyDebounce = useDebounce(searchKey, { wait: 500 });
  // const filterData = useMemo(() => {
  //   if (data?.data?.recordList?.length) {
  //     return data?.data?.recordList?.map(x => {
  //       return {
  //         label: x?.dictValue?.value,
  //         value: x?.dictKey
  //       };
  //     });
  //   }
  // }, [data?.data?.recordList]);
  useEffect(() => {
    if (!filterData) {
      reqMainDict().then(data => {
        if (data?.data?.recordList?.length) {
          const arrs = formatData(data?.data?.recordList);
          setFilterData(arrs);
        }
      });
    }
  }, [filterData, reqMainDict]);
  useEffect(() => {
    reqMainDict({
      groupCode: 'mainDiagnosis',
      dictValue: keyDebounce,
      numPerPage: 999
    }).then(data => {
      setFilterData([]);
      // console.log('data?.data?.recordList', data?.data?.recordList);
      if (data?.data?.recordList?.length) {
        const arrs = formatData(data?.data?.recordList);
        setFilterData(arrs);
      }
    });
  }, [keyDebounce, reqMainDict]);
  const filterArray = useCallback(
    (arr: Array<any>): any => {
      return arr
        ?.filter(item => {
          return (
            item.hisType === form.getFieldValue('hisType') ||
            item.hisType === (3 as any)
          );
        })
        ?.map(x => {
          return {
            label: x.name,
            value: x.id,
            children: x?.children ? filterArray(x?.children) : undefined
          };
        });
    },
    [form]
  );
  //   ({ id }) => {
  //     return {
  //       title: id ? '编辑症状' : '添加症状',
  //       onSubmit:(values) =>{
  //         console.log(values);
  //         handleSubmit(() => {
  //           return useApi.科室管理列表.request({
  //             id: id
  //           }),
  //         });
  //       },
  //       myFormProps: {
  //         initValues: {
  //           status: '0'
  //         }
  //       } as any,
  //       items: [
  //         {
  //           title: '症状名称',
  //           dataIndex: 'name',
  //           width: 250
  //         },
  //         {
  //           title: '建议诊断代码',
  //           dataIndex: 'name',
  //           width: 250
  //         },
  //         {
  //           title: '所属科室',
  //           dataIndex: 'name',
  //           width: 250
  //         }
  //       ]
  //     };
  //   })
  const switchModalVisible = useModal(
    record => {
      return {
        onSubmit: values => {
          return handleSubmit(() => {
            if (record?.type === 'edit') {
              return api.编辑症状.request({
                hisId: env.hisId,
                diagnosisName: values?.diagnosisName,
                diagnosisCode: diagnosisCode,
                symptomName: values?.symptomName,
                deptId: values?.deptId,
                id: record.id
              });
            }
            return api.添加症状.request({
              hisId: env.hisId,
              diagnosisName: values?.diagnosisName,
              diagnosisCode: diagnosisCode,
              symptomName: values?.symptomName,
              deptId: values?.deptId
            });
          });
        },
        title: record ? '编辑症状' : '添加症状',
        items: [
          {
            name: 'symptomName',
            label: '症状名称'
          },
          {
            name: 'diagnosisName',
            label: '建议诊断名称',
            labelName: 'diagnosisName',
            render: (
              <Select
                filterOption={false}
                showSearch
                style={{ width: '100%' }}
                placeholder='建议诊断名称'
                options={filterData}
                onSearch={value => {
                  setSearchKey(value);
                }}
                onChange={value => {
                  console.log('value', value);
                  setDiagnosisCode(value);
                }}
              />
            )
          },
          {
            name: 'deptId',
            label: '所属科室',
            labelName: 'deptName',
            render: (
              <ArrSelect
                options={deptDataList?.data?.map(x => ({
                  label: x.name,
                  value: x.no
                }))}
              />
            )
          }
        ]
      };
    },
    [filterData, searchKey]
  );
  return (
    <>
      <MyTableList
        tableTitle='症状列表'
        action={
          <Button
            type={'primary'}
            icon={<PlusOutlined />}
            onClick={() => switchModalVisible()}>
            添加症状
          </Button>
        }
        getList={({ params, pagination }) => {
          setPageSize(params?.pageNum);
          return api.症状列表分页.request({
            ...params,
            hisType: '2'
          });
        }}
        showTool={false}
        columns={[
          {
            title: '序号',
            width: 100,
            render: (_value, _row, index) => {
              return (pageSize - 1) * 10 + index + 1;
            }
          },
          {
            title: '症状名称',
            dataIndex: 'symptomName',
            searchIndex: 'symptomName',
            width: 250,
            search: true
          },
          {
            title: '建议诊断代码',
            dataIndex: 'diagnosisCode',
            width: 250
          },
          {
            title: '建议诊断名称',
            dataIndex: 'diagnosisName',
            width: 250
          },
          {
            title: '所属科室',
            dataIndex: 'deptName',
            width: 200
          },
          {
            title: '创建人',
            dataIndex: 'createBy',
            width: 200
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 200
          },
          {
            title: '操作',
            fixed: 'right',
            width: 280,
            render: record => {
              return (
                <ActionsWrap max={99}>
                  <LinkButton
                    onClick={() => {
                      setDiagnosisCode(record.diagnosisCode);
                      switchModalVisible({
                        ...record,
                        type: 'edit'
                      });
                    }}>
                    编辑
                  </LinkButton>

                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          api.删除症状.request({
                            id: record.id
                          }),
                        '删除'
                      );
                    }}>
                    <span style={{ color: 'red' }}>删除</span>
                  </LinkButton>
                </ActionsWrap>
              );
            }
          }
        ]}
      />
    </>
  );
};
