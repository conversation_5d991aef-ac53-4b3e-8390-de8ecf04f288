import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiResponseData,
  ListApiRequestParams,
  ApiResponse1,
  ApiResponse,
  ListApiResponseData2,
  BillingRecord
} from '@apiHooks';
import { DicItem } from '@src/pages/operate/recipeTemplate/api';
type Questionnaire = {
  id: '@natural'; //主键ID
  hisId: '@natural'; //机构ID
  title: '@ctitle'; //诊前问卷标题
  surveyKey: '@guid'; //问卷key, 问卷唯一标识
  surveyTitle: '@ctitle'; //问卷标题（关联的问卷的标题）
  validDateStart: '@Date'; //起点有效时间
  validDateEnd: '@Date'; //终点有效时间
  state: '@pick(0,1)'; //状态，0：停用，1：启用
  creatorName: '@cname'; //创建人
  creatorId: '@natural(10000,90000)'; //创建人id
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //修改时间
  depts: [
    //科室列表
    {
      deptId: '@natural(100000)'; //科室id，deptMain主键id
      deptName: '@cword(4)科'; //科室名称
      deptNo: '489302'; //科室编号
    }
  ];
  scenes: "@pick('REGISTER','OP_PAY')"; //REGISTER挂号，OP_PAY门诊缴费
};

export type SymptomItem = {
  id: string; // 症状id
  symptomName: string; // 症状名称
  diagnosisCode: string; // 诊断代码
  diagnosisName: string; // 诊断名称
  deptName: string; // 科室
  hospitalName: string; // 所属医疗机构
  createBy: string; // 创建人
  createTime: string; // 创建时间
  items?: Symptom[]; // 项目列表
};
type QueryBillingDeptList = {
  deptName: string;
  deptCode: string;
  pDeptName: string;
  deptId: string;
  campus: string;
  id: number;
  hospitalName: string;
  symptoms: {
    symptomName: string;
  }[];
  itmes: {
    itemName: string;
  };
  status: number;
};
export interface DeptItem {
  id: number;
  hisId: 40009;
  name: '儿科';
  no: '10001';
  sortNo: 0;
  employeeCount: null;
  tel: '023-8973495873';
  status: 1;
  pid: 85;
  pathCode: '/p85/p86/';
  hisType: 2;
  isSummary: 1;
  address: '';
  createTime: '2021-08-23 08:53:48';
  updateTime: '2021-08-23 08:53:48';
  children: DeptItem[];
}
interface DeptTreeOptions1 {
  id: number;
  hisId: 40009;
  name: '儿科';
  no: '10001';
  sortNo: 0;
  employeeCount: null;
  tel: '023-8973495873';
  status: 1;
  pid: 85;
  pathCode: '/p85/p86/';
  hisType: 2;
  isSummary: 1;
  address: '';
  createTime: '2021-08-23 08:53:48';
  updateTime: '2021-08-23 08:53:48';
  deptName: '';
  deptId: '';
  children: DeptItem[];
}
export type Symptom = {
  itemId: string;
  itemCode: string;
  itemName: string;
  unit: string;
  itemType: string;
  isMust?: number;
  collectMethodsList?: {
    collectMethodId: string;
    collectMethodName: string;
    extFields: string;
  }[];
  itemCheckMethods?: any;
  itemCheckParts?: any;
  itemCollectMethods?: any;
  checkPartsList?: any;
  checkData?: Symptom;
};
export type BillingRecordList = {
  patientId: string;
  patientName: string;
  patCardNo: string;
  deptId: string;
  deptName: string;
  doctorId?: string;
  doctorName?: string;
  itemName?: string;
  status?: string;
  registerOrderId?: string;
  createTime?: string;
};
export default {
  分页查询诊前问卷详: createApiHooks(
    (
      params: ListApiRequestParams & {
        surveyKey?: string;
        deptId?: string;
        status?: string;
        searchStartTime?: string;
        searchEndTime?: string;
      }
    ) =>
      request.get<ListApiResponseData<Questionnaire>>(
        '/intelligent/mch/intelligent/pre-survey',
        {
          params
        }
      )
  ),
  院区列表: createApiHooks(
    (data: {
      hisType?: string | number;
      deptName?: string;
      status?: string;
      sort?: string;
      hisId?: string;
    }) =>
      request
        .get<
          ApiResponse1<
            {
              id: number;
              no: number;
              name: string;
              pid: number;
              img: string;
              sortNo: number;
              address: string;
              hospitalDeptNo: string;
            }[]
          >
        >('/mch/his/deptMain/district', {
          params: data
        })
        .then(res => {
          return {
            ...res,
            data: {
              code: res.data.code,
              msg: res.data.msg,
              data: {
                currentPage: 1,
                totalCount: res.data.data.length,
                recordList: res.data.data || []
              }
            }
          };
        })
  ),
  医院开通的业务平台列表: createApiHooks(() =>
    request.get<
      ApiResponse1<
        {
          code: number;
          desc: string;
        }[]
      >
    >('/mch/his/hospital-config/hisTypeList')
  ),
  查询开单科室列表: createApiHooks(
    (params: ListApiRequestParams & { hisType?: string; deptName?: string }) =>
      request.get<ListApiResponseData<QueryBillingDeptList>>(
        '/mch/his/billing/queryBillingDeptList',
        {
          params
        }
      )
  ),
  开单科室详情: createApiHooks((params: { id: string }) =>
    request.get<
      ApiResponse1<{
        deptName: string;
        deptCode: string;
        pDeptName: string;
        deptId: string;
        campus: string;
        id: number;
        hospitalName: string;
        symptoms: {
          symptomName: string;
          diagnosisName: string;
          diagnosisCode: string;
          items: Symptom[];
        }[];
        itmes: {
          itemName: string;
        };
        status: number;
        symptomPatterns?: number;
        isBack?: number;
      }>
    >(`/mch/his/billing/queryBillingDeptInfo/${params.id}`, {
      params
    })
  ),
  添加开单科室: createApiHooks((
    data: {
      hisId: string;
      deptIds: { deptId: number }[]; // 科室编码
      hisType: number;
    } // 所属业务平台 1互联网医院、2智慧医院、4智能随访
  ) =>
    request.post<ApiResponse<any>>('/mch/his/billing/addBillingDept', data, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  ),
  科室管理列表分页: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisType?: string;
        deptName?: string;
        districtId?: string;
      }
    ) =>
      request.get<ApiResponse1<ListApiResponseData2<DeptItem[]>>>(
        '/mch/his/deptMain/pageTree',
        {
          params: data
        }
      )
  ),
  科室列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        hisId: string;
        hisType: number;
        lastStage?: boolean;
        status?: 1 | 0;
        deptName?: string;
      }
    ) =>
      request.get<
        ApiResponse1<
          {
            id: '@natural'; //主键id
            hisId: '2219'; //医院hisid
            name: '@cword(2)科室'; //科室名称
            no: '@word(4)'; //科室编码
          }[]
        >
      >('/mch/his/deptMain/list', {
        params
      })
  ),
  症状列表分页: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisType?: string;
        symptomName?: string;
        deptId?: string;
      }
    ) =>
      request.get<ApiResponse1<ListApiResponseData2<SymptomItem[]>>>(
        '/mch/his/billing/querySymptomList',
        {
          params: data
        }
      )
  ),
  查询字典列表: createApiHooks(
    ({
      groupCode,
      ...params
    }: {
      groupCode?: string;
      pageNum?: number;
      dictValue?: string;
      numPerPage?: number;
    }) =>
      request.get<{
        code: number;
        data: {
          totalCount: number;
          recordList: DicItem[];
        };
      }>(`/kaiqiao/his/dictItem/page/${groupCode}`, {
        params
      })
  ),
  添加症状: createApiHooks(
    (params: {
      hisId: string; // 医院ID
      symptomName: string; // 症状名称
      diagnosisCode: string; // 诊断代码
      diagnosisName: string; // 诊断名称
      deptId: string; // 科室ID
    }) =>
      request.post<ApiResponse<any>>(
        '/mch/his/billing/addSymptomInfo',
        params,
        {
          headers: { 'Content-Type': 'application/json' }
        }
      )
  ),
  删除症状: createApiHooks((params: { id: string }) =>
    request.delete<ApiResponse<any>>(
      `/mch/his/billing/deleteSymptomInfo/${params.id}`,

      {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      }
    )
  ),
  编辑症状: createApiHooks(
    (params: {
      hisId: string; // 医院ID
      symptomName: string; // 症状名称
      diagnosisCode: string; // 诊断代码
      diagnosisName: string; // 诊断名称
      deptId: string; // 科室ID
      id: string; // 症状ID
    }) =>
      request.put<ApiResponse<any>>(
        '/mch/his/billing/updateSymptomInfo',
        params,
        {
          headers: { 'Content-Type': 'application/json' }
        }
      )
  ),
  查询症状项目信息: createApiHooks(
    (data: { hisType?: string; itemName?: string; itemType?: string }) =>
      request.get<
        ApiResponse1<
          {
            itemId: string;
            itemCode: string;
            itemName: string;
            unit: string;
            itemType: string;
          }[]
        >
      >('/mch/his/billing/querySymptomItemList', {
        params: data
      })
  ),
  设置开单科室症状信息: createApiHooks(
    (params: {
      symptomPatterns: 1 | 2;
      isBack: 0 | 1;
      id: number; // 科室ID
      symptomList: SymptomItem[];
      hisId: string; // 医院ID
    }) =>
      request.post<ApiResponse<any>>(
        '/mch/his/billing/updateBillingDeptInfo',
        params,
        {
          headers: { 'Content-Type': 'application/json' }
        }
      )
  ),
  根据项目id查询症状项目信息: createApiHooks((data: { id: string }) =>
    request.get<
      ApiResponse1<{
        itemId: string;
        itemCode: string;
        itemName: string;
        unit: string;
        itemType: string;
        checkPartsList?: any;
      }>
    >(`/mch/his/billing/querySymptomItemInfo/${data.id}`, {
      params: data
    })
  ),
  科室管理列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        hisType?: string | number; // 所属业务平台 1互联网医院、2智慧医院、3互/智
      }
    ) =>
      request
        .get<ApiResponse<DeptItem[]>>('/mch/his/deptMain', { params: data })
        .then(res => {
          return {
            ...res,
            data: {
              code: res.data.code,
              msg: res.data.msg,
              data: {
                currentPage: 1,
                totalCount: 1,
                recordList: res.data.data || []
              }
            }
          };
        })
  ),
  查询开单记录列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        createTime?: string;
        patientName?: string;
        patCardNo?: string;
      }
    ) =>
      request.get<BillingRecord<BillingRecordList>>(
        '/intelligent/mch/intelligent/billing/queryBillingRecordList',
        {
          params
        }
      )
  ),
  执行科室表: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        hisType?: string | number; // 所属业务平台 1互联网医院、2智慧医院、3互/智
      }
    ) =>
      request
        .get<ApiResponse<DeptTreeOptions1[]>>(
          '/mch/his/billing/queryBillingExecuteDeptList',
          { params: data }
        )
        .then(res => {
          return {
            ...res,
            data: {
              code: res.data.code,
              msg: res.data.msg,
              data: res.data.data || []
            }
          };
        })
  ),
  exportTypes: createApiHooks(
    (params: {
      hisId?: string;
      deptName?: string;
      checkStatus?: string;
      publicStatus?: string;
      doctorName?: string;
      phone?: string;
      hospitalName?: string;
      startDate?: string;
      endDate?: string;
    }) =>
      request.get<Blob>('/intelligent/mch/intelligent/billing/export', {
        responseType: 'blob',
        params
      })
  )
};
