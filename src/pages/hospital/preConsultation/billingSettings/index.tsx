import {
  ActionsWrap,
  ArrSelect,
  CardLayout,
  LinkButton,
  TableList,
  actionConfirm,
  handleSubmit,
  useModal
} from 'parsec-admin';
import { useEffect, useMemo, useState } from 'react';
import {
  Radio,
  Form,
  Row,
  Col,
  Switch,
  Button,
  message,
  Input,
  Checkbox
} from 'antd';
import useApi, { Symptom, SymptomItem } from '../api';
import env from '@configs/env';
// import styles from './index.module.less';
// import env from '@configs/env';
import useSelectModal from '../components/SelectSymptomsModal';
import useSelectItemModal, {
  operationType1
} from '../components/SelectItemModal';
import { useHistory, useParams } from 'react-router';
import styles from './index.module.less';
import classNames from 'classnames';
const { TextArea } = Input;
const getType = str => {
  if (str === 'C') {
    return '检验项目配置';
  } else if (str === 'D') {
    return '检查项目配置';
  }
  return '项目配置';
};
const CheckboxGroup = Checkbox.Group;
export default () => {
  const [form] = Form.useForm();
  const [selectionType, setSelectionType] = useState<1 | 2>(1);
  const [isCallBack, setIsCallBack] = useState<0 | 1>(0);
  const [methods, setMethods] = useState<any[]>([]);
  const [selectedRowKey, setSelectedRowKey] = useState<string>();
  const [symptomsData, setSymptomsData] = useState<SymptomItem[]>([]);
  // const [checkData, setCheckData] = useState<Symptom>();

  const useModal2 = useSelectItemModal();
  const { name, id } = useParams<{ name: string; id: string }>();
  const history = useHistory();
  const { goBack } = history;
  const isDetail = history.location.pathname.split('/').includes('detail');
  const {
    data: { data: depList }
  } = useApi.执行科室表({
    initValue: { data: { recordList: [] } }
  });
  const onFinish = async () => {
    if (symptomsData?.length) {
      handleSubmit(() => {
        return useApi.设置开单科室症状信息
          .request({
            id: deptInfo?.id as number,
            symptomList: symptomsData,
            isBack: isCallBack,
            symptomPatterns: selectionType,
            hisId: env.hisId
          })
          .then(() => {
            // history.goBack();
          })
          .finally(() => {
            // history.goBack();
          });
      }, '保存');
      // const res = await useApi.设置开单科室症状信息.request({
      //   id: deptInfo?.deptId as string,
      //   symptomList: symptomsData,
      //   isBack: isCallBack,
      //   symptomPatterns: selectionType
      // });
      // console.log('res', res);
    }
  };
  const { data: deptData } = useApi.开单科室详情({
    needInit: !!id,
    params: {
      id: id
    }
  });
  // useEffect(() => {
  //   console.log('testData', testData);
  // }, [testData]);
  // const { data: deptData } = useApi.查询开单科室列表({
  //   params: {
  //     hisType: '2',
  //     deptName: name
  //   },
  //   needInit: !!name
  // });

  const deptInfo = useMemo(() => {
    return deptData?.data;
  }, [deptData]);

  const useModal1 = useSelectModal(deptInfo?.deptId || '');

  useEffect(() => {
    if (deptInfo) {
      form.setFieldsValue({
        name: deptInfo?.symptomPatterns,
        name2: deptInfo?.isBack
      });
      setSymptomsData(deptInfo?.symptoms as SymptomItem[]);
    }
  }, [deptData, deptInfo, id]);

  const selectDepartItems = useMemo(() => {
    if (selectedRowKey) {
      return symptomsData.find(item => item.id === selectedRowKey)?.items || [];
    }
    return [];
  }, [selectedRowKey, symptomsData]);
  // 表格的列配置
  const columns = [
    {
      title: '症状名称',
      dataIndex: 'symptomName',
      key: 'symptomName'
    },
    {
      title: '对应诊断',
      dataIndex: 'diagnosisName',
      key: 'diagnosisName'
    },
    {
      title: '操作',
      width: 80,
      fixed: 'right',
      render: (v, record: any) => {
        return (
          !isDetail && (
            <ActionsWrap max={6}>
              <LinkButton
                onClick={() => {
                  actionConfirm(() => {
                    return Promise.resolve(
                      setSymptomsData(prev => {
                        return prev.filter(
                          item => item.symptomName !== record.symptomName
                        );
                      })
                    );
                  }, '删除');
                }}>
                删除
              </LinkButton>
            </ActionsWrap>
          )
        );
      }
    }
  ];

  const switchEditModalVisible = useModal(
    (p: Symptom) => {
      const { checkData } = p;
      if (getType(p.itemType) === '检验项目配置') {
        console.log('checkData', checkData);
        return {
          title: '检验项目配置',
          onSubmit: (values: any) => {
            let executionDeptName = '';
            depList ||
              ([] as any)?.filter(item => {
                if (item.deptId === values?.executionDeptCode) {
                  executionDeptName = item.deptName;
                }
              });
            const selectData = JSON.parse(
              JSON.stringify(
                checkData?.collectMethodsList?.find(
                  item => item.collectMethodId === values.collectMethod
                ) || ''
              )
            );

            if (selectData || p?.collectMethodsList?.length) {
              const arrs = JSON.parse(JSON.stringify(symptomsData));
              const newArrs = arrs?.map(item => {
                if (item.id === selectedRowKey) {
                  item?.items?.forEach(d => {
                    if (d.itemId === p.itemId) {
                      d.collectMethodsList = [selectData];
                      d.itemCheckParts = selectData.collectMethodName;
                      d.itemCheckMethods = selectData.extFields;
                      d.executionDeptCode = values?.executionDeptCode;
                      d.executionDeptName = executionDeptName;
                      d.reminder = values.reminder;
                    }
                  });
                }
                return item;
              });
              setSymptomsData(newArrs);
              message.success('配置成功');
              return Promise.resolve(selectData);
            } else {
              message.error('请选择检查方法');
              return Promise.reject('请选择检查方法');
            }
          },

          items: [
            {
              label: '项目名称',
              name: 'itemName',
              render: v => <Input value={v} disabled></Input>
            },
            {
              label: '标本类别',
              name: 'collectMethod',
              render: () => (
                <ArrSelect
                  options={
                    checkData?.collectMethodsList?.map(item => {
                      return {
                        label: item.collectMethodName,
                        value: item.collectMethodId
                      };
                    }) || []
                  }
                />
              )
            },
            ...(env.hisId === '40007'
              ? []
              : [
                  {
                    label: '执行科室',
                    name: 'executionDeptCode',
                    render: () => (
                      <ArrSelect
                        options={
                          depList?.length > 0
                            ? depList.map(x => ({
                                value: x.deptId,
                                children: x.deptName
                              }))
                            : [] // 确保返回空数组而不是布尔值
                        }
                        placeholder={'请选择执行科室'}
                      />
                    )
                  }
                ]),
            {
              label: '温馨提示',
              name: 'reminder',
              render: () => (
                <TextArea
                  rows={4}
                  maxLength={100}
                  placeholder='请输入温馨提示'
                />
              )
            }
          ]
        };
      }
      return {
        title: '检查项目配置',
        onSubmit: (values: any) => {
          let executionDeptName = '';

          depList ||
            ([] as any)?.filter(item => {
              if (item.deptId === values?.executionDeptCode) {
                executionDeptName = item.deptName;
              }
            });
          const selectData = [] as any;
          checkData?.checkPartsList?.map(item => {
            values?.partsName?.map(nav => {
              if (item.partsName === nav) {
                selectData.push(item);
              }
            });
          });
          if (
            (selectData && values?.methodId?.length) ||
            p?.checkPartsList?.length
          ) {
            selectData?.map(item => {
              item.checkMethodsList = item.checkMethodsList.filter(nav =>
                values.methodId.includes(nav.methodId)
              );
            });
            const mString = [] as any;
            selectData?.map(item => {
              item?.checkMethodsList?.map(nav => mString.push(nav.methodName));
            });
            const arrs = JSON.parse(JSON.stringify(symptomsData));
            const newArrs = arrs?.map(item => {
              if (item.id === selectedRowKey) {
                item?.items?.forEach(d => {
                  if (d.itemId === p.itemId) {
                    d.itemCheckParts = values.partsName.join(',');
                    d.checkPartsList = selectData;
                    d.itemCheckMethods = mString.join(',');
                    d.executionDeptCode = values?.executionDeptCode;
                    d.executionDeptName = executionDeptName;
                    d.reminder = values.reminder;
                  }
                });
              }
              return item;
            });
            setSymptomsData(newArrs);
            message.success('配置成功');
            return Promise.resolve(selectData);
          } else {
            message.error('请选择检查配置');
            return Promise.reject('请选择检查方法');
          }
        },

        items: [
          {
            label: '项目名称',
            name: 'itemName',
            render: v => <Input value={v} disabled></Input>
          },
          {
            label: '检查部位',
            name: 'partsName',
            render: () => (
              <CheckboxGroup
                onChange={e => {
                  const currentData = checkData?.checkPartsList
                    ?.filter(item => e?.includes(item?.partsName))
                    ?.reduce((acc, cur) => {
                      if (!cur?.checkMethodsList?.length) return acc;
                      acc.push(
                        ...(cur?.checkMethodsList || []).map(item => ({
                          label: item.methodName,
                          value: item.methodId
                        }))
                      );
                      return acc;
                    }, []);
                  setMethods(currentData);
                }}
                options={
                  checkData?.checkPartsList?.map(item => {
                    return {
                      label: item.partsName || '',
                      value: item.partsName || ''
                    };
                  }) || []
                }></CheckboxGroup>
            )
          },
          {
            label: '检查方法',
            name: 'methodId',
            render: () => <CheckboxGroup options={methods || []} />
          },
          ...(env.hisId === '40007'
            ? []
            : [
                {
                  label: '执行科室',
                  name: 'executionDeptCode',
                  render: () => (
                    <ArrSelect
                      options={
                        depList?.length > 0
                          ? depList.map(x => ({
                              value: x.deptId,
                              children: x.deptName
                            }))
                          : [] // 确保返回空数组而不是布尔值
                      }
                      placeholder={'请选择执行科室'}
                    />
                  )
                }
              ]),
          {
            label: '温馨提示',
            name: 'reminder',
            render: () => <TextArea rows={4} placeholder='请输入温馨提示' />
          }
        ]
      };
    },
    [depList]
  );
  return (
    <CardLayout
      title={
        <span style={{ color: '#333', fontSize: '18px', fontWeight: 'bold' }}>
          科室：{name}
        </span>
      }>
      <Form form={form} onFinish={onFinish}>
        <Row>
          <Col span={8}>
            <Form.Item label={`症状选择方式`} name='name'>
              {!isDetail ? (
                <Radio.Group
                  name='radiogroup'
                  onChange={({ target: { value } }) => {
                    setSelectionType(value);
                  }}
                  value={selectionType}>
                  <Radio value={1}>单选</Radio>
                  <Radio value={2}>多选</Radio>
                </Radio.Group>
              ) : (
                <span>{deptInfo?.symptomPatterns === 1 ? '单选' : '多选'}</span>
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={`建议诊断回传`} name='name2'>
              {!isDetail ? (
                <Radio.Group
                  defaultValue={1}
                  name='radiogroup2'
                  onChange={({ target: { value } }) => {
                    setIsCallBack(value);
                  }}
                  value={isCallBack}>
                  <Radio value={1}>回传</Radio>
                  <Radio value={0}>不回传</Radio>
                </Radio.Group>
              ) : (
                <span>{deptInfo?.isBack === 1 ? '回传' : '不回传'}</span>
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={8}>
            <TableList
              rowClassName={record => {
                return classNames(styles.pointer, {
                  [styles.selected]: record.id === selectedRowKey
                });
              }}
              onRow={record => {
                return {
                  onClick: event => {
                    setSelectedRowKey(record.id);
                  }
                };
              }}
              tableTitle={'项目列表'}
              showExpand={false}
              scroll={{ x: '100%' }}
              action={
                !isDetail && (
                  <LinkButton
                    onClick={() => {
                      useModal1().then((res: SymptomItem[]) => {
                        const filterArr: SymptomItem[] = [];
                        res.forEach(item => {
                          if (
                            symptomsData.findIndex(
                              v => v.symptomName === item.symptomName
                            ) === -1
                          ) {
                            filterArr.push(item);
                          }
                        });
                        setSymptomsData(prev => {
                          return prev.concat(filterArr);
                        });
                      });
                    }}>
                    项目列表
                  </LinkButton>
                )
              }
              showTool={false}
              columns={columns}
              dataSource={symptomsData}
              rowKey={'key'}
              pagination={false}
            />
          </Col>
          <Col span={16}>
            <TableList
              tableTitle={'开单项目'}
              showExpand={false}
              scroll={{ x: '100%' }}
              action={
                !isDetail && (
                  <LinkButton
                    onClick={() => {
                      if (selectedRowKey) {
                        const arrs = JSON.parse(JSON.stringify(symptomsData));
                        useModal2().then(res => {
                          const newArrs = arrs?.map(item => {
                            if (item.id === selectedRowKey) {
                              res.forEach(v => {
                                v.isMust = 0;
                                if (
                                  item?.items?.length &&
                                  item?.items.findIndex(
                                    d => d.itemId === v.itemId
                                  ) === -1
                                ) {
                                  item.items.push(v);
                                } else {
                                  item.items = res;
                                }
                              });
                            }
                            return item;
                          });
                          setSymptomsData(newArrs);
                        });
                        return;
                      } else {
                        message.error('请先选择症状');
                      }
                    }}>
                    选择项目
                  </LinkButton>
                )
              }
              showTool={false}
              dataSource={selectDepartItems}
              columns={[
                { title: '项目编号', dataIndex: 'itemCode', width: 80 },
                { title: '项目名称', dataIndex: 'itemName', width: 120 },
                {
                  title: '项目类别',
                  dataIndex: 'itemType',
                  width: 120,
                  render: (text: string) => operationType1[text]
                },
                {
                  title: '检查部位/检验标本',
                  dataIndex: 'itemCheckParts',
                  width: 120,
                  render: (text: string) => {
                    if (!text || text === 'null') {
                      return '--';
                    } else {
                      return text;
                    }
                  }
                },
                {
                  title: '检查方法',
                  dataIndex: 'itemCheckMethods',
                  width: 120,
                  render: (text: string) => {
                    if (!text || text === 'null') {
                      return '--';
                    } else {
                      return text;
                    }
                  }
                },
                {
                  title: '执行科室',
                  dataIndex: 'executionDeptName',
                  width: 120
                },
                {
                  title: '温馨提示',
                  dataIndex: 'reminder',
                  width: 120,
                  render: (text: string) => {
                    return text && text?.substring(0, 8) + '...';
                  }
                },
                {
                  title: '必选',
                  dataIndex: 'isMust',
                  width: 80,
                  render: (v, record) => (
                    <Switch
                      checked={v === 1}
                      onChange={() => {
                        if (selectedRowKey) {
                          const arrs = JSON.parse(JSON.stringify(symptomsData));
                          const newArrs = arrs?.map(item => {
                            if (item.id === selectedRowKey) {
                              item?.items?.forEach(d => {
                                if (d.itemId === record.itemId) {
                                  d.isMust = d.isMust === 1 ? 0 : 1;
                                }
                              });
                            }
                            return item;
                          });
                          setSymptomsData(newArrs);

                          return;
                        }
                      }}
                      disabled={isDetail}
                    />
                  )
                },
                {
                  title: '操作',
                  width: 160,
                  render: (v, record: any) => {
                    return (
                      <ActionsWrap max={6}>
                        {!isDetail && (
                          <>
                            <LinkButton
                              style={{ marginRight: '14px' }}
                              onClick={async () => {
                                const data = await useApi.根据项目id查询症状项目信息.request(
                                  {
                                    id: v?.itemId
                                  }
                                );
                                if (
                                  getType(v.itemType) === '检查项目配置' &&
                                  v?.checkPartsList?.length
                                ) {
                                  const currentData = [] as any;
                                  data?.data?.checkPartsList?.map(nav => {
                                    v?.checkPartsList?.map(item => {
                                      if (item.partsName === nav.partsName) {
                                        nav?.checkMethodsList?.map(item => {
                                          currentData.push({
                                            label: item.methodName,
                                            value: item.methodId
                                          });
                                        });
                                      }
                                    });
                                  });
                                  setMethods(currentData as any[]);
                                } else {
                                  setMethods([]);
                                }
                                const methodId = [] as any;
                                v?.checkPartsList?.map(nav => {
                                  nav?.checkMethodsList?.map(item => {
                                    methodId.push(item.methodId);
                                  });
                                });
                                console.log(
                                  '111',
                                  v?.collectMethodsList?.[0]?.collectMethodId,
                                  v?.checkPartsList
                                );
                                switchEditModalVisible({
                                  ...v,
                                  checkData: data?.data,
                                  collectMethod:
                                    v?.collectMethodsList?.[0]?.collectMethodId,
                                  partsName:
                                    v?.checkPartsList &&
                                    v?.checkPartsList?.map(
                                      item => item.partsName
                                    ),
                                  methodId
                                });
                              }}>
                              编辑
                            </LinkButton>
                            <LinkButton
                              onClick={() => {
                                const arrs = symptomsData;
                                const newArrs = arrs?.map(item => {
                                  if (item.id === selectedRowKey) {
                                    const data1 = item?.items?.filter(
                                      d => d.itemId !== record.itemId
                                    );
                                    item.items = item?.items?.filter(
                                      d => d.itemId !== record.itemId
                                    );
                                  }
                                  return item;
                                });
                                setSymptomsData(newArrs);

                                return;
                              }}>
                              删除
                            </LinkButton>
                          </>
                        )}
                      </ActionsWrap>
                    );
                  }
                }
              ]}
            />
          </Col>
        </Row>
        {!isDetail && (
          <Form.Item>
            <Button
              style={{ marginRight: 20 }}
              onClick={() => {
                goBack();
              }}>
              取消
            </Button>
            <Button htmlType='submit' type='primary'>
              保存
            </Button>
          </Form.Item>
        )}
      </Form>
    </CardLayout>
  );
};
