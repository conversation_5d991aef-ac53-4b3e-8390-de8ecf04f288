import { request } from 'parsec-admin';
import createApiHooks from 'create-api-hooks';
import { ApiResponse, ListApiResponseData } from '@configs/d';
export default {
  获取特定科室挂号订单列表: createApiHooks(
    (params: {
      page: number;
      limit: number;
      appointmentStatus?: string;
      appointmentDateStart?: string;
      appointmentDateEnd?: string;
      name?: string;
    }) => {
      return request.get<
        ListApiResponseData<{
          id: number;
          hisId: number;
          type: string;
          registerType: string;
          patientId: string;
          patientName: number;
          patientMobile: number;
          patientMobileCipher: string;
          patientSex: string;
          patientAge: string;
          parentIdNo: string;
          visitDate: string;
          visitBeginTime: string;
          visitEndTime: string;
          hisAreaName: string;
          deptName: string;
          doctorName: string;
          visitPosition: string;
          payOrderId: string;
          status: string;
          payChannel: string;
          payType: string;
          totalFee: string;
          createTime: string;
        }>
      >('/intelligent/mch/register/specific/dept', { params });
    }
  ),
  取消特定科室挂号订单: createApiHooks(
    (params: { list: (number | string)[] }) => {
      return request.put<ApiResponse<any>>(
        '/intelligent/mch/register/specific/dept',
        params,
        {
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  )
};
