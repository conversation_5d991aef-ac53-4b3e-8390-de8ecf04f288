import MyTableList from '@components/myTableList';
import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  DayRangePicker,
  DetailLayout,
  FormDescriptions,
  LinkButton
} from 'parsec-admin';
import React, { useEffect, useMemo, useState } from 'react';
import moment from 'moment';
import apis from './api';
import { Button, message, Modal, Input, Checkbox } from 'antd';
import { Price } from '@kqinfo/ui';
import storage from '@utils/storage';
import { replaceRange } from 'parsec-hooks';
const status = {
  S: '预约成功',
  C: '已取消'
};
const type = {
  1: '预约挂号',
  2: '当班挂号'
};
const registerType = {
  0: '未标识',
  1: '普通号',
  2: '专家号'
};
export default () => {
  const hisName = useMemo(() => {
    const userInfo: any = storage.get('userInfo');
    return userInfo?.hisName || '';
  }, []);
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );
  const [open, setOpen] = useState(false);
  const [detail, setDetail] = useState<any>({});
  const { loading, request } = apis.取消特定科室挂号订单({
    needInit: false
  });
  const [allCheck, setAllCheck] = useState(false);
  const {
    data: { data },
    request: getList
  } = apis.获取特定科室挂号订单列表({
    needInit: false
  });
  useEffect(() => {
    if (
      selectedRowKeys.length === data?.recordList?.length &&
      selectedRowKeys.length !== 0
    ) {
      setAllCheck(true);
    } else {
      setAllCheck(false);
    }
  }, [selectedRowKeys, data]);
  return (
    <>
      <MyTableList
        showExpand={false}
        tableTitle='出生医学证明办理预约记录'
        getList={({ pagination: { current = 1 }, params }) => {
          delete params.sort;
          return getList({
            ...params,
            page: current as number,
            limit: 10
          });
        }}
        action={
          <Button
            loading={loading}
            onClick={() => {
              if (selectedRowKeys.length) {
                actionConfirm(() => request({ list: selectedRowKeys }), '取消');
              } else {
                message.error('当前没有选择');
              }
            }}>
            批量取消
          </Button>
        }
        columns={[
          {
            title: (
              <Checkbox
                value={allCheck}
                checked={allCheck}
                onChange={e => {
                  if (!e.target.checked) {
                    setSelectedRowKeys([]);
                  } else {
                    let arr = data?.recordList || [];
                    arr = arr.filter(
                      item => moment().format('YYYY-MM-DD') <= item?.visitDate
                    );
                    if (arr?.length) {
                      setSelectedRowKeys(arr.map(item => item.id));
                    } else {
                      message.warn('当前没有可选择的订单');
                      setAllCheck(false);
                      return;
                    }
                  }
                  setAllCheck(e.target.checked);
                }}
              />
            ),
            width: 50,
            render: (record: any) => (
              <Checkbox
                checked={selectedRowKeys.includes(record.id)}
                onChange={e => {
                  if (e.target.checked) {
                    setSelectedRowKeys([...selectedRowKeys, record.id]);
                  } else {
                    setSelectedRowKeys(prevState =>
                      (prevState || []).filter(item => item !== record.id)
                    );
                  }
                }}
                disabled={moment().format('YYYY-MM-DD') > record?.visitDate}
              />
            )
          },
          {
            title: '医院名称',
            searchIndex: 'hisName',
            search: (
              <ArrSelect defaultValue={hisName} options={[hisName]} disabled />
            )
          },
          {
            title: '姓名',
            searchIndex: 'name',
            search: <Input placeholder={'请输入姓名或就诊卡号'} />
          },
          {
            title: '序号',
            width: 80,
            render: (_, v, index) => {
              return index + 1;
            }
          },
          {
            title: '预约时间',
            dataIndex: 'visitDate',
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                valueFormat={'YYYY-MM-DD'}
              />
            ),
            searchIndex: ['appointmentDateStart', 'appointmentDateEnd'],
            render: (v, record: any) => {
              return (
                v +
                  '  ' +
                  record?.visitBeginTime +
                  '  ' +
                  record?.visitEndTime || '-'
              );
            }
          },
          {
            title: '平台订单号',
            dataIndex: 'id'
          },
          {
            title: '预约科室',
            dataIndex: 'deptName',
            width: 100
          },
          {
            title: '就诊人姓名',
            dataIndex: 'patientName',
            width: 110
          },
          {
            title: '业务类型',
            dataIndex: 'type',
            render: v => type[v],
            width: 100
          },
          {
            title: '联系电话',
            dataIndex: 'patientMobile',
            render: v => replaceRange({ text: v || '' })
          },
          {
            title: '预约状态',
            searchIndex: 'appointmentStatus',
            search: <ArrSelect options={status} />
          },
          {
            title: '订单状态',
            dataIndex: 'status',
            width: 100,
            render: v =>
              v === 'S' ? (
                '预约成功'
              ) : (
                <span style={{ color: 'red' }}>取消预约</span>
              )
          },
          {
            title: '操作',
            fixed: 'right',
            render: (record: any) => (
              <ActionsWrap>
                <LinkButton
                  onClick={() => {
                    setDetail(record);
                    setOpen(true);
                  }}>
                  查看
                </LinkButton>
                {record?.status === 'S' && (
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () => request({ list: [record?.id] }),
                        '取消',
                        {
                          props: {
                            content: '取消后将进行退号操作，请确认后再操作！'
                          }
                        }
                      );
                    }}>
                    取消预约
                  </LinkButton>
                )}
              </ActionsWrap>
            )
          }
        ]}
      />
      <Modal
        title={'挂号订单详情'}
        visible={open}
        width={1200}
        onCancel={() => setOpen(false)}>
        <DetailLayout
          cardsProps={[
            {
              title: '就诊信息',
              children: (
                <FormDescriptions
                  items={[
                    {
                      label: '姓名',
                      children: detail?.patientName || '-'
                    },
                    {
                      label: '性别',
                      children:
                        detail?.patientSex === 'M'
                          ? '男'
                          : detail?.patientSex === 'F'
                          ? '女'
                          : '-'
                    },
                    {
                      label: '年龄',
                      children: detail?.patientAge || '-'
                    },
                    {
                      label: '患者ID',
                      children: detail?.patientId || '-'
                    },
                    {
                      label: '联系电话',
                      children: detail?.patientMobile || '-'
                    },
                    {
                      label: '身份证号',
                      children: detail?.parentIdNo || '-'
                    },
                    {
                      label: '就诊院区',
                      children: detail?.hisAreaName || '-'
                    },
                    {
                      label: '就诊科室',
                      children: detail?.deptName || '-'
                    },
                    {
                      label: '就诊医师',
                      children: detail?.doctorName || '-'
                    },
                    {
                      label: '号源类型',
                      children: registerType[detail?.registerType] || '-'
                    },
                    {
                      label: '就诊时段',
                      children:
                        detail?.visitDate +
                          '  ' +
                          detail?.visitBeginTime +
                          '  ' +
                          detail?.visitEndTime || '-'
                    },
                    {
                      label: '就诊位置',
                      children: detail?.visitPosition || '-'
                    }
                  ]}
                />
              )
            },

            {
              title: '订单信息',
              children: (
                <FormDescriptions
                  items={[
                    {
                      label: '订单状态',
                      children: status[detail?.status] || '-'
                    },
                    {
                      label: '业务类型',
                      children: detail?.visitPosition || '-'
                    },
                    {
                      label: '业务渠道',
                      children: detail?.payChannel || '-'
                    },
                    {
                      label: '支付方式',
                      children: detail?.payType || '-'
                    },
                    {
                      label: '订单金额',
                      children: (
                        <Price
                          style={{ fontSize: '13px' }}
                          price={detail?.totalFee || '-'}
                        />
                      )
                    },
                    {
                      label: '下单时间',
                      children: detail?.createTime || '-'
                    },
                    {
                      label: '平台订单号',
                      children: detail?.id || '-'
                    },
                    {
                      label: '医院单号',
                      children: detail?.hisOrderNo || '-'
                    }
                  ]}
                />
              )
            }
          ]}
        />
      </Modal>
    </>
  );
};
