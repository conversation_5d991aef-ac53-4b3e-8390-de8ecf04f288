import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
export interface ApiResponse<D> {
  code: 0 | 200 | 999; // 999用户未登录
  msg: string | null;
  data: D;
}

export type DetailListItem = {
  detailsName: string;
  detailsCode: string;
  serialNo: number;
  isSelected: 1 | 0;
  id: number;
};

export type Serial = {
  serialNo: number;
  serialCode: string;
  serialName: string;
  // isSelected: number | null;
  isEnabled: 1 | 0;
  detailsList: DetailListItem[];
};

export interface SortData {
  cmsDoctorSort: string;
  serialNoVoList: Serial[];
}

export default {
  医生班次排序数据: createApiHooks(() =>
    request.get<ApiResponse<SortData>>(
      `/mch/his/doctor/order/findDoctorSerialNoAll`
    )
  ),
  // 排序基础数据: createApiHooks(() =>
  //   request.get<ApiResponse<any>>(`/mch/his/doctor/order/base/findAll`)
  // ),
  医生班次排序更新: createApiHooks((params: any) =>
    request.post<ApiResponse<any>>(`/mch/his/doctor/order/add`, params, {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    })
  ),
  更新新增配置: createApiHooks((params: { cmdDockerSort: 1 | 0 }) =>
    request.get<ApiResponse<any>>(`/mch/his/doctor/order/sortConfigRenewal`, {
      params
    })
  )
};
