import React, { useEffect, useState, ElementRef, useRef } from 'react';
import {
  Typography,
  Radio,
  RadioChangeEvent,
  Button,
  Row,
  Col,
  Switch,
  Divider,
  message
} from 'antd';
import { Space } from '@kqinfo/ui';
import styles from './index.module.less';
import apis, { Serial } from './api';
import CustomItem from './components/customItem';
import Item from './components/item';

const { Text, Title } = Typography;
const SmartHospital: React.FC = () => {
  const [serialNoVoList, setSerialNoVoList] = useState<Serial[]>([]);
  const [cmsDoctorSort, setCmsDoctorSort] = useState(0);
  console.log('======>', cmsDoctorSort);
  const [loading, setLoading] = useState(false);
  // 自定义
  const [specCustom, setSpecCustom] = useState<Serial>({} as Serial);
  // 同一医生班次合并
  const [specMerge, setSpecMerge] = useState<{
    item?: Serial;
    checked: boolean;
  }>({
    item: undefined,
    checked: false
  });
  // 满诊医生
  const [specFull, setSpecFull] = useState<{ item?: Serial; value: string }>({
    item: undefined,
    value: ''
  });
  const customRef = useRef<ElementRef<typeof CustomItem>>(null);

  const queryData = async () => {
    const res = await apis.医生班次排序数据.request();
    setCmsDoctorSort(+res.data.cmsDoctorSort);
    const list: Serial[] = [];
    // 三种特殊情况（不通用）
    res.data.serialNoVoList.forEach(v => {
      if (v.serialName.includes('同一医生班次合并')) {
        console.log('v======>', v);
        setSpecMerge({
          item: v,
          checked:
            (v?.detailsList ?? []).filter(
              item => item.detailsName === '合并' && item.isSelected
            ).length > 0
        });
      } else if (v.serialName.includes('满诊医生')) {
        setSpecFull({
          item: v,
          value:
            (v?.detailsList ?? []).find(item => item.isSelected)?.detailsCode ??
            ''
        });
      } else if (v.serialName.includes('自定义')) {
        setSpecCustom(v);
      } else {
        list.push(v);
      }
    });
    setSerialNoVoList(list);
  };
  useEffect(() => {
    queryData();
  }, []);

  const transformStatus = checked => {
    return checked ? 1 : 0;
  };

  const save = async () => {
    setLoading(true);
    try {
      apis.更新新增配置.request({ cmdDockerSort: radioValue === 1 ? 1 : 0 });
      if (radioValue === 1) {
        const arr: Serial[] = [];
        if (specMerge.item) {
          const newItem: Serial = {
            ...specMerge.item,
            detailsList: specMerge.item.detailsList.map(v => {
              return {
                ...v,
                isSelected:
                  v.detailsName === '合并'
                    ? transformStatus(specMerge.checked)
                    : transformStatus(!specMerge.checked)
              };
            })
          };
          arr.push(newItem);
        }
        if (specFull.item) {
          const newItem: Serial = {
            ...specFull.item,
            detailsList: specFull.item.detailsList.map(v => {
              return {
                ...v,
                isSelected: specFull.value === v.detailsCode ? 1 : 0
              };
            })
          };
          arr.push(newItem);
        }
        arr.push(...serialNoVoList);
        // 自定义排序
        arr.push({
          ...specCustom,
          isEnabled: customRef.current?.checked ? 1 : 0
        });
        await apis.医生班次排序更新.request(arr);
        await customRef.current?.submit();
        setLoading(false);
        message.success('保存成功');
      }
    } catch {
      setLoading(false);
    }
  };

  const handleData = (item, index) => {
    const list = serialNoVoList.map((v, i) => {
      if (i === index) {
        return {
          ...v,
          ...item
        };
      }
      return v;
    });
    setSerialNoVoList(list);
  };
  const [radioValue, setRadioValue] = useState(1);
  return (
    <React.Fragment>
      <Title level={5}>排序规则</Title>
      <Radio.Group
        onChange={(e: RadioChangeEvent) => {
          setRadioValue(e.target.value);
        }}
        value={radioValue}>
        <Radio value={1}>管理后台规则</Radio>
        <Radio value={0}>前置机规则</Radio>
      </Radio.Group>
      <Divider />
      {radioValue === 1 ? (
        <div className={styles.content}>
          <Row className={styles.row}>
            {specMerge.item && (
              <Col span={6}>
                <Space size={20}>
                  <Text strong>同一医生班次合并展示</Text>
                  <Switch
                    checked={specMerge.checked}
                    onChange={(check: boolean) => {
                      setSpecMerge(prev => ({ ...prev, checked: check }));
                    }}
                  />
                </Space>
              </Col>
            )}
            {specFull.item && (
              <Col span={6}>
                <Space size={20}>
                  <Text strong>满诊医生</Text>
                  <Radio.Group
                    onChange={(e: RadioChangeEvent) => {
                      setSpecFull(prev => ({ ...prev, value: e.target.value }));
                    }}
                    value={specFull.value}>
                    {specFull.item.detailsList.map(item => (
                      <Radio value={item.detailsCode} key={item.detailsCode}>
                        {item.detailsName}
                      </Radio>
                    ))}
                  </Radio.Group>
                </Space>
              </Col>
            )}
          </Row>
          {serialNoVoList.map(v => {
            return (
              <Item
                key={v.serialCode}
                data={v}
                index={serialNoVoList.indexOf(v)}
                handleData={handleData}
              />
            );
          })}
          <Divider />
          <CustomItem ref={customRef} open={!!specCustom?.isEnabled} />
        </div>
      ) : null}

      <div className={styles.bottom}>
        <Space size={20}>
          <Button type='primary' onClick={save} loading={loading}>
            保存当前排序
          </Button>
          {/* <Button>取消</Button> */}
        </Space>
      </div>
    </React.Fragment>
  );
};

export default SmartHospital;
