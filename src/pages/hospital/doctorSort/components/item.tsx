import React, { useEffect, useState } from 'react';
import { Switch, InputNumber, Divider } from 'antd';
import { ReactSortable } from 'react-sortablejs';
// import cls from 'classnames';
import { Space } from '@kqinfo/ui';
import styles from './index.module.less';
import { Serial } from '../api';

interface Props {
  data: Serial;
  index: number;
  handleData: (newValue: Serial, index: number) => void;
}

const CustomItem: React.FC<Props> = props => {
  const { data, index: _index, handleData } = props;
  const [info, setInfo] = useState(data);
  useEffect(() => {
    setInfo(data);
  }, [data]);

  const handle = (data: Serial) => {
    setInfo(data);
    handleData(data, _index);
  };

  return (
    <>
      <Divider />
      <Space size={20} vertical className={styles.itemBox}>
        <Space size={50}>
          <Space alignItems='center' size={30}>
            <span className={styles.title}>{info.serialName}</span>
            <Switch
              checked={!!info.isEnabled}
              onChange={checked => {
                const data: Serial = { ...info, isEnabled: checked ? 1 : 0 };
                handle(data);
              }}
            />
          </Space>
          {!!info.isEnabled && (
            <Space alignItems='center' size={10}>
              <span style={{ width: 80 }}>规则优先级</span>
              <InputNumber
                value={info.serialNo}
                onChange={(num: number) => {
                  const data: Serial = { ...info, serialNo: num };
                  handle(data);
                }}
              />
            </Space>
          )}
        </Space>
        {!!info.isEnabled && (
          <>
            <Space vertical size={10}>
              <Space size={5}>
                <Space
                  alignItems={'center'}
                  justify={'center'}
                  style={{
                    backgroundColor: '#f3cf99',
                    borderRadius: '50%',
                    color: '#fff',
                    width: '15px',
                    height: '15px'
                  }}>
                  !
                </Space>
                操作说明：添加
                {info.serialName.includes('班次') ? '班次' : '职称'}
                标签时，按添加先后自动排序，可拖动
                {info.serialName.includes('班次') ? '班次' : '职称'}
                到相应位置并保存，将同步更新排序号
              </Space>
            </Space>
            {info.detailsList.length ? (
              <ReactSortable
                setList={list => {
                  const newList = list.map((item, index) => {
                    return { ...item, serialNo: index + 1 };
                  });
                  const data = { ...info, detailsList: newList };
                  handle(data);
                }}
                className={styles.sortBox}
                list={info.detailsList}>
                {info.detailsList.map(item => (
                  <div key={item.detailsCode} className={styles.listItem}>
                    {item.detailsName}
                  </div>
                ))}
              </ReactSortable>
            ) : null}
          </>
        )}
      </Space>
    </>
  );
};

export default CustomItem;
