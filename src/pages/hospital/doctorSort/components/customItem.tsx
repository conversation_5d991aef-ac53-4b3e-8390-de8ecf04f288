import React, { useState, forwardRef, Ref, useImperative<PERSON>andle } from 'react';
import { Select, Cascader, Button, message, Switch, Input } from 'antd';
import { ReactSortable } from 'react-sortablejs';
// import cls from 'classnames';
import { Space } from '@kqinfo/ui';
import styles from './index.module.less';
import env from '@configs/env';
import api from '@pages/hospital/campus/api';
import useDeptApi from '@pages/hospital/departmentSmart/api';
import useApi from '@pages/hospital/doctorSmart/api';
import { getTreeOptions } from '@pages/hospital/doctorDetailSmart/utils';

type refType = Ref<{ submit: () => Promise<any>; checked: boolean }>;

interface Props {
  open: boolean;
}
interface ListItem {
  value: string;
  label: string;
  id: string;
}
const hisId = env.hisId;

const CustomItem = (props: Props, ref: refType) => {
  const { open } = props;
  const [deptId, setDeptId] = useState<number>();
  const [aredId, setAreaId] = useState<number>();
  // const [recordList, setRecordList] = useState<any[]>([]);
  const [list, setList] = useState<ListItem[]>([]);
  const [checked, setChecked] = useState(false);

  React.useEffect(() => {
    setChecked(!!open);
  }, [open]);

  const {
    data: { data: hisTypes }
  } = api.医院开通的业务平台列表({
    deepCache: true,
    initValue: { data: [] }
  });
  const {
    data: {
      data: { recordList: deptList }
    }
  } = useDeptApi.科室管理列表({
    params: { hisId, hisType: aredId },
    needInit: !!hisId && !!aredId,
    initValue: {
      data: {
        recordList: []
      }
    }
  });

  const query = async () => {
    const res = await useApi.医生列表.request({
      pageNum: 1,
      numPerPage: 999,
      type: '1',
      orderBy: 'sortNo',
      sort: 'ASC',
      hisId,
      hisType: aredId,
      deptId
    });
    const newList = (res.data?.recordList ?? []).map(item => {
      return {
        label: item.name,
        value: item.id + '',
        id: item.id + ''
      };
    });
    setList(newList);
  };

  const submit = async () => {
    if (!deptId) return;
    await useApi.医生排序.request({
      hisId,
      deptDoctorId: deptId as number,
      data: list.map((item, index) => {
        return {
          id: parseInt(item.value),
          sortNo: index
        };
      })
    });
  };
  useImperativeHandle(ref, () => {
    return {
      submit,
      checked
    };
  });

  return (
    <Space size={20} vertical>
      <Space size={50}>
        <Space alignItems='center' size={30}>
          <span className={styles.title}>自定义排序</span>
          <Switch checked={checked} onChange={setChecked} />
        </Space>
        {checked && (
          <Space alignItems='center' size={30}>
            <span style={{ width: 120 }}>规则优先级</span>
            <Input disabled defaultValue={3} />
          </Space>
        )}
      </Space>
      {checked && (
        <>
          <Space vertical size={10}>
            <Space alignItems={'center'} size={20}>
              <Select
                placeholder='请选择院区'
                value={aredId}
                onChange={setAreaId}
                options={hisTypes?.map(x => {
                  return {
                    label: x.desc,
                    value: x.code
                  };
                })}
                style={{
                  width: '200px'
                }}
              />
              <Cascader
                allowClear={false}
                expandTrigger='hover'
                onChange={(v: any) => {
                  const id = v[v.length - 1];
                  setDeptId(id as number);
                  setList([]);
                }}
                style={{
                  width: '300px'
                }}
                placeholder='请选择上级科室'
                options={getTreeOptions(deptList) as any}
              />
              <Button
                type='primary'
                onClick={() => {
                  if (!deptId || !aredId) {
                    message.warn('先选择在查询');
                    return;
                  }
                  query();
                }}>
                查询
              </Button>
              <Button
                onClick={() => {
                  setAreaId(0);
                  setDeptId(0);
                }}>
                重置
              </Button>
            </Space>
            <Space size={5}>
              <Space
                alignItems={'center'}
                justify={'center'}
                style={{
                  backgroundColor: '#f3cf99',
                  borderRadius: '50%',
                  color: '#fff',
                  width: '15px',
                  height: '15px'
                }}>
                !
              </Space>
              操作说明：1、点击上方科室选择框，选择对应科室进行查询；2、拖动医生到相应位置并保存，手机端将同步更新排序。
            </Space>
          </Space>
          {list.length ? (
            <ReactSortable
              setList={list => {
                setList(list);
              }}
              className={styles.sortBox}
              selected={deptId + ''}
              list={list}>
              {list.map(item => (
                <div key={item.value} className={styles.listItem}>
                  {item.label}
                </div>
              ))}
            </ReactSortable>
          ) : null}
        </>
      )}
    </Space>
  );
};

export default forwardRef(CustomItem);
