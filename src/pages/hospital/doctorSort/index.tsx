import React from 'react';
import { CardLayout } from 'parsec-admin';
import { Tabs } from 'antd';
import SmartHospital from './smartHospital';
const { TabPane } = Tabs;

const DoctorSort: React.FC = () => {
  return (
    <CardLayout>
      <Tabs defaultActiveKey='1'>
        <TabPane tab='智慧医院' key='1'>
          <SmartHospital />
        </TabPane>
      </Tabs>
    </CardLayout>
  );
};

export default DoctorSort;
