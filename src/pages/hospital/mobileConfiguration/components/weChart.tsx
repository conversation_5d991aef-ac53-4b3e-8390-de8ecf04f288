import {
  FormListActionType,
  ProFormDigit,
  ProFormItem,
  ProFormSwitch
} from '@ant-design/pro-components';
import {
  ProForm,
  ProFormGroup,
  ProFormList,
  ProFormText
} from '@ant-design/pro-components';
import { Button, Space, Form, Spin, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { PartTitle } from '@kqinfo/ui';
import './index.less';
import { UploadImg } from 'parsec-admin';
import apis from '../api';
import { ArrSelect } from 'parsec-admin/es';

export default () => {
  const [form] = Form.useForm();
  const actionRef = useRef<FormListActionType<any>>();
  const actionRef1 = useRef<FormListActionType<any>>();
  const actionRef2 = useRef<FormListActionType<any>>();
  const [formArr, setFormArr] = useState<string[]>([
    // 'doctorRecordInfo',
    // 'expandInfo',
    // 'bannerInfo'
  ]);
  const {
    data: { data: configData },
    loading,
    request
  } = apis.首页配置查询({
    needInit: true
  });
  useEffect(() => {
    if (configData?.length) {
      const arr: any[] = [];
      let doctor: any;
      let ecpand: any;
      let banner: any;
      configData.forEach(item => {
        if (item?.configType === 'DOCTOR') {
          arr.push({ sort: item.sort, type: 'doctorRecordInfo' });
          form.setFieldValue('isCheckdoctorRecordInfo', item?.status);
          doctor = item;
        } else if (item?.configType === 'EXPAND') {
          arr.push({ sort: item.sort, type: 'expandInfo' });
          form.setFieldValue('isCheckexpandInfo', item?.status);
          ecpand = item;
        } else if (item?.configType === 'BANNER') {
          arr.push({ sort: item.sort, type: 'bannerInfo' });
          banner = item;
          form.setFieldValue('isCheckbannerInfo', item?.status);
        }
      });
      if (arr?.length) {
        arr.sort((a, b) => a.sort - b.sort);
        setFormArr(arr.map(item => item.type));
      }
      form.setFieldValue('showCount', doctor?.doctorRecordInfo?.showCount || 0);
      form.setFieldValue('expandInfo', ecpand?.expandInfo || []);
      form.setFieldValue('bannerInfo', banner?.bannerInfo || []);
    }
  }, [configData, form]);
  function TitleBox(name: string, type: string, actionRefs): JSX.Element {
    return (
      <Space className='spaceBox' key={type}>
        <PartTitle className={'tilte'}>{name}</PartTitle>
        <ProFormSwitch name={'isCheck' + type} valuePropName={'checked'} />
        <Button
          onClick={() => {
            const index = formArr.findIndex(item => item === type);
            if (index) {
              const arr = [...formArr];
              const sort = arr[index];
              arr[index] = arr[index - 1];
              arr[index - 1] = sort;
              setFormArr(arr);
            }
          }}>
          上移
        </Button>
        <Button
          onClick={() => {
            const index = formArr.findIndex(item => item === type);
            if (index !== formArr.length - 1) {
              const arr = [...formArr];
              const sort = arr[index];
              arr[index] = arr[index + 1];
              arr[index + 1] = sort;
              setFormArr(arr);
            }
          }}>
          下移
        </Button>
        {type !== 'doctorRecordInfo' ? (
          <Button onClick={() => actionRefs?.current?.add()}>添加</Button>
        ) : null}
      </Space>
    );
  }

  return (
    <div className={'loadingBox'}>
      <Spin tip='Loading...' spinning={loading} className={'loading'}>
        <ProForm
          layout={'horizontal'}
          form={form}
          onFinish={async e => {
            // console.log(e);
            const sort1 = formArr.findIndex(
              item => item === 'doctorRecordInfo'
            );
            const sort2 = formArr.findIndex(item => item === 'expandInfo');
            const sort3 = formArr.findIndex(item => item === 'bannerInfo');
            const data: any = configData;
            (configData || []).forEach(item => {
              if (item?.configType === 'DOCTOR') {
                item.sort = sort1;
                item.doctorRecordInfo = { showCount: e.showCount };
                item.status = e?.isCheckdoctorRecordInfo ? 1 : 0;
              } else if (item?.configType === 'EXPAND') {
                item.sort = sort2;
                item.expandInfo = e.expandInfo;
                item.status = e?.isCheckexpandInfo ? 1 : 0;
              } else if (item?.configType === 'BANNER') {
                item.sort = sort3;
                item.bannerInfo = e.bannerInfo;
                item.status = e?.isCheckbannerInfo ? 1 : 0;
              }
            });
            if (e?.bannerInfo?.length) {
              e.bannerInfo = e?.bannerInfo.map((item, index) => {
                return {
                  ...item,
                  jumpType: 'H5',
                  sort: index
                };
              });
            }
            if (e?.expandInfo?.length) {
              e.expandInfo = e?.expandInfo.map((item, index) => {
                return {
                  ...item,
                  sort: index
                };
              });
            }

            apis.首页配置信息修改.request({ list: data }).then(async () => {
              const res = await request();
              if (res) {
                message.success('保存成功！');
              }
            });
          }}>
          {formArr.map(item => {
            if (item === 'doctorRecordInfo') {
              return (
                <>
                  {TitleBox('就诊过的医生', 'doctorRecordInfo', actionRef)}

                  <ProFormDigit
                    name='showCount'
                    label='最大展示医生数'
                    required={true}
                    width={'md'}
                    rules={[
                      { required: true, message: '请输入最大展示医生数' }
                    ]}
                  />
                </>
              );
            } else if (item === 'expandInfo') {
              return (
                <>
                  {TitleBox('扩展功能区', 'expandInfo', actionRef1)}

                  <ProFormList
                    copyIconProps={false}
                    actionRef={actionRef1}
                    name='expandInfo'
                    deleteIconProps={false}
                    creatorButtonProps={false}>
                    {(filed, name) => (
                      <>
                        <ProFormGroup key='group'>
                          <ProFormText name='title' width={100} />
                          <ProFormItem
                            name='imgUrl'
                            label='图标'
                            rules={[{ required: true, message: '请选择图标' }]}>
                            <UploadImg className={'icon'} arrValue={false} />
                          </ProFormItem>
                          <ProFormItem name='jumpType'>
                            <ArrSelect
                              placeholder={'请选择对接方式'}
                              onChange={e => {
                                const arr = form.getFieldValue('expandInfo');
                                arr[name].jumpType = e;
                                form.setFieldValue('expandInfo', arr);
                              }}
                              options={[
                                {
                                  label: 'H5',
                                  value: 'H5'
                                },
                                {
                                  label: '小程序',
                                  value: 'MINI_APP'
                                }
                              ]}
                            />
                          </ProFormItem>
                          <ProFormText
                            placeholder={
                              '请输入跳转链接地址,若为空将提示功能暂未开放'
                            }
                            label={'链接地址'}
                            name='jumpUrl'
                          />
                          {actionRef1.current?.get(name)?.jumpType ===
                            'MINI_APP' && (
                            <ProFormText
                              placeholder={'请输入小程序appid'}
                              rules={[
                                {
                                  required: true,
                                  message: '请输入小程序appid'
                                }
                              ]}
                              label={'appid'}
                              name='appId'
                            />
                          )}
                          <Button
                            ghost
                            danger
                            onClick={() => actionRef1.current?.remove(name)}>
                            删除
                          </Button>
                        </ProFormGroup>
                      </>
                    )}
                  </ProFormList>
                </>
              );
            } else {
              return (
                <>
                  {TitleBox('banner图', 'bannerInfo', actionRef2)}

                  <ProFormList
                    copyIconProps={false}
                    actionRef={actionRef2}
                    name='bannerInfo'
                    deleteIconProps={false}
                    creatorButtonProps={false}>
                    {(filed, index) => (
                      <ProFormGroup key='group'>
                        <ProFormText
                          name='title'
                          label={'名称'}
                          width={100}
                          rules={[{ required: true, message: '请输入名称' }]}
                        />
                        <ProFormItem
                          name='imgUrl'
                          label='banner图'
                          rules={[
                            { required: true, message: '请选择banner图' }
                          ]}>
                          <UploadImg className={'banner'} arrValue={false} />
                        </ProFormItem>
                        <ProFormText
                          placeholder={
                            '请输入跳转链接地址,若为空将提示功能暂未开放'
                          }
                          label={'链接地址'}
                          name='jumpUrl'
                        />
                        <Button
                          ghost
                          danger
                          onClick={() => actionRef2.current?.remove(index)}>
                          删除
                        </Button>
                      </ProFormGroup>
                    )}
                  </ProFormList>
                </>
              );
            }
          })}
        </ProForm>
      </Spin>
    </div>
  );
};
