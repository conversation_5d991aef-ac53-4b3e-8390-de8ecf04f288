.spaceBox {
  gap: 30px !important;
  margin-bottom: 20px;
  .tilte {
    font-size: 18px;
  }
  > .ant-space-item {
    > .ant-form-item {
      margin: 0 !important;
    }
  }
}
.icon {
  .ant-upload-list-picture-card-container {
    width: 50px;
    height: 50px;
    overflow: hidden;
  }
}
.banner,
.icon {
  .ant-upload,
  .ant-upload-select,
  .ant-upload-select-picture-card {
    width: auto !important;
    height: auto !important;
    border: none !important;
    background-color: white;
    .anticon-plus {
      display: none;
    }
  }
}
.banner {
  .ant-upload-list-picture-card-container {
    width: 200px;
    height: 70px;
    overflow: hidden;
  }
}
.loadingBox{
  width: 100%;
  min-height: 356px;
  .loading{
    min-height: 356px;
  }
}
