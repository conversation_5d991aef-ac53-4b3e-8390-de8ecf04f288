import {
  FormListActionType,
  ProFormDigit,
  ProFormItem,
  ProFormSwitch,
  ProForm,
  ProFormGroup,
  ProFormList,
  ProFormText
} from '@ant-design/pro-components';
import { Button, Space, Form, Spin, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { PartTitle } from '@kqinfo/ui';
import './index.less';
import { UploadImg } from 'parsec-admin';
import apis from '../api';
import { ArrSelect } from 'parsec-admin/es';

// mock 首页弹框数据
const mockSamrtHosp = {
  id: '1684734526411104260',
  hisId: 40007,
  hisName: '陈家桥医院',
  configType: 'SMARTHOSPHOME',
  status: 1,
  sort: 3,
  doctorRecordInfo: {
    showCount: 0
  },
  expandInfo: [],
  bannerInfo: [],
  smartHospitalHomeInfo: [
    {
      popupTitle: '欢迎来到智慧医院',
      popupContent: '请查看我们的最新功能和服务。',
      popupImage: 'https://example.com/popup-image1.jpg',
      popupLink: 'https://example.com/more-info1'
    },
    {
      popupTitle: '智慧医院新功能',
      popupContent: '探索我们最新的健康管理工具。',
      popupImage: 'https://example.com/popup-image2.jpg',
      popupLink: 'https://example.com/more-info2'
    }
  ],
  createTime: '2023-07-28 09:16:26',
  updateTime: '2025-03-14 16:25:56'
};

export default () => {
  const [form] = Form.useForm();
  // Refs for form list actions
  const actionRefs = {
    doctorRecordInfo: useRef<FormListActionType<any>>(),
    expandInfo: useRef<FormListActionType<any>>(),
    bannerInfo: useRef<FormListActionType<any>>(),
    smartHospitalHome: useRef<FormListActionType<any>>()
  };
  // Define a mapping for item types to their corresponding titles
  const itemTitleMap = {
    doctorRecordInfo: '就诊过的医生',
    expandInfo: '扩展功能区',
    bannerInfo: 'banner图',
    smartHospitalHome: '智慧医院首页弹框'
    // Add new item types and titles here as needed
  };
  const [formArr, setFormArr] = useState<string[]>([]);
  // Fetch configuration data
  const {
    data: { data: configData },
    loading,
    request
  } = apis.首页配置查询({ needInit: true });

  useEffect(() => {
    if (configData?.length) {
      // Map configuration data to form fields
      const arr = configData.map(item => {
        const typeMap = {
          DOCTOR: 'doctorRecordInfo',
          EXPAND: 'expandInfo',
          BANNER: 'bannerInfo',
          SMARTHOSPHOME: 'smartHospitalHome'
        };
        const type = typeMap[item.configType];
        form.setFieldValue(`isCheck${type}`, item.status);
        return { sort: item.sort, type };
      });

      // Sort and set form array
      arr.sort((a, b) => a.sort - b.sort);
      console.log('arr', arr);
      setFormArr(arr.map(item => item.type));

      // Set initial form values
      const doctor = configData.find(item => item.configType === 'DOCTOR');
      const expand = configData.find(item => item.configType === 'EXPAND');
      const banner = configData.find(item => item.configType === 'BANNER');
      const smartHospInfo = configData.find(
        item => item.configType === 'SMARTHOSPHOME'
      );

      form.setFieldsValue({
        showCount: doctor?.doctorRecordInfo?.showCount || 0,
        expandInfo: expand?.expandInfo || [],
        bannerInfo: banner?.bannerInfo || [],
        smartHospitalHome: smartHospInfo?.smartHospitalHome || []
      });
    }
  }, [configData, form]);

  // Component for title and action buttons
  const TitleBox = (name: string, type: string, actionRef) => (
    <Space className='spaceBox' key={type}>
      <PartTitle className='tilte'>{name}</PartTitle>
      <ProFormSwitch name={`isCheck${type}`} valuePropName='checked' />
      <Button onClick={() => moveItem(type, -1)}>上移</Button>
      <Button onClick={() => moveItem(type, 1)}>下移</Button>
      {type !== 'doctorRecordInfo' && (
        <Button onClick={() => actionRef.current?.add()}>添加</Button>
      )}
    </Space>
  );

  // Function to move items in the form array
  const moveItem = (type: string, direction: number) => {
    const index = formArr.findIndex(item => item === type);
    if (
      index >= 0 &&
      index + direction >= 0 &&
      index + direction < formArr.length
    ) {
      const newArr = [...formArr];
      [newArr[index], newArr[index + direction]] = [
        newArr[index + direction],
        newArr[index]
      ];
      setFormArr(newArr);
    }
  };

  // Handle form submission
  const handleFinish = async e => {
    if (!configData) {
      message.error('Configuration data is not available.');
      return;
    }

    // Prepare data for submission
    const data = configData.map(item => {
      const typeMap = {
        DOCTOR: 'doctorRecordInfo',
        EXPAND: 'expandInfo',
        BANNER: 'bannerInfo',
        SMARTHOSPHOME: 'smartHospitalHome'
      };
      const type = typeMap[item.configType];
      const sortIndex = formArr.findIndex(arrItem => arrItem === type);
      return {
        ...item,
        sort: sortIndex,
        [type]: e[type],
        status: e[`isCheck${type}`] ? 1 : 0
      };
    });

    // Update banner and expand info with additional properties
    if (e.bannerInfo?.length) {
      e.bannerInfo = e.bannerInfo.map((item, index) => ({
        ...item,
        jumpType: 'H5',
        sort: index
      }));
    }
    if (e.expandInfo?.length) {
      e.expandInfo = e.expandInfo.map((item, index) => ({
        ...item,
        sort: index
      }));
    }

    // Submit data and handle response
    apis.首页配置信息修改.request({ list: data }).then(async () => {
      const res = await request();
      if (res) {
        message.success('保存成功！');
      }
    });
  };

  // Render form list based on type
  const renderFormList = (type: string, actionRef) => (
    <ProFormList
      copyIconProps={false}
      actionRef={actionRef}
      name={type}
      deleteIconProps={false}
      creatorButtonProps={false}>
      {(field, index) => (
        <ProFormGroup key='group'>
          {type === 'smartHospitalHome' ? (
            <ProFormItem name='title'>
              <ArrSelect
                placeholder={'请选择对接方式'}
                //  onChange={e => {
                //    const arr = form.getFieldValue('expandInfo');
                //    arr[name].jumpType = e;
                //    form.setFieldValue('expandInfo', arr);
                //  }}
                options={[
                  {
                    label: '图文活动',
                    value: 'imageText'
                  },
                  {
                    label: '专家推荐',
                    value: 'doctorRecordInfo'
                  }
                ]}
              />
            </ProFormItem>
          ) : (
            <ProFormText
              name='title'
              label={type === 'bannerInfo' ? '名称' : undefined}
              width={100}
              rules={[{ required: true, message: '请输入名称' }]}
            />
          )}

          <ProFormItem
            name='imgUrl'
            label={type === 'bannerInfo' ? 'banner图' : '图标'}
            rules={[
              {
                required: true,
                message: `请选择${type === 'bannerInfo' ? 'banner图' : '图标'}`
              }
            ]}>
            <UploadImg
              className={type === 'bannerInfo' ? 'banner' : 'icon'}
              arrValue={false}
            />
          </ProFormItem>
          <ProFormText
            placeholder='请输入跳转链接地址,若为空将提示功能暂未开放'
            label='链接地址'
            name='jumpUrl'
          />
          {type === 'expandInfo' && (
            <ProFormItem name='jumpType'>
              <ArrSelect
                placeholder='请选择对接方式'
                onChange={e => {
                  const arr = form.getFieldValue('expandInfo');
                  arr[index].jumpType = e;
                  form.setFieldValue('expandInfo', arr);
                }}
                options={[
                  { label: 'H5', value: 'H5' },
                  { label: '小程序', value: 'MINI_APP' }
                ]}
              />
            </ProFormItem>
          )}
          {type === 'expandInfo' &&
            actionRef.current?.get(index)?.jumpType === 'MINI_APP' && (
              <ProFormText
                placeholder='请输入小程序appid'
                rules={[{ required: true, message: '请输入小程序appid' }]}
                label='appid'
                name='appId'
              />
            )}
          <Button ghost danger onClick={() => actionRef.current?.remove(index)}>
            删除
          </Button>
        </ProFormGroup>
      )}
    </ProFormList>
  );

  return (
    <div className='loadingBox'>
      <Spin tip='Loading...' spinning={loading} className='loading'>
        <ProForm layout='horizontal' form={form} onFinish={handleFinish}>
          {formArr.map(item => (
            <>
              {TitleBox(itemTitleMap[item] || '', item, actionRefs[item])}
              {item === 'doctorRecordInfo' ? (
                <ProFormDigit
                  name='showCount'
                  label='最大展示医生数'
                  required
                  width='md'
                  rules={[{ required: true, message: '请输入最大展示医生数' }]}
                />
              ) : (
                renderFormList(item, actionRefs[item])
              )}
            </>
          ))}
        </ProForm>
      </Spin>
    </div>
  );
};
