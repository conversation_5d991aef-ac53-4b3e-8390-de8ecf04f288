import { request } from 'parsec-admin';
import createApiHooks from 'create-api-hooks';
import { ApiResponse } from '@configs/d';
interface Config {
  id: '@natural'; //主键ID
  hisId: '40064'; //机构ID
  hisName: '@cword(6)'; //机构名称
  configType: 'DOCTOR' | 'EXPAND' | 'BANNER' | 'SMARTHOSPHOME'; //配置类型（医生数-DOCTOR；拓展功能-EXPAND；banner控制-BANNER）
  sort: number; //外层顺序
  doctorRecordInfo: {
    showCount: '@integer(0, 10)'; //最大展示医生数
  }; //就诊过医生配置信息
  expandInfo: [
    {
      //拓展功能区配置信息
      title: '@cword(6)'; //图标标题
      imgUrl: '@image(100x100)'; //图片url
      jumpType: ''; //跳转类型（H5-H5，小程序-MINI_APP）
      jumpUrl: 'https://httpbin.org/get?q=@word(8)'; //跳转地址、路径
      appId: ''; //小程序appid
      sort: number; //排序
    }
  ];
  bannerInfo: [
    {
      //banner信息
      title: '@cword(6)'; //图标标题
      imgUrl: '@image(100x100)'; //图片url
      jumpType: ''; //跳转类型（H5-H5，小程序-MINI_APP）
      jumpUrl: 'https://httpbin.org/get?q=@word(8)'; //跳转地址、路径
      appId: ''; //小程序appid
      sort: number; //排序
    }
  ];
  // smartHospitalHome
  smartHospitalHome: [
    {
      //banner信息
      title: '@cword(6)'; //图标标题
      imgUrl: '@image(100x100)'; //图片url
      jumpType: ''; //跳转类型（H5-H5，小程序-MINI_APP）
      jumpUrl: 'https://httpbin.org/get?q=@word(8)'; //跳转地址、路径
      appId: ''; //小程序appid
      sort: number; //排序
    }
  ];
  status: 0 | 1;
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //修改时间
}

export default {
  首页配置查询: createApiHooks(() => {
    return request.get<ApiResponse<Config[]>>(
      '/intelligent/mch/intelligent/home/<USER>/config'
    );
  }),
  首页配置信息修改: createApiHooks(params => {
    return request.put<ApiResponse<any>>(
      '/intelligent/mch/intelligent/home/<USER>/config',
      params
    );
  })
};
