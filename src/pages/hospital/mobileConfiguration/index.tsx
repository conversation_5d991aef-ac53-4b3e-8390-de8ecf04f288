import { Tabs } from 'antd';
import React from 'react';
import { BlankLayout } from 'parsec-admin';
import We<PERSON>hart from '@pages/hospital/mobileConfiguration/components/weChart';
import NewWeChart from '@pages/hospital/mobileConfiguration/components/weChartduplicate';

export default () => {
  return (
    <BlankLayout>
      <Tabs defaultActiveKey='1'>
        <Tabs.TabPane tab='微信首页配置' key='1'>
          {/* <WeChart /> */}
          <NewWeChart />
        </Tabs.TabPane>
      </Tabs>
    </BlankLayout>
  );
};
