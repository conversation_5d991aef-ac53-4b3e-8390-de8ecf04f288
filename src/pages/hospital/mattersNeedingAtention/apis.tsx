import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ApiResponse,
  ListApiRequestParams,
  ListApiResponseData
} from '@configs/d';
import { Docinfo, docType } from './d';

interface detail {
  id: '@natural'; //主键ID
  hisId: '@natural'; //机构ID
  hisName: '@cword(6)'; //机构名称
  noticeType: string; //提示类型(挂号须知-GHXZ；挂号成功提示-GHCGTS；首页提示-SYTS)
  noticeTypeName: '@cword(6)'; //提示类型名称
  noticeMethod: "@pick('DLYM','TC','WBK')"; //提示方法(独立页面-DLYM；弹窗-TC；文本块-WBK)
  noticeMethodName: '@cword(6)'; //提示方法名称
  noticeInfo: '@paragraph'; //提示内容（支持富文本）
  status: '@@boolean'; //启用状态
  createId: '@natural'; //创建者ID
  creator: '@cname'; //创建者
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //修改时间
  deptNo: '';
}
interface data {
  hisId: '@natural'; //机构ID
  hisName: '@cword(6)'; //机构名称
  noticeType: "@pick('GHXZ','GHCGTS','SYTS')"; //提示类型(挂号须知-GHXZ；挂号成功提示-GHCGTS；首页提示-SYTS)
  noticeTypeName: '@cword(6)'; //提示类型名称
  noticeMethod: "@pick('DLYM','TC','WBK')"; //提示方法(独立页面-DLYM；弹窗-TC；文本块-WBK)
  noticeMethodName: '@cword(6)'; //提示方法名称
  noticeInfo: '@paragraph'; //提示内容（支持富文本）
  status: '@@boolean'; //启用状态
}
export interface DeptItem {
  id: number;
  hisId: 40009;
  name: '儿科';
  no: '10001';
  sortNo: 0;
  employeeCount: null;
  tel: '023-8973495873';
  status: 1;
  pid: 85;
  pathCode: '/p85/p86/';
  hisType: 2;
  isSummary: 1;
  address: '';
  createTime: '2021-08-23 08:53:48';
  updateTime: '2021-08-23 08:53:48';
  children: DeptItem[];
}
export type listParams = ListApiRequestParams & {
  sort?: string;
  hisId?: string;
  deptNo?: string;
  deptId?: number | string;
  hisType?: string | number;
  refundStatus?: string;
  beginTime?: string;
  endTime?: string;
  pageNum?: number;
  numPerPage: number;
  targetHisId?: string;
  type: docType;
  orderBy?: string;
  showInquiry?: 0 | 1; //是否返回问诊配置 0不需要，1需要
  scheduleState?: 0 | 1; //排班状态 1已排班 0未排班
  scheduleDate?: string; //排班时间，默认为当天
};
export default {
  注意事项列表查询: createApiHooks(
    (
      params: {
        noticeType?: string;
        noticeMethod?: string;
      } & ListApiRequestParams
    ) =>
      request.get<ApiResponse<detail[]>>('/mch/content/notice/info', { params })
  ),
  注意事项详情查询: createApiHooks((id: string) =>
    request.get<ApiResponse<detail>>(`/mch/content/notice/info/${id}`)
  ),
  注意事项删除: createApiHooks((id: string) =>
    request.delete<ApiResponse<any>>(`/mch/content/notice/info/${id}`)
  ),
  注意事项新增: createApiHooks((params: data) =>
    request.post<ApiResponse<detail>>(`/mch/content/notice/info`, params, {
      headers: { 'Content-Type': 'application/json' }
    })
  ),
  注意事项修改: createApiHooks((params: data & { id: '' }) =>
    request.put<ApiResponse<detail>>(`/mch/content/notice/info`, params)
  ),
  查询字典组列表: createApiHooks((params: ListApiRequestParams) =>
    request.get<
      ListApiResponseData<{
        id: 1; //Id
        code: string; //医院Id
        name: string; // 字典组名称
        remark: '@ctitle'; // 备注
        groupType: 'query.groupType'; // 字典组类型
      }>
    >('/kaiqiao/his/dictGroup/page', { params })
  ),
  查询字典列表: createApiHooks(
    (params: { groupCode: string } & ListApiRequestParams) =>
      request.get<
        ListApiResponseData<{
          id: 1; //Id
          hisId: 'query.hisId'; // 医院id
          dictGroupCode: 'query.groupCode'; // 字典组code
          dictKey: '@word'; // 字典键
          dictValue: {
            value: '@word';
          }; // 字典值
          sortNo: '@natural(1, 100)'; // 排序号 数值越大越靠前
        }>
      >(`/kaiqiao/his/dictItem/page/${params.groupCode}`)
  ),
  科室管理列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        hisType?: string | number; // 所属业务平台 1互联网医院、2智慧医院、3互/智
      }
    ) =>
      request
        .get<ApiResponse<DeptItem[]>>('/mch/his/deptMain', { params: data })
        .then(res => {
          return {
            ...res,
            data: {
              code: res.data.code,
              msg: res.data.msg,
              data: {
                currentPage: 1,
                totalCount: 1,
                recordList: res.data.data || []
              }
            }
          };
        })
  ),
  医生列表: createApiHooks((params: listParams) =>
    request.get<ListApiResponseData<Docinfo>>(
      `/mch/his/${getDoctorTypePath(params.type)}`,
      {
        params
      }
    )
  )
};

function getDoctorTypePath(type?: string | number) {
  if (!type) {
    return 'doctorMain';
  }
  if (['2', 2].includes(type)) {
    return 'nurse';
  }
  if (['4', 4].includes(type)) {
    return 'pharmacist';
  }
  return 'doctorMain';
}
