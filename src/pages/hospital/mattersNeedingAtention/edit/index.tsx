import React, { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, Card, Cascader, Form, Space } from 'antd';
import '../index.less';
import TabPaneLayout from '@pages/hospital/info/layout';
import useApi from '../apis';
import { ArrSelect, Editor, handleSubmit } from 'parsec-admin';
import { useHistory, useParams } from 'react-router-dom';
import {
  getTreeOptions,
  getTreeOptionsNoSplit
} from '@pages/hospital/doctorDetailSmart/utils';
import env from '@configs/env';
import storage from '@utils/storage';
import apis from '@pages/hospital/mattersNeedingAtention/apis';
import { TransferChange } from '@kqinfo/ui';
export default () => {
  const hisName = useMemo(() => {
    const userInfo: any = storage.get('userInfo');
    return userInfo?.hisName || '';
  }, []);
  const history = useHistory();
  const [form] = Form.useForm();
  const [deptObj, setDeptObj] = useState({} as any);
  const [isdept, setIsdept] = useState('');
  const { id } = useParams<{ id: string }>();
  const [values, setValues] = useState<any>([]);
  const {
    data: { data: depList }
  } = apis.科室管理列表({
    initValue: [{ data: { recordList: [] } }]
  });
  const {
    data: { data }
  } = apis.医生列表({
    params: {
      pageNum: 1,
      numPerPage: 999,
      type: '1'
    },
    initValue: {
      data: {
        recordList: []
      }
    },
    needInit: !!id
  });
  const {
    data: { data: enmu }
  } = apis.查询字典组列表({
    params: {
      pageNum: 1,
      numPerPage: 999
    }
  });
  const [noticeType, setNoticeType] = useState<{ [key: string]: string }>({});
  const [noticeMethod, setNoticeMethod] = useState<{ [key: string]: string }>(
    {}
  );
  useEffect(() => {
    if (enmu?.recordList && enmu?.recordList?.length) {
      const arr1 = enmu?.recordList.filter(item => item.code === 'noticeType');
      const arr2 = enmu?.recordList.filter(
        item => item.code === 'noticeMethod'
      );
      if (arr1?.length && arr1?.[0]?.code) {
        apis.查询字典列表
          .request({
            pageNum: 1,
            numPerPage: 999,
            groupCode: arr1?.[0]?.code
          })
          .then(({ data }) => {
            if (data?.recordList?.length) {
              const obj: { [key: string]: string } = {};
              data?.recordList.forEach(item => {
                obj[item.dictKey || ''] = item.dictValue.value;
              });
              setNoticeType(obj);
            }
          });
      }
      if (arr2?.length && arr2?.[0]?.code) {
        apis.查询字典列表
          .request({
            pageNum: 1,
            numPerPage: 999,
            groupCode: arr2?.[0]?.code
          })
          .then(({ data }) => {
            if (data?.recordList?.length) {
              const obj: { [key: string]: string } = {};
              data?.recordList.forEach(item => {
                obj[item.dictKey || ''] = item.dictValue.value;
              });
              setNoticeMethod(obj);
            }
          });
      }
    }
  }, [enmu]);
  const {
    data: { data: details },
    loading
  } = useApi.注意事项详情查询({
    params: id,
    needInit: id !== 'add'
  });
  const { request, loading: saveLoading } = useApi.注意事项修改({
    needInit: false
  });
  const { request: addRequst, loading: addLoading } = useApi.注意事项新增({
    needInit: false
  });
  useEffect(() => {
    if (Object.keys(details || {})?.length) {
      details && setIsdept(details?.noticeType);
      const deptObj = { ...details, no: details?.deptNo };
      setDeptObj(deptObj);
      form.setFieldsValue(details);
    }
    setValues(details?.noticeInfo || '');
  }, [details, form]);
  return (
    <>
      <TabPaneLayout
        loading={loading}
        title={details?.hisName || hisName}
        formChild={
          <>
            <Form
              name='hospitalInfoForm'
              className={'formBox'}
              size='large'
              form={form}>
              <Form.Item
                wrapperCol={{ span: 8 }}
                label='注意事项类别'
                rules={[
                  {
                    required: true,
                    message: '请输入注意事项类别'
                  }
                ]}
                name='noticeType'>
                <ArrSelect
                  placeholder={'请选择注意事项类别'}
                  options={noticeType}
                  onChange={value => {
                    setIsdept(value + '');
                  }}
                />
              </Form.Item>
              {isdept === 'GHKSTS' && (
                <>
                  <Form.Item
                    wrapperCol={{ span: 8 }}
                    label='科室'
                    rules={[
                      {
                        required: true,
                        message: '请选择科室'
                      }
                    ]}
                    name='deptName'>
                    <TransferChange
                      mode={'cascade'}
                      data={getTreeOptions(depList?.recordList || []) as any}>
                      <Cascader
                        expandTrigger='hover'
                        showSearch
                        placeholder='请选择科室'
                        options={
                          getTreeOptionsNoSplit(
                            depList?.recordList || []
                          ) as any
                        }
                        onChange={(v, item) => {
                          const deptList = item?.[item?.length - 1];
                          setDeptObj(deptList);
                        }}
                      />
                    </TransferChange>
                  </Form.Item>
                </>
              )}
              {isdept === 'GHYSTS' && (
                <>
                  <Form.Item
                    wrapperCol={{ span: 8 }}
                    label='医生'
                    rules={[
                      {
                        required: true,
                        message: '请选择医生'
                      }
                    ]}
                    name='doctorId'>
                    <ArrSelect
                      placeholder={'请选择医生'}
                      options={
                        data?.recordList?.map(item => ({
                          ...item,
                          label: item.name,
                          value: item.doctorId
                        })) || []
                      }
                    />
                  </Form.Item>
                </>
              )}
              <Form.Item
                label='提示方式'
                wrapperCol={{ span: 8 }}
                rules={[
                  {
                    required: true,
                    message: '请输入提示方式'
                  }
                ]}
                name='noticeMethod'>
                <ArrSelect
                  placeholder={'请选择提示方式'}
                  options={noticeMethod}
                />
              </Form.Item>
              <Form.Item
                style={{ marginTop: '50px' }}
                name='noticeInfo'
                rules={[
                  {
                    required: true,
                    message: '请输入注意事项'
                  }
                ]}>
                <Editor onChange={e => setValues(e)} />
              </Form.Item>
              <Button
                type={'primary'}
                loading={saveLoading || addLoading}
                onClick={() => {
                  form.validateFields().then(value => {
                    handleSubmit(() =>
                      id === 'add'
                        ? addRequst({
                            ...value,
                            noticeTypeName: noticeType[value.noticeType],
                            noticeMethodName: noticeMethod[value.noticeMethod],
                            status: true,
                            hisName,
                            hisId: env.hisId,
                            deptNo: isdept === 'GHYSTS' ? '' : deptObj?.no,
                            deptName: deptObj?.label
                          })
                        : request({
                            ...details,
                            ...value,
                            noticeTypeName: noticeType[value.noticeType],
                            noticeMethodName: noticeMethod[value.noticeMethod],
                            deptNo: isdept === 'GHYSTS' ? '' : deptObj?.no,
                            deptName: deptObj?.label
                          })
                    ).then(() => history.goBack());
                  });
                }}>
                保存
              </Button>
            </Form>
          </>
        }
        previewChild={
          <Space size={15} direction='vertical' style={{ width: '100%' }}>
            <Card
              bordered={false}
              style={{ borderRadius: '10px' }}
              bodyStyle={{ padding: '15px 10px', textAlign: 'center' }}>
              <div
                style={{ minHeight: 700 }}
                dangerouslySetInnerHTML={{
                  __html: values ? values : '<p>暂无数据</p>'
                }}
              />
            </Card>
          </Space>
        }
      />
    </>
  );
};
