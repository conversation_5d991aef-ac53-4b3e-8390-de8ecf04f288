import {
  actionConfirm,
  ActionsWrap,
  ArrSelect,
  handleSubmit,
  LinkButton,
  TableList
} from 'parsec-admin';
import React, { useEffect, useMemo, useState } from 'react';
import apis from './apis';
import { Button } from 'antd';
import { useHistory } from 'react-router-dom';
import storage from '@utils/storage';
export default () => {
  const { push } = useHistory();
  const hisName = useMemo(() => {
    const userInfo: any = storage.get('userInfo');
    return userInfo?.hisName || '';
  }, []);
  const {
    data: { data: enmu }
  } = apis.查询字典组列表();
  const [noticeType, setNoticeType] = useState<{ [key: string]: string }>({});
  const [noticeMethod, setNoticeMethod] = useState<{ [key: string]: string }>(
    {}
  );
  useEffect(() => {
    if (enmu?.recordList && enmu?.recordList?.length) {
      const key1 = enmu?.recordList.filter(item => item.code === 'noticeType');
      const key2 = enmu?.recordList.filter(
        item => item.code === 'noticeMethod'
      );
      const params = { pageNum: 1, numPerPage: 999 };
      if (key1?.length && key1?.[0]?.code) {
        apis.查询字典列表
          .request({
            ...params,
            groupCode: key1?.[0]?.code
          })
          .then(({ data }) => {
            if (data?.recordList?.length) {
              const obj: { [key: string]: string } = {};
              data?.recordList.forEach(item => {
                obj[item.dictKey || ''] = item.dictValue.value;
              });
              setNoticeType(obj);
            }
          });
      }
      if (key2?.length && key2?.[0]?.code) {
        apis.查询字典列表
          .request({
            ...params,
            groupCode: key2?.[0]?.code
          })
          .then(({ data }) => {
            if (data?.recordList?.length) {
              const obj: { [key: string]: string } = {};
              data?.recordList.forEach(item => {
                obj[item.dictKey || ''] = item.dictValue.value;
              });
              setNoticeMethod(obj);
            }
          });
      }
    }
  }, [enmu]);
  return (
    <>
      <TableList
        tableTitle='配置列表'
        getList={({ pagination: { current = 1 }, params }) => {
          return apis.注意事项列表查询
            .request({
              ...params,
              pageNum: current,
              numPerPage: 10
            })
            .then(({ data }) => {
              return { list: data || [] };
            });
        }}
        action={
          <Button onClick={() => push('/mattersNeedingAttention/edit/add')}>
            新建
          </Button>
        }
        showTool={false}
        showExpand={false}
        columns={[
          {
            title: '医院名称',
            searchIndex: 'hisName',
            search: (
              <ArrSelect defaultValue={hisName} options={hisName} disabled />
            )
          },
          {
            title: '医疗机构名称',
            dataIndex: 'hisName'
          },
          {
            title: '注意事项类别',
            dataIndex: 'noticeTypeName',
            searchIndex: 'noticeType',
            search: <ArrSelect options={noticeType} />
          },
          {
            title: '科室',
            dataIndex: 'deptName'
          },
          {
            title: '医生',
            dataIndex: 'doctorName'
          },
          {
            title: '提示方式',
            searchIndex: 'noticeMethod',
            search: <ArrSelect options={noticeMethod} />,
            dataIndex: 'noticeMethodName'
          },
          {
            title: '创建人',
            dataIndex: 'creator'
          },
          {
            title: '状态',
            dataIndex: 'status',
            render: v => (v ? '启用' : '未启用')
          },
          {
            title: '创建时间',
            dataIndex: 'createTime'
          },
          {
            title: '操作',
            fixed: 'right',
            render: (record: any) => (
              <ActionsWrap>
                <LinkButton
                  onClick={() => {
                    handleSubmit(() =>
                      apis.注意事项修改.request({
                        ...record,
                        status: !record.status
                      })
                    );
                  }}>
                  {record?.status ? '停用' : '启用'}
                </LinkButton>
                <LinkButton
                  onClick={() =>
                    push(`/mattersNeedingAttention/edit/${record.id}`)
                  }>
                  编辑
                </LinkButton>
                <LinkButton
                  style={{ color: 'red' }}
                  onClick={() => {
                    actionConfirm(
                      () => apis.注意事项删除.request(record.id),
                      '删除'
                    );
                  }}>
                  删除
                </LinkButton>
              </ActionsWrap>
            )
          }
        ]}
      />
    </>
  );
};
