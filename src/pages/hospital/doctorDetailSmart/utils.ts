export const mapCodeToOptions = (val?: { code: number; desc: string }[]) => {
  if (!Array.isArray(val)) {
    return;
  }
  return val.map(item => ({
    value: item.code,
    label: item.desc
  }));
};

interface DeptTreeOptions {
  id: number;
  hisId: 40009;
  name: '儿科';
  no: '10001';
  sortNo: 0;
  employeeCount: null;
  tel: '023-8973495873';
  status: 1;
  pid: 85;
  pathCode: '/p85/p86/';
  hisType: 2;
  isSummary: 1;
  address: '';
  createTime: '2021-08-23 08:53:48';
  updateTime: '2021-08-23 08:53:48';
  children: DeptTreeOptions[];
}
interface DeptTreeOptions1 {
  id: number;
  hisId: 40009;
  name: '儿科';
  no: '10001';
  sortNo: 0;
  employeeCount: null;
  tel: '023-8973495873';
  status: 1;
  pid: 85;
  pathCode: '/p85/p86/';
  hisType: 2;
  isSummary: 1;
  address: '';
  createTime: '2021-08-23 08:53:48';
  updateTime: '2021-08-23 08:53:48';
  deptName: '';
  deptId: '';
}
interface TreeOptions {
  label?: string;
  value?: string | number;
  children?: TreeOptions[];
}
interface TreeOptionsNo {
  label?: string;
  value?: string | number;
  children?: TreeOptions[];
  no?: string;
}
export const getTreeOptionsNoSplit = (val?: DeptTreeOptions[]) => {
  function generateTree(item?: DeptTreeOptions): TreeOptionsNo | undefined {
    if (!item) {
      return;
    }
    const resultItem: TreeOptionsNo = {};

    resultItem.label = item.name;
    resultItem.no = item.no;
    resultItem.value = item.id;
    if (item.children) {
      resultItem.children = item.children.map(generateTree) as any;
    }
    return resultItem;
  }
  if (!Array.isArray(val)) {
    return [];
  }
  return val.map(generateTree).filter(Boolean) as DeptTreeOptions[];
};
export const getTreeOptions = (val?: DeptTreeOptions[]) => {
  function generateTree(item?: DeptTreeOptions): TreeOptions | undefined {
    if (!item) {
      return;
    }
    const resultItem: TreeOptions = {};

    resultItem.label = item.name;
    resultItem.value = item.id;
    if (item.children) {
      resultItem.children = item.children.map(generateTree) as any;
    }
    return resultItem;
  }
  if (!Array.isArray(val)) {
    return [];
  }
  return val.map(generateTree).filter(Boolean) as DeptTreeOptions[];
};
export const getTreeOptionsNo = (val?: DeptTreeOptions[]) => {
  function generateTree(item?: DeptTreeOptions): TreeOptions | undefined {
    if (!item) {
      return;
    }
    const resultItem: TreeOptions = {};

    resultItem.label = item.name;
    resultItem.value = item.no;
    if (item.children) {
      resultItem.children = item.children.map(generateTree) as any;
    }
    return resultItem;
  }
  if (!Array.isArray(val)) {
    return [];
  }
  return val.map(generateTree).filter(Boolean) as DeptTreeOptions[];
};
// 将no作为value
export const getTreeOptionsByNo = (val?: DeptTreeOptions[]) => {
  function generateTree(item?: DeptTreeOptions): TreeOptions | undefined {
    if (!item) {
      return;
    }
    const resultItem: TreeOptions = {};

    resultItem.label = item.name;
    resultItem.value = item.no;
    if (item.children) {
      resultItem.children = item.children.map(generateTree) as any;
    }
    return resultItem;
  }
  if (!Array.isArray(val)) {
    return [];
  }
  return val.map(generateTree).filter(Boolean) as DeptTreeOptions[];
};

export type ValueType = {
  id?: number;
  hisType?: number;
  deptList?: { id: number; name: string }[];
};
