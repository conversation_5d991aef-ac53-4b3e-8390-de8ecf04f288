import useflowApi from '../../authority/api';
import env from '@configs/env';
import { ArrSelect } from 'parsec-admin';
import React from 'react';
import { ArrSelectProps } from 'parsec-admin/lib/components/arrSelect';

interface RoleSelectProp extends Omit<ArrSelectProps, 'options'> {
  clientType: string;
}
export default (props: RoleSelectProp) => {
  const hisId = env.hisId;
  const { clientType } = props;
  const {
    data: { data: roleData }
  } = useflowApi.roleList({
    initValue: {
      data: {
        recordList: []
      }
    },
    params: {
      hisId,
      clientType
    },
    needInit: !!hisId && !!clientType
  });
  return (
    <ArrSelect
      {...props}
      options={(roleData?.recordList || []).map(x => ({
        value: x.id,
        children: x.roleName
      }))}
    />
  );
};
