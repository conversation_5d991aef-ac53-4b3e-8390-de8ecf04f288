import React, { useState, useEffect, useMemo } from 'react';
import { CardLayout, UploadImg, handleSubmit } from 'parsec-admin';
import PlatformList from './platformList';
import { Spin, message, Tabs } from 'antd';
import { ArrSelect } from 'parsec-admin';
import styled from 'styled-components';
import useApi from '../doctorSmart/api';
import {
  Form,
  Input,
  Col,
  Row,
  Button,
  Switch,
  Space,
  Select,
  DatePicker,
  InputNumber
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import MoNameSwitch from './moNameSwitch';
import {
  doctorlevels,
  druglevels,
  nurselevels,
  categories,
  prescriptionQualifications,
  docType
} from '../doctor/d';
import { useLocation } from 'react-router-dom';
import moment from 'moment';
import env from '@configs/env';
import { useSessionStorage } from 'react-use';
import { Space as KqSpace, TransferChange } from '@kqinfo/ui';
import apis from '@pages/organization/config/api';
import ConfigStore from '@src/store/ConfigStore';
import RoleSelect from '@pages/hospital/doctorDetailSmart/roleSelect';
import { useHistory, useParams } from 'react-router';
import MutiCheckBox from './mutiCheckBox';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
// const isDev = process.env.NODE_ENV !== 'production';
const MySwitch: React.FC<{
  disabled?: boolean;
  value?: string | number;
  onChange?: (v: string) => void;
}> = props => {
  return (
    <Switch
      disabled={props.disabled}
      checked={props.value === '1' || props.value === 1}
      onChange={v => {
        props.onChange && props.onChange(v ? '1' : '0');
      }}
    />
  );
};
export const initInquirys = [
  { type: '1', isOnDuty: '0', name: '图文问诊' },
  { type: '2', isOnDuty: '0', name: '电话问诊' },
  { type: '3', isOnDuty: '0', name: '视频问诊' },
  { type: '11', isOnDuty: '0', name: '健康咨询' },
  { type: '21', isOnDuty: '0', name: '随访咨询' },
  { type: '7', isOnDuty: '0', name: '新冠咨询' },
  { type: '31', isOnDuty: '0', name: '护理咨询' }
];
export default ({ isLocal = false }: { isLocal?: boolean }) => {
  const history = useHistory();
  const { id, targetHisId, hisId: paramsHisId } = useParams<{
    id: string;
    targetHisId: string;
    hisId: string;
  }>();
  const [isFlowup] = useSessionStorage('isFlowUp', '0');
  const { config } = ConfigStore.useContainer();
  useEffect(() => {
    console.log('isFlowup', isFlowup);
  }, [isFlowup]);
  const locat = useLocation();
  // 1医生 2护士 3职员 4药师
  const type: docType = useMemo(() => {
    if (locat.pathname.includes('/doctor')) {
      return '1';
    }
    if (locat.pathname.includes('/nurse')) {
      return '2';
    }
    if (locat.pathname.includes('/drug')) {
      return '4';
    }
    return '1';
  }, [locat]);

  const name = useMemo(() => {
    if (type === '1') {
      return '医生';
    }
    if (type === '2') {
      return '护士';
    }
    if (type === '4') {
      return '药师';
    }
    return '医生';
  }, [type]);
  const levels = useMemo(() => {
    if (type === '1') {
      return doctorlevels;
    }
    if (type === '2') {
      return nurselevels;
    }
    if (type === '4') {
      return druglevels;
    }
    return doctorlevels;
  }, [type]);

  const hisId = env.hisId;
  console.log('hisId', hisId);
  const {
    data: detailData,
    request: requestDetail,
    loading
  } = useApi.详情主键ID({
    params: id,
    needInit: !!id && id !== 'new' && !isLocal
  });
  const {
    data: localDetailData,
    loading: loadingLocalDetail
  } = useApi.本地医生详情主键ID({
    params: { hisId: paramsHisId, targetHisId, doctorMainId: id },
    needInit: !!id && isLocal && !!targetHisId && !!paramsHisId
  });
  const loadingDetail = useMemo(
    () => (isLocal ? loadingLocalDetail : loading),
    [isLocal, loading, loadingLocalDetail]
  );
  const data = useMemo(() => (isLocal ? localDetailData : detailData), [
    detailData,
    isLocal,
    localDetailData
  ]);
  const hisTypeList = useMemo(() => {
    return data?.data?.hisTypeList?.map(item => item.hisType) || [];
  }, [data?.data?.hisTypeList]);

  const { request: requestUpdate, loading: loadingUpdate } = useApi.医生更新({
    cache: false,
    needInit: false
  });
  const { request: requestSave, loading: loadingSave } = useApi.医生新增({
    cache: false,
    needInit: false
  });
  const [values, setValues] = useState<any>();
  const {
    data: { data: doctorConfigData },
    request: configReq
  } = useApi.获取服务配置信息({
    cache: false,
    params: { id: id },
    needInit: !!hisTypeList?.includes(1) && !isLocal
  });
  const {
    data: { data: configLocalData }
  } = useApi.本地医生服务配置列表({
    cache: false,
    params: { id: id, targetHisId, hisId },
    needInit: !!hisTypeList?.includes(1) && isLocal && !!targetHisId
  });
  const configData = useMemo(
    () => (isLocal ? configLocalData : doctorConfigData),
    [configLocalData, doctorConfigData, isLocal]
  );
  const {
    request: requestSaveForservice,
    loading: loadingSaveForService
  } = useApi.服务设置更新({
    cache: false,
    needInit: false
  });
  const {
    data: { data: hisDetailData }
  } = apis.基础配置详情({
    initValue: { data: {} },
    needInit: !isLocal && !targetHisId
  });
  const {
    data: { data: hisLocalDetails }
  } = apis.本地基础配置详情({
    initValue: { data: {} },
    needInit: isLocal && !!targetHisId,
    params: {
      targetHisId,
      hisId
    }
  });
  const hisDetails = useMemo(
    () => (isLocal ? hisLocalDetails : hisDetailData),
    [hisDetailData, hisLocalDetails, isLocal]
  );
  const HospitalDistrict = useMemo(() => {
    if (
      hisDetails &&
      hisDetails?.isEnableHisDistrictTag &&
      hisDetails?.hisDistrictTag
    ) {
      return {
        isShow: true,
        hisDistrictTag: hisDetails.hisDistrictTag?.split(',') || []
      };
    }
    return { isShow: false, hisDistrictTag: [] };
  }, [hisDetails]);
  // const {
  //   data: { data: deptList }
  // } = useApi.科室列表({
  //   params: {
  //     hisId
  //   },
  //   needInit: !!hisId
  // });

  const {
    request: flowupSet,
    loading: loadingSaveFlowUp
  } = useApi.随访设置更新({
    needInit: false
  });
  const {
    request: saveCoRoleReq,
    loading: coLoading
  } = useApi.更新医生医联体角色({
    needInit: false
  });
  const {
    data: { data: followData }
  } = useApi.查询医生账号信息({
    params: { id: id },
    needInit: !!hisTypeList?.includes(4) || !!hisTypeList?.includes(5)
  });
  // const { request: reqSyncPrice, loading: loadingSync } = useApi.同步问诊价格({
  //   needInit: false
  // });

  const [form] = useForm();
  const [formService] = useForm();
  const [formFlowUp] = useForm();
  const [formSmartHis] = useForm();
  const [isShowRegFee, setIsShowRegFee] = useState(false);
  useEffect(() => {
    // 新建时 初始化
    form.setFieldsValue({
      inquirys: initInquirys,
      recommend: '0'
    });

    formService.setFieldsValue({
      inquirys: initInquirys,
      recommend: '0'
    });
  }, [form, formService]);

  const [valuesForService, setValuesForService] = useState<any>();

  // 初始化基本信息
  useEffect(() => {
    if (data && data?.data) {
      const detail = data?.data;
      detail.doctorInfo = detail.doctorInfo || ({} as any);
      const arr: any[] = [];
      for (let i = 0; i < detail.hisTypeList?.length; i++) {
        for (let j = 0; j < detail.hisTypeList[i]?.deptList?.length; j++) {
          if (
            detail.hisTypeList[i]?.deptList[j].introduction &&
            detail.hisTypeList[i]?.deptList[j].specialty
          )
            arr.push({
              depId: detail.hisTypeList[i]?.deptList[j].id,
              depSpecialty: detail.hisTypeList[i]?.deptList[j].specialty,
              depIntroduction: detail.hisTypeList[i]?.deptList[j].introduction
            });
        }
      }
      const regFeeList = data?.data?.hisTypeList?.filter(
        item => item.hisType === 2
      )?.[0] as any;
      console.log(regFeeList, 'regFeeList');
      const values = {
        list: arr,
        image: detail.image || undefined,
        name: detail.name,
        doctorId: detail.doctorId,
        mobile: detail.mobile,
        idNumber: detail.doctorInfo.idNumber,
        medicalInsuranceCode: detail.medicalInsuranceCode,
        cnMedicine: detail.cnMedicine,
        hisType: detail.hisType + '',

        // level: detail.grade,
        level: (levels || [])?.find(x => detail.level === x.label)?.value, // level要取grade ，是的，你没看错
        position: detail.doctorInfo.position,
        practiceNumber: detail.doctorInfo.practiceNumber,
        practiceMedicalInstitution:
          detail.doctorInfo.practiceMedicalInstitution,
        category: detail.doctorInfo.category,
        policyNumber: detail.doctorInfo.policyNumber,
        insuranceDate: detail.doctorInfo.insuranceStartDate
          ? [
              moment(detail.doctorInfo.insuranceStartDate),
              moment(detail.doctorInfo.insuranceEndDate)
            ]
          : null,
        internetDeptNo: (detail.internetDeptNo || '')
          .split(',')
          .filter(x => !!x),
        wisdomDeptNo: (detail.wisdomDeptNo || '').split(',').filter(x => !!x),
        auditTime: detail.doctorInfo.auditTime
          ? moment(detail.doctorInfo.auditTime)
          : null,
        practiceScope: detail.doctorInfo.practiceScope,
        practiceLevel: detail.doctorInfo.practiceLevel,
        moNames: detail.moNames,
        prescriptionQualification: detail.doctorInfo.prescriptionQualification,
        underwritingUnit: detail.doctorInfo.underwritingUnit,
        ValidDate: detail.doctorInfo.startValidDate
          ? [
              moment(detail.doctorInfo.startValidDate),
              moment(detail.doctorInfo.endValidDate)
            ]
          : null,
        internetSortNo: detail.internetSortNo,
        recommend: (detail.recommend || '0') + '',

        signatureImg: detail.doctorInfo.signatureImg,
        specialty: detail.specialty,
        introduction: detail.introduction,
        workingLife: detail.workingLife,
        hisTypeList: detail.hisTypeList,
        defaultType: detail.hisTypeList.map(item => item.hisType),
        hisDistrictTag: detail?.hisDistrictTag,
        regFee: regFeeList?.regFee
      };
      // if (values.defaultType.includes(1)) configReq();
      // if (values.defaultType.includes(4)) followReq();
      setValues(values);
      form.setFieldsValue(values);
    }
  }, [data, form, levels]);

  // 初始化服务权限
  useEffect(() => {
    if (data && data?.data) {
      const detail = data?.data;
      const inquirys: Array<{
        type: string;
        isOnDuty: string;
        remune?: number;
        price?: number;
        amount?: number;
      }> = initInquirys;
      (configData?.inquiryList || []).forEach(item => {
        const inquiry = inquirys.find(x => x.type === item.type);
        if (inquiry) {
          inquiry.isOnDuty = item.isOnDuty;
          inquiry.remune = item.remune / 100;
          inquiry.price = item.price / 100;
          if (item.amount) {
            inquiry.amount = item.amount / 100;
          } else {
            inquiry.amount = undefined;
          }
        }
      });
      // const obj = Object.assign({}, detail, detail.doctorInfoVo);
      detail.doctorInfo = detail.doctorInfo || ({} as any);
      // const typeObj = detail.hisTypeList?.find(item => item.hisType === 1);
      // const values = {
      //   moNames: configData?.moNames,
      //   hisType: detail.hisType + '',
      //   inquirys,
      //   recommend: typeObj?.recommend,
      //   sortNo: typeObj?.sortNo
      //   // recommend: (detail.recommend || '0') + '',
      // };
      // setValuesForService(values);
      // formService.setFieldsValue(values);
    }
  }, [configData?.inquiryList, data]);

  // 点击同步服务权限
  // const onServicePermissionSynchronous = useCallback(() => {
  //   const detail = data?.data;
  //   const inquirys: Array<{
  //     type: string;
  //     isOnDuty: string;
  //     remune?: number;
  //     price?: number;
  //     amount?: number;
  //   }> = initInquirys;
  //   (configData?.inquiryList || []).forEach(item => {
  //     const inquiry = inquirys.find(x => x.type === item.type);
  //     if (inquiry) {
  //       inquiry.isOnDuty = item.isOnDuty;
  //       inquiry.remune = item.remune / 100;
  //       inquiry.price = item.price / 100;
  //       if (item.amount) {
  //         inquiry.amount = item.amount / 100;
  //       } else {
  //         inquiry.amount = undefined;
  //       }
  //     }
  //   });
  //   const typeObj = detail.hisTypeList?.find(item => item.hisType === 1);
  //   const values = {
  //     moNames: encodeURI(configData?.moNames || ''),
  //     hisType: detail.hisType + '',
  //     inquirys,
  //     recommend: typeObj?.recommend,
  //     sortNo: typeObj?.sortNo
  //     // recommend: (detail.recommend || '0') + '',
  //   };
  //   setValuesForService(values);
  //   formService.setFieldsValue(values);
  // }, [configData?.inquiryList, configData?.moNames, data?.data, formService]);

  const onFinish = (values: any) => {
    console.log('Success:', values);
    // 科室选择
    values.internetDeptNo = (values.internetDeptNo || []).join(',');
    values.wisdomDeptNo = (values.wisdomDeptNo || []).join(',');

    if (values.auditTime) {
      values.auditTime = moment(values.auditTime).format('YYYY-MM-DD HH:mm:ss');
    }

    //保险时间
    if (values.insuranceDate) {
      values.insuranceStartDate = moment(values.insuranceDate[0]).format(
        'YYYY-MM-DD HH:mm:ss'
      );
      values.insuranceEndDate = moment(values.insuranceDate[1]).format(
        'YYYY-MM-DD HH:mm:ss'
      );
    }
    delete values.insuranceDate;
    // 有效时间
    if (values.ValidDate) {
      values.startValidDate = moment(values.ValidDate[0]).format(
        'YYYY-MM-DD HH:mm:ss'
      );
      values.endValidDate = moment(values.ValidDate[1]).format(
        'YYYY-MM-DD HH:mm:ss'
      );
    }

    // 要传中文
    values.level = levels.find(x => values.level === x.value)?.label;

    delete values.ValidDate;
    //服务设置
    (values.inquirys || []).forEach((item: any) => {
      item.remune *= 100;
      item.price *= 100;
      item.amount *= 100;
    });
    // values.inquirys = JSON.stringify(values.inquirys);
    delete values.inquirys;
    values.type = type;
    console.log('Success:', values);
    values.doctorInfo = {
      ...data?.data?.doctorInfo,
      idNumber: values.idNumber,
      medicalInsuranceCode: values.medicalInsuranceCode,
      cnMedicine: values.cnMedicine,
      mobile: values.mobile,
      doctorId: values.doctorId,
      name: values.name,
      image: values.image,
      practiceScope: values.practiceScope,
      practiceLevel: values.practiceLevel,
      practiceNumber: values.practiceNumber,
      category: values.category,
      position: values.position,
      administrativePosition: values.administrativePosition,
      practiceMedicalInstitution: values.practiceMedicalInstitution,
      prescriptionQualification: values.prescriptionQualification,
      auditTime: values.auditTime,
      policyNumber: values.policyNumber,
      underwritingUnit: values.underwritingUnit,
      insuranceStartDate: values.insuranceStartDate,
      insuranceEndDate: values.insuranceEndDate,
      startValidDate: values.startValidDate,
      endValidDate: values.endValidDate
    };

    console.log(values, 'values');

    if (!values?.hisTypeList || values?.hisTypeList?.length === 0)
      return message.error('请至少选择一个业务平台');
    if (values?.regFee !== undefined) {
      values.hisTypeList = values?.hisTypeList.map(v => {
        if (v.hisType === 2) {
          v.regFee = values.regFee;
        }
        return v;
      });
    }
    // values?.list?.forEach(item => {
    //   values?.hisTypeList?.forEach(innerItem => {
    //     if(item.depId)
    //   });
    // });

    const newTypeArr = values?.hisTypeList?.map(item => {
      const arr = item?.deptList?.map(innerItem => ({
        ...innerItem,
        introduction: '',
        specialty: ''
      }));
      return {
        ...item,
        deptList: arr
      };
    });

    const arr = newTypeArr;
    for (let index = 0; index < values?.list?.length; index++) {
      for (let innerIndex = 0; innerIndex < arr?.length; innerIndex++) {
        // const obj = arr[innerIndex]?.deptList?.find(
        //   item => item.id === values?.list[index]?.depId
        // );
        for (let j = 0; j < arr[innerIndex]?.deptList?.length; j++) {
          if (arr[innerIndex]?.deptList[j].id === values?.list[index]?.depId) {
            arr[innerIndex].deptList[j].specialty =
              values?.list[index]?.depSpecialty;
            arr[innerIndex].deptList[j].introduction =
              values?.list[index]?.depIntroduction;
            break;
          }
        }
      }
    }

    if (id) {
      return handleSubmit(
        () =>
          requestUpdate({
            id,
            hisId,
            ...values,
            hisTypeList: arr
          }).then(res => {
            if (res.code === 0) {
              requestDetail();
              // history.goBack();
            } else {
              message.error(res.msg);
            }
          }),
        '保存'
      );
    } else {
      return handleSubmit(
        () =>
          requestSave({
            hisId,
            ...values,
            hisTypeList: arr
          }).then(res => {
            if (res.code === 0) {
              history.goBack();
            } else {
              message.error(res.msg);
            }
          }),
        '创建'
      );
    }
  };
  const onFinishflowUp = (values: any) => {
    if (followData?.account?.id) {
      return handleSubmit(
        () =>
          flowupSet({
            id: String(followData?.account?.id),
            hisId,
            followRoleId: values.followRoleId
          }).then(() => {
            requestDetail();
          }),
        '保存'
      );
    }
  };
  const saveCoRole = (values: any) => {
    console.log('values', values);
    if (followData?.account?.id) {
      return handleSubmit(
        () =>
          saveCoRoleReq({
            id: followData.account.id as any,
            hisId,
            doctorIds: data?.data?.doctorId,
            cooperateRoleId: values?.cooperateRoleId
          }).then(() => {
            requestDetail();
          }),
        '保存'
      );
    }
  };
  // 服务设置保存
  const onFinishForService = (valuesForService: any) => {
    /** 后端需要使用分作为单位 */
    valuesForService.inquirys = (valuesForService.inquirys || []).map(
      (item: any) => ({
        ...item,
        remune: item.remune * 100,
        price: item.price * 100,
        amount: item.amount * 100
      })
    );
    const arr = data?.data?.hisTypeList;
    for (let i = 0; i < data?.data?.hisTypeList?.length; i++) {
      if (arr[i].hisType === 1) {
        arr[i].recommend = Number(valuesForService.recommend) ? '1' : '0';
        arr[i].sortNo = valuesForService.sortNo;
        break;
      }
    }
    delete valuesForService.recommend;
    delete valuesForService.sortNo;
    const params = {
      moNames: valuesForService.moNames,
      inquirys: JSON.stringify(valuesForService.inquirys),
      isEnableNursingAppointConsult:
        valuesForService.isEnableNursingAppointConsult
    };
    if (id) {
      return handleSubmit(
        () =>
          requestSaveForservice({
            id,
            hisId,
            hisTypeList: arr,
            ...params
          }).then(() => {
            // requestDetail();
            configReq();
          }),
        '保存'
      );
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  const onValuesChange = (changedValues: any, values: any) => {
    console.log('changedValues', changedValues);
    if (changedValues?.hisTypeList) {
      setIsShowRegFee(!!changedValues?.hisTypeList?.find(v => v.hisType === 2));
    }
    if (values['hospitalIntroduction']) {
      values['hospitalIntroduction'] = values['hospitalIntroduction'].toHTML();
    }
    setValues(values);
  };

  const onSmartFinish = valuesForService => {
    if (id) {
      return handleSubmit(
        () =>
          useApi.智慧医院更新
            .request({
              id,
              tags: valuesForService.tags
            })
            .then(() => {
              configReq();
            }),
        '保存'
      );
    }
  };

  const onValuesChangeForService = (changedValues: any, values: any) => {
    setValuesForService(values);
  };
  useEffect(() => {
    if (hisTypeList?.includes(1)) {
      const detail = data?.data;
      const inquirys: Array<{
        type: string;
        isOnDuty: string;
        remune?: number;
        price?: number;
        amount?: number;
      }> = initInquirys;
      (configData?.inquiryList || []).forEach(item => {
        const inquiry = inquirys.find(x => x.type === item.type);
        if (inquiry) {
          inquiry.isOnDuty = item.isOnDuty;
          inquiry.remune = item.remune / 100;
          inquiry.price = item.price / 100;
          if (item.amount) {
            inquiry.amount = item.amount / 100;
          } else {
            inquiry.amount = undefined;
          }
        }
      });
      const typeObj = detail.hisTypeList?.find(item => item.hisType === 1);
      const values = {
        moNames: configData?.moNames,
        hisType: detail.hisType + '',
        inquirys,
        recommend: typeObj?.recommend,
        sortNo: typeObj?.sortNo
        // recommend: (detail.recommend || '0') + '',
      };
      setValuesForService(values);
      formService.setFieldsValue(values);
    }
    if (hisTypeList?.includes(2)) {
      const detail = data?.data;
      const typeObj = detail.hisTypeList?.find(item => item.hisType === 2);
      formSmartHis.setFieldsValue({ tags: typeObj?.tags ?? [] });
    }
  }, [
    configData,
    configData?.inquiryList,
    configData?.moNames,
    data?.data,
    formService,
    hisTypeList,
    formSmartHis
  ]);

  console.log(
    'hisType======>',
    id && data?.data?.hisTypeList?.map(item => item.hisType).includes(2)
  );
  return (
    <>
      <CardLayout>
        <Tabs defaultActiveKey='1'>
          <TabPane tab={`${name}信息`} key='1'>
            {/* <Introduce loading={loading} data={data} /> */}
            <Spin spinning={loadingDetail || loadingUpdate || loadingSave}>
              <Form
                name='hospitalInfoForm'
                size='large'
                form={form}
                initialValues={{ ...values }}
                scrollToFirstError={true}
                onValuesChange={onValuesChange}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}>
                <Wrapper edit={false}>
                  <CardLayout title={'平台信息'}>
                    <Form.Item name='hisTypeList'>
                      <PlatformList
                        disabled={isLocal}
                        targetHisId={targetHisId}
                      />
                    </Form.Item>
                  </CardLayout>
                  <CardLayout title={'个人信息'}>
                    <Row gutter={[16, 0]}>
                      <Col span={8}>
                        <Form.Item
                          label={`${name}头像`}
                          name='image'
                          rules={[
                            {
                              required: true,
                              message: `请上传${name}头像`
                            }
                          ]}
                          extra='建议尺寸为 126px * 168px'>
                          <UploadImg
                            disabled={isLocal}
                            arrValue={false}
                            showUploadList={{
                              showPreviewIcon: true,
                              showRemoveIcon: true,
                              showDownloadIcon: false
                            }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label={`${name}姓名`}
                          name='name'
                          rules={[
                            {
                              required: true,
                              message: `请输入${name}姓名`
                            }
                          ]}>
                          <Input
                            disabled={isLocal}
                            placeholder={`请输入${name}姓名`}
                          />
                        </Form.Item>
                        <Form.Item
                          label={`${name}编号`}
                          name='doctorId'
                          rules={[
                            {
                              required: true,
                              message: `请输入${name}编号`
                            },
                            {
                              pattern: new RegExp(/^[\dA-Za-z]+$/),
                              message: '只能输入数字和字母'
                            }
                          ]}>
                          <Input
                            disabled={isLocal}
                            placeholder={`请输入${name}编号`}
                          />
                        </Form.Item>
                        <Form.Item
                          label='联系电话'
                          name='mobile'
                          rules={[
                            {
                              pattern: /^1[3456789]\d{9}$/,
                              message: '手机号码格式不正确'
                            }
                          ]}>
                          <Input
                            disabled={isLocal}
                            placeholder='请输入联系电话'
                          />
                        </Form.Item>
                        <Form.Item
                          label='身份证号'
                          name='idNumber'
                          rules={[
                            {
                              validator: (_, value: string) => {
                                if (!value) {
                                  return Promise.resolve();
                                }
                                // 身份证
                                if (
                                  /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(
                                    value
                                  )
                                ) {
                                  return Promise.resolve();
                                }
                                // 台湾居民来往大陆通行证
                                if (
                                  /(^\d{8}$)|(^[a-zA-Z0-9]{10}$)|(^\d{18}$)/.test(
                                    value
                                  )
                                ) {
                                  return Promise.resolve();
                                }
                                return Promise.reject(
                                  new Error('请输入正确的身份证号码')
                                );
                              }
                            }
                          ]}>
                          <Input
                            disabled={isLocal}
                            placeholder='请输入身份证号'
                          />
                        </Form.Item>
                        <Form.Item label='医保编码' name='medicalInsuranceCode'>
                          <Input
                            disabled={isLocal}
                            placeholder='请输入医保编码'
                          />
                        </Form.Item>
                        <Space align='start' size={20}>
                          <Form.Item label='中医问诊' name='cnMedicine'>
                            <MySwitch disabled={isLocal} />
                          </Form.Item>
                          <div style={{ paddingTop: 10 }}>
                            开启中医问诊后，医生可开中药饮片处方
                          </div>
                        </Space>
                      </Col>
                    </Row>
                  </CardLayout>
                  <CardLayout title={'执业信息'}>
                    <Row gutter={[16, 0]}>
                      <Col span={12}>
                        <Form.Item
                          label={`${name}职称`}
                          name='level'
                          rules={[
                            {
                              required: true,
                              message: `请选择${name}职称`
                            }
                          ]}>
                          <Select
                            disabled={isLocal}
                            options={levels}
                            placeholder={`请选择${name}职称`}
                          />
                        </Form.Item>

                        <Form.Item
                          label='教学职称'
                          name='position'
                          rules={[
                            {
                              // required: true,
                              message: `请输入教学职称`
                            }
                          ]}>
                          <Input
                            disabled={isLocal}
                            placeholder='请输入教学职称'
                          />
                        </Form.Item>
                        <Form.Item
                          label='执业证号'
                          name='practiceNumber'
                          rules={[
                            {
                              // required: true,
                              message: `请输入执业证号`
                            },
                            {
                              pattern: new RegExp(/^[\dA-Za-z]+$/),
                              message: '只能输入数字和字母'
                            }
                          ]}>
                          <Input
                            disabled={isLocal}
                            placeholder='请输入执业证号'
                          />
                        </Form.Item>
                        <Form.Item
                          label='执业医疗'
                          name='practiceMedicalInstitution'
                          rules={[
                            {
                              // required: true,
                              message: `请输入执业医疗机构名称`
                            }
                          ]}>
                          <Input
                            disabled={isLocal}
                            placeholder='请输入执业医疗机构名称'
                          />
                        </Form.Item>
                        <Form.Item
                          label={`${name}类别`}
                          name='category'
                          rules={[
                            {
                              // required: true,
                              message: `请选择${name}类别`
                            }
                          ]}>
                          <Select
                            disabled={isLocal}
                            options={categories}
                            placeholder={`请选择${name}类别`}
                          />
                        </Form.Item>
                        <Form.Item
                          label='保险单号'
                          name='policyNumber'
                          rules={[
                            {
                              // required: true,
                              message: `请输入保险单号`
                            },
                            {
                              pattern: new RegExp(/^[\dA-Za-z]+$/),
                              message: '只能输入数字和字母'
                            }
                          ]}>
                          <Input
                            disabled={isLocal}
                            placeholder='请输入保险单号'
                          />
                        </Form.Item>
                        <Form.Item label='保险期限' name='insuranceDate'>
                          <RangePicker
                            disabled={isLocal}
                            style={{ width: 400 }}
                            placeholder={['请选择开始日期', '请选择结束时间']}
                          />
                        </Form.Item>
                        {isShowRegFee && (
                          <Form.Item label='挂号金额(单位:分)' name='regFee'>
                            <InputNumber
                              disabled={isLocal}
                              style={{ width: '100%' }}
                              placeholder='请输入请输入挂号金额（单位：分）'
                            />
                          </Form.Item>
                        )}
                      </Col>
                      <Col span={12}>
                        <Form.Item label='首次获证' name='auditTime'>
                          <DatePicker
                            disabled={isLocal}
                            placeholder='请选择首次获证日期'
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                        <Form.Item
                          label='执业范围'
                          name='practiceScope'
                          rules={[
                            {
                              // required: true,
                              message: `请输入执业范围`
                            }
                          ]}>
                          <Input
                            disabled={isLocal}
                            placeholder='请输入执业范围'
                          />
                        </Form.Item>
                        <Form.Item
                          label='执业级别'
                          name='practiceLevel'
                          rules={[
                            {
                              // required: true,
                              message: `请输入执业级别`
                            }
                          ]}>
                          <Input
                            disabled={isLocal}
                            placeholder='请输入执业级别'
                          />
                        </Form.Item>
                        <Form.Item label='从业年限' name='workingLife'>
                          <InputNumber
                            disabled={isLocal}
                            style={{ width: '100%' }}
                            max={100}
                            min={0}
                            placeholder='请输入从业年限'
                          />
                        </Form.Item>
                        <Form.Item
                          label='处方资格'
                          name='prescriptionQualification'
                          rules={[
                            {
                              // required: true,
                              message: `请选择处方资格`
                            }
                          ]}>
                          <Select
                            disabled={isLocal}
                            options={prescriptionQualifications}
                            placeholder='请选择处方资格'
                          />
                        </Form.Item>
                        <Form.Item
                          label='承保单位'
                          name='underwritingUnit'
                          rules={[
                            {
                              // required: true,
                              message: '请输承保单位'
                            }
                          ]}>
                          <Input
                            disabled={isLocal}
                            placeholder='请输入承保单位'
                          />
                        </Form.Item>
                        <Form.Item label='有效时间' name='ValidDate'>
                          <RangePicker
                            disabled={isLocal}
                            style={{ width: 400 }}
                            placeholder={['请选择开始日期', '请选择结束时间']}
                          />
                        </Form.Item>
                      </Col>

                      {HospitalDistrict.isShow && (
                        <Col span={12}>
                          <Form.Item label='所属院区' name='hisDistrictTag'>
                            <ArrSelect
                              disabled={isLocal}
                              options={HospitalDistrict.hisDistrictTag}
                              placeholder={'请选择所属院区'}
                            />
                          </Form.Item>
                        </Col>
                      )}
                    </Row>
                  </CardLayout>
                  <CardLayout title={'电子签名'}>
                    <Form.Item label='' name='signatureImg'>
                      <UploadImg
                        disabled={isLocal}
                        showUploadList={{
                          showPreviewIcon: true,
                          showRemoveIcon: true,
                          showDownloadIcon: false
                        }}
                      />
                    </Form.Item>
                  </CardLayout>
                  <CardLayout title={'介绍信息'}>
                    <Row gutter={[16, 0]}>
                      <Col span={24}>
                        <Form.Item
                          label={
                            <SmallLabel>
                              擅长领域
                              <span>不得超过1000字</span>
                            </SmallLabel>
                          }
                          name='specialty'
                          rules={[
                            {
                              // required: true,
                              message: '请输入擅长领域'
                            },
                            {
                              max: 1000,
                              message: '擅长领域不得超过1000个字符'
                            }
                          ]}>
                          <Input.TextArea
                            disabled={isLocal}
                            rows={5}
                            placeholder='请输入擅长领域'
                          />
                        </Form.Item>
                      </Col>
                      <Col span={24}>
                        <Form.Item
                          label={
                            <SmallLabel>
                              {`${name}介绍`}
                              <span>不得超过2000字</span>
                            </SmallLabel>
                          }
                          name='introduction'
                          rules={[
                            {
                              // required: true,
                              message: `请输入${name}介绍`
                            },
                            {
                              max: 2000,
                              message: `${name}介绍不得超过2000个字符`
                            }
                          ]}>
                          <Input.TextArea
                            disabled={isLocal}
                            rows={5}
                            placeholder={`请输入${name}介绍`}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Form.List name={'list'}>
                      {(fields, { add, remove }) => (
                        <KqSpace vertical size={10}>
                          {fields.map((field, index) => {
                            let deptSelect: any[] = [];
                            for (
                              let i = 0;
                              i < form.getFieldValue('hisTypeList')?.length;
                              i++
                            ) {
                              const arr = form
                                .getFieldValue('hisTypeList')
                                [i].deptList?.map(item => ({
                                  label: item.name,
                                  value: item.id
                                }));
                              if (arr) deptSelect = [...deptSelect, ...arr];
                            }
                            return (
                              <KqSpace
                                key={field.key}
                                vertical
                                size={20}
                                style={{
                                  borderTop: '1px solid #d9d9d9',
                                  paddingTop: '10px'
                                }}>
                                <KqSpace
                                  alignItems={'center'}
                                  flex={1}
                                  size={50}
                                  justify={'space-between'}>
                                  <Form.Item
                                    {...field}
                                    label={'科室'}
                                    name={[field.name, 'depId']}
                                    rules={[
                                      {
                                        required: true,
                                        message: '请选择科室'
                                      }
                                    ]}>
                                    <TransferChange>
                                      {(onchange, value) => (
                                        <Select
                                          value={value}
                                          onChange={e => {
                                            const obj = form
                                              .getFieldValue('list')
                                              ?.find(
                                                innerItem =>
                                                  innerItem?.depId === e
                                              );
                                            if (!obj?.depId) onchange(e);
                                            else {
                                              message.warning(
                                                '该科室已有擅长介绍'
                                              );
                                            }
                                          }}
                                          placeholder='请选择科室'
                                          options={deptSelect}
                                        />
                                      )}
                                    </TransferChange>
                                  </Form.Item>
                                  <Button danger onClick={() => remove(index)}>
                                    删除该科室擅长介绍
                                  </Button>
                                </KqSpace>
                                <Form.Item
                                  {...field}
                                  label={
                                    <SmallLabel>
                                      擅长领域(科室)<span>不得超过1000字</span>
                                    </SmallLabel>
                                  }
                                  name={[field.name, 'depSpecialty']}
                                  rules={[
                                    {
                                      required: true,
                                      message: '请输入擅长领域'
                                    },
                                    {
                                      max: 1000,
                                      message: '擅长领域不得超过1000个字符'
                                    }
                                  ]}>
                                  <Input.TextArea
                                    rows={5}
                                    placeholder='请输入擅长领域'
                                  />
                                </Form.Item>
                                <Form.Item
                                  {...field}
                                  label={
                                    <SmallLabel>
                                      {`${name}介绍`}(科室)
                                      <span>不得超过1000字</span>
                                    </SmallLabel>
                                  }
                                  name={[field.name, 'depIntroduction']}
                                  rules={[
                                    {
                                      required: true,
                                      message: `请输入${name}介绍`
                                    },
                                    {
                                      max: 1000,
                                      message: `${name}介绍不得超过1000个字符`
                                    }
                                  ]}>
                                  <Input.TextArea
                                    rows={5}
                                    placeholder={`请输入${name}介绍`}
                                  />
                                </Form.Item>
                              </KqSpace>
                            );
                          })}
                          {!isLocal && (
                            <KqSpace flex={1} justify={'center'}>
                              <Button
                                style={{ width: '200px' }}
                                onClick={() => {
                                  add();
                                }}>
                                {`新增${name}科室介绍`}
                              </Button>
                            </KqSpace>
                          )}
                        </KqSpace>
                      )}
                    </Form.List>
                  </CardLayout>
                  <CardLayout>
                    {!isLocal && (
                      <Form.Item style={{ textAlign: 'right' }}>
                        <Space size={22}>
                          <Button
                            type='ghost'
                            onClick={() => {
                              history.goBack();
                            }}>
                            取消
                          </Button>
                          <Button
                            loading={
                              loadingDetail || loadingUpdate || loadingSave
                            }
                            type='primary'
                            htmlType='submit'>
                            {`保存${name}信息`}
                          </Button>
                        </Space>
                      </Form.Item>
                    )}
                  </CardLayout>
                </Wrapper>
              </Form>
            </Spin>
          </TabPane>
          {/* 权限设置必须编辑并且有互联医院权限才能操作 */}
          {/*{id && values?.defaultType?.includes(1) && (*/}
          {id &&
            data?.data?.hisTypeList?.map(item => item.hisType).includes(1) &&
            !isLocal && (
              <TabPane tab='互联网医院信息' key='2'>
                <Spin spinning={loadingDetail || loadingSaveForService}>
                  <Form
                    name='hospitalInfoFormForService'
                    size='large'
                    form={formService}
                    initialValues={{ ...valuesForService }}
                    scrollToFirstError={true}
                    onValuesChange={onValuesChangeForService}
                    onFinish={onFinishForService}>
                    <Wrapper edit={false}>
                      <CardLayout title={'权限信息'}>
                        <Row gutter={[16, 0]}>
                          <Col span={8}></Col>
                          <Col span={4}>
                            <Form.Item
                              label='首页推荐'
                              name='recommend'
                              rules={[
                                {
                                  required: false,
                                  message: '请选择首页推荐'
                                }
                              ]}>
                              <MySwitch />
                            </Form.Item>
                          </Col>
                          <Col span={7}>
                            <Form.Item
                              label='排列序号'
                              name='sortNo'
                              required={false}>
                              <InputNumber
                                placeholder='请输入排列序号'
                                style={{ width: 200 }}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Form.List name={'inquirys'}>
                          {fields => {
                            console.log(fields, 'fields');
                            const arr = [0, 1, 2];
                            if (!hisDetails?.isEnableNewCrownConsult) {
                              arr.push(5);
                            }
                            if (!hisDetails?.isEnableNursingConsult) {
                              arr.push(6);
                            }
                            return (
                              fields.filter(item => !arr.includes(item.name)) ||
                              []
                            ).map((field, index) => {
                              const labelName = [
                                '图文问诊',
                                '电话问诊',
                                '视频问诊',
                                '健康咨询',
                                '随访咨询',
                                '新冠咨询',
                                '护理咨询'
                              ][field.name];
                              return (
                                <Row gutter={[16, 0]} key={field.fieldKey}>
                                  <Col span={4}>
                                    <Form.Item
                                      label={labelName}
                                      name={[field.name, 'isOnDuty']}>
                                      <MySwitch />
                                    </Form.Item>
                                  </Col>
                                  <Col span={7}>
                                    <Form.Item
                                      label='执行价格'
                                      name={[field.name, 'remune']}>
                                      <InputNumber
                                        min={0}
                                        disabled={
                                          index !== 0 &&
                                          labelName !== '新冠咨询' &&
                                          labelName !== '护理咨询'
                                        }
                                        placeholder='请输入（元）'
                                        style={{ width: '100%' }}
                                      />
                                    </Form.Item>
                                  </Col>
                                  <Col span={7}>
                                    <Form.Item
                                      label='展示原价'
                                      name={[field.name, 'amount']}>
                                      <InputNumber
                                        min={0}
                                        disabled={
                                          index !== 0 &&
                                          labelName !== '新冠咨询' &&
                                          labelName !== '护理咨询'
                                        }
                                        placeholder='请输入（元）'
                                        style={{ width: '100%' }}
                                      />
                                    </Form.Item>
                                  </Col>

                                  {index === 2 &&
                                    !!id &&
                                    labelName !== '护理咨询' &&
                                    labelName !== '新冠咨询' && (
                                      <Col span={6}>
                                        <Form.Item
                                          label='排班号数'
                                          name={[
                                            field.name,
                                            'sumResourceNumFromNowOn'
                                          ]}>
                                          <InputNumber
                                            disabled
                                            placeholder='排班号数'
                                            style={{ width: '100%' }}
                                          />
                                        </Form.Item>
                                      </Col>
                                    )}
                                </Row>
                              );
                            });
                          }}
                        </Form.List>
                        {/*{type === '1' && (*/}
                        <Form.Item name='moNames'>
                          <MoNameSwitch />
                        </Form.Item>
                        {/*)}*/}
                      </CardLayout>
                      <CardLayout>
                        <Form.Item style={{ textAlign: 'right' }}>
                          <Space size={22}>
                            <Button
                              type='ghost'
                              onClick={() => {
                                history.goBack();
                              }}>
                              取消
                            </Button>
                            {/*<Button*/}
                            {/*  loading={loadingSync}*/}
                            {/*  type='primary'*/}
                            {/*  onClick={() => {*/}
                            {/*    reqSyncPrice({*/}
                            {/*      doctorIds: data?.data?.doctorId*/}
                            {/*    }).then(() => {*/}
                            {/*      configReq().then(() => {*/}
                            {/*        onServicePermissionSynchronous();*/}
                            {/*        message.success('同步价格成功');*/}
                            {/*      });*/}
                            {/*    });*/}
                            {/*  }}>*/}
                            {/*  同步问诊价格*/}
                            {/*</Button>*/}
                            <Button
                              loading={loadingDetail || loadingSaveForService}
                              type='primary'
                              htmlType='submit'>
                              保存权限设置
                            </Button>
                          </Space>
                        </Form.Item>
                      </CardLayout>
                    </Wrapper>
                  </Form>
                </Spin>
              </TabPane>
            )}
          {id &&
            data?.data?.hisTypeList?.map(item => item.hisType).includes(2) &&
            !isLocal && (
              <TabPane tab='智慧医院信息' key='3'>
                <Spin spinning={loadingDetail || loadingSaveForService}>
                  <Form
                    name='smartForm'
                    size='large'
                    form={formSmartHis}
                    initialValues={{ ...followData?.account }}
                    scrollToFirstError={true}
                    onFinish={onSmartFinish}>
                    <Wrapper edit={false}>
                      <CardLayout title={'标签信息'}>
                        <Row gutter={[16, 0]}>
                          <Col span={8}>
                            <Form.Item name='tags'>
                              <MutiCheckBox
                                list={[
                                  {
                                    label: '名医名诊',
                                    value: 'FAMOUS'
                                  }
                                ]}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </CardLayout>
                      <CardLayout>
                        <Form.Item style={{ textAlign: 'right' }}>
                          <Space size={22}>
                            <Button
                              type='ghost'
                              onClick={() => {
                                history.goBack();
                              }}>
                              取消
                            </Button>
                            <Button
                              loading={loadingDetail || loadingSaveFlowUp}
                              type='primary'
                              htmlType='submit'>
                              保存配置
                            </Button>
                          </Space>
                        </Form.Item>
                      </CardLayout>
                    </Wrapper>
                  </Form>
                </Spin>
              </TabPane>
            )}
          {followData?.account?.id &&
            data?.data?.hisTypeList?.map(item => item.hisType).includes(4) &&
            !isLocal && (
              <TabPane tab='随访配置' key='4'>
                <Spin spinning={loadingDetail || loadingSaveFlowUp}>
                  <Form
                    name='hospitalflowUpForm'
                    size='large'
                    form={formFlowUp}
                    initialValues={{ ...followData?.account }}
                    scrollToFirstError={true}
                    onFinish={onFinishflowUp}
                    onFinishFailed={onFinishFailed}>
                    <Wrapper edit={false}>
                      <CardLayout title={' 随访配置信息'}>
                        <Row gutter={[16, 0]}>
                          <Col span={8}>
                            <Form.Item
                              label={`选择随访角色`}
                              name='followRoleId'
                              rules={[
                                {
                                  required: true,
                                  message: `请选择随访角色`
                                }
                              ]}>
                              <RoleSelect clientType={'1'} />
                            </Form.Item>
                          </Col>
                        </Row>
                      </CardLayout>
                      <CardLayout>
                        <Form.Item style={{ textAlign: 'right' }}>
                          <Space size={22}>
                            <Button
                              type='ghost'
                              onClick={() => {
                                history.goBack();
                              }}>
                              取消
                            </Button>
                            <Button
                              loading={loadingDetail || loadingSaveFlowUp}
                              type='primary'
                              htmlType='submit'>
                              保存随访设置
                            </Button>
                          </Space>
                        </Form.Item>
                      </CardLayout>
                    </Wrapper>
                  </Form>
                </Spin>
              </TabPane>
            )}
          {data?.data?.hisTypeList?.map(item => item.hisType).includes(5) &&
            !isLocal && (
              <TabPane tab='医联体平台' key='5'>
                <Form
                  name='hospitalflowUpForm'
                  size='large'
                  initialValues={{
                    cooperateRoleId: followData?.account?.cooperateRoleId
                  }}
                  scrollToFirstError={true}
                  onFinish={saveCoRole}
                  onFinishFailed={onFinishFailed}>
                  <Wrapper edit={false}>
                    <Row gutter={[16, 0]}>
                      <Col span={8}>
                        <Form.Item
                          label={`所属角色`}
                          name='cooperateRoleId'
                          rules={[
                            {
                              required: true,
                              message: `请选择所属角色`
                            }
                          ]}>
                          <RoleSelect clientType={'2'} />
                        </Form.Item>
                      </Col>
                    </Row>
                    <CardLayout>
                      <Form.Item style={{ textAlign: 'right' }}>
                        <Space size={22}>
                          <Button
                            type='ghost'
                            onClick={() => {
                              history.goBack();
                            }}>
                            取消
                          </Button>
                          <Button
                            loading={coLoading}
                            type='primary'
                            htmlType='submit'>
                            保存
                          </Button>
                        </Space>
                      </Form.Item>
                    </CardLayout>
                  </Wrapper>
                </Form>
              </TabPane>
            )}
        </Tabs>
      </CardLayout>
    </>
  );
};

const Wrapper = styled.div<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
`;

const SmallLabel = styled.div`
  width: 80px;
  position: relative;

  span {
    position: absolute;
    left: 0;
    bottom: -15px;
    font-size: 11px;
  }
`;
