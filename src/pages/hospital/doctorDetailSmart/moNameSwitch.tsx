import React, { useEffect, useMemo, useState } from 'react';
import { Switch, Col, Row, Form, Input } from 'antd';
import env from '@configs/env';

interface Iprops {
  onChange?: (value?: string) => void;
  value?: string;
}

export default (props: Iprops) => {
  const { value, onChange } = props;
  // 配置项，可以通过添加这个来动态添加权限
  const labelList = [
    '门诊加号',
    '报告解读',
    '在线处方',
    '检验检查',
    '远程会诊',
    '双向转诊',
    '护理预约'
  ];
  // 默认初始值
  const initialValue = labelList.map(label => `${label}|0`);
  const [valueList, setValueList] = useState<string[]>(
    value ? decodeURI(value).split(',') : [...initialValue, `预约挂号|0|`]
  );

  const registerLinkValue = useMemo(() => {
    const item = valueList.find(item => item.includes('预约挂号'));
    return item?.match(/预约挂号\|[0|1]\|?(.*)$/)?.[1] || '';
  }, [valueList]);

  const registerLinkEnabled = useMemo(() => {
    return !!valueList.find(item => item.includes('预约挂号|1'));
  }, [valueList]);

  function handleSwitchChange(value: boolean, label: string) {
    const index = labelList.findIndex(item => item === label);
    const res = [...valueList];
    res[index] = value ? `${labelList[index]}|1` : `${labelList[index]}|0`;
    setValueList(res);
  }

  function handleRegisterCheckChange(checked: boolean) {
    let matched = false;
    const newValueList = valueList.map(item => {
      if (item.includes('预约挂号')) {
        matched = true;
        return item.replace(/预约挂号\|[0|1]\|?(.*)$/, (_, inputStr) => {
          return `预约挂号|${checked ? '1' : '0'}|${inputStr}`;
        });
      }
      return item;
    });
    if (!matched) {
      newValueList.push(checked ? `预约挂号|1` : `预约挂号|0`);
    }
    setValueList(newValueList);
  }

  function handleRegisterInputChange(e: any) {
    const val = e.target.value;
    const newValueList = valueList.map(item => {
      if (item.includes('预约挂号')) {
        return `预约挂号|1|${val}`;
      }
      return item;
    });
    setValueList(newValueList);
  }

  //更新回显值
  useEffect(() => {
    if (value && value !== encodeURI(valueList.join(','))) {
      setValueList(decodeURI(value).split(','));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  useEffect(() => {
    if (onChange) {
      onChange(encodeURI(valueList.join(',')));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [valueList]);

  return (
    <Row justify='space-between'>
      <Col span={4}>
        <Form.Item label='预约挂号'>
          <Switch
            checked={registerLinkEnabled}
            onChange={handleRegisterCheckChange}
          />
        </Form.Item>
      </Col>
      <Col span={20}>
        <Form.Item label='链接地址'>
          <Input
            disabled={!registerLinkEnabled}
            value={registerLinkValue}
            placeholder='请输入链接地址'
            style={{ width: '100%' }}
            onChange={handleRegisterInputChange}
          />
        </Form.Item>
      </Col>
      {labelList.map(label => {
        const checked =
          valueList.find(item => item.includes(label))?.split('|')[1] === '1';
        return (
          <Col span={4} key={label}>
            <Form.Item
              label={label}
              hidden={['40009', '8900'].includes(env.hisId)}>
              <Switch
                checked={checked}
                onChange={value => handleSwitchChange(value, label)}
              />
            </Form.Item>
          </Col>
        );
      })}
    </Row>
  );
};
