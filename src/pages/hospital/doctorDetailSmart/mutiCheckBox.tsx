import React, { useState } from 'react';
import { Checkbox, Space } from 'antd';

interface List {
  label: string;
  value: string;
}

interface Props {
  value?: string[];
  onChange?: (value?: string[]) => void;
  list: List[];
}

const MutiCheckBox = (props: Props) => {
  const { value = [], onChange, list } = props;
  const [selectList, setSelectList] = useState<string[]>(value);
  const change = (value: string) => {
    let newList: string[] = [];
    if (selectList.includes(value)) {
      newList = selectList.filter(v => v !== value);
    } else {
      newList = [...selectList, value];
    }
    setSelectList(newList);
    onChange?.(newList);
  };
  return (
    <Space size='large'>
      {list.map(v => {
        return (
          <Checkbox
            checked={selectList.includes(v.value)}
            onChange={() => change(v.value)}
            key={v.value}>
            {v.label}
          </Checkbox>
        );
      })}
    </Space>
  );
};

export default MutiCheckBox;
