import { Button, message, Space } from 'antd';
import api from '@pages/hospital/campus/api';
import PlatFormSelectItem from './platformSelectItem';
import { useMemo } from 'react';
import { mapCodeToOptions, ValueType } from './utils';

export default ({
  value,
  onChange,
  disabled = false,
  targetHisId
}: {
  value?: ValueType[];
  onChange?: (v?: ValueType[]) => void;
  disabled?: boolean;
  targetHisId?: string;
}) => {
  const innerVal = useMemo(
    () =>
      value?.map(i => ({
        ...i,
        id: i.id || Math.round(Math.random() * 1000000)
      })) || [],
    [value]
  );

  const {
    data: { data: hisTypes }
  } = api.医院开通的业务平台列表({
    deepCache: true,
    initValue: { data: [] }
  });

  const hisTypeOptions = useMemo(() => mapCodeToOptions(hisTypes) || [], [
    hisTypes
  ]);

  const restOptions = useMemo(() => {
    const selectedHisType = innerVal.map(i => i.hisType).filter(Boolean);
    return hisTypeOptions.filter(i => !selectedHisType.includes(i.value));
  }, [hisTypeOptions, innerVal]);

  const handleAddItem = () => {
    if (innerVal.some(i => i.hisType === undefined)) {
      message.error('请完善当前信息再添加');
      return;
    }
    if (innerVal.length === hisTypeOptions.length) {
      message.error('配置已达上限，无法新增');
      return;
    }
    onChange?.([...innerVal, { id: Math.round(Math.random() * 1000000) }]);
  };

  const handleItemChange = val => {
    const newVal = innerVal.map(i => {
      if (i.id === val.id) {
        return val;
      }
      return i;
    });
    const hisTypeList = newVal.map(i => i.hisType).filter(Boolean);
    console.log('hisTypeList', hisTypeList);
    if ([...new Set(hisTypeList)].length !== hisTypeList.length) {
      message.error('请勿重复选择同一平台');
      return;
    }
    onChange?.(newVal);
  };

  const handleItemDelete = (id: number) => {
    onChange?.(innerVal.filter(i => i.id !== id));
  };

  return (
    <Space direction='vertical'>
      {innerVal.map(i => (
        <PlatFormSelectItem
          key={i.id}
          id={i.id}
          disabled={disabled}
          hisType={i.hisType}
          deptIds={i.deptList?.map(j => j.id) || []}
          onChange={handleItemChange}
          onDelete={handleItemDelete}
          targetHisId={targetHisId}
          hisTypeOptions={i.hisType ? hisTypeOptions : restOptions}
        />
      ))}
      {!disabled && (
        <Button type='primary' onClick={handleAddItem}>
          新增业务平台
        </Button>
      )}
    </Space>
  );
};
