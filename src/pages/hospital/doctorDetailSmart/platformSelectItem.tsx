import { Select, TreeSelect, Space } from 'antd';
import useApi from '../doctorSmart/api';
import env from '@src/configs/env';
import { useEffect, useMemo } from 'react';
import { getTreeOptions, ValueType } from './utils';
import { DeleteOutlined } from '@ant-design/icons';

export default ({
  hisTypeOptions,
  id,
  deptIds,
  onChange,
  hisType,
  onDelete,
  disabled = false,
  targetHisId
}: {
  id;
  hisType?: number;
  deptIds: number[];
  hisTypeOptions: { label: string; value: any }[];
  onChange?: (value: ValueType) => void;
  onDelete?: (id: number) => void;
  disabled?: boolean;
  targetHisId?: string;
}) => {
  const hisId = useMemo(() => env.hisId, []);
  const {
    data: {
      data: { recordList: deptList }
    },
    loading,
    request: reqDept
  } = useApi.科室管理列表({
    params: { hisId: hisId },
    initValue: {
      data: {
        recordList: []
      }
    },
    needInit: false
  });
  const {
    data: {
      data: { recordList: deptLocalList }
    },
    loading: deptLocalLoading,
    request: reqDeptLocal
  } = useApi.本地科室管理列表({
    params: { hisId: hisId },
    initValue: {
      data: {
        recordList: []
      }
    },
    needInit: false
  });
  const deptLoading = useMemo(
    () => (targetHisId ? deptLocalLoading : loading),
    [deptLocalLoading, loading, targetHisId]
  );
  const deptListHisType = useMemo(
    () => (targetHisId ? deptLocalList : deptList),
    [targetHisId, deptLocalList, deptList]
  );
  const handlePlatFormChange = (v: number) => {
    onChange?.({
      id,
      hisType: v
    });
  };

  const handleDeptChange = (val, label) => {
    onChange?.({
      id,
      hisType,
      deptList: val.map((id, index) => ({
        id,
        name: label[index]
      }))
    });
  };

  useEffect(() => {
    onChange?.({
      hisType
    });
    if (hisType !== undefined) {
      if (targetHisId) {
        reqDeptLocal({
          hisType,
          hisId,
          targetHisId
        });
      } else {
        reqDept({
          hisType
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hisType, reqDept, targetHisId, reqDeptLocal]);

  return (
    <Space>
      <Select
        value={hisType}
        style={{ width: '200px' }}
        options={hisTypeOptions}
        disabled={disabled}
        onChange={handlePlatFormChange}
        placeholder={'请选择平台'}
      />
      <TreeSelect
        value={deptIds}
        style={{ width: '400px' }}
        showSearch={true}
        treeNodeFilterProp='label'
        placeholder='请选择所属科室'
        loading={deptLoading}
        disabled={disabled}
        treeCheckable={true}
        onChange={handleDeptChange}
        treeData={deptLoading ? undefined : getTreeOptions(deptListHisType)}
        showCheckedStrategy={'SHOW_CHILD'}
      />
      {!disabled && (
        <DeleteOutlined
          style={{ fontSize: 26 }}
          onClick={() => onDelete?.(id)}
        />
      )}
    </Space>
  );
};
