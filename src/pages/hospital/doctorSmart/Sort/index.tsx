import React, { useState } from 'react';
import Sort, { ListItem } from '@components/Sort';
import { Button, Cascader, message, Select } from 'antd';
import { CardLayout } from 'parsec-admin';
import useDeptApi from '@pages/hospital/departmentSmart/api';
import useApi from '../api';
import env from '@configs/env';
import { Space } from '@kqinfo/ui';
import api from '@pages/hospital/campus/api';
import { getTreeOptions } from '@pages/hospital/doctorDetailSmart/utils';

export default () => {
  const hisId = env.hisId;
  const {
    data: { data: hisTypes }
  } = api.医院开通的业务平台列表({
    deepCache: true,
    initValue: { data: [] }
  });
  const [deptId, setDeptId] = useState<number>();
  const [aredId, setAreaId] = useState<number>();
  const {
    data: {
      data: { recordList: deptList }
    },
    loading: loadingDdepList
  } = useDeptApi.科室管理列表({
    params: { hisId, hisType: aredId },
    needInit: !!hisId && !!aredId,
    initValue: {
      data: {
        recordList: []
      }
    }
  });
  const {
    data: { data },
    loading: loadingDocList
  } = useApi.医生列表({
    params: {
      pageNum: 1,
      numPerPage: 999,
      type: '1',
      hisId,
      hisType: aredId,
      deptId,
      orderBy: 'sortNo',
      sort: 'ASC'
    },
    initValue: {
      data: {
        recordList: []
      }
    },
    needInit: !!deptId
  });

  const [list, setList] = useState<ListItem[]>([]);

  const {
    request: request医生排序,
    loading: loading医生排序
  } = useApi.医生排序({
    needInit: false
  });

  return (
    <div>
      <CardLayout title='医生排序'>
        <Space vertical size={10}>
          <Space alignItems={'center'} size={10}>
            <Select
              placeholder='请选择院区'
              value={aredId}
              onChange={setAreaId}
              options={hisTypes?.map(x => {
                return {
                  label: x.desc,
                  value: x.code
                };
              })}
              style={{
                width: '200px'
              }}
            />
            <Cascader
              allowClear={false}
              expandTrigger='hover'
              onChange={(v: any) => {
                const id = v[v.length - 1];
                setDeptId(id as number);
                setList([]);
              }}
              placeholder='请选择上级科室'
              options={getTreeOptions(deptList) as any}
            />
          </Space>
          <Space size={5}>
            <Space
              alignItems={'center'}
              justify={'center'}
              style={{
                backgroundColor: '#f3cf99',
                borderRadius: '50%',
                color: '#fff',
                width: '15px',
                height: '15px'
              }}>
              !
            </Space>
            操作说明：1、拖动院区到相应位置并保存，手机端将同步更新排序。
          </Space>
        </Space>
      </CardLayout>
      {data?.recordList && (
        <CardLayout title=''>
          <Space alignItems={'center'} size={5}>
            <Space
              alignItems={'center'}
              justify={'center'}
              style={{
                borderRadius: '50%',
                background: '#fce799',
                height: '15px',
                width: '15px',
                color: '#fff',
                fontWeight: 'bold'
              }}>
              !
            </Space>
            <Space>
              操作说明：1、点击上方科室选择框，选择对应科室进行查询；2、拖动医生到相应位置并保存，手机端将同步更新排序
            </Space>
          </Space>
          <Sort
            onChange={list => {
              setList(list);
            }}
            selected={deptId + ''}
            list={
              data?.recordList.map(item => {
                return {
                  label: item.name,
                  value: item.id + ''
                };
              }) || []
            }
          />
          <Button
            loading={loadingDdepList || loadingDocList || loading医生排序}
            type='primary'
            style={{
              marginTop: '30px'
            }}
            onClick={() => {
              if (!deptId) {
                return;
              }
              request医生排序({
                hisId,
                deptDoctorId: deptId,
                data: list.map((item, index) => {
                  return {
                    id: parseInt(item.value),
                    sortNo: index
                  };
                })
              }).then(() => {
                message.success('成功');
              });
            }}>
            保存当前排序
          </Button>
        </CardLayout>
      )}
    </div>
  );
};
