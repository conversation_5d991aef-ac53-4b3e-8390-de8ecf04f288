import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { DeptItem } from '../departmentSmart/api';
import { Docinfo, DocDetail, DeptInfo, docType } from './d';

export interface ApiResponse<D> {
  code: 0 | 200 | 999; // 999用户未登录
  msg: string | null;
  data?: D;
}

export interface ListApiRequestParams {
  pageNum?: number;
  numPerPage?: number;
}

export type ListApiResponseData<D> = ApiResponse<{
  currentPage: number;
  totalCount: number;
  recordList: D[];
}>;
export type listParams = ListApiRequestParams & {
  sort?: string;
  hisId?: string;
  deptNo?: string;
  deptId?: number | string;
  hisType?: string | number;
  refundStatus?: string;
  beginTime?: string;
  endTime?: string;
  targetHisId?: string;
  type: docType;
  orderBy?: string;
  showInquiry?: 0 | 1; //是否返回问诊配置 0不需要，1需要
  scheduleState?: 0 | 1; //排班状态 1已排班 0未排班
  scheduleDate?: string; //排班时间，默认为当天
};
interface InServiceConfiguration {
  moNames: string; //医生服务权限 多个权限用‘,’分隔，1开启权限  0关闭权限
  inquiryList: {
    id: number; //问诊配置主键ID
    doctorId: string; //医生编号
    hisId: number;
    isOnDuty: string; //在线状态 0离线 1在线
    maxInquiry: number; //每日最大问诊量
    amount: number; //原价
    type: string;
    price: number;
    remune: number; //现价
  }[]; //医生问诊配置
}
export default {
  医生列表: createApiHooks((params: listParams) =>
    request.get<ListApiResponseData<Docinfo>>(
      `/mch/his/${getDoctorTypePath(params.type)}`,
      {
        params
      }
    )
  ),
  本地医生列表: createApiHooks((params: listParams) =>
    request.get<ListApiResponseData<Docinfo>>(`/mch/his/ls-main/lsDoctorList`, {
      params
    })
  ),
  科室管理列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        hisType?: string | number; // 所属业务平台 1互联网医院、2智慧医院、3互/智
      }
    ) =>
      request
        .get<ApiResponse<DeptItem[]>>('/mch/his/deptMain', { params: data })
        .then(res => {
          return {
            ...res,
            data: {
              code: res.data.code,
              msg: res.data.msg,
              data: {
                currentPage: 1,
                totalCount: 1,
                recordList: res.data.data || []
              }
            }
          };
        })
  ),
  本地科室管理列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        targetHisId?: string;
        hisType?: string | number; // 所属业务平台 1互联网医院、2智慧医院、3互/智
      }
    ) =>
      request
        .get<ApiResponse<DeptItem[]>>('/mch/his/ls-main/deptTree', {
          params: data
        })
        .then(res => {
          return {
            ...res,
            data: {
              code: res.data.code,
              msg: res.data.msg,
              data: {
                currentPage: 1,
                totalCount: 1,
                recordList: res.data.data || []
              }
            }
          };
        })
  ),
  科室列表: createApiHooks((params: { hisId: string }) => {
    return request.post<ApiResponse<Array<DeptInfo>>>(
      '/mch/his/doctor/topDepts',
      params
    );
  }),
  医生详情: createApiHooks(
    (params: { id: string; hisId: string; deptDoctorId: string }) => {
      return request.get<DocDetail>(
        `/mch/his/doctorMain/detailByIdAndDeptDoctorId`,
        { params }
      );
    }
  ),
  详情主键ID: createApiHooks((id: string) => {
    return request.get<DocDetail>(`/mch/his/doctorMain/${id}`);
  }),
  本地医生详情主键ID: createApiHooks(
    (params: { doctorMainId: string; targetHisId: string; hisId: string }) => {
      return request.get<DocDetail>(`/mch/his/ls-main/lsDoctorDetail`, {
        params
      });
    }
  ),
  获取服务配置信息: createApiHooks((params: { id: string }) => {
    return request.get<ApiResponse<InServiceConfiguration>>(
      `/mch/his/doctorMain/serviceList`,
      {
        params
      }
    );
  }),
  本地医生服务配置列表: createApiHooks(
    (params: { id: string; targetHisId: string; hisId: string }) => {
      return request.get<ApiResponse<InServiceConfiguration>>(
        `/mch/his/ls-main/lsServiceList`,
        {
          params
        }
      );
    }
  ),
  医生新增: createApiHooks((params: Docinfo) => {
    return request.post<ApiResponse<null>>(
      `/mch/his/${getDoctorTypePath(params.type)}`,
      params,
      {
        headers: {
          Accept: 'application/json, text/javascript, */*; q=0.01',
          'Content-Type': 'application/json; charset=UTF-8'
        }
      }
    );
  }),
  随访设置更新: createApiHooks(
    (params: { hisId: string; id?: string; followRoleId: string }) => {
      return request.put(`/mch/user/doctorAccount/follow-role`, params);
    }
  ),
  更新医生医联体角色: createApiHooks(
    (params: {
      hisId: string;
      id?: string;
      cooperateRoleId: string;
      doctorIds: string;
    }) => {
      return request.put(`/mch/user/doctorAccount/cooperate-role`, params, {
        params: {
          doctorIds: params.doctorIds
        }
      });
    }
  ),
  查询医生账号信息: createApiHooks((params: { id: string }) => {
    return request.get<{
      data: { account: { id: number; cooperateRoleId: number } };
    }>(`/mch/his/doctorMain/accountDetail`, {
      params
    });
  }),
  同步问诊价格: createApiHooks((params: { doctorIds: string }) => {
    return request.get('/mch/his/doctorMain/syncRegistrationFee', { params });
  }),
  服务设置更新: createApiHooks(
    (params: {
      hisId: string;
      id?: string;
      inquirys: string;
      hisTypeList: any[];
      moNames: string;
    }) => {
      return request.put(`/mch/his/doctorMain/serviceModify`, params);
    }
  ),
  医生更新: createApiHooks(
    (
      params: {
        [N in keyof Docinfo]?: Docinfo[N];
      }
    ) => {
      return request.put<ApiResponse<null>>(
        `/mch/his/${getDoctorTypePath(params.type)}`,
        params
      );
    }
  ),
  医生启用停用: createApiHooks(
    (params: { id: number; doctorHisType: number }) => {
      return request.put('/mch/his/doctorMain/status', null, {
        params
      });
    }
  ),
  医生删除: createApiHooks((params: { id: number }) => {
    return request.delete(`/mch/his/doctorMain/${params.id}`);
  }),
  重置密码: createApiHooks(
    (params: { account: string; id: number; hisId: string }) => {
      return request.post('/mch/user/doctorAccount/resetpassword', params, {
        headers: {
          Accept: 'application/json, text/javascript, */*; q=0.01',
          'Content-Type': 'application/json; charset=UTF-8'
        }
      });
    }
  ),
  批量更新: createApiHooks(
    (params: { ids: string; operType: 'del' | 'valid' | 'invalid' }) => {
      return request.post('/mch/his/doctor/updateBatch', params);
    }
  ),
  批量启用停用: createApiHooks(
    (params: {
      type?: string | number;
      ids: (string | number)[];
      status: 0 | 1; // 0停用 1启用
    }) => {
      return request.put(
        `/mch/his/${getDoctorTypePath(params.type)}/batchStatus`,
        params,
        {
          headers: {
            Accept: 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/json; charset=UTF-8'
          }
        }
      );
    }
  ),
  本地医生启用停用: createApiHooks(
    (data: {
      targetHisId: '@natural'; //其他机构HisId
      ids: string[];
      status: number; //监管状态
      hisId: number;
    }) => {
      return request.post('/mch/his/ls-main/clsDoctorStatus', data, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  ),
  批量删除: createApiHooks(
    (params: { ids: (string | number)[]; type?: string | number }) => {
      return request.put(
        `/mch/his/${getDoctorTypePath(params.type)}/batchDeleted`,
        params,
        {
          headers: {
            Accept: 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/json; charset=UTF-8'
          }
        }
      );
    }
  ),
  批量导入: createApiHooks((params: { file: File; type: docType }) => {
    return request.post('/mch/his/doctor/import', params);
  }),
  同步: createApiHooks((params: { hisId?: string }) => {
    return request.get('/mch/his/doctorMain/sync', { params });
  }),
  获取智慧医院医生同步状态: createApiHooks(() => {
    return request.get<{ data: number }>('/mch/his/doctorMain/syncState');
  }),
  医生排序: createApiHooks(
    ({
      hisId,
      deptDoctorId,
      data
    }: {
      hisId?: string;
      deptDoctorId: number;
      data: { id: number; sortNo: number }[];
    }) => {
      return request.put('/mch/his/doctorMain/sort', data, {
        params: { hisId, deptDoctorId }
      });
    }
  ),
  获取互联网医院业务平台信息: createApiHooks((params: { id: number }) => {
    return request.get<{ data: { qrUrl: string } }>(
      '/mch/his/doctorMain/internetDetail',
      {
        params
      }
    );
  }),
  医生导入模板下载: createApiHooks(() =>
    request.get<Blob>('/mch/his/doctorMain/import/template', {
      responseType: 'blob'
    })
  ),
  医生导入: createApiHooks((params: { file: File; type?: string | number }) =>
    request.post(`/mch/his/${getDoctorTypePath(params.type)}/import`, params)
  ),
  智慧医院更新: createApiHooks(
    (params: { id: number | string; tags?: string[] }) =>
      request.put(`/mch/his/doctorMain/serviceWisdomModify`, params)
  ),
  // todo 增加后端接口导出
  导出Excel: createApiHooks((params: any) =>
    request.get<Blob>(`/mch/his/doctorMain/export-supervision`, {
      params,
      responseType: 'blob'
    })
  )
};

function getDoctorTypePath(type?: string | number) {
  if (!type) {
    return 'doctorMain';
  }
  if (['2', 2].includes(type)) {
    return 'nurse';
  }
  if (['4', 4].includes(type)) {
    return 'pharmacist';
  }
  return 'doctorMain';
}
