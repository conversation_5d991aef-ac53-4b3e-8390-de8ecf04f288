import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  LinkButton,
  ArrSelect,
  ActionsWrap,
  actionConfirm,
  handleSubmit,
  useReloadTableList
} from 'parsec-admin';
import useApi, { listParams } from './api';
import MyTableList from '@components/myTableList';
import { Button, Modal, Popover, Cascader, Form, Switch, message } from 'antd';
import {
  DeleteOutlined,
  PlusOutlined,
  DownloadOutlined,
  UploadOutlined,
  ImportOutlined
} from '@ant-design/icons';
import { useHistory, useLocation } from 'react-router';
import { Docinfo, doctorlevels, druglevels, nurselevels, docType } from './d';
import permisstion from '@utils/permisstion';
// import { getHisTypes } from '../departmentSmart';
// import { initInquirys } from '../doctorDetailSmart';
import env from '@configs/env';
import moment from 'moment';
import api from '@pages/hospital/campus/api';
import { Space, Tag, TransferChange } from '@kqinfo/ui';
import { saveAs } from 'file-saver';
import { getTreeOptions } from '@pages/hospital/doctorDetailSmart/utils';
import {
  clearTimeInterval,
  setTimeInterval
} from 'parsec-hooks/lib/downCountHooks';
import qs from 'qs';
import Organization from '@components/organization';

enum HisType {
  '互联网' = 1,
  '智慧' = 2,
  '随访' = 4,
  '医联体'
}
export default ({ isLocal = false }: { isLocal?: boolean }) => {
  const hisId = env.hisId;
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );
  const searchRef = useRef<any>();
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const history = useHistory();
  const [isShowImport, setIsShowImoort] = useState(false);
  // const { loading: loadingImport, request: requestImport } = useApi.批量导入({
  //   needInit: false
  // });
  const { loading: importLoad, request: importReq } = useApi.医生导入({
    needInit: false
  });
  const {
    data: { data: depList }
  } = useApi.科室管理列表({
    initValue: { data: { recordList: [] } }
  });
  // const depSelectList = useMemo(() => {
  //   return depList?.recordList?.map(item => ({
  //     value: item.id,
  //     children: item.name
  //   }));
  // }, [depList]);
  console.log(depList, 'depList');
  // const depSelectList = useMemo(() => {
  //   return depList?.recordList?.map(x => {
  //     return {
  //       label: x.name,
  //       value: x.id,
  //       children: x.children.map(x => {
  //         return {
  //           label: x.name,
  //           value: x.id,
  //           children: x?.children.map(x => {
  //             return {
  //               label: x.name,
  //               value: x.id,
  //               children: x?.children.map(x => {
  //                 return {
  //                   label: x.name,
  //                   value: x.id,
  //                   children: x?.children.map(x => {
  //                     return {
  //                       label: x.name,
  //                       value: x.id,
  //                       children: []
  //                     };
  //                   })
  //                 };
  //               })
  //             };
  //           })
  //         };
  //       })
  //     };
  //   });
  // }, [depList?.recordList]);
  const {
    data: { data: hisTypes }
  } = api.医院开通的业务平台列表({
    deepCache: true,
    initValue: { data: [] }
  });
  const locat = useLocation();
  // 1医生 2护士 3职员 4药师
  const type: docType = useMemo(() => {
    if (locat.pathname.includes('/doctor')) {
      return '1';
    }
    if (locat.pathname.includes('/nurse')) {
      return '2';
    }
    if (locat.pathname.includes('/drug')) {
      return '4';
    }
    return '1';
  }, [locat]);

  const name = useMemo(() => {
    if (type === '1') {
      return '医生';
    }
    if (type === '2') {
      return '护士';
    }
    if (type === '4') {
      return '药师';
    }
    return '医生';
  }, [type]);
  const levels = useMemo(() => {
    if (type === '1') {
      return doctorlevels;
    }
    if (type === '2') {
      return nurselevels;
    }
    if (type === '4') {
      return druglevels;
    }
    return doctorlevels;
  }, [type]);

  const [loading, setLoading] = useState(false);
  const reloadTableList = useReloadTableList();
  const beginReq = () => {
    return new Promise((resolve, reject) => {
      const time = setTimeInterval(() => {
        useApi.获取智慧医院医生同步状态.request().then(r => {
          if (r?.data === 0) {
            setLoading(false);
            reloadTableList();
            clearTimeInterval(time);
            resolve(true);
          }
        });
      }, 1000);
    });
  };
  const { hisType: defaultHisType } = qs.parse(
    window.location.href.split('?')[1]
  ) as {
    hisType?: number;
  };
  const form = Form.useForm()[0];
  useEffect(() => {
    if (defaultHisType) {
      form.setFieldsValue({ hisType: 1 });
    }
  }, [defaultHisType, form]);
  const [targetHisId, setTargetHisId] = useState<any>(env.hisId);
  return (
    <>
      <MyTableList
        exportExcelButton
        form={form}
        params={{ targetHisId }}
        pageHeaderProps={false}
        showHeaderExtra={isLocal}
        action={
          isLocal ? (
            <Organization
              value={targetHisId}
              headerExtraText={name}
              onChange={setTargetHisId}
            />
          ) : (
            <>
              <ActionsWrap max={4}>
                <Button
                  onClick={async () => {
                    // todo exportExcelButton 之前采用的是 标准版前端导出列表 现在改为后端接口导出
                    try {
                      // 获取当前日期和时间，并格式化为字符串
                      const formattedDate = moment().format('YYYYMMDD_HHmmss');
                      // 拼接文件名
                      const fileName = `医生信息导出_${formattedDate}.xlsx`;

                      // 发起请求并保存文件
                      const res = await useApi.导出Excel.request();
                      saveAs(res, fileName);
                    } catch (error) {
                      message.error('er' + error);
                    }
                  }}>
                  导出医生信息
                </Button>
                {type === '1' && permisstion.canSortDoctor && (
                  <Button
                    type={'default'}
                    onClick={() => {
                      history.push('/doctorSmart/Sort');
                    }}>
                    医生排序
                  </Button>
                )}
                {type === '1' && permisstion.canSyncDoctor && (
                  <Button
                    loading={loading}
                    type={'default'}
                    onClick={() => {
                      Modal.confirm({
                        title: '提示',
                        content: '是否确认同步',
                        onOk: () => {
                          useApi.同步.request({ hisId }).then(() => {
                            setLoading(true);
                            beginReq();
                          });
                        }
                      });
                    }}>
                    {loading ? '同步中...' : '同步医生'}
                  </Button>
                )}
                {/*<Button*/}
                {/*  type={'default'}*/}
                {/*  onClick={() => {*/}
                {/*    useApi.医生导入模板下载.request().then(r => {*/}
                {/*      saveAs(r, '医生导入模板.xlsx');*/}
                {/*    });*/}
                {/*  }}>*/}
                {/*  下载导入模板*/}
                {/*</Button>*/}
                {/*<Button*/}
                {/*  icon={<UploadOutlined />}*/}
                {/*  loading={importLoad}*/}
                {/*  onClick={() => {*/}
                {/*    const el = document.createElement('input');*/}
                {/*    el.setAttribute('type', 'file');*/}
                {/*    el.addEventListener('change', () => {*/}
                {/*      const file = el.files?.[0];*/}
                {/*      if (file) {*/}
                {/*        handleSubmit(() => importReq({ file }), '导入');*/}
                {/*      }*/}
                {/*    });*/}
                {/*    el.click();*/}
                {/*    el.remove();*/}
                {/*  }}>*/}
                {/*  批量导入 */}
                {/*</Button>*/}
                {permisstion.canAddDoctor(type) && (
                  <Button
                    type={'default'}
                    icon={<PlusOutlined />}
                    onClick={() => {
                      if (type === '1') {
                        history.push('/doctorSmart/new');
                      }
                      if (type === '2') {
                        history.push('/nurse/new');
                      }
                      if (type === '4') {
                        history.push('/drug/new');
                      }
                    }}>
                    {`添加${name}`}
                  </Button>
                )}
                {permisstion.canImportDoctor(type) && (
                  <Button
                    type={'default'}
                    icon={<ImportOutlined />}
                    onClick={() => {
                      setIsShowImoort(true);
                    }}>
                    批量导入
                  </Button>
                )}
              </ActionsWrap>
            </>
          )
        }
        paginationExtra={
          <>
            {!!selectedRowKeys?.length && (
              <div>
                {permisstion.canDoctorStatus(type) && (
                  <Button
                    icon={<DeleteOutlined />}
                    type={'primary'}
                    onClick={() => {
                      const ids = selectedRowKeys.map(
                        x => (x + '').split(',')[0]
                      );

                      actionConfirm(
                        () =>
                          isLocal
                            ? useApi.本地医生启用停用.request({
                                ids,
                                status: 0,
                                targetHisId: selectedRows?.[0]?.targetHisId,
                                hisId: selectedRows?.[0]?.hisId
                              })
                            : useApi.批量启用停用.request({
                                ids,
                                status: 1
                              }),
                        '批量启用'
                      );
                    }}>
                    批量启用
                  </Button>
                )}
                <span> </span>
                {permisstion.canDoctorStatus(type) && (
                  <Button
                    icon={<DeleteOutlined />}
                    type={'primary'}
                    onClick={() => {
                      const ids = selectedRowKeys.map(
                        x => (x + '').split(',')[0]
                      );

                      actionConfirm(
                        () =>
                          isLocal
                            ? useApi.本地医生启用停用.request({
                                ids,
                                status: 0,
                                targetHisId: selectedRows?.[0]?.targetHisId,
                                hisId: selectedRows?.[0]?.hisId
                              })
                            : useApi.批量启用停用.request({
                                ids,
                                status: 0
                              }),
                        '批量停用'
                      );
                    }}>
                    批量停用
                  </Button>
                )}
                <span> </span>
                {permisstion.canDelDoctor(type) && !isLocal && (
                  <Button
                    icon={<DeleteOutlined />}
                    type={'primary'}
                    onClick={() => {
                      const ids = selectedRowKeys?.map(
                        x => (x + '').split(',')[0]
                      );
                      actionConfirm(
                        () =>
                          useApi.批量删除.request({
                            ids,
                            type
                          }),
                        '批量删除'
                      );
                    }}>
                    批量删除
                  </Button>
                )}
                <span> 已选择{selectedRowKeys?.length}条</span>
              </div>
            )}
          </>
        }
        getList={({ params }: { params: listParams }) => {
          searchRef.current = { ...params };
          const p: any = {
            ...params,
            ...form.getFieldsValue(), //setFieldValue是异步的如果刚进页面更改了form不使用getFieldsValue，params拿不到最新的数据
            type
          };
          return isLocal
            ? useApi.本地医生列表.request(p)
            : useApi.医生列表.request(p);
        }}
        tableTitle={`${name}列表`}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRowKeys(selectedRowKeys);
            setSelectedRows(selectedRows);
          }
        }}
        rowKey={item => {
          return item.id + ',' + item.deptDoctorId;
        }}
        columns={useMemo(
          () =>
            [
              {
                title: '所属业务平台',
                render: false,
                searchIndex: 'hisType',
                search: !isLocal && (
                  <ArrSelect
                    options={(hisTypes || []).map(x => ({
                      value: x.code,
                      children: x.desc
                    }))}
                    placeholder={'请选择业务平台'}
                  />
                )
              },
              {
                title: '科室',
                searchIndex: 'deptId',
                search: !isLocal && (
                  <TransferChange
                    mode={'cascade'}
                    data={getTreeOptions(depList?.recordList) as any}>
                    <Cascader
                      expandTrigger='hover'
                      placeholder='请选择上级科室'
                      options={getTreeOptions(depList?.recordList) as any}
                    />
                  </TransferChange>
                ),
                render: false
              },
              {
                title: `${name}姓名`,
                dataIndex: 'name',
                width: 160,
                search: true
              },
              {
                title: `${name}编号`,
                dataIndex: 'doctorId',
                width: 100
              },
              {
                title: `${name}职称`,
                dataIndex: 'level',
                width: 120,
                search: (
                  <ArrSelect
                    options={(levels || []).map(level => {
                      return {
                        value: level.label,
                        children: level.label
                      };
                    })}
                  />
                )
                // render: text => {
                //   return levels.find(x => x.value === text)?.label || '';
                // }
              },
              // {
              //   title: '所属业务平台',
              //   dataIndex: 'doctorHisType',
              //   width: 180,
              //   search: <ArrSelect options={getHisTypes('3')} />,
              //   searchIndex: 'hisType',
              //   render: (v, r: any) => {
              //     return getHisTypes('3')[v];
              //   }
              // },
              {
                title: '科室',
                dataIndex: 'deptName',
                width: 180,
                // searchIndex: 'deptId',
                excelRender: (v, r: Docinfo) => {
                  let arr: any[] = [];
                  for (let i = 0; i < r.hisTypeList.length; i++) {
                    arr = [
                      ...arr,
                      ...r.hisTypeList[i].deptList.map(item => item.name)
                    ];
                  }
                  return arr.join(',');
                },
                // search: (
                //   <TransferChange mode={'cascade'} data={depSelectList}>
                //     <Cascader
                //       expandTrigger='hover'
                //       placeholder='请选择上级科室'
                //       options={depSelectList}
                //     />
                //   </TransferChange>
                // ),
                render: (v, r: Docinfo) => {
                  let arr: any[] = [];
                  for (let i = 0; i < r.hisTypeList.length; i++) {
                    arr = [
                      ...arr,
                      ...r.hisTypeList[i].deptList.map(item => ({
                        ...item,
                        hisType: r.hisTypeList[i].hisType
                      }))
                    ];
                  }
                  let arr2: any[] = [];
                  if (arr.length > 2) {
                    arr2 = arr.slice(2);
                    arr = arr.slice(0, 2);
                  }
                  return (
                    <Space vertical size={5}>
                      {arr.map(item => (
                        <Space alignItems={'center'} key={item.id} size={5}>
                          <Space style={{ width: '50%' }}>{item.name}</Space>
                          <Tag
                            style={{
                              width: '90px',
                              height: '16px',
                              fontSize: '12px'
                            }}
                            ghost
                            block
                            color={
                              item.hisType === 1
                                ? '#108EE9'
                                : item.hisType === 2
                                ? '#F59A23'
                                : item.hisType === 4
                                ? '#D95E38'
                                : '#389e0d'
                            }>
                            {
                              hisTypes?.find(
                                innerItem => innerItem?.code === item.hisType
                              )?.desc
                            }
                          </Tag>
                        </Space>
                      ))}
                      {arr2.length > 0 && (
                        <Popover
                          content={() =>
                            arr2.map(item => (
                              <Space
                                alignItems={'center'}
                                key={item.id}
                                size={5}>
                                <Space>{item.name}</Space>
                                <Tag
                                  style={{
                                    width: '60px',
                                    height: '16px',
                                    fontSize: '12px'
                                  }}
                                  ghost
                                  block
                                  color={
                                    item.hisType === 1
                                      ? '#108EE9'
                                      : item.hisType === 2
                                      ? '#F59A23'
                                      : '#D95E38'
                                  }>
                                  {HisType[item.hisType] || ''}
                                </Tag>
                              </Space>
                            ))
                          }
                          trigger='hover'>
                          <Button>...</Button>
                        </Popover>
                      )}
                    </Space>
                  );
                }
              },
              // {
              //   title: '互联网医院展示排序',
              //   dataIndex: 'internetSortNo',
              //   width: 160
              // },
              // {
              //   title: '互联网问诊',
              //   dataIndex: 'pictureInquiry',
              //   width: 180,
              //   render: (text, record: Docinfo) => {
              //     console.log(record, 'record');
              //     const items = record.inquirys
              //       .filter(x => x.isOnDuty === '1')
              //       .map(x => initInquirys.find(y => x.type === y.type)?.name)
              //       .filter(x => !!x);
              //     return items.length !== 0 ? items.join(',') : '未开通';
              //   }
              // },
              // {
              //   title: '视频号数',
              //   dataIndex: 'sumResourceNumFromNowOn',
              //   width: 100,
              //   render: (text, record: Docinfo) => {
              //     return (
              //       (record.inquirys || []).find(x => x.type === '3')
              //         ?.sumResourceNumFromNowOn || 0
              //     );
              //   }
              // },
              // {
              //   title: '问诊方式',
              //   dataIndex: 'typeFilter',
              //   width: 100,
              //   render: false,
              //   search: (
              //     <ArrSelect
              //       options={[
              //         {
              //           value: '1',
              //           children: '图文问诊'
              //         },
              //         {
              //           value: '2',
              //           children: '电话问诊'
              //         },
              //         {
              //           value: '3',
              //           children: '视频问诊'
              //         }
              //       ]}
              //     />
              //   )
              // },
              // {
              //   title: '首页推荐',
              //   dataIndex: 'recommend',
              //   width: 100,
              //   search: (
              //     <ArrSelect
              //       options={[
              //         {
              //           value: '0',
              //           children: '关闭'
              //         },
              //         {
              //           value: '1',
              //           children: '开启'
              //         }
              //       ]}
              //     />
              //   ),
              //   render: text => {
              //     return text === '1' || text === 1 ? '开启' : '关闭';
              //   }
              // },
              {
                title: '医院名称',
                dataIndex: 'hisName',
                width: 120,
                hidden: !isLocal
              },
              {
                title: '所属机构',
                dataIndex: 'institutionName',
                width: 120,
                hidden: !isLocal
              },
              {
                title: '创建时间',
                dataIndex: 'createTime',
                width: 150
              },
              // {
              //   title: '状态',
              //   dataIndex: 'internetStatus/wisdomStatus',
              //   width: 100,
              //   searchIndex: 'status',
              //   search: (
              //     <ArrSelect
              //       options={{
              //         '0': '停用',
              //         '1': '启用'
              //       }}
              //     />
              //   ),
              //   render: (text, r: any) => {
              //     return (r.doctorHisType === 1
              //       ? r.internetStatus
              //       : r.wisdomStatus) === 0
              //       ? '停用'
              //       : '启用';
              //   }
              // },
              {
                title: '状态',
                dataIndex: 'status',
                width: 100,
                searchIndex: 'status',
                search: (
                  <ArrSelect
                    options={[
                      { code: '0', name: '停用' },
                      { code: '1', name: '启用' }
                    ].map(x => ({
                      value: x.code,
                      children: x.name
                    }))}
                    placeholder={'请选择业务平台'}
                  />
                ),
                excelRender: v => (v === 0 ? '停用' : '启用'),
                render: (text, r: any) => {
                  return isLocal &&
                    Number(targetHisId) !== Number(env.hisId) ? (
                    <Switch
                      checkedChildren='开启'
                      unCheckedChildren='关闭'
                      checked={text}
                      onChange={checked => {
                        handleSubmit(() => {
                          return useApi.本地医生启用停用.request({
                            ids: [r?.id || ''],
                            status: checked ? 1 : 0,
                            targetHisId: r.targetHisId,
                            hisId: r?.hisId
                          });
                        }, `${checked ? '开启' : '关闭'}`);
                      }}
                    />
                  ) : text === 0 ? (
                    '停用'
                  ) : (
                    '启用'
                  );
                }
              },
              // {
              //   title: '监管上报状态',
              //   dataIndex: 'statusxxx',
              //   width: 140,
              //   search: (
              //     <ArrSelect
              //       options={{
              //         '1': '启用',
              //         '2': '停用'
              //       }}
              //     />
              //   )
              //   // render: text => {
              //   //   return text === '1' ? '启用' : '停用';
              //   // }
              // },
              {
                title: '操作',
                fixed: 'right',
                width: isLocal ? 100 : 300,
                render: (record: Docinfo) => {
                  const status = record.status;
                  const { hisTypeList } = record;
                  let isb = false;
                  if (hisTypeList?.length) {
                    isb = hisTypeList.some(item => item.hisType === 1);
                  }
                  return (
                    <>
                      {isLocal ? (
                        <LinkButton
                          onClick={() => {
                            const params = `/${record.id}/${record.hisId}/${record?.targetHisId}`;
                            if (type === '1') {
                              history.push(
                                `/localAdmin/doctorSmart/edit` + params
                              );
                            }
                            if (type === '2') {
                              history.push(`/localAdmin/nurse/edit` + params);
                            }
                            if (type === '4') {
                              history.push(`/localAdmin/drug/edit` + params);
                            }
                          }}>
                          查看
                        </LinkButton>
                      ) : (
                        <ActionsWrap max={999}>
                          {permisstion.canDoctorStatus(type) && (
                            <LinkButton
                              onClick={() => {
                                actionConfirm(
                                  () =>
                                    useApi.批量启用停用.request({
                                      type,
                                      ids: [record.id],
                                      status: status === 0 ? 1 : 0
                                    }),
                                  status === 0 ? '启用' : '停用'
                                );
                              }}>
                              {status === 0 ? '启用' : '停用'}
                            </LinkButton>
                          )}
                          {permisstion.canUpdateDoctor(type) && (
                            <LinkButton
                              onClick={() => {
                                if (type === '1') {
                                  history.push(
                                    `/doctorSmart/edit/${record.id}`
                                  );
                                }
                                if (type === '2') {
                                  history.push(`/nurse/edit/${record.id}`);
                                }
                                if (type === '4') {
                                  history.push(`/drug/edit/${record.id}`);
                                }
                              }}>
                              编辑
                            </LinkButton>
                          )}
                          {isb ? (
                            <LinkButton
                              onClick={() => {
                                useApi.获取互联网医院业务平台信息
                                  .request({
                                    id: record.id
                                  })
                                  .then(r => {
                                    if (r?.data?.qrUrl)
                                      window.open(r?.data?.qrUrl);
                                  });
                              }}>
                              二维码
                            </LinkButton>
                          ) : null}
                          {/* {permisstion.canResetDoctor && ( <LinkButtononClick={() => { actionConfirm( () => useApi.重置密码.request({ account: record.doctorId,  id: record.id, hisId   }), '重置密码' ); }}> 重置密码</LinkButtononClick=>)} */}
                          {permisstion.canDelDoctor(type) && (
                            <LinkButton
                              onClick={() => {
                                actionConfirm(
                                  () =>
                                    useApi.批量删除.request({
                                      ids: [record.id]
                                    }),
                                  '删除'
                                );
                              }}>
                              删除
                            </LinkButton>
                          )}
                        </ActionsWrap>
                      )}
                    </>
                  );
                }
              }
            ].filter(item => !item?.hidden) as any[],
          [
            hisTypes,
            depList?.recordList,
            name,
            levels,
            isLocal,
            targetHisId,
            type,
            history
          ]
        )}
      />
      <Modal
        title={`批量导入${name}`}
        visible={isShowImport}
        maskClosable={false}
        footer={null}
        onCancel={() => {
          setIsShowImoort(false);
        }}>
        <div
          className='files-modal'
          style={{
            textAlign: 'center'
          }}>
          <Button
            icon={<DownloadOutlined />}
            onClick={() => {
              useApi.医生导入模板下载.request().then(r => {
                saveAs(r, '医生导入模板.xlsx');
              });
            }}>
            下载导入模板
          </Button>
          <br />
          <br />
          <Button
            icon={<UploadOutlined />}
            loading={importLoad}
            onClick={() => {
              const el = document.createElement('input');
              el.setAttribute('type', 'file');
              el.addEventListener('change', () => {
                const file = el.files?.[0];
                if (file) {
                  handleSubmit(() => importReq({ file, type }), '导入');
                }
              });
              el.click();
              el.remove();
            }}>
            上传导入文件
          </Button>
        </div>
      </Modal>
    </>
  );
};
