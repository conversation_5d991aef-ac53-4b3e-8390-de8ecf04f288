// 用于区分编辑的是哪种医生
export type docType = '1' | '2' | '3' | '4'; // 1医生 2护士 3职员 4药师
export const docTypes = [
  { value: '1', label: '医生' },
  { value: '2', label: '护士' },
  // { value: '3', label: '职员' },
  { value: '4', label: '药师' }
];
export const doctorlevels = [
  { value: '1', label: '主任医师' },
  { value: '2', label: '副主任医师' },
  { value: '3', label: '主治医师' },
  { value: '4', label: '医师' }
  // { value: '5', label: '医士' }
];
export const druglevels = [
  { value: '1', label: '主任药师' },
  { value: '2', label: '副主任药师' },
  { value: '3', label: '主管药师' },
  { value: '4', label: '药师' }
  // { value: '5', label: '药士' }
];
export const nurselevels = [
  { value: '1', label: '主任护师' },
  { value: '2', label: '副主任护师' },
  { value: '3', label: '主管护师' },
  { value: '4', label: '护师' },
  { value: '5', label: '护士' }
];
export const categories = [
  { value: '1', label: '临床' },
  { value: '2', label: '中医' },
  { value: '3', label: '口腔' },
  { value: '4', label: '公共卫生' }
];
export const prescriptionQualifications = [
  { value: '99', label: '无' },
  { value: '0', label: '具备所有权限' },
  { value: '1', label: '抗菌药物处方资格' },
  { value: '2', label: '麻精处方资格' }
];

export interface Docinfo {
  hisTypeList: {
    hisType: number;
    deptList: { id: number; name: string; no: string }[];
  }[];
  id: number;
  targetHisId: number;
  doctorHisType: number;
  internetStatus: number; // 互联网医院状态
  wisdomStatus: number; // 智慧医院状态
  hisId: number;
  hisName: 'null';
  doctorId: '222111167';
  deptId: '003';
  deptName: '内二科一部1';
  name: '测算';
  sex: null;
  level: '1';
  grade: null;
  image: string;
  specialty: '*****************************************************';
  introduction: '啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊111111111111';
  pdeptName: null;
  pdeptId: '003';
  sortNo: 1;
  status: number;
  workingLife: 1;
  mobile: '15489512545';
  createTime: '2020-04-17 14:15:38';
  updateTime: '2020-04-17 14:34:26';
  extFields: null;
  hisDoctorName: null;
  qrTicket: null;
  qrContent: null;
  qrUrl?: string;
  circleImage: null;
  type: '1';
  deptmentId: null;
  deptmentName: '';
  consultationAuthority: '0';
  recommend: '0';
  educationTitle: null;
  consultCount: null;
  doctorInfoId: null;
  deptDoctorId: string;
  practiceScope: null;
  category: null;
  practiceLevel: null;
  practiceNumber: null;
  position: null;
  idNumber: null;
  auditTime: null;
  practiceMedicalInstitution: null;
  title: null;
  prescriptionQualification: null;
  policyNumber: null;
  underwritingUnit: null;
  insuranceStartDate: null;
  insuranceEndDate: null;
  insuranceDueDate: null;
  remark: null;
  startValidDate: null;
  endValidDate: null;
  doctorInfoVo: null;
  reviewAdmin: null;
  reviewDoctor: null;
  scheduleState: 0 | 1;
  inquirys: Array<{
    amount: 100;
    createTime: '2019-09-04 21:34:27';
    deptId: '2018008';
    deptName: '皮ac';
    doctorId: '900';
    doctorName: '潘龙测试';
    hisDoctorId: '2218-900';
    hisId: 2218;
    hisName: '重庆医科大学附属儿童医院';
    id: 1567604067490;
    isFull: null;
    isOnDuty: string; // 0 1
    maxInquiry: 50;
    price: 100;
    remune: 200;
    type: string; // 1 2 3
    updateTime: '2019-09-04 21:34:27';
    sumResourceNumFromNowOn: number;
  }>;
  inquiryConfigParamList: null;
  ableType: null;
  score: 0;
  favoriteRate: null;
  serviceTimes: 0;
  completed: 0;
  favourTimes: 0;
  evaluated: 0;
  replyTime: null;
  timelyReply: '0%';
  evaluationLabel: null;
  replyLabel: null;
  onDuty: '1';
  isFull: '0';
  fans: 0;
  signatureImg: null;
}

export interface DocDetail {
  code: 0;
  msg: '条件查询医生详情成功';
  data: {
    circleImage: string;
    deptId: '101';
    internetDeptNo: string; // "99091,218,29"
    wisdomDeptNo: string; // "99091,218,29"
    deptImage: null;
    deptName: '内分泌科门诊';
    doctorId: '40';
    mobile: string;
    cnMedicine: 0 | 1;
    medicalInsuranceCode: string;
    recommend: '1' | '0';
    internetSortNo: number;
    doctorAccount?: any;
    hisDistrictTag: "@pick('A院区','B院区')"; //所属院区标签
    hisTypeList: {
      hisType: number;
      recommend: number | string;
      sortNo: number;
      tags?: string | null;
      deptList: {
        id: number;
        name: string;
        specialty: string;
        introduction: string;
        no: string;
      }[];
    }[];
    doctorInfo: {
      recommend: '0';
      administrativePosition: '';
      auditTime: '2020-08-01 00:00:00';
      category: null;
      certDN: null;
      certEncKey: null;
      certEndTime: null;
      certIssuer: null;
      certQrCode: null;
      certSN: null;
      certStartTime: null;
      certUserState: null;
      createTime: '2021-08-27 08:57:14';
      doctorId: '6408';
      endValidDate: '2022-08-01 00:00:00';
      hisId: 40009;
      id: 1007;
      idNumber: '****************';
      insuranceDueDate: null;
      insuranceEndDate: '2022-08-01 00:00:00';
      insuranceStartDate: '2020-08-01 00:00:00';
      name: '城南';
      policyNumber: '89283924';
      position: '主任医生';
      practiceLevel: '执业医师';
      practiceMedicalInstitution: '宽仁医院';
      practiceNumber: 'No1231231231';
      practiceScope: '全科';
      prescriptionQualification: null;
      remark: null;
      signatureImg: null;
      startValidDate: '2020-08-01 00:00:00';
      title: '主任医生';
      underwritingUnit: '人寿保险';
    };
    grade: '2';
    hisDistrict: null;
    hisDoctorId: '40';
    hisDoctorName: '唐仕芳';
    hisId: 40009;
    hisName: '重庆北部宽仁医院';
    hisType: 2;
    id: 236;
    image: string;
    inquirys: Array<{
      amount: 100;
      createTime: '2019-09-04 21:34:27';
      deptId: '2018008';
      deptName: '皮ac';
      doctorId: '900';
      doctorName: '潘龙测试';
      hisDoctorId: '2218-900';
      hisId: 2218;
      hisName: '重庆医科大学附属儿童医院';
      id: 1567604067490;
      isFull: null;
      isOnDuty: string; // 0 1
      maxInquiry: 50;
      price: 100;
      remune: 200;
      type: string; // 1 2 3
      updateTime: '2019-09-04 21:34:27';
      sumResourceNumFromNowOn: number;
    }>;
    introduction: '1';
    isDeleted: 0;
    level: '主任医师';
    moNames: null;
    name: '唐仕芳';
    qrContent: null;
    qrTicket: null;
    qrUrl: null;
    specialty: '专治疑难杂症';
    status: null;
    type: '1';
    workingLife: 1;
  };
}

export interface DeptInfo {
  id: 2;
  hisId: 2218;
  hisName: 'null';
  no: '001';
  name: '内科';
  hasChild: 0;
  pid: '-1';
  tel: null;
  sortNo: 0;
  level: 0;
  img: string;
  initials: 'N';
  status: '2';
  skill: null;
  createTime: '2018-07-09 15:01:32';
  updateTime: '2018-07-09 15:01:32';
  summary: '52';
  searchName: null;
  qrTicket: null;
  qrContent: null;
  qrUrl: null;
  qrTicketFollow: null;
  qrContentFollow: null;
  qrUrlFollow: null;
  employeeCount: null;
  medicalDepartment: null;
  menuLink: null;
  hospitalDeptNo: null;
  hospitalDeptName: null;
  standardDeptNo: '14';
  standardDeptName: '医疗美容科';
  parnetDept: null;
  qrcodeType: null;
}
