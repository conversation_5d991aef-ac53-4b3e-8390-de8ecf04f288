import React, { useCallback, useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  Cascader,
  Col,
  Form,
  Input,
  message,
  Row,
  Select,
  Space
} from 'antd';
import {
  MinusCircleOutlined,
  PlusOutlined,
  SendOutlined
} from '@ant-design/icons';
import TabPaneLayout from '@pages/hospital/info/layout';
// import BraftEditor from 'braft-editor';
import BraftEditor from '@components/braftEditor';
import 'braft-editor/dist/index.css';
import { HospitalData, hospitalLevel } from '@pages/hospital/info/apis';
import styled from 'styled-components';
import useApi from './apis';
import { usePromise } from 'parsec-hooks';
import permisstion from '@utils/permisstion';
// import { ArrSelect } from 'parsec-admin';
// import { getHisTypes } from '../departmentSmart';
import env from '@configs/env';

export default ({
  data,
  loading = false
}: {
  data: HospitalData;
  loading: boolean;
}) => {
  const [form] = Form.useForm();
  const hisId = env.hisId;
  const [values, setValues] = useState<HospitalData>({
    hospitalAddressJson: []
  });

  const { request, loading: saveLoading } = useApi.saveHospitalInfo({
    needInit: false
  });

  useEffect(() => {
    let hospitalAddressJson: string[] = [];
    try {
      hospitalAddressJson = JSON.parse(data?.hospitalAddress || '');
    } catch (error) {
      console.log(error);
    }
    console.log(hospitalAddressJson);
    form.setFieldsValue({
      ...data,
      type: ({
        INTERNET: 1,
        WISDOM: 2,
        'WISDOM,INTERNET': 3,
        'INTERNET,WISDOM': 3
      } as any)[data.type || ''],
      hospitalAddressJson: hospitalAddressJson,
      hospitalIntroduction: BraftEditor.createEditorState(
        data?.hospitalIntroduction || '<p></p>'
      )
    });
    setValues({ ...data });
  }, [data, form]);

  const layout = {
    labelCol: { span: 3, style: { minWidth: '110px' } },
    wrapperCol: { span: 21 }
  };

  const rowLayout = {
    labelCol: { span: 6, style: { minWidth: '110px' } },
    wrapperCol: { span: 18 }
  };
  const tailLayout = {
    wrapperCol: { offset: 8, span: 16 }
  };
  const formItemLayoutWithOutLabel = {
    wrapperCol: {
      xs: { span: 24, offset: 0 },
      sm: { span: 21, offset: 3 }
    }
  };
  const onFinish = useCallback(() => {
    console.log('提交表单且数据验证成功后回调事件:', values);
    form
      .validateFields()
      .then(async (values: any) => {
        if (typeof values['hospitalIntroduction'] === 'object') {
          values['hospitalIntroduction'] = values[
            'hospitalIntroduction'
          ].toHTML();
        }
        await request({
          ...data,
          ...values,
          type: ({
            1: 'INTERNET',
            2: 'WISDOM',
            3: 'INTERNET,WISDOM'
          } as any)[values.type || ''],
          hisId: hisId,
          hospitalAddress: JSON.stringify(values['hospitalAddressJson'])
        }).then(res => {
          if (res.code === 0) {
            message.success('保存成功');
          } else {
            message.error(res.msg);
          }
        });
      })
      .catch(e => {
        if (e?.errorFields) {
          message.error(e.errorFields[0].errors[0]);
        }
      });
  }, [data, form, hisId, request, values]);

  const onFinishFailed = (errorInfo: any) => {
    console.log('提交表单且数据验证失败后回调事件:', errorInfo);
  };

  const onValuesChange = (changedValues: any, values: any) => {
    console.log('字段值更新时触发回调事件:', changedValues, values);
    if (typeof values['hospitalIntroduction'] === 'object') {
      values['hospitalIntroduction'] = values['hospitalIntroduction'].toHTML();
    }
    setValues(values);
  };
  const { data: PcaCode = [] } = usePromise(
    useCallback(
      () =>
        import('china-division/dist/pca-code.json').then(
          ({ default: data }) => data
        ),
      []
    )
  );
  return (
    <TabPaneLayout
      title='医院简介'
      loading={loading}
      formChild={
        <>
          <Form
            name='hospitalInfoForm'
            size='large'
            form={form}
            // initialValues={{ ...values }}
            onValuesChange={onValuesChange}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}>
            <Form.Item
              {...layout}
              label='医院名称'
              name='hospitalName'
              rules={[
                {
                  required: true,
                  message: '请输入医院名称!'
                }
              ]}>
              <Input placeholder='请输入医院名称' />
            </Form.Item>
            {/*<Form.Item*/}
            {/*  {...layout}*/}
            {/*  label='业务平台'*/}
            {/*  name='type'*/}
            {/*  rules={[*/}
            {/*    {*/}
            {/*      required: true,*/}
            {/*      message: '请选择业务平台!'*/}
            {/*    }*/}
            {/*  ]}>*/}
            {/*  <ArrSelect*/}
            {/*    options={getHisTypes('3')}*/}
            {/*    style={{*/}
            {/*      width: '100%'*/}
            {/*    }}*/}
            {/*  />*/}
            {/*</Form.Item>*/}

            <Row>
              <Col span={12}>
                <Form.Item
                  {...rowLayout}
                  label='医院等级等次'
                  name='hospitalLevel'
                  rules={[
                    {
                      required: true,
                      message: '请选择医院等级等次!'
                    }
                  ]}>
                  <Select placeholder='请选择医院等级等次'>
                    {(hospitalLevel || []).map(
                      (
                        v: { label: string; value: string | number },
                        index: number
                      ) => (
                        <Select.Option value={v.label} key={index}>
                          {v.label}
                        </Select.Option>
                      )
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  {...rowLayout}
                  label='联系电话'
                  name='contactNumber'
                  rules={[
                    {
                      message: '请输入医院联系电话!'
                    }
                  ]}>
                  <Input placeholder='请输入医院联系电话' />
                </Form.Item>
              </Col>
            </Row>

            <Form.List name={'hospitalAddressJson'}>
              {(fields, { add, remove }) => {
                return (
                  <div>
                    {(fields || []).map((field, index) => (
                      <Form.Item
                        {...(index === 0 ? layout : formItemLayoutWithOutLabel)}
                        label={index === 0 ? '地址信息' : ''}
                        required={true}
                        key={field.key}>
                        <Row>
                          <Col span={22}>
                            <Space
                              size={19}
                              direction='vertical'
                              style={{ width: '100%' }}>
                              <Row gutter={16}>
                                <Col span={16}>
                                  <Form.Item
                                    name={[field.name, 'desc']}
                                    rules={[
                                      {
                                        required: true,
                                        message: '请输入地址信息.'
                                      }
                                    ]}
                                    noStyle>
                                    <Input placeholder='请输入医院名称（如：渝北本部院区）' />
                                  </Form.Item>
                                </Col>
                                <Col span={8}>
                                  <Form.Item
                                    name={[field.name, 'area']}
                                    rules={[
                                      {
                                        required: true,
                                        message: '请选择所在地区.'
                                      }
                                    ]}
                                    noStyle>
                                    <Cascader
                                      placeholder='请选择所在地区'
                                      fieldNames={{
                                        label: 'name',
                                        value: 'code',
                                        children: 'children'
                                      }}
                                      style={{ width: '100%' }}
                                      changeOnSelect
                                      options={PcaCode as any}
                                    />
                                  </Form.Item>
                                </Col>
                              </Row>
                              <Row>
                                <Col span={24}>
                                  <Form.Item
                                    name={[field.name, 'detail']}
                                    rules={[
                                      {
                                        required: true,
                                        message: '请输入详细地址信息.'
                                      }
                                    ]}
                                    noStyle>
                                    <Input.TextArea
                                      rows={3}
                                      placeholder='请输入详细地址信息，如道路、门牌号、小区、楼栋号、单元室等'
                                    />
                                  </Form.Item>
                                </Col>
                              </Row>
                            </Space>
                          </Col>
                          <Col
                            span={2}
                            style={{
                              display: 'inline-flex',
                              justifyContent: 'center',
                              alignItems: 'center'
                            }}>
                            <span>
                              {fields.length > 1 ? (
                                <MinusCircleOutlined
                                  className='dynamic-delete-button'
                                  style={{ margin: '0 8px' }}
                                  onClick={() => {
                                    remove(field.name);
                                  }}
                                />
                              ) : null}
                            </span>
                          </Col>
                        </Row>
                      </Form.Item>
                    ))}
                    <Form.Item style={{ textAlign: 'right' }}>
                      <Button
                        type='primary'
                        onClick={() => {
                          add();
                        }}>
                        <PlusOutlined /> 添加地址
                      </Button>
                    </Form.Item>
                  </div>
                );
              }}
            </Form.List>
            <Form.Item
              {...layout}
              label='医院介绍信息'
              name='hospitalIntroduction'
              rules={[
                {
                  required: true,
                  message: '请输入医院介绍信息!'
                }
              ]}>
              <Editor placeholder='请输入正文内容' />
            </Form.Item>

            {permisstion.canUpdateHospital && (
              <Form.Item {...tailLayout} style={{ textAlign: 'right' }}>
                <Space size={22}>
                  <Button type='ghost' style={{ width: '136px' }}>
                    取消
                  </Button>
                  <Button
                    type='primary'
                    htmlType='submit'
                    loading={saveLoading}
                    style={{ width: '136px' }}>
                    保存
                  </Button>
                </Space>
              </Form.Item>
            )}
          </Form>
        </>
      }
      previewChild={
        <Space size={15} direction='vertical' style={{ width: '100%' }}>
          <Card
            bordered={false}
            style={{ borderRadius: '10px' }}
            bodyStyle={{ padding: '15px 10px' }}>
            <Row>
              <Col span={9} className='preview-form-label'>
                <span>医疗名称：</span>
              </Col>
              <Col span={15} className='preview-form-value'>
                {values?.hospitalName}
              </Col>
            </Row>
            <Row>
              <Col span={9} className='preview-form-label'>
                <span>医院等级等次：</span>
              </Col>
              <Col span={15} className='preview-form-value'>
                {values?.hospitalLevel ? values?.hospitalLevel : '--'}
              </Col>
            </Row>
            <Row>
              <Col span={9} className='preview-form-label'>
                <span>联 系 电 话：</span>
              </Col>
              <Col span={15} className='preview-form-value'>
                <span>{values?.contactNumber}</span>{' '}
                <a href={`tel:${values?.contactNumber}`}>拨打电话</a>
              </Col>
            </Row>
            <Row>
              <Col className='preview-form-label row tal'>
                <span>地址信息：</span>
              </Col>
              <Space size={15} direction='vertical' style={{ width: '100%' }}>
                {(values.hospitalAddressJson || []).map(
                  (
                    item: {
                      desc: string;
                      area: string;
                      detail: string;
                    },
                    index: number
                  ) => {
                    if (!item) {
                      return null;
                    }
                    const area = item.area || [];
                    const c1 = PcaCode.find(x => x.code === area[0]);
                    const c2 = c1?.children.find(x => x.code === area[1]);
                    const c3 = c2?.children.find(x => x.code === area[2]);
                    return (
                      <Col className='preview-form-value' key={index}>
                        <span>
                          【{item.desc}】{c1?.name || ''} {c2?.name || ''}{' '}
                          {c3?.name || ''} {item.detail}
                        </span>
                        <div>
                          <SendOutlined />
                        </div>
                      </Col>
                    );
                  }
                )}
              </Space>
            </Row>
          </Card>
          <Card
            bordered={false}
            style={{ borderRadius: '10px' }}
            bodyStyle={{ padding: '15px 10px' }}>
            <Row>
              <Col span={24} className='preview-form-label tal'>
                医院介绍信息
              </Col>
              <Col>
                <div
                  style={{ minHeight: 300 }}
                  dangerouslySetInnerHTML={{
                    __html: values?.hospitalIntroduction
                      ? values?.hospitalIntroduction
                      : '<p>暂无数据</p>'
                  }}
                />
              </Col>
            </Row>
          </Card>
        </Space>
      }
    />
  );
};

const Editor = styled(BraftEditor)`
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  .bf-content {
    height: 300px !important;
  }
  &:hover {
    border-color: #4e9fe6;
  }
`;
