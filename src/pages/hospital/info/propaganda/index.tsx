import { useCallback, useState } from 'react';
import TabPaneLayout from '@pages/hospital/info/layout';
import { HospitalData } from '@pages/hospital/info/apis';
import { UploadImg } from 'parsec-admin';
import orgApi from '@pages/organization/list/api';
import BannerConfig from './bannerConfig';
import {
  Button,
  Form,
  Input,
  Space,
  Carousel,
  message,
  Switch,
  InputNumber
} from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import styles from './styles.module.less';
import { Icon } from '@kqinfo/ui';
import hotDeptImg from '../images/hotDept.png';

export default ({
  data,
  loading = false
}: {
  data: HospitalData;
  loading: boolean;
}) => {
  const [form] = Form.useForm();
  const [formData, setFormData] = useState<HospitalData>(data);
  const { request, loading: saveLoading } = orgApi.修改医院({
    needInit: false
  });
  const onFinish = useCallback(
    async values => {
      await request(values);
      message.success('保存成功');
    },
    [request]
  );

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  const layout = {
    labelCol: { span: 5, style: { minWidth: '146px' } },
    wrapperCol: { span: 19 }
  };

  const tailLayout = {
    wrapperCol: { offset: 8, span: 16 }
  };

  return (
    <TabPaneLayout
      title='宣传信息'
      loading={loading}
      formChild={
        <>
          <Form
            {...layout}
            form={form}
            initialValues={shallowTrimStrToUndefined(data)}
            onValuesChange={(_, allVal) => {
              setFormData(allVal);
            }}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}>
            <Form.Item name='id' hidden />
            <Form.Item name='hisId' hidden />
            <Form.Item
              label='医院logo'
              name='hospitalLogo'
              extra='建议图片比例1：1，尺寸200*200，上传格式JPG、PNG、jpeg'
              rules={[
                {
                  required: true,
                  message: '请上传医院logo!'
                }
              ]}>
              <UploadImg
                length={1}
                arrValue={false}
                showUploadList={{
                  showPreviewIcon: true,
                  showRemoveIcon: true,
                  showDownloadIcon: false
                }}
              />
            </Form.Item>
            <Form.Item
              label='医院背景图'
              name='hospitalBanner'
              extra='建议图片比例2：1，上传格式JPG、PNG、jpeg'
              rules={[
                {
                  required: true,
                  message: '请上传医院背景图!'
                }
              ]}>
              <UploadImg
                length={1}
                arrValue={false}
                showUploadList={{
                  showPreviewIcon: true,
                  showRemoveIcon: true,
                  showDownloadIcon: false
                }}
              />
            </Form.Item>
            <Form.Item
              label='融合首页背景'
              name='fuseBanner'
              extra='建议图片比例2：1，上传格式JPG、PNG、jpeg'>
              <UploadImg
                length={1}
                arrValue={false}
                showUploadList={{
                  showPreviewIcon: true,
                  showRemoveIcon: true,
                  showDownloadIcon: false
                }}
              />
            </Form.Item>
            <Form.Item
              label='首页banner'
              name={['activityBanner', 'imageList']}
              // rules={[
              //   {
              //     required: false,
              //     message: '请上传首页banner!'
              //   }
              // ]}
            >
              <BannerConfig />
            </Form.Item>
            <Form.Item
              label='是否自动播放'
              valuePropName={'checked'}
              name={['activityBanner', 'autoplay']}>
              <Switch />
            </Form.Item>
            <Form.Item
              label='轮播间隔时间'
              name={['activityBanner', 'interval']}>
              <InputNumber
                placeholder='轮播间隔时间'
                min={500}
                step={100}
                style={{ width: 200 }}
                addonAfter='毫秒'
              />
            </Form.Item>
            <Form.Item label='公告' name='notice'>
              <Input.TextArea rows={4} placeholder='输入公告信息' />
            </Form.Item>

            <Form.Item {...tailLayout} style={{ textAlign: 'right' }}>
              <Space size={22}>
                <Button type='ghost' style={{ width: '136px' }}>
                  取消
                </Button>
                <Button
                  type='primary'
                  htmlType='submit'
                  loading={saveLoading}
                  style={{ width: '136px' }}>
                  保存
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </>
      }
      previewChild={
        <Space size={0} direction='vertical' style={{ width: '100%' }}>
          <div className={styles.previewTop}>
            <img src={formData.hospitalBanner} alt='homebanner' />
          </div>
          <div className={styles.previewMain}>
            <Input
              size='large'
              readOnly
              className={styles.previewSearch}
              placeholder='搜索医生、科室、疾病'
              prefix={<SearchOutlined color='#666' />}
            />
            <div className={styles.homeNotice}>
              <Icon
                name='kq-notice'
                className={styles.homeNoticeIcon}
                // color={'#ff9d46' }
                color={'#ff9d46'}
              />
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              {`公告：${formData?.notice || '暂无'}`}
            </div>
            <img
              src={hotDeptImg}
              alt='热门科室'
              className={styles.hotDeptImg}
            />
            <div className={styles.activityBanner}>
              {formData.activityBanner && (
                <Carousel
                  autoplay={formData.activityBanner?.autoplay}
                  dots={false}
                  autoplaySpeed={
                    formData.activityBanner?.autoplay
                      ? formData.activityBanner?.interval
                      : undefined
                  }>
                  {formData.activityBanner?.imageList?.map(item => {
                    return (
                      <img
                        key={item.sort}
                        className={styles.activityBannerItem}
                        src={item.imageUrl}
                        alt='banner图'
                      />
                    );
                  })}
                </Carousel>
              )}
            </div>
          </div>
        </Space>
      }
    />
  );
};

function shallowTrimStrToUndefined(data?: any) {
  if (typeof data === 'object' && !!data) {
    return Object.keys(data).reduce((prev, key) => {
      prev[key] = data[key] === '' ? undefined : data[key];
      return prev;
    }, {});
  }
  return data;
}
