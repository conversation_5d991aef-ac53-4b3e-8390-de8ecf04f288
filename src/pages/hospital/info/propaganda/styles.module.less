.activeBannerWrap {
  width: 100%;
  height: auto;
}
.activeBanners {
  // width: 500px;
  display: flex;
  flex-wrap: wrap;
}
.activeBannerItem {
  border-radius: 4px;
  position: relative;
  box-shadow: 0 0 2px 0px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  width: 200px;
  height: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 15px;
  margin-bottom: 10px;
  .imageUrl {
    width: 100%;
    height: 100%;
  }
  .bannerAction {
    position: absolute;
    width: 100%;
    left: 0;
    height: 30px;
    bottom: 0px;
    background-color: rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .bannerActionIcon {
    margin: 0 10px;
    font-size: 20px;
    color: #2780da;
  }
}
.activeBannerConfig {
  margin-top: 20px;
  .activeBannerRow {
    margin-top: 10px;
  }
}
.previewTop {
  width: 100%;
  height: 120px;
  img {
    width: 100%;
    height: 100%;
  }
}
.previewMain {
  position: relative;
  top: -10px;
  padding-top: 20px;
  padding-bottom: 100px;
  background-color: #fff;
  border-radius: 10px;
  .previewSearch {
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.1);
    position: absolute;
    width: calc(100% - 24px);
    left: 12px;
    top: -19px;
    height: 38px;
    border-radius: 38px;
    border: none;
  }
}

.homeNotice {
  position: relative;
  display: flex;
  // min-height:36px ;
  line-height: 16px;
  padding: 10px 5px;
  flex-wrap: wrap;
  font-size: 12px;
  .homeNoticeIcon {
    position: absolute;
    top: 10px;
    left: 0;
    font-size: 16px;
    // color: #ff9d46;
    margin-left: 10px;
  }
}
.hotDeptImg {
  width: 100%;
  height: auto;
}
.activityBanner {
  padding: 10px;
  width: 100%;
  .activityBannerItem {
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    height: 100px;
    width: 100%;
  }
}
