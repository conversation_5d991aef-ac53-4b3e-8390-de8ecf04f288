/* eslint-disable jsx-a11y/anchor-is-valid */
import { PlusOutlined } from '@ant-design/icons';
import ProForm, {
  ModalForm,
  ProFormSelect,
  ProFormText
} from '@ant-design/pro-form';
import { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Form } from 'antd';
import { useMemo, useState } from 'react';
import { UploadImg } from 'parsec-admin';

const linkTypeEnm = {
  H5: 'H5',
  applet: '小程序',
  none: '不跳转'
};

export default ({
  value = [],
  onChange
}: {
  value?: any[];
  onChange?: (v: any) => void;
}) => {
  const [form] = Form.useForm();
  const [mode, setMode] = useState<'add' | 'update'>('add');
  const [modalVisit, setModalVisit] = useState(false);

  const innerValue = useMemo(
    () =>
      value?.every(i => !!i.id)
        ? value
        : value.map(i => (i.id ? i : { ...i, id: generateId() })),
    [value]
  );

  const handleFactory = (up: boolean) => (item: any) => {
    if (Array.isArray(innerValue) && item?.id) {
      const findIndex = innerValue?.findIndex(i => i.id === item.id);
      const newIndex = up ? findIndex - 1 : findIndex + 1;
      if (newIndex < 0 || newIndex >= innerValue.length) {
        return;
      }
      const newValue = [...innerValue];
      const tempItem = newValue[newIndex];
      newValue[newIndex] = item;
      newValue[findIndex] = tempItem;
      onChange?.(newValue);
    }
  };

  const handleUp = handleFactory(true);
  const handleDown = handleFactory(false);

  const columns: ProColumns[] = useMemo(
    () => [
      {
        title: '序号',
        dataIndex: 'index',
        valueType: 'index',
        width: 30
      },
      {
        title: '图片',
        dataIndex: 'imageUrl',
        valueType: 'image',
        width: 60
      },
      {
        title: '跳转类型',
        dataIndex: 'linkType',
        valueEnum: linkTypeEnm,
        ellipsis: true,
        width: 60
      },
      {
        title: 'h5链接',
        dataIndex: 'h5Link',
        ellipsis: true,
        width: 130
      },
      {
        title: '小程序原始id',
        dataIndex: 'ghAppId',
        ellipsis: true,
        width: 100
      },
      {
        title: '小程序id',
        dataIndex: 'appId',
        ellipsis: true,
        width: 100
      },
      {
        title: '小程序路径',
        dataIndex: 'appletLink',
        ellipsis: true,
        width: 100
      },
      {
        title: '操作',
        valueType: 'option',
        width: 180,
        key: 'option',
        render: (_, record) =>
          [
            <a
              key='up'
              onClick={() => {
                handleUp(record);
              }}>
              上移
            </a>,
            <a
              key='down'
              onClick={() => {
                handleDown(record);
              }}>
              下移
            </a>,
            <a
              key='edit'
              onClick={() => {
                form.setFieldsValue(record);
                setMode('update');
                setModalVisit(true);
              }}>
              编辑
            </a>,
            <a
              key='delete'
              onClick={() => {
                onChange?.(innerValue?.filter(i => i.id !== record.id));
              }}>
              删除
            </a>
          ].filter(Boolean)
      }
    ],
    [form, handleDown, handleUp, onChange, innerValue]
  );

  return (
    <>
      <Button
        type='primary'
        style={{ marginBottom: 12 }}
        onClick={() => {
          form.resetFields();
          setMode('add');
          setModalVisit(true);
        }}>
        <PlusOutlined />
        添加轮播图
      </Button>

      <ProTable
        scroll={{ x: 360 }}
        dataSource={innerValue}
        bordered
        columns={columns}
        search={false}
        toolBarRender={false}
        pagination={false}
      />
      <ModalForm
        title={`新建轮播图`}
        form={form}
        visible={modalVisit}
        onVisibleChange={setModalVisit}
        autoFocusFirstInput
        onFinish={async values => {
          if (mode === 'add') {
            values.id = generateId();
            onChange?.([...(value || []), values]);
          } else {
            onChange?.(
              innerValue?.map(i => {
                if (i.id === values.id) {
                  return values;
                }
                return i;
              })
            );
          }
          return true;
        }}>
        <ProForm.Group>
          <ProFormSelect
            name='linkType'
            label='跳转类型'
            width={'sm'}
            rules={[
              {
                required: true,
                message: '请选择跳转类型!'
              }
            ]}
            valueEnum={linkTypeEnm}
          />
          <ProFormText width={'sm'} name='h5Link' label='h5链接' />
          <ProFormText width={'lg'} name='appletLink' label='小程序链接' />
          <ProFormText
            width={'lg'}
            name='ghAppId'
            label='小程序原始id'
            placeholder={'小程序原始id，gh_开头'}
          />
          <ProFormText width={'lg'} name='appId' label='小程序id' />
          <ProFormText name='id' hidden />
        </ProForm.Group>
        <Form.Item
          required
          name='imageUrl'
          label='轮播图片'
          rules={[
            {
              required: true,
              message: '请上传轮播图片!'
            }
          ]}>
          <UploadImg
            length={1}
            arrValue={false}
            showUploadList={{
              showPreviewIcon: true,
              showRemoveIcon: true,
              showDownloadIcon: false
            }}
          />
        </Form.Item>
      </ModalForm>
    </>
  );
};

function generateId() {
  return Math.round(Math.random() * 1000000);
}
