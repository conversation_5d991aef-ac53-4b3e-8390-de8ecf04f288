import React from 'react';
import { CardLayout } from 'parsec-admin';
import { Tabs } from 'antd';
import useApi from './apis';
import Introduce from './introduce';
import Publicity from './publicity';
import env from '@configs/env';
import Propaganda from './propaganda';
const { TabPane } = Tabs;
export default () => {
  const hisId = env.hisId;
  const {
    data: { data },
    loading
  } = useApi.getHospitalInfoById({
    needInit: true,
    params: { hisId: hisId },
    initValue: { data: {} }
  });

  const callback = (activeKey: string) => {
    console.log(activeKey);
  };

  return (
    <>
      <CardLayout>
        <Tabs defaultActiveKey='1' onChange={callback}>
          <TabPane tab='医院简介' key='1'>
            <Introduce loading={loading} data={data} />
          </TabPane>
          {env.hisId !== '40030' && (
            <TabPane tab='互联网医院公示' key='2'>
              <Publicity loading={loading} data={data} />
            </TabPane>
          )}
          {/* {env.hisId === '40009' && (
            <TabPane tab='宣传信息' key='3'>
              <Propaganda loading={loading} data={data} />
            </TabPane>
          )} */}
          <TabPane tab='宣传信息' key='3'>
            {data?.hisId && <Propaganda loading={loading} data={data} />}
          </TabPane>
        </Tabs>
      </CardLayout>
    </>
  );
};
