import React from 'react';
import { Row, Col, Spin } from 'antd';
import styled from 'styled-components';

export interface LayoutProps {
  title: string;
  previewChild: React.ReactNode;
  formChild?: React.ReactNode;
  loading?: boolean;
}

export default ({
  title,
  previewChild,
  formChild,
  loading = false,
  ...props
}: LayoutProps) => {
  return (
    <Spin spinning={loading} tip={'加载中...'}>
      <Row {...props}>
        <Col xs={24} sm={24} md={24} lg={24} xl={formChild ? 10 : 24} pull={1}>
          <Phone>
            <div className='top' />
            <div className='title'>{title}</div>
            <div className='bg' />
            <div className='content'>{previewChild}</div>
          </Phone>
        </Col>
        {formChild && (
          <Col xs={24} sm={24} md={24} lg={24} xl={14}>
            {formChild}
          </Col>
        )}
      </Row>
    </Spin>
  );
};

const Phone = styled.div`
  position: relative;
  width: 400px;
  height: 822px;
  overflow: hidden;
  margin: 30px auto;
  z-index: 999;
  > .top {
    height: 29px;
    background: url("${require('./images/top.png')}");
    background-size: cover;
    position: absolute;
    top: 16px;
    left: 16px;
    right: 16px;
    display: block;
  }
  > .title {
    position: absolute;
    top: 42px;
    left: 16px;
    right: 16px;
    height: 44px;
    line-height: 44px;
    background: url("${require('./images/title.png')}") center center no-repeat;
    background-size: cover;
    text-align: center;
    font-weight: 500;
    font-size: 16px;
    color: #000000;
  }
  > .bg {
    position: absolute;
    background: url("${require('./images/sjkk.png')}") center center no-repeat;
    background-size: cover;
    width: 400px;
    height: 822px;
    top: 0;
    left: 0;
    right: 0;
  }
  > .content {
    position: absolute;
    height: 720px;
    background: #f4fafe;
    top: 84px;
    left: 20px;
    right: 20px;
    bottom: 16px;
    padding: 15px;
    overflow-y: auto;
    word-wrap: break-word;
    word-break: break-all;
    border-bottom-left-radius: 40px;
    border-bottom-right-radius: 40px;
    ::-webkit-scrollbar {display:none}
    .preview-form-label {
      color: #a7a7a7;
      font-size: 13px;
      width: 100%;
      text-align: justify;
      vertical-align: top;

      &::after {
          display: inline-block;
          width: 100%;
          content: '';
          height: 0;
      }
    }

    .row {
      border-top: 1px dotted #a7a7a7;
      padding-top: 15px;
    }

    .tal {
      text-align: left;
    }

    .preview-form-value {
      font-size: 13px;
      color: #333333;
      text-align: left;
      display: flex;

      > span {
        flex: 1;
      }
      > a {
        color: #2780d9;
        margin-left: 10px;

        &:hover {
          color: #2780d9
        }
      }
      > img {
        width: 317px;
        height: 236px;
        margin: 0 auto;
        object-fit: cover;
        overflow: hidden;
      }
    }
  }
`;
