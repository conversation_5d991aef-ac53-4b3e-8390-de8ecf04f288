import React, { useCallback, useEffect, useState } from 'react';
import TabPaneLayout from '@pages/hospital/info/layout';
import useApi, { HospitalData, hospitalLevel } from '@pages/hospital/info/apis';
import {
  Button,
  Col,
  Form,
  Input,
  Row,
  Select,
  Space,
  DatePicker,
  Card,
  message
} from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import BraftEditor from '@components/braftEditor';
import { Upload, LinkButton } from 'parsec-admin';
import moment from 'moment';
import env from '@src/configs/env';

const { RangePicker } = DatePicker;

export default ({
  data,
  loading = false
}: {
  data: HospitalData;
  loading: boolean;
}) => {
  const [form] = Form.useForm();
  const hisId = env.hisId;
  const { request, loading: saveLoading } = useApi.saveHospitalInfo({
    needInit: false
  });

  const [values, setValues] = useState<HospitalData>({
    hospitalAddress: "[{ desc: '', area: '[]', detail: '' }]"
  });

  useEffect(() => {
    form.setFieldsValue({
      ...data,
      honorImages: BraftEditor.createEditorState(
        data?.honorImages || '<p></p>'
      ),
      certFirstTime: data?.certFirstTime
        ? moment(data?.certFirstTime)
        : undefined,
      certUpdateTime: data?.certUpdateTime
        ? moment(data?.certUpdateTime)
        : undefined,
      licenseTimePicker:
        data?.licenseStartTime && data?.licenseEndTime
          ? [moment(data?.licenseStartTime), moment(data?.licenseEndTime)]
          : undefined
    });
    setValues({ ...data });
  }, [data, form]);

  const onValuesChange = (changedValues: any, values: any) => {
    if (typeof values['honorImages'] === 'object') {
      values['honorImages'] = values['honorImages'].toHTML();
    }
    setValues(values);
  };

  const onFinish = useCallback(() => {
    form
      .validateFields()
      .then(async (values: any) => {
        let params: any = {};
        if (values['licenseTimePicker'] !== null) {
          const licenseTimeValue = values['licenseTimePicker'];
          params = {
            licenseStartTime: licenseTimeValue
              ? licenseTimeValue[0].format('YYYY-MM-DD HH:mm:ss')
              : '', //执业许可标志开始时间
            licenseEndTime: licenseTimeValue
              ? licenseTimeValue[1].format('YYYY-MM-DD HH:mm:ss')
              : '' //执业许可标志结束时间
          };
        }
        if (typeof values['honorImages'] === 'object') {
          values['honorImages'] = values['honorImages'].toHTML();
        }

        delete values['licenseTimePicker'];
        await request({
          ...data,
          ...values,
          hisId: hisId,
          instCode: values['instCode'] ? values['instCode'] : '', //医疗机构代码
          certFirstTime: values['certFirstTime']
            ? values['certFirstTime'].format('YYYY-MM-DD HH:mm:ss')
            : '', //首次获证时间
          certUpdateTime: values['certUpdateTime']
            ? values['certUpdateTime'].format('YYYY-MM-DD HH:mm:ss')
            : '', //证件更新时间
          ...params
        }).then(() => {
          message.success('保存成功');
        });
      })
      .catch(e => {
        console.log(e);
        if (e?.errorFields) {
          message.error(e.errorFields[0].errors[0]);
        }
      });
  }, [data, form, hisId, request]);

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  const layout = {
    labelCol: { span: 5, style: { minWidth: '146px' } },
    wrapperCol: { span: 19 }
  };

  const rowLayout = {
    labelCol: { span: 9, style: { minWidth: '146px' } },
    wrapperCol: { span: 15 }
  };
  const rowLayout2 = {
    labelCol: { span: 10 },
    wrapperCol: { span: 14 }
  };
  const tailLayout = {
    wrapperCol: { offset: 8, span: 16 }
  };

  const onRemove = () => {
    form.setFieldsValue({
      licence: ''
    });
    setValues({ ...values, licence: '' });
  };

  return (
    <TabPaneLayout
      title='互联网医院公示'
      loading={loading}
      formChild={
        <>
          <Form
            name='hospitalInfoForm'
            size='large'
            form={form}
            initialValues={{ ...values }}
            onValuesChange={onValuesChange}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}>
            <Form.Item
              {...layout}
              label='医疗机构名称'
              name='name'
              rules={[
                {
                  required: true,
                  message: '请输入医疗机构名称!'
                }
              ]}>
              <Input placeholder='请输入医疗机构名称' />
            </Form.Item>
            <Form.Item
              {...layout}
              label='医疗机构等级等次'
              name='level'
              rules={[
                {
                  required: true,
                  message: '请选择医疗机构等级等次!'
                }
              ]}>
              <Select placeholder='请选择医疗机构等级等次'>
                {(hospitalLevel || []).map(
                  (
                    v: { label: string; value: string | number },
                    index: number
                  ) => (
                    <Select.Option value={v.label} key={index}>
                      {v.label}
                    </Select.Option>
                  )
                )}
              </Select>
            </Form.Item>

            <Row gutter={16}>
              <Col span={14}>
                <Form.Item label='医疗机构执业许可证' required {...rowLayout}>
                  <Form.Item
                    name='licence'
                    noStyle
                    rules={[
                      {
                        required: true,
                        message: '请上传医疗机构执业许可证'
                      }
                    ]}>
                    <Upload length={1} arrValue={false}>
                      <UploadDragWarp>
                        <div className='ant-upload-drag-icon'>
                          <InboxOutlined />
                          <div>
                            <span>点击上传图片</span>
                          </div>
                        </div>
                        <div className='ant-upload-text'>
                          支持jpg\jpeg\png等多种格式、单张
                        </div>
                        <div className='ant-upload-hint'>图片最大支持10M</div>
                      </UploadDragWarp>
                    </Upload>
                  </Form.Item>
                </Form.Item>
              </Col>
              <Col span={10} style={{ textAlign: 'right' }}>
                {values?.licence && (
                  <Preview>
                    <img src={values?.licence} alt='医疗机构执业许可证' />
                    <p>
                      <LinkButton onClick={() => onRemove()}>
                        移除图片
                      </LinkButton>
                    </p>
                  </Preview>
                )}
              </Col>
            </Row>
            <Form.Item
              {...layout}
              label='互联网诊疗服务范围'
              name='serviceScope'
              rules={[
                {
                  required: true,
                  message: '请输入互联网诊疗服务范围的科室/病种等信息!'
                }
              ]}>
              <Input.TextArea
                rows={4}
                placeholder='请输入互联网诊疗服务范围的科室/病种等信息'
              />
            </Form.Item>
            <Form.Item
              {...layout}
              label='医疗机构奖惩信息'
              name='honorImages'
              rules={[
                {
                  required: true,
                  message: '请输入医疗机构奖惩信息!'
                }
              ]}>
              <Editor placeholder='请输入医疗机构奖惩信息' />
            </Form.Item>
            <Form.Item
              {...layout}
              label='医疗机构代码'
              name='instCode'
              rules={[
                {
                  message: '请输入医疗机构代码!'
                }
              ]}>
              <Input placeholder='请输入医疗机构代码' />
            </Form.Item>
            <Form.Item
              {...layout}
              label='医疗机构执业许可证登记号'
              name='instRegisterNumber'
              rules={[
                {
                  message: '请输入医疗机构执业许可证登记号!'
                }
              ]}>
              <Input placeholder='请输入医疗机构执业许可证登记号' />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label='首次获证时间'
                  name='certFirstTime'
                  rules={[
                    {
                      required: true,
                      message: '请选择首次获证时间!'
                    }
                  ]}
                  {...rowLayout2}>
                  <DatePicker
                    placeholder={'请选择首次获证时间！'}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label='证件更新时间'
                  name='certUpdateTime'
                  rules={[
                    {
                      required: true,
                      message: '请选择证件更新时间!'
                    }
                  ]}
                  {...rowLayout2}>
                  <DatePicker
                    placeholder={'请选择证件更新时间！'}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              {...layout}
              label='医疗机构信息安全等保证号'
              name='instInfoSafeEnsure'
              rules={[
                {
                  message: '请输入医疗机构信息安全等保证号!'
                }
              ]}>
              <Input placeholder='请输入医疗机构信息安全等保证号' />
            </Form.Item>
            <Form.Item
              {...layout}
              label='执业许可标志开始时间'
              name='licenseTimePicker'
              rules={[
                {
                  required: true,
                  message: '请选择执业许可标志开始时间!'
                }
              ]}>
              <RangePicker />
            </Form.Item>

            <Form.Item {...tailLayout} style={{ textAlign: 'right' }}>
              <Space size={22}>
                <Button type='ghost' style={{ width: '136px' }}>
                  取消
                </Button>
                <Button
                  type='primary'
                  htmlType='submit'
                  loading={saveLoading}
                  style={{ width: '136px' }}>
                  保存
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </>
      }
      previewChild={
        <Space size={15} direction='vertical' style={{ width: '100%' }}>
          <Card
            bordered={false}
            style={{ borderRadius: '10px' }}
            bodyStyle={{ padding: '15px 10px' }}>
            <Row>
              <Col span={10} className='preview-form-label'>
                <span>医疗机构名称：</span>
              </Col>
              <Col span={14} className='preview-form-value'>
                {values?.name}
              </Col>
            </Row>
            <Row>
              <Col span={10} className='preview-form-label'>
                <span>医疗机构等级等次：</span>
              </Col>
              <Col span={14} className='preview-form-value'>
                {values?.level}
              </Col>
            </Row>
            <Row>
              <Col className='preview-form-label row tal'>
                <span>医疗机构执业许可证：</span>
              </Col>
              <Col className='preview-form-value'>
                {values?.licence ? (
                  <img src={values?.licence} alt='医疗机构执业许可证' />
                ) : (
                  <div></div>
                )}
              </Col>
            </Row>
            <Row>
              <Col className='preview-form-label row tal'>
                <span>互联网诊疗服务范围（科目/病种）：</span>
              </Col>
              <Col className='preview-form-value'>
                {values?.serviceScope || '无'}
              </Col>
            </Row>
          </Card>
          <Card
            bordered={false}
            style={{ borderRadius: '10px' }}
            bodyStyle={{ padding: '15px 10px' }}>
            <Row>
              <Col span={24} className='preview-form-label tal'>
                荣誉奖项
              </Col>
              <Col>
                <div
                  style={{ minHeight: 300 }}
                  dangerouslySetInnerHTML={{
                    __html: values?.honorImages
                      ? values?.honorImages
                      : '<p>暂无数据</p>'
                  }}
                />
              </Col>
            </Row>
          </Card>
        </Space>
      }
    />
  );
};

const Editor = styled(BraftEditor)`
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  .bf-content {
    height: 200px !important;
  }
  &:hover {
    border-color: #4e9fe6;
  }
`;

const Preview = styled.div`
  position: absolute;
  right: 10px;
  width: 300px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 10px 25px;
  text-align: center;
  &:hover {
    border-color: #4e9fe6;
  }
  > img {
    width: 225px;
    max-height: 140px;
    margin: 0 auto;
    overflow: hidden;
    object-fit: cover;
  }
  a {
    color: #2780d9;
    margin-left: 10px;

    &:hover {
      color: #2780d9;
    }
  }
`;
const UploadDragWarp = styled.div`
  text-align: center;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 25px 35px;
  &:hover {
    border-color: #2780d9;
  }
  > .ant-upload-drag-icon {
    .anticon {
      color: #2780d9;
      font-size: 58px !important;
    }
    //margin-bottom: 15px !important;
    > div {
      margin-top: 15px;
      margin-bottom: 6px;
      font-size: 14px !important;
      color: #333333;
    }
  }
  .ant-upload-text,
  .ant-upload-hint {
    font-size: 14px !important;
    color: rgba(0, 0, 0, 0.45) !important;
  }
`;
