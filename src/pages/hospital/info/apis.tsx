import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ApiResponse } from '@src/pages/login/apis';

export interface HospitalData {
  id?: number;
  hisId?: number; // hisId
  name?: string; // 医疗机构名称
  level?: string; // 医疗机构等级等次--代码
  grading?: string; // 医疗机构等级等次--名称
  licence?: string; // 医疗机构执业许可证
  serviceScope?: string; // 服务范围
  honorImages?: string; // 荣誉奖项
  instCode?: string; // 医疗机构代码
  instRegisterNumber?: string; // 医疗机构执业许可证登记号
  instInfoSafeEnsure?: string; // 医疗机构信息安全等保证号
  certFirstTime?: string;
  certUpdateTime?: string;
  licenseStartTime?: string; // 执业许可标志开始时间
  licenseEndTime?: string; // 执业许可标志结束时间
  createTime?: string;
  updateTime?: string;
  hospitalName?: string; // 医院名称
  type?: string; // "INTERNET,WISDOM" 医院提供服务的：INTERNET开通互联网医院权限 WISDOM开通智慧医院权限 多个已","分割
  hospitalLevel?: string; // 医院等级等次
  hospitalAddress?: string; //{ desc: string; area: string; detail: string }[]; // 医院地址信息json字符串
  hospitalAddressJson?: Array<{ desc: string; area: string; detail: string }>;
  hospitalIntroduction?: string; // 医院介绍富文本
  contactNumber?: string; // 医院联系电话
  status?: string;
  adminAccount?: string;
  adminName?: string;
  certFirstDate?: string;
  certUpdateDate?: string;
  licenseStartDate?: string;
  licenseEndDate?: string;
  hisTopImg?: string;
  recommend?: '0';

  sortNo?: number;
  activityBanner?: {
    interval?: number;
    autoplay?: boolean;
    imageList: {
      sort: number; //序号
      imageUrl: string; //图片链接
      h5Link: string; //跳转链接
      appletLink: string; //跳转链接
      linkType: 'H5' | 'applet' | 'none'; // "@pick('H5','applet')"; //页面跳转类型H5（h5页面）applet（小程序）
      appId: string;
    }[];
  };
  hospitalBanner?: string; //医院banner
  notice?: string; //公告
  hospitalLogo?: string; //医院Logo
}
export interface FileSignData {
  accessId: string;
  callback: string;
  dir: string;
  expire: string;
  host: string;
  policy: string;
  sign: string;
  extFields: null;
  code: number;
}

export const hospitalLevel: { label: string; value: string | number }[] = [
  { value: '34', label: '三级特等' },
  { value: '33', label: '三级甲等' },
  { value: '32', label: '三级乙等' },
  { value: '31', label: '三级丙等' },
  { value: '30', label: '三级综合' },
  { value: '18', label: '三级未定级' },
  { value: '23', label: '二级甲等' },
  { value: '22', label: '二级乙等' },
  { value: '21', label: '二级丙等' },
  { value: '17', label: '二级未定级' },
  { value: '13', label: '一级甲等' },
  { value: '12', label: '一级乙等' },
  { value: '11', label: '一级丙等' },
  { value: '16', label: '一级未定级' },
  { value: '15', label: '未定级' }
];

export default {
  读取配置: createApiHooks((data: { hisId: string }) =>
    request.get<any>(`/mch/his/hospital-config/base-settings`, {
      params: data
    })
  ),
  getHospitalInfoById: createApiHooks((params: { hisId: string }) =>
    request.post<{
      code: number;
      msg: string;
      data: HospitalData & { hospitalAddress: '[]' };
    }>(`/mch/his/hospitalInfo/get/hisId?hisId=${params.hisId}`)
  ),
  saveHospitalInfo: createApiHooks((params: HospitalData) =>
    request.post<ApiResponse<any>>('/mch/his/hospital/update', params, {
      headers: {
        Accept: 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/json; charset=UTF-8'
      }
    })
  )
};
