import React, { useCallback, useMemo, useState } from 'react';
import {
  ArrSelect,
  useModal,
  UploadImg,
  ActionsWrap,
  LinkButton,
  actionConfirm,
  Editor,
  handleSubmit
} from 'parsec-admin';
import useApi from './api';
import MyTableList from '@components/myTableList';
import {
  Form,
  Button,
  Input,
  InputNumber,
  Cascader,
  Radio,
  Checkbox,
  message
} from 'antd';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import permisstion from '@utils/permisstion';
import { useHistory } from 'react-router';
import env from '@configs/env';
import { rpxToPx, Space, TransferChange } from '@kqinfo/ui';
import api from '@pages/hospital/campus/api';
import { useEffectState } from 'parsec-hooks';
// import BraftEditor from 'braft-editor';
import { saveAs } from 'file-saver';
import ConfigStore from '@src/store/ConfigStore';

const { TextArea } = Input;
const paginationSetUp = {
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '40', '60', '100'],
  showTotal: total => `共 ${total} 条数据`
};
export const getHisTypes = (hisType?: string) => {
  if (hisType + '' === '1') {
    return {
      '1': '互联网医院'
    } as Record<string, string>;
  }
  if (hisType + '' === '2') {
    return {
      '2': '智慧医院'
    } as Record<string, string>;
  }
  return {
    '1': '互联网医院',
    '2': '智慧医院',
    '3': '互联网医院/智慧医院'
  } as Record<string, string>;
};

export default () => {
  const history = useHistory();
  const hisId = env.hisId;

  const { config } = ConfigStore.useContainer();

  const {
    data: { data: hisTypes }
  } = api.医院开通的业务平台列表({
    deepCache: true,
    initValue: { data: [] }
  });
  const { loading: loadingImport, request: requestImport } = useApi.科室导入({
    needInit: false
  });
  const {
    data: {
      data: { recordList }
    }
  } = api.院区列表({
    initValue: { data: { recordList: [] } }
  });
  const {
    data: { data: standardDeptList }
  } = useApi.标准科室列表({
    params: { hisId },
    needInit: !!hisId,
    initValue: { data: {} }
  });

  const {
    data: { data: catyList }
  } = useApi.获取科别列表({
    needInit: !!hisId,
    initValue: { data: [] }
  });

  const {
    data: {
      data: { recordList: deptList }
    },
    request: depReq
  } = useApi.科室管理列表({
    params: { hisId },
    needInit: !!hisId,
    initValue: {
      data: {
        data: {
          recordList: []
        }
      }
    }
  });
  const [state, setState] = useState({});

  // const [isSmartHospital, setIsSmartHospital] = useState<any>();
  const [tabId, setTabId] = useEffectState<any>(recordList?.[0]?.id);
  const params = useMemo(() => ({ pid: tabId }), [tabId]);

  const [form] = Form.useForm();

  const filterArray = useCallback(
    (arr: Array<any>): any => {
      return arr
        ?.filter(item => {
          return (
            item.hisType === form.getFieldValue('hisType') ||
            item.hisType === (3 as any)
          );
        })
        ?.map(x => {
          return {
            label: x.name,
            value: x.id,
            children: x?.children ? filterArray(x?.children) : undefined
          };
        });
    },
    [form]
  );

  const switchPrizeModalVisible = useModal(
    ({ id, pathDeep, isEdit, isCampus, employeeCount }) => ({
      destroyOnClose: true,
      title: isEdit ? '新增/编辑科室' : '增加子级科室',
      width: 800,
      form,
      onSubmit: ({ ...params }: any) => {
        params.hisId = hisId;
        params.hisName = 'null';

        params.cnMedicine = params.cnMedicine ? 1 : 0;

        params.hisTypeList = params.hisTypeList.map(item => {
          return {
            hisType: item,
            employeeCount: state[item] || 0
          };
        });

        for (let i = 0; i < params.no.length; i++) {
          if (params.no[i] === ' ') {
            message.error('科室编码不能包含空格');
            return Promise.resolve();
          }
        }

        if (pathDeep !== 0 && params?.district?.length > 0) {
          params.pid = params.district[params.district.length - 1];
        }

        return handleSubmit(
          () => (params.id ? useApi.修改科室 : useApi.新增科室).request(params),
          `${params.id ? '修改' : '新增'}`
        );
      },
      afterClose: () => {
        form.resetFields();
        setState({});
      },
      items: [
        {
          name: 'id',
          render: false
        },
        {
          label: '业务平台',
          required: true,
          name: 'hisTypeList',
          render: (
            <TransferChange>
              {(onchange, value) => (
                <Checkbox.Group
                  value={value}
                  onChange={onchange}
                  disabled={!isCampus}>
                  {hisTypes?.map((item: any) => (
                    <Space
                      alignItems={'center'}
                      size={20}
                      key={item.code}
                      style={{ marginBottom: rpxToPx(10) }}>
                      <Checkbox value={item.code}>{item.desc}</Checkbox>
                      {/*{value?.includes(item.code) && item.code === 1 && (*/}
                      {/*  <InputNumber*/}
                      {/*    disabled={!isCampus}*/}
                      {/*    value={state[item.code]}*/}
                      {/*    onChange={e => {*/}
                      {/*      setState({*/}
                      {/*        ...state,*/}
                      {/*        [item.code]: e*/}
                      {/*      });*/}
                      {/*    }}*/}
                      {/*    defaultValue={employeeCount}*/}
                      {/*    size={'small'}*/}
                      {/*    min={0}*/}
                      {/*    addonAfter='人'*/}
                      {/*  />*/}
                      {/*)}*/}
                    </Space>
                  ))}
                </Checkbox.Group>
              )}
            </TransferChange>
          )
        },
        // {
        //   label: '业务平台',
        //   required: true,
        //   name: 'hisType',
        //   render: (
        //     <TransferChange>
        //       {(onchange, value) => (
        //         <ArrSelect
        //           onChange={value => {
        //             setIsSmartHospital(value);
        //             form.setFieldsValue({ district: undefined });
        //             onchange(value);
        //           }}
        //           value={value}
        //           disabled={id > 0}
        //           options={(hisTypes || []).map(x => ({
        //             value: x.code,
        //             children: x.desc
        //           }))}
        //           placeholder='请选择业务平台'
        //         />
        //       )}
        //     </TransferChange>
        //   )
        // },
        {
          label: isCampus ? '院区' : '上级科室',
          name: 'pid',
          required: true,
          render: (
            <TransferChange mode={'cascade'} data={filterArray(deptList)}>
              <Cascader
                //todo disabled暂时开放
                // disabled={true}
                // disabled={pathDeep === -1 ? false : pathDeep !== 0}
                placeholder='请选择上级科室'
                changeOnSelect={true}
                options={filterArray(deptList)}
              />
            </TransferChange>
          )
        },
        {
          label: '科室编码',
          name: 'no',
          required: true,
          formItemProps: {
            rules:
              hisId === '40005'
                ? undefined
                : [
                    {
                      pattern: new RegExp(/^[\dA-Za-z]+$/),
                      message: '只能输入数字和字母'
                    }
                  ]
          },
          render: () => <Input placeholder='请输入科室编码' />
        },
        {
          label: '科别',
          name: 'caty',
          render: <ArrSelect options={catyList || []} stringValue />,
          required: config?.isEnableMedicarePay === 1 ? true : false
        },
        {
          label: '科室名称',
          name: 'name',
          required: true
        },
        {
          label: '标准科室',
          name: 'standardDeptNo',
          render: <ArrSelect options={standardDeptList || {}} stringValue />
        },
        {
          label: '对应诊疗科目',
          name: 'medicalDepartment',
          render: <TextArea autoSize={{ minRows: 3, maxRows: 5 }} />
        },
        {
          label: '职工人数',
          name: 'employeeCount',
          render: <InputNumber min={0} style={{ width: '100%' }} />,
          required: true
        },
        {
          label: '科室排序',
          name: 'sortNo',
          render: (
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              placeholder='输入的数值越小排名越靠前'
            />
          )
        },
        // {
        //   label: '互联网医院科室排序',
        //   name: 'internetSortNo',
        //   render: (
        //     <InputNumber
        //       min={0}
        //       style={{ width: '100%' }}
        //       placeholder='输入的数值越小排名越靠前'
        //     />
        //   )
        // },
        // {
        //   label: '智慧医院科室排序',
        //   name: 'wisdomSortNo',
        //   render: (
        //     <InputNumber
        //       min={0}
        //       style={{ width: '100%' }}
        //       placeholder='输入的数值越小排名越靠前'
        //     />
        //   )
        // },
        {
          label: '科室图标',
          name: 'img',
          render: (
            <UploadImg
              arrValue={false}
              showUploadList={{
                showPreviewIcon: true,
                showRemoveIcon: true,
                showDownloadIcon: false
              }}
            />
          ),
          formItemProps: {
            extra: <span>建议上传正方形图标</span>
          }
        },
        {
          label: '科室位置',
          name: 'address'
        },
        {
          label: '联系电话',
          name: 'tel'
        },
        {
          label: '科室介绍',
          name: 'summary',
          // render: <TextArea autoSize={{ minRows: 3, maxRows: 5 }} />,
          render: <Editor />
        }
      ]
    }),
    [config, catyList, hisId, standardDeptList, deptList, state]
  );

  return (
    <MyTableList
      // tableTitle='科室管理'
      needGet={!!tabId}
      params={params}
      tableTitle={
        <Space alignItems={'center'} size={20}>
          <Space>科室列表</Space>
          <Radio.Group
            onChange={val => {
              setTabId(val.target.value);
            }}
            value={tabId}
            buttonStyle='solid'
            optionType='button'>
            <Space flexWrap={'wrap'}>
              {recordList?.map(item => (
                <Radio.Button key={item.id} value={item.id}>
                  {item.name}
                </Radio.Button>
              ))}
            </Space>
          </Radio.Group>
        </Space>
      }
      action={
        (permisstion.canAddDept || true) && (
          <ActionsWrap max={4}>
            <Button
              type={'default'}
              onClick={() => {
                history.push('/hospital/deptSmart/Sort');
              }}>
              科室排序
            </Button>
            <Button
              type={'default'}
              onClick={() => {
                actionConfirm(() => useApi.同步.request({ hisId }), '同步');
              }}>
              同步HIS科室
            </Button>
            <Button
              type={'default'}
              onClick={() => {
                useApi.科室导入模板下载.request().then(r => {
                  saveAs(r, '科室导入模板.xlsx');
                });
              }}>
              下载导入模板
            </Button>
            <Button
              icon={<UploadOutlined />}
              loading={loadingImport}
              onClick={() => {
                const el = document.createElement('input');
                el.setAttribute('type', 'file');
                el.addEventListener('change', () => {
                  const file = el.files?.[0];
                  if (file) {
                    handleSubmit(() => requestImport({ file }), '导入');
                  }
                });
                el.click();
                el.remove();
              }}>
              批量导入
            </Button>
            <Button
              type={'default'}
              icon={<PlusOutlined />}
              onClick={() =>
                switchPrizeModalVisible({
                  pathDeep: -1,
                  isCampus: true,
                  pid: tabId,
                  employeeCount: 0
                  // deptList: deptList
                })
              }>
              添加
            </Button>
          </ActionsWrap>
        )
      }
      pagination={paginationSetUp}
      getList={async ({
        pagination: { current = 1, pageSize = 10 },
        params
      }) => {
        const data = await useApi.科室管理列表分页.request({
          ...params,
          pageNum: current,
          numPerPage: pageSize
        });
        depReq();
        // useApi.科室管理列表
        //   .request({
        //     hisId: params.hisId
        //   })
        //   .then(res => {
        //     // 全量数据用于渲染父级
        //     setDeptList(res?.data?.recordList || []);
        //   });
        return data;
      }}
      showTool={false}
      columns={useMemo(
        () => [
          {
            title: '科室名称及编码',
            dataIndex: 'name',
            width: 250,
            render: (v, r: any) => {
              return `${r.name} ${r.no}`;
            }
          },
          // {
          //   title: '职工人数',
          //   dataIndex: 'employeeCount',
          //   width: 100,
          //   render: v => v || 0
          // },
          // {
          //   title: '互联网医院科室排序',
          //   dataIndex: 'internetSortNo',
          //   width: 160
          // },
          // {
          //   title: '科室介绍',
          //   dataIndex: 'isSummary',
          //   width: 100,
          //   render: v => {
          //     return v === 1 ? '有' : '无';
          //   }
          // },
          {
            title: '所属业务平台',
            dataIndex: 'hisType',
            width: 180,
            search: (
              <ArrSelect
                options={(hisTypes || []).map(x => ({
                  value: x.code,
                  children: x.desc
                }))}
                placeholder='请选择业务平台'
              />
            ),
            render: (v, r: any) => {
              return r?.hisTypeList?.map(item => {
                const types = hisTypes.find(
                  innerItem => innerItem.code === item.hisType
                );
                return <Space>{types?.desc}</Space>;
              });
            }
          },
          {
            title: '科室名称',
            dataIndex: 'deptName',
            search: true,
            render: false
          },
          // {
          //   title: '联系电话',
          //   dataIndex: 'tel',
          //   width: 160
          // },
          {
            title: '状态',
            dataIndex: 'status',
            search: <ArrSelect options={{ '1': '正常', ' 0': '停用' }} />,
            width: 100,
            render: v => {
              return v === 1 ? '正常' : '停用';
            }
          },
          {
            title: '创建时间',
            width: 180,
            dataIndex: 'createTime'
          },
          {
            title: '操作',
            fixed: 'right',
            width: 280,
            render: permisstion.canUpdateDept
              ? record => {
                  // const path: number[] = record.pathCode
                  //   .split('/')
                  //   .filter((x: any) => !!x)
                  //   .map((x: any) => {
                  //     return parseFloat(x.replace('p', ''));
                  //   });

                  // const pathDeep = path.length - 1;
                  const pathDeep = record.children.length;
                  return (
                    <ActionsWrap max={99}>
                      <LinkButton
                        onClick={async () => {
                          // console.log('path', path);
                          // let district: number[] = [];
                          // if (pathDeep === 0) {
                          //   district = [];
                          // }
                          // if (pathDeep === 1) {
                          //   district = [record.children?.[0]];
                          // }
                          // if (pathDeep === 2) {
                          //   district = [
                          //     record.children?.[0],
                          //     record.children?.[1]
                          //   ];
                          // }

                          const {
                            data: detail
                          } = await useApi.科室详情.request({
                            hisType: record.hisType,
                            id: record.id
                          });

                          // detail?.hisTypeList?.forEach(item =>
                          //   setState({
                          //     ...state,
                          //     [item.hisType]: item.employeeCount
                          //   })
                          // );
                          setState({
                            1: detail?.hisTypeList?.[0]?.employeeCount
                          });

                          switchPrizeModalVisible({
                            ...detail,
                            isCampus: true,
                            isEdit: true,
                            img: detail?.img || undefined,
                            standardDeptNo: detail?.standardDeptNo || '',
                            hisTypeList: detail?.hisTypeList?.map(
                              item => item.hisType
                            ),
                            // district: [record.pid, record.id],
                            pid: detail?.pid,
                            // employeeCount:
                            //   detail?.hisTypeList?.[0]?.employeeCount || 0,
                            employeeCount: detail?.employeeCount,
                            // deptList: deptList,
                            pathDeep
                          });
                        }}>
                        编辑
                      </LinkButton>
                      <LinkButton
                        onClick={async () => {
                          await depReq();
                          // if (pathDeep === 0) {
                          //   // 院区
                          //   switchPrizeModalVisible({
                          //     pathDeep: 1,
                          //     hisType: record.hisType,
                          //     district: [record.id]
                          //   });
                          // } else {
                          // 子集
                          switchPrizeModalVisible({
                            pathDeep: 2,
                            isEdit: false,
                            hisTypeList: record?.hisTypeList?.map(
                              item => item.hisType
                            ),
                            employeeCount: 0,
                            // district: [record.pid, record.id]
                            pid: record?.id
                          });
                          // }
                        }}>
                        增加子级科室
                      </LinkButton>
                      <LinkButton
                        onClick={() => {
                          actionConfirm(
                            () =>
                              useApi.科室启用停用.request({
                                id: record.id
                              }),
                            record.status === 1 ? '停用' : '启用'
                          );
                        }}>
                        {record.status === 1 ? '停用' : '启用'}
                      </LinkButton>
                      <LinkButton
                        onClick={() => {
                          actionConfirm(
                            () =>
                              useApi.科室删除.request({
                                id: record.id
                              }),
                            '删除'
                          );
                        }}>
                        删除
                      </LinkButton>
                    </ActionsWrap>
                  );
                }
              : false
          }
        ],
        [depReq, hisTypes, switchPrizeModalVisible]
      )}
    />
  );
};
