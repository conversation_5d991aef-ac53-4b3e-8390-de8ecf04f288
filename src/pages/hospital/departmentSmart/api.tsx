import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiRequestParams,
  ApiResponse,
  ApiResponse1,
  ListApiResponseData2
} from '@src/configs/apis';

export interface DeptItem {
  id: number;
  hisId: 40009;
  name: '儿科';
  no: '10001';
  sortNo: 0;
  employeeCount: null;
  tel: '023-8973495873';
  status: 1;
  pid: 85;
  pathCode: '/p85/p86/';
  hisType: 2;
  isSummary: 1;
  address: '';
  createTime: '2021-08-23 08:53:48';
  updateTime: '2021-08-23 08:53:48';
  children: DeptItem[];
}

interface Add {
  name: string; //基础数据中的科室名称
  no: string; //基础数据中的科室编码
  sortNo: number; //序号 值越小越靠前
  hisType: 1 | 2 | 3; //所属业务平台 1互联网医院、2智慧医院、4智能随访
  parent: number[];
}
export type DeptChildren = {
  id: number; //id
  hisId: number; //医院id
  no: string; //科室编码
  sortNo: number; //序
  name: string;
};
export interface Detail {
  id: number; //id
  hisId: number; //医院id
  createTime: string; //
  updateTime: string; //
  name: string; //科室名称
  no: string; //科室编码
  sortNo: string; //序号 值越小越靠前
  parent: number[]; //上级科室列表
  children: DeptChildren[];
}

interface Edit {
  id: number; //id
  sortNo: number; //序号 值越小越靠前
  parent: number[];
  //上级科室列表
  children: DeptChildren[];
}
export default {
  科室管理列表: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisId?: string;
        hisType?: string | number;
        districtId?: string;
      }
    ) =>
      request
        .get<ApiResponse<DeptItem[]>>('/mch/his/deptMain', { params: data })
        .then(res => {
          return {
            ...res,
            data: {
              code: res.data.code,
              msg: res.data.msg,
              data: {
                currentPage: 1,
                totalCount: 1,
                recordList: res.data.data || []
              }
            }
          };
        })
  ),
  科室管理列表分页: createApiHooks(
    (
      data: ListApiRequestParams & {
        hisType?: string;
        pid?: string;
        status?: string;
        deptName?: string;
        districtId?: string;
      }
    ) =>
      request.get<ApiResponse1<ListApiResponseData2<DeptItem[]>>>(
        '/mch/his/deptMain/pageTree',
        {
          params: data
        }
      )
    // .then(res => {
    //   return {
    //     ...res,
    //     data: {
    //       code: res.data.code,
    //       msg: res.data.msg,
    //       data: {
    //         currentPage: 1,
    //         totalCount: 1,
    //         recordList: res.data.data || []
    //       }
    //     }
    //   };
    // })
  ),
  标准科室列表: createApiHooks((data: { hisId?: string }) =>
    request.post<ApiResponse<{ [value: string]: string }>>(
      '/mch/his/dept/getStandardDept',
      data
    )
  ),
  新增科室: createApiHooks(
    (data: {
      hisId?: string;
      hisName?: string;
      no?: string;
      name?: string;
      summary?: string;
      hasChild?: number;
      pid?: string;
      tel?: string;
      sortNo?: string;
      level?: number;
      img?: string;
      initials?: string;
      status?: string;
      skill?: string;
      employeeCount?: number;
      medicalDepartment?: string;
      menuLink?: string;
      hospitalDeptNo?: string;
      hospitalDeptName?: string;
      standardDeptNo?: string;
      standardDeptName: string;
    }) =>
      request.post<ApiResponse<Record<string, unknown>>>(
        '/mch/his/deptMain',
        data,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
  ),
  修改科室: createApiHooks(
    (data: {
      id?: string;
      hisId?: string;
      hisName?: string;
      no?: string;
      name?: string;
      summary?: string;
      hasChild?: number;
      pid?: string;
      tel?: string;
      sortNo?: string;
      level?: number;
      img?: string;
      initials?: string;
      status?: string;
      skill?: string;
      employeeCount?: number;
      medicalDepartment?: string;
      menuLink?: string;
      hospitalDeptNo?: string;
      hospitalDeptName?: string;
      standardDeptNo?: string;
      standardDeptName?: string;
    }) =>
      request.put<ApiResponse<Record<string, unknown>>>(
        '/mch/his/deptMain',
        data
      )
  ),
  科室启用停用: createApiHooks(({ id }: { id: string }) => {
    return request.put(`/mch/his/deptMain/status`, null, {
      params: { id }
    });
  }),
  开单科室启停: createApiHooks(
    ({ id, status }: { id: string; status: string }) => {
      return request.put(
        `/mch/his/billing/billingDeptStartStop`,
        { id, status },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }
  ),
  科室详情: createApiHooks(
    ({ hisType, id }: { hisType?: string; id: string }) => {
      return request.get<
        ApiResponse<{
          img: string;
          standardDeptNo: number;
          pid: number;
          summary?: string;
          employeeCount: number;
          hisTypeList?: { hisType: number; employeeCount: number }[];
        }>
      >(`/mch/his/deptMain/${id}`, {
        params: { hisType }
      });
    }
  ),
  科室删除: createApiHooks(({ id }: { id: string }) => {
    return request.delete(`/mch/his/deptMain/${id}`);
  }),
  删除开单科室: createApiHooks(({ id }: { id: string }) => {
    return request.delete(`/mch/his/billing/deleteBillingDeptInfo/${id}`);
  }),
  同步: createApiHooks((params: { hisId?: string }) => {
    return request.get('/mch/his/deptMain/sync', { params });
  }),
  科室排序: createApiHooks(
    ({
      hisId,
      data
    }: {
      hisId?: string;
      data: { id: number; sortNo: number }[];
    }) => {
      return request.put('/mch/his/deptMain/sort', data, { params: { hisId } });
    }
  ),
  科室导入: createApiHooks((params: { file: File }) =>
    request.post('/mch/his/deptMain/import', params)
  ),
  获取科别列表: createApiHooks(() =>
    request.get<ApiResponse<{ label: string; value: string }[]>>(
      '/mch/his/dept-caty/list'
    )
  ),
  科室导入模板下载: createApiHooks(() =>
    request.get<Blob>('/mch/his/deptMain/import/template', {
      responseType: 'blob'
    })
  ),
  科室上级树查询: createApiHooks(
    (params: {
      hisType: number;
      hisId?: string;
      districtNo?: string;
      keywords?: string;
    }) => request.get<ApiResponse<any>>('/mch/his/front-dept/tree', { params })
  ),
  科室配置新增: createApiHooks((params: Add) =>
    request.post<ApiResponse<any>>('/mch/his/front-dept', params, {
      headers: { 'Content-Type': 'application/json' }
    })
  ),
  科室配置详情: createApiHooks((id: string) =>
    request.get<ApiResponse<Detail>>(`/mch/his/front-dept/detail/${id}`)
  ),
  科室配置删除: createApiHooks((id: string) =>
    request.delete<ApiResponse<Detail>>(`/mch/his/front-dept/${id}`)
  ),
  科室配置编辑: createApiHooks((params: Edit) =>
    request.put<ApiResponse<any>>(`/mch/his/front-dept`, params, {
      headers: { 'Content-Type': 'application/json' }
    })
  ),
  科室配置列表: createApiHooks(
    (params: {
      hisType: number;
      hisId?: string;
      districtNo?: string;
      keywords?: string;
    }) => request.get<ApiResponse<any>>('/mch/his/front-dept/tree', { params })
  ),
  查看挂号科室展示配置: createApiHooks(() =>
    request.get<ApiResponse<any>>(
      `/mch/his/hospital-config/base-settings/enableWisdomFrontDept`
    )
  ),
  科室配置数据状态查询: createApiHooks(
    (params: { hisType: number; hisId?: string }) =>
      request.get<ApiResponse<any>>(`/mch/his/front-dept/data-state`, {
        params
      })
  ),
  科室配置数据初始化: createApiHooks(
    (params: { hisType: number; hisId?: string }) =>
      request.get<ApiResponse<any>>(`/mch/his/front-dept/init`, { params })
  )
};
