import React, { useMemo, useState } from 'react';
import Sort, { ListItem } from '@components/Sort';
import { Button, message, Select } from 'antd';
import { CardLayout } from 'parsec-admin';
import useApi from '@pages/hospital/departmentSmart/api';
import useCampusApi from '@pages/hospital/campus/api';
import env from '@configs/env';
import { Space } from '@kqinfo/ui';

export default () => {
  const [hisType, setHisType] = useState<string>();
  const hisId = env.hisId;
  const {
    data: {
      data: { recordList: list }
    },
    loading: deptLoading
  } = useApi.科室管理列表({
    needInit: !!hisId && !!hisType,
    params: {
      hisId,
      hisType
    },
    initValue: {
      data: {
        recordList: []
      }
    }
  });

  const {
    data: { data: hisTypes }
  } = useCampusApi.医院开通的业务平台列表({
    deepCache: true,
    initValue: { data: [] }
  });

  const [aredId, setAreaId] = useState<number>();
  const dept = useMemo(() => {
    return list.find(x => x.id === aredId);
  }, [list, aredId]);

  const [deptId, setDeptId] = useState<number>();
  const [childId, setChildId] = useState<number>();
  const [childrenId, setChildrenId] = useState<number>();

  const child = useMemo(() => {
    return dept?.children?.find(x => x.id === deptId);
  }, [dept, deptId]);

  const children = useMemo(() => {
    const obj = dept?.children?.find(x => x.id === deptId);
    return obj?.children?.find(x => x.id === childId);
  }, [childId, dept?.children, deptId]);

  const lowChildren = useMemo(() => {
    const obj = dept?.children?.find(x => x.id === deptId);
    const obj2 = obj?.children?.find(x => x.id === childId);
    return obj2?.children?.find(x => x.id === childrenId);
  }, [childId, childrenId, dept?.children, deptId]);
  console.log(lowChildren, 'lowChildren');
  const { loading, request: request科室排序 } = useApi.科室排序({
    needInit: false
  });
  const [deptSort, setDeptSort] = useState<ListItem[]>([]);
  const [childSort, setChildSort] = useState<ListItem[]>([]);
  const [childrenSort, setChildrenSort] = useState<ListItem[]>([]);
  const [lowChildrenSort, setLowChildrenSort] = useState<ListItem[]>([]);
  return (
    <div>
      <CardLayout title='选择院区'>
        <Space size={20}>
          <Select
            options={(hisTypes || []).map(x => ({
              value: x.code,
              label: x.desc
            }))}
            onChange={(v: string) => {
              setHisType(v);
              setAreaId(undefined);
            }}
            style={{
              width: '200px'
            }}
            placeholder='请选择业务平台'
          />
          <Select
            placeholder='请选择院区'
            value={aredId}
            loading={deptLoading}
            disabled={deptLoading}
            onChange={v => {
              console.log(v);
              setAreaId(v);
            }}
            options={list?.map(x => {
              return {
                label: x.name,
                value: x.id
              };
            })}
            style={{
              width: '200px'
            }}
          />
        </Space>
      </CardLayout>
      {dept && (
        <CardLayout title='上级科室排序'>
          <Space alignItems={'center'} size={5}>
            <Space
              alignItems={'center'}
              justify={'center'}
              style={{
                borderRadius: '50%',
                background: '#fce799',
                height: '15px',
                width: '15px',
                color: '#fff',
                fontWeight: 'bold'
              }}>
              !
            </Space>
            <Space>
              操作说明：1、拖动科室到相应位置并保存，手机端将同步更新排序；2、点击具体科室进入下级科室排序
            </Space>
          </Space>
          <Sort
            onChange={list => {
              setDeptSort(list);
              setDeptId(undefined);
              setChildSort([]);
              console.log('onChange', list);
            }}
            selected={deptId + ''}
            onSelect={item => {
              console.log('onSelect', item);
              setDeptId(
                item.value === deptId + '' ? undefined : parseInt(item.value)
              );
            }}
            list={
              dept.children.map(item => {
                return {
                  label: item.name,
                  value: item.id + ''
                };
              }) || []
            }
          />
          <Button
            loading={loading}
            type='primary'
            style={{
              marginTop: '30px'
            }}
            onClick={() => {
              request科室排序({
                hisId,
                data: deptSort.map((item, index) => {
                  return {
                    id: parseInt(item.value),
                    sortNo: index
                  };
                })
              }).then(() => {
                message.success('成功');
              });
            }}>
            保存当前排序
          </Button>
        </CardLayout>
      )}
      {child && (
        <CardLayout title={`${child.name}下级科室排序`}>
          <Space alignItems={'center'} size={5}>
            <Space
              alignItems={'center'}
              justify={'center'}
              style={{
                borderRadius: '50%',
                background: '#fce799',
                height: '15px',
                width: '15px',
                color: '#fff'
              }}>
              !
            </Space>
            <Space>
              操作说明：1、拖动科室到相应位置并保存，手机端将同步更新排序
            </Space>
          </Space>
          <Sort
            onChange={list => {
              setChildSort(list);
              console.log('onChange', list);
            }}
            selected={childId + ''}
            onSelect={item => {
              setChildId(
                item.value === childId + '' ? undefined : parseInt(item.value)
              );
            }}
            list={
              child.children.map(item => {
                return {
                  label: item.name,
                  value: item.id + ''
                };
              }) || []
            }
          />
          <Button
            loading={loading}
            type='primary'
            style={{
              marginTop: '30px'
            }}
            onClick={() => {
              request科室排序({
                hisId,
                data: childSort.map((item, index) => {
                  return {
                    id: parseInt(item.value),
                    sortNo: index
                  };
                })
              }).then(() => {
                message.success('成功');
              });
            }}>
            保存当前排序
          </Button>
        </CardLayout>
      )}
      {children?.children?.length && (
        <CardLayout title={`${children.name}下级科室排序`}>
          <Space alignItems={'center'} size={5}>
            <Space
              alignItems={'center'}
              justify={'center'}
              style={{
                borderRadius: '50%',
                background: '#fce799',
                height: '15px',
                width: '15px',
                color: '#fff'
              }}>
              !
            </Space>
            <Space>
              操作说明：1、拖动科室到相应位置并保存，手机端将同步更新排序
            </Space>
          </Space>
          <Sort
            onChange={list => {
              setChildrenSort(list);
              console.log('onChange', list);
            }}
            selected={childrenId + ''}
            onSelect={item => {
              setChildrenId(
                item.value === childrenId + ''
                  ? undefined
                  : parseInt(item.value)
              );
            }}
            list={
              children.children.map(item => {
                return {
                  label: item.name,
                  value: item.id + ''
                };
              }) || []
            }
          />
          <Button
            loading={loading}
            type='primary'
            style={{
              marginTop: '30px'
            }}
            onClick={() => {
              request科室排序({
                hisId,
                data: childrenSort.map((item, index) => {
                  return {
                    id: parseInt(item.value),
                    sortNo: index
                  };
                })
              }).then(() => {
                message.success('成功');
              });
            }}>
            保存当前排序
          </Button>
        </CardLayout>
      )}
      {lowChildren?.children?.length && (
        <CardLayout title={`${lowChildren.name}下级科室排序`}>
          <Space alignItems={'center'} size={5}>
            <Space
              alignItems={'center'}
              justify={'center'}
              style={{
                borderRadius: '50%',
                background: '#fce799',
                height: '15px',
                width: '15px',
                color: '#fff'
              }}>
              !
            </Space>
            <Space>
              操作说明：1、拖动科室到相应位置并保存，手机端将同步更新排序
            </Space>
          </Space>
          <Sort
            onChange={list => {
              setLowChildrenSort(list);
              console.log('onChange', list);
            }}
            list={
              lowChildren.children.map(item => {
                return {
                  label: item.name,
                  value: item.id + ''
                };
              }) || []
            }
          />
          <Button
            loading={loading}
            type='primary'
            style={{
              marginTop: '30px'
            }}
            onClick={() => {
              request科室排序({
                hisId,
                data: lowChildrenSort.map((item, index) => {
                  return {
                    id: parseInt(item.value),
                    sortNo: index
                  };
                })
              }).then(() => {
                message.success('成功');
              });
            }}>
            保存当前排序
          </Button>
        </CardLayout>
      )}
    </div>
  );
};
