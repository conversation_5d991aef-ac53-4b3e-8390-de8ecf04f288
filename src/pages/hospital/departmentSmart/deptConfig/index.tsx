import React, { useCallback, useState, useEffect } from 'react';
import {
  CardLayout,
  ActionsWrap,
  LinkButton,
  useModal,
  handleSubmit,
  actionConfirm
} from 'parsec-admin';
import {
  Button,
  Radio,
  Form,
  Input,
  InputNumber,
  Cascader,
  TreeSelect,
  Table,
  Divider,
  Spin
} from 'antd';
import { Space, TransferChange } from '@kqinfo/ui';
import api from '@pages/hospital/campus/api';
import useApi, { Detail, DeptChildren } from '../api';
import env from '@configs/env';
import { useEffectState } from 'parsec-hooks';
import { ReactSortable } from 'react-sortablejs';
import styles from './index.module.less';
import permisstion from '@utils/permisstion';

const { Item } = Form;

const splitName = (name: string) => {
  return {
    name: name.split('-')[1],
    no: name.split('-')[0]
  };
};

const DeptConfig = () => {
  const [form] = Form.useForm();
  const hisId = env.hisId;
  const [dataSource, setDataSource] = useState([]);
  const [childrenSort, setChildrenSort] = useState<DeptChildren[]>([]);
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(false);

  const {
    data: {
      data: { recordList }
    }
  } = api.院区列表({
    initValue: { data: { recordList: [] } }
  });
  const {
    data: { data: statusData }
  } = useApi.科室配置数据状态查询({
    needInit: true,
    params: { hisType: 2, hisId }
  });
  // 基础树
  const {
    data: {
      data: { recordList: deptList }
    },
    request: deptListRequest
  } = useApi.科室管理列表({
    params: { hisId, hisType: 2 },
    needInit: false,
    initValue: {
      data: {
        data: {
          recordList: []
        }
      }
    }
  });
  // 科室树
  const {
    data: { data: deptTree },
    request: deptTreeRequest
  } = useApi.科室上级树查询({
    params: { hisId, hisType: 2 },
    needInit: false,
    initValue: {
      data: []
    }
  });
  const [tabId, setTabId] = useEffectState<any>(recordList?.[0]?.no);

  const filterArray = useCallback((arr: Array<any>): any => {
    return arr?.map(x => {
      return {
        label: x.name,
        value: x.id,
        children: x?.children ? filterArray(x?.children) : undefined
      };
    });
  }, []);

  const filterArrayCascader = useCallback((arr: Array<any>): any => {
    return arr?.map(x => {
      return {
        label: x.name,
        value: `${x.no}-${x.name}`,
        children: x?.children ? filterArrayCascader(x?.children) : undefined
      };
    });
  }, []);

  /**
   * 弹窗树的数据请求
   */
  const openCommonRequest = async () => {
    setLoading(true);
    const params = { hisId, hisType: 2 };
    await Promise.all([deptListRequest(params), deptTreeRequest(params)]);
    setLoading(false);
  };

  const queryTableData = React.useCallback(
    async (tabId: string, keywords: string) => {
      const params: any = {
        hisType: 2,
        hisId,
        districtNo: tabId
      };
      if (keywords) {
        params.keywords = keywords;
      }
      const res = await useApi.科室配置列表.request(params);
      setDataSource(res?.data ?? []);
    },
    [hisId]
  );

  const openAddModal = useModal(
    ({ id }) => {
      return {
        title: id ? '编辑科室' : '添加科室',
        width: 600,
        onSubmit: () => {
          return handleSubmit(async () => {
            const res = await form.validateFields();
            const params = {
              ...res,
              ...splitName(res.name),
              hisType: 2,
              districtNo: tabId,
              parent: Array.isArray(res.parent) ? res.parent : [res.parent]
            };
            if (id) {
              params.id = id;
              params.children = childrenSort;
            }
            const api = id
              ? useApi.科室配置编辑.request
              : useApi.科室配置新增.request;
            await api(params);
            // 重置表单
            form.resetFields();
            // 刷新表格数据
            queryTableData(tabId, '');
          });
        },
        children: (
          <>
            {id && <h3>科室信息</h3>}
            <Form
              form={form}
              onValuesChange={changeValues => {
                if ('name' in changeValues) {
                  form.setFieldsValue({ no: splitName(changeValues.name).no });
                }
              }}>
              <Item name='parent' required label='上级科室'>
                <TreeSelect
                  showSearch
                  style={{ width: '100%' }}
                  dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                  placeholder='请选择上级科室'
                  allowClear
                  // multiple
                  filterTreeNode={true}
                  treeNodeFilterProp='label'
                  treeData={filterArray(deptTree)}
                />
              </Item>
              <Item name='name' required label='科室名称'>
                <TransferChange
                  mode={'cascade'}
                  data={filterArrayCascader(deptList)}>
                  <Cascader
                    style={{ width: '100%' }}
                    placeholder='科室名称'
                    showSearch
                    changeOnSelect={true}
                    options={filterArrayCascader(deptList)}
                  />
                </TransferChange>
              </Item>
              <Item name='no' required label='科室编码'>
                <Input disabled={true} placeholder='科室自带编码' />
              </Item>
              <Item name='sortNo' required label='科室序号'>
                <InputNumber style={{ width: '100%' }} placeholder='请输入' />
              </Item>
            </Form>

            {id && (
              <div>
                <h3>子级科室排序</h3>
                <Space alignItems={'center'} size={5}>
                  <Space
                    alignItems={'center'}
                    justify={'center'}
                    style={{
                      borderRadius: '50%',
                      background: '#fce799',
                      height: '15px',
                      width: '15px',
                      color: '#fff',
                      fontWeight: 'bold'
                    }}>
                    !
                  </Space>
                  <Space>
                    操作说明：拖动科室到相应位置，手机端将同步更新排序（值越小越靠前）。
                  </Space>
                </Space>
                <ReactSortable
                  list={childrenSort}
                  setList={list => {
                    const newList = list.map((v, i) => {
                      return {
                        ...v,
                        sortNo: i + 1
                      };
                    });
                    setChildrenSort(newList);
                  }}
                  className={styles.sortBox}>
                  {childrenSort.map(item => (
                    <div key={item.id} className={styles.sortItem}>
                      {item.name}
                    </div>
                  ))}
                </ReactSortable>
              </div>
            )}
          </>
        )
      };
    },
    [childrenSort, tabId]
  );

  useEffect(() => {
    if (tabId) {
      setSearch('');
      queryTableData(tabId, '');
    }
  }, [tabId, queryTableData]);

  return (
    <CardLayout>
      <Space
        justify='space-between'
        alignItems='center'
        className={styles.page}>
        <Space alignItems='center'>
          <span style={{ width: '100px' }}>科室名称：</span>
          <Input
            placeholder='请输入科室名称'
            value={search}
            onChange={e => {
              setSearch(e.target.value);
            }}
          />
        </Space>
        <Space size={10}>
          <Button
            type='primary'
            onClick={() => {
              queryTableData(tabId, search);
            }}>
            查询
          </Button>
          <Button
            onClick={() => {
              setSearch('');
              queryTableData(tabId, '');
            }}>
            重置
          </Button>
        </Space>
      </Space>
      <Divider />
      <Space
        justify='space-between'
        alignItems='center'
        className={styles.tableBox}>
        <Space alignItems={'center'} size={20}>
          <Space>科室列表</Space>
          <Radio.Group
            onChange={val => {
              setTabId(val.target.value);
            }}
            value={tabId}
            buttonStyle='solid'
            optionType='button'>
            <Space flexWrap={'wrap'}>
              {recordList?.map(item => (
                <Radio.Button key={item.no} value={item.no}>
                  {item.name}
                </Radio.Button>
              ))}
            </Space>
          </Radio.Group>
        </Space>
        <Space size={20}>
          {statusData && (
            <Button
              onClick={async () => {
                await useApi.科室配置数据初始化.request({ hisType: 2, hisId });
                queryTableData(tabId, '');
              }}>
              初始数据
            </Button>
          )}
          {permisstion.deptConfigAdd && (
            <Button
              type={'primary'}
              onClick={async () => {
                await openCommonRequest();
                openAddModal();
              }}>
              添加科室
            </Button>
          )}
        </Space>
      </Space>
      <Table
        dataSource={dataSource ?? []}
        pagination={false}
        rowKey={(record: any) => {
          return `${record.pid}-${record.id}-${record.no}`;
        }}
        scroll={{ x: 500, y: 600 }}
        columns={[
          {
            title: '科室名称及编码',
            dataIndex: 'name',
            width: 250,
            render: (_, record) => {
              return <span>{`${record.name}-${record.no}`}</span>;
            }
          },
          {
            title: '所属业务平台',
            dataIndex: 'hisType',
            width: 250,
            render: item => {
              return '智慧医院';
            }
          },
          {
            title: '操作',
            fixed: 'right',
            width: 280,
            render: record => {
              return (
                <ActionsWrap max={99}>
                  {permisstion.deptConfigEdit && (
                    <LinkButton
                      onClick={async () => {
                        await openCommonRequest();
                        const res = await useApi.科室配置详情.request(
                          record.id
                        );
                        const data: Detail = res.data as Detail;
                        setChildrenSort(data?.children ?? []);
                        const values = {
                          ...data,
                          name: `${data?.no}-${data?.name}`
                        };
                        form.setFieldsValue(values);
                        openAddModal({ id: record.id });
                      }}>
                      编辑
                    </LinkButton>
                  )}
                  {permisstion.deptConfigDelete && (
                    <LinkButton
                      onClick={async () => {
                        actionConfirm(async () => {
                          await useApi.科室配置删除.request(record.id);
                          queryTableData(tabId, search);
                        }, '删除');
                      }}>
                      删除
                    </LinkButton>
                  )}
                </ActionsWrap>
              );
            }
          }
        ]}
      />
      {loading && (
        <div className={styles.mask}>
          <Spin size='large' />
        </div>
      )}
    </CardLayout>
  );
};

export default DeptConfig;
