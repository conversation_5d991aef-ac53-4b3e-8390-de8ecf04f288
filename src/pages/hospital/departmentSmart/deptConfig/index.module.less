.page {
  :global {
    .ant-table-content {
      .ant-table-thead {
        position: relative !important;
      }
    }
  }
}

.box {
  background-color: #fff;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border-radius: 6px;
  padding: 6px;
  display: flex;
}

.left {
  width: 40%;
  height: 100%;
  overflow: auto;
}
.right {
  width: 60%;
  height: 100%;
  overflow: auto;
  margin-left: 10px;
}

.leftItem {
  padding: 8px;
  box-sizing: border-box;
  border-bottom: 1px solid #ddd;
  cursor: pointer;
}

.leftItemActive {
  border-left: 2px solid #dd1f25;
  color: #dd1f25;
}

.rightItem {
  :global {
    .ant-collapse-header {
      padding: 0px !important;
    }
  }
}

.rightItemTitle {
  padding: 8px;
  border-bottom: 1px solid #ddd;
  cursor: pointer;
}

.rightThreeItemTitle {
  padding: 8px;
}

.sortBox {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16px;
}

.sortItem {
  border: 1px solid #0075d0;
  padding: 4px 8px;
  margin: 0 16px 16px 0;
  border-radius: 8px;
  color: #0075d0;
  cursor: pointer;
  min-width: 70px;
  text-align: center;
}

.tableBox {
  margin-bottom: 16px;
}

.mask {
  position: absolute;
  inset: 0;
  // background-color: #00000080;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
