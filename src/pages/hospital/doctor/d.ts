// 用于区分编辑的是哪种医生
export type docType = '1' | '2' | '3' | '4'; // 1医生 2护士 3职员 4药师
export const docTypes = [
  { value: '1', label: '医生' },
  { value: '2', label: '护士' },
  // { value: '3', label: '职员' },
  { value: '4', label: '药师' }
];
export const doctorlevels = [
  { value: '1', label: '主任医师' },
  { value: '2', label: '主任中医师' },
  { value: '3', label: '副主任医师' },
  { value: '4', label: '副主任中医师' },
  { value: '5', label: '主治医师' },
  { value: '6', label: '主治中医师' },
  { value: '7', label: '住院医师' },
  { value: '8', label: '医师' },
  { value: '9', label: '中医师' },
  { value: '10', label: '医士' },
  { value: '11', label: '中医士' },
  { value: '12', label: '主任技师' },
  { value: '13', label: '副主任技师' },
  { value: '14', label: '主管技师' },
  { value: '15', label: '技师' },
  { value: '16', label: '技士' },
  { value: '17', label: '副教授' }
];
export const druglevels = [
  { value: '1', label: '主任药师' },
  { value: '2', label: '副主任药师' },
  { value: '3', label: '主管药师' },
  { value: '4', label: '药师' },
  { value: '5', label: '中药师' },
  { value: '6', label: '药士' },
  { value: '7', label: '中药士' }
];
export const nurselevels = [
  { value: '1', label: '主任护师' },
  { value: '2', label: '副主任护师' },
  { value: '3', label: '主管护师' },
  { value: '4', label: '护师' },
  { value: '5', label: '护士' }
];
export const categories = [
  { value: '1', label: '临床' },
  { value: '2', label: '中医' },
  { value: '3', label: '口腔' },
  { value: '4', label: '公共卫生' }
];
export const prescriptionQualifications = [
  { value: '99', label: '无' },
  { value: '0', label: '具备所有权限' },
  { value: '1', label: '抗菌药物处方资格' },
  { value: '2', label: '麻精处方资格' }
];

export interface Docinfo {
  id: number;
  hisId: number;
  hisName: 'null';
  doctorId: '222111167';
  deptId: '003';
  deptName: '内二科一部1';
  name: '测算';
  sex: null;
  level: '1';
  grade: null;
  image: string;
  specialty: '12222222222222222222222222222222222222222222222222222';
  introduction: '啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊111111111111';
  pdeptName: null;
  pdeptId: '003';
  sortNo: 1;
  status: string;
  workingLife: 1;
  mobile: '15489512545';
  createTime: '2020-04-17 14:15:38';
  updateTime: '2020-04-17 14:34:26';
  extFields: null;
  hisDoctorName: null;
  qrTicket: null;
  qrContent: null;
  qrUrl?: string;
  circleImage: null;
  type: '1';
  deptmentId: null;
  deptmentName: '';
  consultationAuthority: '0';
  recommend: '0';
  educationTitle: null;
  consultCount: null;
  doctorInfoId: null;
  practiceScope: null;
  category: null;
  practiceLevel: null;
  practiceNumber: null;
  position: null;
  idNumber: null;
  auditTime: null;
  practiceMedicalInstitution: null;
  title: null;
  prescriptionQualification: null;
  policyNumber: null;
  underwritingUnit: null;
  insuranceStartDate: null;
  insuranceEndDate: null;
  insuranceDueDate: null;
  remark: null;
  startValidDate: null;
  endValidDate: null;
  doctorInfoVo: null;
  reviewAdmin: null;
  reviewDoctor: null;
  inquirys: Array<{
    amount: 100;
    createTime: '2019-09-04 21:34:27';
    deptId: '2018008';
    deptName: '皮ac';
    doctorId: '900';
    doctorName: '潘龙测试';
    hisDoctorId: '2218-900';
    hisId: 2218;
    hisName: '重庆医科大学附属儿童医院';
    id: 1567604067490;
    isFull: null;
    isOnDuty: string; // 0 1
    maxInquiry: 50;
    price: 100;
    remune: 200;
    type: string; // 1 2 3
    updateTime: '2019-09-04 21:34:27';
    sumResourceNumFromNowOn: number;
  }>;
  inquiryConfigParamList: null;
  ableType: null;
  score: 0;
  favoriteRate: null;
  serviceTimes: 0;
  completed: 0;
  favourTimes: 0;
  evaluated: 0;
  replyTime: null;
  timelyReply: '0%';
  evaluationLabel: null;
  replyLabel: null;
  onDuty: '1';
  isFull: '0';
  fans: 0;
  signatureImg: null;
}

export interface DocDetail {
  code: 0;
  msg: '条件查询医生详情成功';
  data: {
    doctorVoList: [
      {
        id: 1531129221571;
        hisId: 2218;
        hisName: 'null';
        doctorId: '222111167';
        deptId: '003';
        deptName: '内二科一部1';
        name: '测算';
        sex: null;
        level: '1';
        grade: null;
        image: string;
        specialty: '12222222222222222222222222222222222222222222222222222';
        introduction: '啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊111111111111';
        pdeptName: null;
        pdeptId: '003';
        sortNo: 1;
        status: '2';
        workingLife: 1;
        mobile: '15489512545';
        createTime: '2020-04-17 14:15:38';
        updateTime: '2020-04-20 12:49:49';
        moNames: string;
        extFields: 'null';
        hisDoctorName: null;
        qrTicket: null;
        qrContent: null;
        qrUrl: null;
        circleImage: null;
        type: '1';
        deptmentId: null;
        deptmentName: '';
        consultationAuthority: '0';
        recommend: null;
        educationTitle: null;
        consultCount: null;
        doctorInfoId: 121;
        practiceScope: '1';
        category: 'null';
        practiceLevel: '1';
        practiceNumber: 1;
        position: '1';
        idNumber: '500234202003106510';
        auditTime: '2020-04-17 00:00:00';
        practiceMedicalInstitution: '1';
        title: null;
        prescriptionQualification: '0';
        policyNumber: '1';
        underwritingUnit: '1';
        insuranceStartDate: '2020-04-17 00:00:00';
        insuranceEndDate: '2020-04-17 00:00:00';
        insuranceDueDate: null;
        remark: null;
        startValidDate: '2020-04-17 00:00:00';
        endValidDate: '2020-04-17 00:00:00';
        doctorInfoVo: {
          id: 121;
          name: '测算';
          practiceScope: '1';
          category: '口腔';
          practiceLevel: '1';
          practiceNumber: 1;
          position: '1';
          doctorId: '222111167';
          idNumber: '500234202003106510';
          auditTime: '2020-04-16T16:00:00.000+0000';
          practiceMedicalInstitution: '1';
          title: null;
          prescriptionQualification: '0';
          policyNumber: '1';
          underwritingUnit: '1';
          insuranceStartDate: '2020-04-17 00:00:00';
          insuranceEndDate: null;
          insuranceDueDate: null;
          createTime: '2020-04-17T06:15:38.000+0000';
          hisId: 2218;
          remark: null;
          startValidDate: '2020-04-17 00:00:00';
          endValidDate: '2020-04-17 00:00:00';
          certEncKey: null;
          certQrCode: null;
          certUserState: null;
          certSN: null;
          certDN: null;
          certStartTime: null;
          certEndTime: null;
          certIssuer: null;
          signatureImg: 'null';
        };
        reviewAdmin: null;
        reviewDoctor: null;
        inquirys: Array<{
          id: 1531129221571;
          hisId: 2218;
          hisName: 'null';
          deptId: '003';
          deptName: '内二科一部1';
          doctorId: '222111167';
          doctorName: null;
          type: string;
          remune: number;
          price: number;
          amount: number;
          sumResourceNumFromNowOn: number;
          isOnDuty: '1';
          maxInquiry: 50;
          createTime: '2020-04-20 12:49:49';
          updateTime: '2020-04-20 12:49:49';
          isFull: null;
          hisDoctorId: '2218-222111167';
        }>;
        inquiryConfigParamList: null;
        ableType: null;
        score: 0.0;
        favoriteRate: null;
        serviceTimes: 0;
        completed: 0;
        favourTimes: 0;
        evaluated: 0;
        replyTime: null;
        timelyReply: '0%';
        evaluationLabel: null;
        replyLabel: null;
        onDuty: '1';
        isFull: '0';
        fans: 0;
        signatureImg: null;
      }
    ];
    params: {
      categoryList: ['临床', '中医', '口腔', '公共卫生'];
      consultationAuthorityMap: {
        '0': '无权限';
        '1': '会诊审核员';
        '2': '会诊医师';
        '3': '会诊报告审核员';
      };
      prescriptionQualificationMap: {
        '0': '具备所有权限';
        '99': '无';
        '1': '抗菌药物处方资格';
        '2': '麻精处方资格';
      };
      doctorTitleList: ['主任医师', '副主任医师', '主治医师', '医师', '医士'];
    };
  };
}

export interface DeptInfo {
  id: 2;
  hisId: 2218;
  hisName: 'null';
  no: '001';
  name: '内科';
  hasChild: 0;
  pid: '-1';
  tel: null;
  sortNo: 0;
  level: 0;
  img: string;
  initials: 'N';
  status: '2';
  skill: null;
  createTime: '2018-07-09 15:01:32';
  updateTime: '2018-07-09 15:01:32';
  summary: '52';
  searchName: null;
  qrTicket: null;
  qrContent: null;
  qrUrl: null;
  qrTicketFollow: null;
  qrContentFollow: null;
  qrUrlFollow: null;
  employeeCount: null;
  medicalDepartment: null;
  menuLink: null;
  hospitalDeptNo: null;
  hospitalDeptName: null;
  standardDeptNo: '14';
  standardDeptName: '医疗美容科';
  parnetDept: null;
  qrcodeType: null;
}
