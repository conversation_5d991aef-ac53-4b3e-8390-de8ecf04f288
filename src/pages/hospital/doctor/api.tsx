import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { Docinfo, DocDetail, DeptInfo, docType } from './d';

export interface ApiResponse<D> {
  code: 0 | 200 | 999; // 999用户未登录
  msg: string | null;
  data?: D;
}

export interface ListApiRequestParams {
  pageNum?: number;
  numPerPage?: number;
}

export type ListApiResponseData<D> = ApiResponse<{
  currentPage: number;
  totalCount: number;
  recordList: D[];
}>;

export default {
  医生列表: createApiHooks(
    async (
      data: ListApiRequestParams & {
        sort?: string;
        hisId?: string;
        refundStatus?: string;
        beginTime?: string;
        endTime?: string;
        type: docType;
      }
    ) => {
      const res = await request.post<
        ApiResponse<{
          pages: {
            currentPage: number;
            totalCount: number;
            recordList: Docinfo[];
          };
        }>
      >('/mch/his/doctor/listPageDoctor', data);
      return {
        data: {
          code: res.data.code,
          data: res.data.data?.pages,
          msg: res.data.msg
        }
      };
    }
  ),
  科室列表: createApiHooks((params: { hisId: string }) => {
    return request.post<ApiResponse<Array<DeptInfo>>>(
      '/mch/his/doctor/topDepts',
      params
    );
  }),
  医生详情: createApiHooks(
    (params: { id: string; hisId: string; doctorId: string }) => {
      return request.post<DocDetail>('/mch/his/doctor/getByDoctor', params);
    }
  ),
  医生新增: createApiHooks((params: Docinfo) => {
    return request.post<ApiResponse<null>>('/mch/his/doctor/add', params);
  }),
  医生更新: createApiHooks(
    (
      params: {
        [N in keyof Docinfo]?: Docinfo[N];
      }
    ) => {
      return request.post('/mch/his/doctor/updateDoctor', params);
    }
  ),
  重置密码: createApiHooks(
    (params: { account: string; id: number; hisId: string }) => {
      return request.post('/mch/user/doctorAccount/resetpassword', params, {
        headers: {
          Accept: 'application/json, text/javascript, */*; q=0.01',
          'Content-Type': 'application/json; charset=UTF-8'
        }
      });
    }
  ),
  批量更新: createApiHooks(
    (params: { ids: string; operType: 'del' | 'valid' | 'invalid' }) => {
      return request.post('/mch/his/doctor/updateBatch', params);
    }
  ),
  批量导入: createApiHooks((params: { file: File; type: docType }) => {
    return request.post('/mch/his/doctor/import', params);
  })
};
