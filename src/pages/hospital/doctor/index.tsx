import React, { useMemo, useState } from 'react';
import {
  LinkButton,
  ArrSelect,
  ActionsWrap,
  actionConfirm
} from 'parsec-admin';
import useApi from './api';
import MyTableList from '@components/myTableList';
import { Button, Modal, message } from 'antd';
import {
  DeleteOutlined,
  PlusOutlined,
  DownloadOutlined,
  UploadOutlined,
  ImportOutlined
} from '@ant-design/icons';
import { useHistory, useLocation } from 'react-router';
import { Docinfo, doctorlevels, druglevels, nurselevels, docType } from './d';
import permisstion from '@utils/permisstion';

export default () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );
  const history = useHistory();
  const [isShowImport, setIsShowImoort] = useState(false);
  const { loading: loadingImport, request: requestImport } = useApi.批量导入({
    needInit: false
  });

  const locat = useLocation();
  // 1医生 2护士 3职员 4药师
  const type: docType = useMemo(() => {
    if (locat.pathname.includes('/doctor')) {
      return '1';
    }
    if (locat.pathname.includes('/nurse')) {
      return '2';
    }
    if (locat.pathname.includes('/drug')) {
      return '4';
    }
    return '1';
  }, [locat]);

  const name = useMemo(() => {
    if (type === '1') {
      return '医生';
    }
    if (type === '2') {
      return '护士';
    }
    if (type === '4') {
      return '药师';
    }
    return '医生';
  }, [type]);
  const levels = useMemo(() => {
    if (type === '1') {
      return doctorlevels;
    }
    if (type === '2') {
      return nurselevels;
    }
    if (type === '4') {
      return druglevels;
    }
    return doctorlevels;
  }, [type]);

  return (
    <>
      <MyTableList
        pageHeaderProps={false}
        action={
          <div>
            {permisstion.canAddDoctor('1') && (
              <Button
                type={'default'}
                icon={<PlusOutlined />}
                onClick={() => {
                  if (type === '1') {
                    history.push('/doctor/new');
                  }
                  if (type === '2') {
                    history.push('/nurse/new');
                  }
                  if (type === '4') {
                    history.push('/drug/new');
                  }
                }}>
                添加账号
              </Button>
            )}
            &nbsp; &nbsp;
            {permisstion.canImportDoctor('1') && (
              <Button
                type={'default'}
                icon={<ImportOutlined />}
                onClick={() => {
                  setIsShowImoort(true);
                }}>
                批量导入
              </Button>
            )}
          </div>
        }
        paginationExtra={
          <>
            {!!selectedRowKeys.length && (
              <div>
                <Button
                  icon={<DeleteOutlined />}
                  type={'primary'}
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.批量更新.request({
                          ids: selectedRowKeys.join(','),
                          operType: 'valid'
                        }),
                      '批量启用'
                    );
                  }}>
                  批量启用
                </Button>
                <span> </span>
                <Button
                  icon={<DeleteOutlined />}
                  type={'primary'}
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.批量更新.request({
                          ids: selectedRowKeys.join(','),
                          operType: 'invalid'
                        }),
                      '批量停用'
                    );
                  }}>
                  批量停用
                </Button>
                <span> </span>
                <Button
                  icon={<DeleteOutlined />}
                  type={'primary'}
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.批量更新.request({
                          ids: selectedRowKeys.join(','),
                          operType: 'del'
                        }),
                      '批量删除'
                    );
                  }}>
                  批量删除
                </Button>
                <span> 已选择{selectedRowKeys.length}条</span>
              </div>
            )}
          </>
        }
        getList={({ params }) =>
          useApi.医生列表.request({
            ...params,
            type
          })
        }
        tableTitle={`${name}管理`}
        rowSelection={{
          onChange: selectedRowKeys => setSelectedRowKeys(selectedRowKeys)
        }}
        columns={useMemo(
          () => [
            {
              title: `${name}姓名`,
              dataIndex: 'name',
              width: 100,
              search: true
            },
            {
              title: `${name}编号`,
              dataIndex: 'doctorId',
              width: 100
            },
            {
              title: `${name}职称`,
              dataIndex: 'level',
              width: 100,
              search: (
                <ArrSelect
                  options={(levels || []).map(level => {
                    return {
                      value: level.label,
                      children: level.label
                    };
                  })}
                />
              )
              // render: text => {
              //   return levels.find(x => x.value === text)?.label || '';
              // }
            },
            {
              title: '科室',
              dataIndex: 'deptName',
              width: 120,
              search: true
            },
            {
              title: '展示排序',
              dataIndex: 'sortNo',
              width: 100
            },
            {
              title: '图文问诊',
              dataIndex: 'pictureInquiry',
              width: 100,
              render: (text, record: Docinfo) => {
                return (record.inquirys || []).find(
                  x => x.type === '1' && x.isOnDuty === '1'
                )
                  ? '开启'
                  : '关闭';
              }
            },
            {
              title: '电话问诊',
              dataIndex: 'phoneInquiry',
              width: 100,
              render: (text, record: Docinfo) => {
                return (record.inquirys || []).find(
                  x => x.type === '2' && x.isOnDuty === '1'
                )
                  ? '开启'
                  : '关闭';
              }
            },
            {
              title: '视频问诊',
              dataIndex: 'videoInquiry',
              width: 100,
              render: (text, record: Docinfo) => {
                return (record.inquirys || []).find(
                  x => x.type === '3' && x.isOnDuty === '1'
                )
                  ? '开启'
                  : '关闭';
              }
            },
            {
              title: '视频号数',
              dataIndex: 'sumResourceNumFromNowOn',
              width: 100,
              render: (text, record: Docinfo) => {
                return (
                  (record.inquirys || []).find(x => x.type === '3')
                    ?.sumResourceNumFromNowOn || 0
                );
              }
            },
            {
              title: '问诊方式',
              dataIndex: 'inquiryMethod',
              width: 100,
              render: false,
              search: (
                <ArrSelect
                  options={[
                    {
                      value: '1',
                      children: '图文问诊'
                    },
                    {
                      value: '2',
                      children: '电话问诊'
                    },
                    {
                      value: '3',
                      children: '视频问诊'
                    }
                  ]}
                />
              )
            },
            {
              title: '首页推荐',
              dataIndex: 'recommend',
              width: 100,
              search: (
                <ArrSelect
                  options={[
                    {
                      value: '0',
                      children: '关闭'
                    },
                    {
                      value: '1',
                      children: '开启'
                    }
                  ]}
                />
              ),
              render: text => {
                return text === '1' ? '开启' : '关闭';
              }
            },
            {
              title: '创建时间',
              dataIndex: 'createTime',
              width: 150
            },
            {
              title: '状态',
              dataIndex: 'status',
              width: 100,
              search: (
                <ArrSelect
                  options={{
                    '1': '启用',
                    '2': '停用'
                  }}
                />
              ),
              render: text => {
                return text === '1' ? '启用' : '停用';
              }
            },
            {
              title: '操作',
              fixed: 'right',
              width: 300,
              render: (record: Docinfo) => (
                <ActionsWrap max={999}>
                  {permisstion.canUpdateDoctor('1') && (
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.医生更新.request({
                              id: record.id,
                              doctorId: record.doctorId,
                              hisId: record.hisId,
                              status: record.status === '1' ? '2' : '1'
                            }),
                          record.status === '1' ? '停用' : '启用'
                        );
                      }}>
                      {record.status === '1' ? '停用' : '启用'}
                    </LinkButton>
                  )}
                  {permisstion.canUpdateDoctor('1') && (
                    <LinkButton
                      onClick={() => {
                        if (type === '1') {
                          history.push(
                            `/doctor/${record.id}/${record.doctorId}`
                          );
                        }
                        if (type === '2') {
                          history.push(
                            `/nurse/${record.id}/${record.doctorId}`
                          );
                        }
                        if (type === '4') {
                          history.push(`/drug/${record.id}/${record.doctorId}`);
                        }
                      }}>
                      编辑
                    </LinkButton>
                  )}
                  <LinkButton
                    onClick={() => {
                      if (record.qrUrl) {
                        window.open(record.qrUrl);
                      }
                    }}>
                    二维码
                  </LinkButton>
                  {/* {permisstion.canResetDoctor && (
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.重置密码.request({
                              account: record.doctorId,
                              id: record.id,
                              hisId
                            }),
                          '重置密码'
                        );
                      }}>
                      重置密码
                    </LinkButton>
                  )} */}
                  {permisstion.canUpdateDoctor('1') && (
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            useApi.医生更新.request({
                              ...record,
                              status: '0'
                            }),
                          '删除'
                        );
                      }}>
                      删除
                    </LinkButton>
                  )}
                </ActionsWrap>
              )
            }
          ],
          [history, type, name, levels]
        )}
      />
      <Modal
        title={`批量导入${name}`}
        visible={isShowImport}
        maskClosable={false}
        footer={null}
        onCancel={() => {
          setIsShowImoort(false);
        }}>
        <div
          className='files-modal'
          style={{
            textAlign: 'center'
          }}>
          <Button
            icon={<DownloadOutlined />}
            onClick={() => {
              window.location.href =
                'https://ihoss.oss-cn-beijing.aliyuncs.com/file/%E6%A0%87%E5%87%86%E7%89%88%E5%8C%BB%E7%94%9F%E5%AF%BC%E5%85%A5%E4%BF%A1%E6%81%AF.xlsx';
            }}>
            下载导入模板
          </Button>
          <br />
          <br />
          <Button
            icon={<UploadOutlined />}
            loading={loadingImport}
            onClick={() => {
              const el = document.createElement('input');
              el.setAttribute('type', 'file');
              el.addEventListener('change', () => {
                const file = el.files?.[0];
                if (file) {
                  requestImport({ file, type })
                    .then(() => {
                      message.success('导入成功');
                    })
                    .catch(() => {
                      message.error('导入失败');
                    });
                }
              });
              el.click();
            }}>
            上传导入文件
          </Button>
        </div>
      </Modal>
    </>
  );
};
