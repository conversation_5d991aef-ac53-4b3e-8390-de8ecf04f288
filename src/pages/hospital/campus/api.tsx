import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ApiResponse1 } from '@apiHooks';

export default {
  院区列表: createApiHooks(
    (data: {
      hisType?: string | number;
      deptName?: string;
      status?: string;
      sort?: string;
      hisId?: string;
    }) =>
      request
        .get<
          ApiResponse1<
            {
              id: number;
              no: number;
              name: string;
              pid: number;
              img: string;
              sortNo: number;
              address: string;
              hospitalDeptNo: string;
            }[]
          >
        >('/mch/his/deptMain/district', {
          params: data
        })
        .then(res => {
          return {
            ...res,
            data: {
              code: res.data.code,
              msg: res.data.msg,
              data: {
                currentPage: 1,
                totalCount: res.data.data.length,
                recordList: res.data.data || []
              }
            }
          };
        })
  ),
  医院开通的业务平台列表: createApiHooks(() =>
    request.get<
      ApiResponse1<
        {
          code: number;
          desc: string;
        }[]
      >
    >('/mch/his/hospital-config/hisTypeList')
  )
};
