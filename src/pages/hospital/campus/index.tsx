import React, { useCallback, useMemo } from 'react';
import {
  ArrSelect,
  useModal,
  ActionsWrap,
  LinkButton,
  actionConfirm,
  handleSubmit,
  Form,
  CheckboxGroup
} from 'parsec-admin';
import useApi from '@src/pages/hospital/departmentSmart/api';
import api from './api';
import MyTableList from '@components/myTableList';
import { Button, Input } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useHistory } from 'react-router';
import env from '@configs/env';
// import BraftEditor from 'braft-editor';

export default () => {
  const history = useHistory();
  const hisId = env.hisId;

  const [form] = Form.useForm();
  const {
    data: { data: hisTypes }
  } = api.医院开通的业务平台列表({
    deepCache: true,
    initValue: { data: [] }
  });

  const filterArray = useCallback(
    (arr: Array<any>): any => {
      return arr
        ?.filter(item => {
          return (
            item.hisType === form.getFieldValue('hisType') ||
            item.hisType === (3 as any)
          );
        })
        ?.map(x => {
          return {
            label: x.name,
            value: x.id,
            children: x?.children ? filterArray(x?.children) : undefined
          };
        });
    },
    [form]
  );

  const switchPrizeModalVisible = useModal(
    ({ id }) => ({
      title: '新增/编辑院区',
      width: 800,
      form,
      onSubmit: ({ ...params }: any) => {
        params.hisId = hisId;
        params.hisName = 'null';
        console.log(params, 'params');
        params.hisTypeList = params.hisTypeList?.map(item => ({
          hisType: item
        }));
        return handleSubmit(
          () => (params.id ? useApi.修改科室 : useApi.新增科室).request(params),
          `${params.id ? '修改' : '新增'}`
        );
      },
      items: [
        {
          name: 'id',
          render: false
        },
        {
          label: '业务平台',
          required: true,
          name: 'hisTypeList',
          render: (
            <CheckboxGroup
              options={(hisTypes || [])?.map(item => ({
                value: item.code,
                label: item.desc
              }))}
            />
          )
        },
        {
          label: '院区编码',
          name: 'no',
          required: true,
          formItemProps: {
            rules: [
              {
                pattern: new RegExp(/^[\dA-Za-z]+$/),
                message: '只能输入数字和字母'
              }
            ]
          },
          render: () => <Input placeholder='请输入科室编码' />
        },
        {
          label: '院区名称',
          name: 'name',
          required: true
        },
        {
          label: '院区位置',
          name: 'address',
          required: true
        }
      ]
    }),
    [hisId]
  );

  return (
    <MyTableList
      tableTitle='院区列表'
      action={
        <ActionsWrap>
          <Button
            type={'default'}
            onClick={() => {
              history.push('/hospital/campus/sort');
            }}>
            排序
          </Button>
          <Button
            type={'primary'}
            icon={<PlusOutlined />}
            onClick={() => switchPrizeModalVisible()}>
            添加
          </Button>
        </ActionsWrap>
      }
      getList={({ params }) =>
        api.院区列表.request({
          ...params
        })
      }
      showTool={false}
      columns={useMemo(
        () => [
          {
            title: '所属业务平台',
            render: false,
            searchIndex: 'hisType',
            search: (
              <ArrSelect
                options={(hisTypes || []).map(x => ({
                  value: x.code,
                  children: x.desc
                }))}
                placeholder={'请选择业务平台'}
              />
            )
          },
          {
            title: '院区编码',
            dataIndex: 'no',
            width: 250
          },
          {
            title: '院区名称',
            dataIndex: 'name',
            searchIndex: 'deptName',
            width: 250,
            search: true
          },
          {
            title: '状态',
            dataIndex: 'status',
            search: <ArrSelect options={{ '1': '正常', ' 0': '停用' }} />,
            width: 100,
            render: v => {
              return v === 1 ? '正常' : '停用';
            }
          },
          {
            title: '创建时间',
            width: 180,
            dataIndex: 'createTime'
          },
          {
            title: '操作',
            fixed: 'right',
            width: 280,
            render: record => {
              return (
                <ActionsWrap max={99}>
                  <LinkButton
                    onClick={() => {
                      useApi.科室详情
                        .request({
                          hisType: record.hisType,
                          id: record.id
                        })
                        .then(r => {
                          switchPrizeModalVisible({
                            ...r?.data,
                            hisTypeList: r?.data?.hisTypeList?.map(
                              item => item.hisType
                            )
                          });
                        });
                    }}>
                    编辑
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          useApi.科室启用停用.request({
                            id: record.id
                          }),
                        record.status === 1 ? '停用' : '启用'
                      );
                    }}>
                    {record.status === 1 ? '停用' : '启用'}
                  </LinkButton>
                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          useApi.科室删除.request({
                            id: record.id
                          }),
                        '删除'
                      );
                    }}>
                    删除
                  </LinkButton>
                </ActionsWrap>
              );
            }
          }
        ],
        [hisTypes, switchPrizeModalVisible]
      )}
    />
  );
};
