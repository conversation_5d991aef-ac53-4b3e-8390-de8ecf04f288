import React, { useState } from 'react';
import Sort, { ListItem } from '@components/Sort';
import {
  Button,
  message
  // Select
} from 'antd';
import { CardLayout } from 'parsec-admin';
import useApi from '@pages/hospital/departmentSmart/api';
import env from '@configs/env';
import api from '@pages/hospital/campus/api';
import { useHistory } from 'react-router';
import { Space } from '@kqinfo/ui';
// import { Space } from '@kqinfo/ui';
export default () => {
  const history = useHistory();
  const hisId = env.hisId;
  // const {
  //   data: { data: hisTypes }
  // } = api.医院开通的业务平台列表({
  //   deepCache: true,
  //   initValue: { data: [] }
  // });
  // const [aredId, setAreaId] = useState<number>();
  const {
    data: {
      data: { recordList }
    }
  } = api.院区列表({
    // params: { hisType: aredId },
    initValue: { data: { recordList: [] } }
    // needInit: !!aredId
  });

  const [deptId, setDeptId] = useState<number>();

  const { loading, request: request科室排序 } = useApi.科室排序({
    needInit: false
  });
  const [deptSort, setDeptSort] = useState<ListItem[]>([]);
  return (
    <div>
      {/*<CardLayout title='选择院区'>*/}
      {/*  <Space vertical size={10}>*/}
      {/*    <Select*/}
      {/*      placeholder='请选择院区'*/}
      {/*      value={aredId}*/}
      {/*      onChange={setAreaId}*/}
      {/*      options={hisTypes?.map(x => {*/}
      {/*        return {*/}
      {/*          label: x.desc,*/}
      {/*          value: x.code*/}
      {/*        };*/}
      {/*      })}*/}
      {/*      style={{*/}
      {/*        width: '200px'*/}
      {/*      }}*/}
      {/*    />*/}
      {/*    <Space size={5}>*/}
      {/*      <Space*/}
      {/*        alignItems={'center'}*/}
      {/*        justify={'center'}*/}
      {/*        style={{*/}
      {/*          backgroundColor: '#f3cf99',*/}
      {/*          borderRadius: '50%',*/}
      {/*          color: '#fff',*/}
      {/*          width: '15px',*/}
      {/*          height: '15px'*/}
      {/*        }}>*/}
      {/*        !*/}
      {/*      </Space>*/}
      {/*      操作说明：1、拖动院区到相应位置并保存，手机端将同步更新排序。*/}
      {/*    </Space>*/}
      {/*  </Space>*/}
      {/*</CardLayout>*/}
      {(recordList || []).length !== 0 && (
        <CardLayout title='院区排序'>
          <Space alignItems={'center'} size={5}>
            <Space
              alignItems={'center'}
              justify={'center'}
              style={{
                borderRadius: '50%',
                background: '#fce799',
                height: '15px',
                width: '15px',
                color: '#fff',
                fontWeight: 'bold'
              }}>
              !
            </Space>
            <Space>
              操作说明：1、拖动院区到相应位置并保存，手机端将同步更新排序。
            </Space>
          </Space>
          <Sort
            onChange={list => {
              setDeptSort(list);
              setDeptId(undefined);
            }}
            selected={deptId + ''}
            onSelect={item => {
              setDeptId(
                item.value === deptId + '' ? undefined : parseInt(item.value)
              );
            }}
            list={
              recordList?.map(item => {
                return {
                  label: item.name,
                  value: item.id + ''
                };
              }) || []
            }
          />
          <Button
            loading={loading}
            type='primary'
            style={{
              marginTop: '30px'
            }}
            onClick={() => {
              request科室排序({
                hisId,
                data: deptSort.map((item, index) => {
                  return {
                    id: parseInt(item.value),
                    sortNo: index
                  };
                })
              }).then(() => {
                message.success('成功');
              });
            }}>
            保存当前排序
          </Button>
          <Button
            style={{
              marginTop: '30px',
              marginLeft: '30px'
            }}
            onClick={() => {
              history.goBack();
            }}>
            取消
          </Button>
        </CardLayout>
      )}
    </div>
  );
};
