import React, { useMemo, useState } from 'react';
import { LinkButton, ArrSelect, ActionsWrap, DayPicker } from 'parsec-admin';
import MyTableList from '@components/myTableList';
import { Button, message, Popover } from 'antd';
import { useHistory } from 'react-router';
import api from '@pages/hospital/campus/api';
import { Space, Tag } from '@kqinfo/ui';
import { Docinfo } from '@pages/hospital/doctorSmart/d';
import apis from './apis';
import storage from '@utils/storage';
import moment from 'moment';
export const enumType = {
  1: '图文',
  2: '电话',
  3: '视频'
};
const ScheduleState = {
  1: '已排班',
  0: '未排班'
};
export default () => {
  const isShow = useMemo(
    () => storage.get('hospital-config')?.inquirySchedulingSource === 'HIS',
    []
  );
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );
  const [selectedList, setSelectedList] = useState<Docinfo[]>([]);
  const history = useHistory();
  const {
    data: { data: hisTypes }
  } = api.医院开通的业务平台列表({
    deepCache: true,
    initValue: { data: [] }
  });
  const { loading, request } = apis.同步指定医生挂号费({
    needInit: false
  });
  return (
    <>
      <MyTableList
        exportExcelButton
        pageHeaderProps={false}
        getList={({ params }) => {
          const { scheduleDate }: any = params;
          if (!scheduleDate) {
            //需要一进来页面就查询今天的排班
            params.scheduleDate = moment().format('YYYY-M-D');
          }
          delete params.sort;
          return apis.医生列表.request({
            ...params,
            type: '1',
            hisType: '1',
            showInquiry: true
          });
        }}
        action={
          <>
            {isShow && (
              <Button
                type={'primary'}
                onClick={() => {
                  request().then(() => {
                    message.success('同步成功！');
                  });
                }}
                loading={loading}>
                同步HIS排班
              </Button>
            )}
            <Button
              onClick={() => {
                if (selectedRowKeys?.length) {
                  const arr = selectedList.map(v => {
                    return {
                      doctorId: v.doctorId,
                      deptNo: v.hisTypeList?.[0]?.deptList?.[0]?.no
                    };
                  });
                  storage.set('scheduleIds', arr);
                  history.push(`/signalSource/pictureSchedule/false/1`);
                } else {
                  message.info('当前没有选择');
                }
              }}>
              批量图文排班
            </Button>
          </>
        }
        tableTitle={`医生列表`}
        rowSelection={{
          onChange: (_, selectedRows) => {
            const arr = selectedRows.filter(v => v?.scheduleState === 0);
            if (arr.length !== selectedRows?.length) {
              message.info('已排班的医生不可勾选！');
            }
            setSelectedList(arr);
            setSelectedRowKeys(arr.map(l => l.id));
          },
          selectedRowKeys
        }}
        showExpand={false}
        rowKey={'id'}
        columns={useMemo(
          () => [
            {
              title: '科室名称',
              searchIndex: 'deptName',
              search: true,
              render: false
            },
            {
              title: `医生姓名`,
              dataIndex: 'name',
              width: 160,
              search: true
            },
            {
              title: `医生编号`,
              dataIndex: 'doctorId',
              width: 100
            },
            {
              title: `医生职称`,
              dataIndex: 'level',
              width: 120,
              search: true
            },
            {
              title: '科室',
              dataIndex: 'deptName',
              width: 180,
              excelRender: (v, r: Docinfo) => {
                let arr: any[] = [];
                for (let i = 0; i < r.hisTypeList.length; i++) {
                  arr = [
                    ...arr,
                    ...r.hisTypeList[i].deptList.map(item => item.name)
                  ];
                }
                return arr.join(',');
              },
              render: (v, r: Docinfo) => {
                let arr: any[] = [];
                for (let i = 0; i < r.hisTypeList.length; i++) {
                  arr = [
                    ...arr,
                    ...r.hisTypeList[i].deptList.map(item => ({
                      ...item,
                      hisType: r.hisTypeList[i].hisType
                    }))
                  ];
                }
                let arr2: any[] = [];
                if (arr.length > 2) {
                  arr2 = arr.slice(2);
                  arr = arr.slice(0, 2);
                }
                return (
                  <Space vertical size={5}>
                    {arr.map(item => (
                      <Space alignItems={'center'} key={item.id} size={5}>
                        <Space style={{ width: '50%' }}>{item.name}</Space>
                        <Tag
                          style={{
                            width: '90px',
                            height: '16px',
                            fontSize: '12px'
                          }}
                          ghost
                          block
                          color={
                            item.hisType === 1
                              ? '#108EE9'
                              : item.hisType === 2
                              ? '#F59A23'
                              : item.hisType === 4
                              ? '#D95E38'
                              : '#389e0d'
                          }>
                          {
                            hisTypes?.find(
                              innerItem => innerItem?.code === item.hisType
                            )?.desc
                          }
                        </Tag>
                      </Space>
                    ))}
                    {arr2.length > 0 && (
                      <Popover
                        content={() =>
                          arr2.map(item => (
                            <Space alignItems={'center'} key={item.id} size={5}>
                              <Space>{item.name}</Space>
                              <Tag
                                style={{
                                  width: '60px',
                                  height: '16px',
                                  fontSize: '12px'
                                }}
                                ghost
                                block
                                color={
                                  item.hisType === 1
                                    ? '#108EE9'
                                    : item.hisType === 2
                                    ? '#F59A23'
                                    : '#D95E38'
                                }>
                                {item.hisType === 1
                                  ? '互联网'
                                  : item.hisType === 2
                                  ? '智慧'
                                  : '随访'}
                              </Tag>
                            </Space>
                          ))
                        }
                        trigger='hover'>
                        <Button>...</Button>
                      </Popover>
                    )}
                  </Space>
                );
              }
            },
            {
              title: '创建时间',
              dataIndex: 'createTime',
              width: 150
            },
            {
              title: '排班时间',
              searchIndex: 'scheduleDate',
              search: (
                <DayPicker
                  allowClear={false}
                  defaultValue={moment() as any}
                  valueFormat={'YYYY-M-D'}
                />
              ),
              hidden: true
            },
            {
              title: '是否排班',
              width: 100,
              dataIndex: 'scheduleState',
              search: <ArrSelect options={ScheduleState} />,
              render: v => ScheduleState[v] || '-'
            },
            {
              title: '服务开通情况',
              dataIndex: 'inquirys',
              width: 210,
              render: v => {
                const arr = Object.keys(enumType);
                if (v && v?.length) {
                  const arr2: string[] = [];
                  v.forEach(item => {
                    if (arr.includes(item?.type) && item?.isOnDuty === '1') {
                      arr2.push(enumType[item?.type] + '问诊');
                    }
                  });
                  return arr2.join(',');
                }
                return '-';
              }
            },
            {
              title: '状态',
              dataIndex: 'status',
              width: 100,
              searchIndex: 'status',
              search: (
                <ArrSelect
                  options={{
                    '0': '停用',
                    '1': '启用'
                  }}
                />
              ),
              render: (text, r: any) => {
                return text === 0 ? '停用' : '启用';
              }
            },
            {
              title: '操作',
              fixed: 'right',
              width: 230,
              render: (record: Docinfo) => {
                return (
                  <ActionsWrap max={999}>
                    <LinkButton
                      onClick={() =>
                        history.push(
                          `/signalSource/videoSchedule/${record.id}/3`
                        )
                      }>
                      视频问诊排班
                    </LinkButton>
                    <LinkButton
                      onClick={() =>
                        history.push(
                          `/signalSource/pictureSchedule/${record.id}/1`
                        )
                      }>
                      图文问诊排班
                    </LinkButton>
                  </ActionsWrap>
                );
              }
            }
          ],
          [hisTypes, history]
        )}
      />
    </>
  );
};
