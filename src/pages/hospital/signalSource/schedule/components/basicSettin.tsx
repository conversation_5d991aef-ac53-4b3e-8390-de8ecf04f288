import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo
} from 'react';
import { Form, Input, Space, Switch } from 'antd';
import { DetailLayout } from 'parsec-admin';
import './index.less';
import { enumType } from '@pages/hospital/signalSource';

export default forwardRef(
  ({ serviceSettin, type }: { serviceSettin: any; type: string }, ref) => {
    const [form] = Form.useForm();
    useEffect(() => {
      if (Object.keys(serviceSettin)) {
        if (serviceSettin?.inquiryList?.length) {
          const arr = serviceSettin?.inquiryList.filter(
            item => item.type === type
          );
          if (arr?.length) {
            arr.forEach(item => {
              item.remune = item.remune / 100;
              item.amount = item.amount / 100;
              item.isOnDuty = Number(item?.isOnDuty || '0');
            });
            form.setFieldValue('list', arr);
          }
        }
      }
    }, [serviceSettin, form, type]);
    useImperativeHandle(ref, () => ({
      submit: () => {
        return form.validateFields();
      }
    }));
    const deptName = useMemo(() => {
      if (serviceSettin && serviceSettin?.hisTypeList?.length) {
        if (serviceSettin?.hisTypeList[0]?.deptList?.length) {
          return serviceSettin?.hisTypeList[0]?.deptList[0]?.name;
        }
      }
      return '';
    }, [serviceSettin]);
    return (
      <Form form={form}>
        <Form.List name={'list'}>
          {fields => (
            <DetailLayout
              className={'formListBox'}
              cardsProps={[
                ...fields.map(({ key, name, ...restField }) => ({
                  title: deptName,
                  children: (
                    <Space key={key} className={'spaceBox'}>
                      <Form.Item
                        {...restField}
                        valuePropName={'checked'}
                        label={`${enumType[type]}问诊`}
                        name={[name, 'isOnDuty']}>
                        <Switch />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        label={'执行价格'}
                        name={[name, 'remune']}
                        rules={[{ required: true, message: '请输入执行价格' }]}>
                        <Input placeholder='请输入价格' addonAfter={'元'} />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        label={'展示原价'}
                        name={[name, 'amount']}>
                        <Input placeholder='请输入价格' addonAfter={'元'} />
                      </Form.Item>
                    </Space>
                  )
                }))
              ]}
            />
          )}
        </Form.List>
      </Form>
    );
  }
);
