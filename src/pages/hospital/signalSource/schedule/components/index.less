.formListBox {
  margin: 0 !important;
  > .ant-card {
    > .ant-card-head {
      padding: 0;
    }
    > .ant-card-body {
      padding: 10px 0;
    }
  }
}
.spaceBox {
  > .ant-space-item {
    > .ant-form-item {
      > .ant-row {
        flex-flow: nowrap;
      }
    }
  }
}
.check {
  background-color: #2986f0;
  color: white !important;
}
.uncheck {
  background-color: #9f9e9e;
  color: #ffffff;
}
.date1 {
  width: 30px;
  height: 30px;
  line-height: 30px;
  margin: 0 auto;
  border-radius: 50%;
}
.dateBox {
  > .ant-picker-panel {
    .ant-picker-body {
      > .ant-picker-content {
        .ant-picker-cell {
          .ant-picker-calendar-date-content {
            height: 10px;
            position: relative;
          }
          .ant-badge-status {
            position: absolute;
            font-size: 12px;
            top: -70%;
            left: 40%;
            > .ant-badge-status-text {
              display: none;
            }
          }
        }
      }
    }
  }
}
