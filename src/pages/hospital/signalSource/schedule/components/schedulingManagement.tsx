import styled from 'styled-components';
import {
  Badge,
  BadgeProps,
  Button,
  Form,
  Modal,
  Typography,
  Empty,
  InputNumber,
  message,
  TimePicker,
  Spin,
  Switch
} from 'antd';
import moment, { Moment } from 'moment';
import React, { useState, useMemo } from 'react';
import './index.less';
import CheckCalendar from './checkCalendar';
import { actionConfirm, ArrSelect, DetailLayout } from 'parsec-admin';
import { DeleteOutlined, DiffOutlined, SaveOutlined } from '@ant-design/icons';
import apis from '@pages/hospital/signalSource/apis';
import permisstion from '@utils/permisstion';
import storage from '@utils/storage';
import { ScheduleIds } from '@configs/d';
const { Paragraph } = Typography;
export default ({
  isBatch,
  id,
  type,
  selectOption
}: {
  isBatch: boolean;
  id: string;
  type: string;
  selectOption: { label: string; value: string | number }[];
}) => {
  const now = moment().format('YYYY-MM-DD');
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [showDate, setShowDate] = useState<Moment>(moment());
  const [selectDate, setSelectDate] = useState<string[]>([]);
  const [selectScheduleItem, setSelectScheduleItem] = useState<string[]>([]);
  const isShow = useMemo(() => {
    const formatDate2 = showDate.format('YYYY-MM-DD');
    return now <= formatDate2;
  }, [showDate, now]);
  const scheduleIds = useMemo(() => {
    if (isBatch) {
      return storage.get('scheduleIds') || [];
    }
    return [];
  }, [isBatch]);
  const {
    data: { data: dateList },
    request: getDateList
  } = apis.日历高亮({
    params: {
      doctorId: id,
      type,
      deptNo: selectOption[0]?.value || ''
    },
    initValue: { data: [] },
    needInit: !!selectOption[0]?.value && !isBatch
  });
  const {
    data: { data: dateListBatch },
    request: getDateListBatch
  } = apis.批量排班日历高亮({
    params: {
      doctor: scheduleIds,
      type
    },
    initValue: { data: [] },
    needInit: scheduleIds.length > 0 && isBatch
  });
  const { request: Reuse, loading: reuseLoading } = apis.复用排班({
    needInit: false
  });
  const { request: ReuseBatch, loading: reuseLoadingBatch } = apis.批量复用排班(
    {
      needInit: false
    }
  );
  const {
    data: { data: detailList },
    request: getDetail
  } = apis.排班详情({
    params: {
      doctorId: id,
      type,
      deptNo: selectOption[0]?.value || '',
      visitDate: showDate.format('YYYY-MM-DD')
    },
    initValue: { data: [] },
    needInit: !!selectOption[0]?.value && !isBatch
  });
  const {
    data: { data: detailListBatch },
    request: getDetailBatch
  } = apis.批量排班详情({
    params: {
      type,
      visitDate: showDate.format('YYYY-MM-DD'),
      doctor: scheduleIds
    },
    initValue: { data: [] },
    needInit: isBatch && scheduleIds.length > 0
  });
  const showDetailList: any[] = useMemo(() => {
    if (isBatch) {
      return (detailListBatch || []).map(v => {
        return {
          ...v,
          id: v.startTime
        };
      });
    }
    return detailList || [];
  }, [detailList, detailListBatch, isBatch]);
  const { request: createRequest } = apis.创建排班({
    needInit: false
  });
  const { request: createRequestBatch } = apis.批量创建排班({
    needInit: false
  });
  const [createLoading, setCreateLoading] = useState(false);
  // const showList: any[] = useMemo(() => {
  //   if (detailList?.length) {
  //     const arr: { title: string; children: any[] }[] = [];
  //     detailList.forEach(item => {
  //       const isb = arr.find(item2 => item2.title === item.deptName);
  //       if (isb) {
  //         isb.children.push(item);
  //       } else {
  //         arr.push({ title: item.deptName, children: [item] });
  //       }
  //     });
  //     return arr;
  //   }
  //   return [];
  // }, [detailList]);
  const getListData = (value: Moment) => {
    let listData;
    const formatDate2 = value.format('YYYY-MM-DD');
    if (((isBatch ? dateListBatch : dateList) || []).includes(formatDate2)) {
      listData = 'processing';
    }
    return listData || '';
  };

  const dateCellRender = (value: Moment) => {
    const listData = getListData(value);
    return <Badge status={listData as BadgeProps['status']} />;
  };
  const dateFullCellRender = (value: Moment) => {
    const isCheck = selectDate.includes(value.format('YYYY-MM-DD'));
    const unCheck = value.format('YYYY-MM-DD') < now;
    return (
      <div
        onClick={() => {
          if (!unCheck) {
            if (isCheck) {
              setSelectDate(prevState =>
                prevState.filter(v => v !== value.format('YYYY-MM-DD'))
              );
            } else {
              setSelectDate(prevState => [
                ...prevState,
                value.format('YYYY-MM-DD')
              ]);
            }
          } else {
            message.warning('不能选择过期的日期');
          }
        }}
        className={
          unCheck ? 'uncheck date1' : isCheck ? 'check date1' : 'date1'
        }>
        {value.format('D')}
      </div>
    );
  };
  function getScheduleItems(arr: any[] = []): JSX.Element[] {
    return arr.map(childrenItem => {
      const isSelect = selectScheduleItem.includes(childrenItem?.id);
      let show = '';
      if (childrenItem?.startTime) {
        show += moment(childrenItem?.startTime).format('HH:mm');
      }
      if (childrenItem?.endTime) {
        show += '-' + moment(childrenItem?.endTime).format('HH:mm');
      }
      const isCheck =
        !!childrenItem?.leftResourceNum || !!childrenItem?.leftNum;
      show +=
        '排:' +
        (childrenItem?.totalResourceNum || childrenItem?.totalNum || 0) +
        '余:' +
        (childrenItem?.leftResourceNum || childrenItem?.leftNum || 0);
      const className = !isCheck
        ? 'scheduleInformation uncheck'
        : isSelect
        ? 'scheduleInformation check'
        : 'scheduleInformation';
      return (
        <div
          key={childrenItem.id}
          onClick={() => {
            if (isCheck) {
              if (isSelect) {
                setSelectScheduleItem(prevState =>
                  prevState.filter(v => v !== childrenItem?.id)
                );
              } else {
                setSelectScheduleItem(prevState => [
                  ...prevState,
                  childrenItem?.id
                ]);
              }
            }
          }}>
          <Paragraph
            className={className}
            ellipsis={{
              rows: 1,
              tooltip: `${show}`
            }}>
            {show}
          </Paragraph>
        </div>
      );
    });
  }
  const { loading: loadingPublish, request: requestPublish } = apis.发布排班({
    needInit: false
  });
  const {
    data: { data: totalData },
    request: getTotal
  } = apis.排班统计({
    params: {
      doctorId: id,
      type,
      deptNo: selectOption[0]?.value || '',
      visitDate: showDate.format('YYYY-MM-DD')
    },
    needInit: !isBatch
  });

  const init = async () => {
    const visitDate = showDate.format('YYYY-MM-DD');
    if (isBatch) {
      const params = {
        doctor: scheduleIds,
        type
      };
      await getDateListBatch(params);
      await getDetailBatch({
        ...params,
        visitDate: showDate.format('YYYY-MM-DD')
      });
    } else {
      let { dept } = form.getFieldsValue();
      if (!dept) {
        dept = selectOption[0]?.value || '';
      }

      const params = {
        doctorId: id,
        type,
        deptNo: dept
      };
      await getDateList(params);
      await getDetail({
        ...params,
        visitDate
      });
      await getTotal({ ...params, visitDate });
    }
  };
  return (
    <Spin spinning={loadingPublish}>
      <Box>
        <div>
          <div className={'title'}> 排班日期</div>

          <CheckCalendar
            value={showDate}
            dateCellRender={dateCellRender}
            onSelect={e => {
              setShowDate(e);
            }}
          />

          <div className={'prompt'}>
            注：日期置灰表示历史日期不可排班，小蓝点表示已排版，小灰点表示已排班且号源已被挂满。
          </div>
          {!isBatch && (
            <div className={'totalBox'}>
              <div>{'今日号源：' + totalData?.totalResourceNum || ''}</div>
              <div>{'已问诊号源：' + totalData?.usedResourceNum || ''}</div>
              <div>{'剩余号源：' + totalData?.leftResourceNum || ''}</div>
              <div>{'取消号源：' + totalData?.canceledResourceNum || ''}</div>
            </div>
          )}
        </div>
        <div>
          <div className={'title'}>{showDate.format('ll') + '排班详情'}</div>
          <div className={'formBox'}>
            <Form form={form} layout={'inline'}>
              {!isBatch && (
                <Form.Item
                  initialValue={
                    selectOption.length ? selectOption[0]?.value : undefined
                  }
                  rules={[{ required: true, message: '请选择科室' }]}
                  label={'科室'}
                  name={'dept'}>
                  <ArrSelect
                    placeholder={'请选择科室'}
                    options={selectOption}
                    style={{ width: 100 }}
                  />
                </Form.Item>
              )}
              <Form.Item
                label={'排班时间'}
                name={'date'}
                rules={[{ required: true, message: '请选择排班时间' }]}>
                <TimePicker.RangePicker
                  style={{ width: '187px' }}
                  format={'HH:mm'}
                />
              </Form.Item>
              <Form.Item
                label={'间隔时间'}
                name={'minuteInterval'}
                rules={[{ required: true, message: '请输入间隔时间' }]}>
                <InputNumber
                  style={{ maxWidth: '190px' }}
                  placeholder='请输入间隔时间'
                  min={0}
                  controls={false}
                  addonAfter={'分钟'}
                />
              </Form.Item>
              <Form.Item
                label={'放号数量'}
                name={'totalResourceNum'}
                rules={[{ required: true, message: '请输入放号数量' }]}>
                <InputNumber
                  style={{ minWidth: '67px', maxWidth: '100px' }}
                  controls={false}
                  placeholder='请输入放号数量'
                />
              </Form.Item>
              {isBatch && (
                <Form.Item
                  label={'是否覆盖已有排班'}
                  name={'cover'}
                  initialValue={0}
                  valuePropName={'checked'}>
                  <Switch checkedChildren='是' unCheckedChildren='否' />
                </Form.Item>
              )}
              {permisstion.creatScheduleShift && (
                <Form.Item style={{ margin: '0 10px' }}>
                  <Button
                    type={'primary'}
                    loading={createLoading}
                    disabled={!isShow}
                    onClick={() => {
                      form.validateFields().then(res => {
                        setCreateLoading(true);
                        const { dept } = res;
                        const visitDate = showDate.format('YYYY-MM-DD');
                        res.startTime =
                          visitDate + ' ' + res.date[0].format('LTS');
                        res.endTime =
                          visitDate + ' ' + res.date[1].format('LTS');
                        delete res.date;
                        delete res.dept;

                        let doctor: ScheduleIds[] = [
                          { doctorId: id, deptNo: dept }
                        ];
                        if (isBatch) {
                          res.cover = res?.cover ? 1 : 0;
                          doctor = scheduleIds;
                        }
                        const params = {
                          ...res,
                          type,
                          doctor,
                          visitDate
                        };
                        (isBatch
                          ? createRequestBatch(params)
                          : createRequest(params)
                        )
                          .then(async () => {
                            await init();
                            message.success('生成排班成功');
                          })
                          .finally(() => {
                            setCreateLoading(false);
                          });
                      });
                    }}>
                    生成排班
                  </Button>
                </Form.Item>
              )}
            </Form>
            <div className={'prompt'}>
              注：排班时间指需要排班的时间段（如：8:00-12:00），间隔时间指在排班时间内需间隔多长时间排一次号（如：15分钟），放号数量指在每一个间隔时间内排多少号数（如：1），点击生成排班则系统自动创建8:00-8:15、8:15-8:30···11:45-12:00，16个时间段，每个时间段1个号源。
            </div>
          </div>

          {!showDetailList?.length ? (
            <Empty
              className={'prompt empty'}
              description={`本日还未排班${
                isShow ? '，可在页面上方生成排班~' : ''
              }`}
            />
          ) : (
            <div>
              <div>
                {permisstion.copyScheduleShift && (
                  <Button
                    icon={<DiffOutlined />}
                    type={'text'}
                    disabled={!isShow}
                    onClick={() => setOpen(true)}>
                    复用排班
                  </Button>
                )}
                {permisstion.deleteScheduleShift && (
                  <Button
                    icon={<DeleteOutlined />}
                    type={'text'}
                    disabled={!isShow}
                    onClick={() => {
                      if (selectScheduleItem.length) {
                        let arr: any[] = [];
                        if (isBatch) {
                          showDetailList
                            .filter(v => selectScheduleItem.includes(v.id))
                            .forEach(l => {
                              if (l?.scheduleList && l?.scheduleList?.length) {
                                l.scheduleList.forEach(k => {
                                  arr.push(k.id);
                                });
                              }
                            });
                        } else {
                          arr = selectScheduleItem;
                        }
                        actionConfirm(
                          () =>
                            apis.删除排班
                              .request({
                                ids: arr.join(',')
                              })
                              .then(async () => {
                                await init();
                                setSelectScheduleItem([]);
                              }),
                          '删除'
                        );
                      } else {
                        message.warning('当前没有选择排班');
                      }
                    }}>
                    删除排班
                  </Button>
                )}
                {permisstion.publishScheduleShift && (
                  <Button
                    icon={<SaveOutlined />}
                    type={'text'}
                    disabled={!isShow}
                    onClick={() => {
                      if (showDetailList?.length) {
                        let arr: any[] = [];
                        if (isBatch) {
                          //批量排班是查找日期的scheduleList下面的ID
                          showDetailList.forEach(item => {
                            if (
                              item?.scheduleList &&
                              item?.scheduleList?.length
                            ) {
                              item.scheduleList.forEach(v => {
                                arr.push(v.id);
                              });
                            }
                          });
                        } else {
                          arr = showDetailList.map(item => item.id);
                        }
                        requestPublish({
                          ids: arr.join(',')
                        }).then(async () => {
                          await init();
                          message.success('发布成功！');
                        });
                      } else {
                        message.warning('当前没有排班');
                      }
                    }}>
                    发布排班
                  </Button>
                )}

                <Button
                  type={'text'}
                  disabled={!isShow}
                  onClick={() => {
                    if (showDetailList?.length) {
                      if (
                        showDetailList?.length === selectScheduleItem.length
                      ) {
                        setSelectScheduleItem([]);
                      } else {
                        setSelectScheduleItem(
                          showDetailList.map(item => item.id)
                        );
                      }
                    }
                  }}>
                  {selectScheduleItem.length === showDetailList?.length
                    ? '取消全选'
                    : '全选'}
                </Button>
                <DetailLayout
                  cardsProps={[
                    {
                      title: '',
                      children: (
                        <ScheduleItem>
                          {getScheduleItems(showDetailList)}
                        </ScheduleItem>
                      )
                    }
                  ]}
                />
              </div>
            </div>
          )}
        </div>
      </Box>
      <Modal
        visible={open}
        onCancel={() => {
          setOpen(false);
          setSelectDate([]);
        }}
        okButtonProps={{
          loading: isBatch ? reuseLoadingBatch : reuseLoading
        }}
        onOk={() => {
          if (selectDate.length) {
            let { dept } = form.getFieldsValue();
            if (!dept) {
              dept = selectOption[0]?.value || '';
            }
            const params = {
              type,
              targetDate: selectDate.join(','),
              sourceDate: showDate.format('YYYY-MM-DD')
            };
            (isBatch
              ? ReuseBatch({
                  ...params,
                  doctor: scheduleIds
                })
              : Reuse({
                  ...params,
                  deptId: dept,
                  doctorId: id
                })
            ).then(() => {
              setOpen(false);
              setSelectDate([]);
              message.success('复用成功！');
            });
          }
        }}
        title={'选择将当前排班复用到以下日期'}>
        <CheckCalendar dateFullCellRender={dateFullCellRender} />
      </Modal>
    </Spin>
  );
};
const Box = styled.div`
  display: flex;
  > div {
    .totalBox {
      width: 80%;
      display: flex;
      flex-wrap: wrap;
      margin: 0 auto;
      > div {
        width: 50%;
        font-weight: bold;
      }
    }
    .prompt {
      padding: 0 15px;
      color: rgba(164, 164, 164);
    }
    .empty {
      margin-top: 20px;
    }
    .title {
      width: 100%;
      text-align: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      font-weight: bold;
      padding-bottom: 10px;
    }
    :first-child {
      width: 25%;
      min-width: 260px;
      border-right: 1px solid rgba(0, 0, 0, 0.1);
    }
    :last-child {
      width: 75%;
      .formBox {
        margin-top: 10px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        > form {
          padding: 0 15px;
        }
        .ant-form-item {
          margin-bottom: 10px;
        }
      }
    }
  }
`;
const ScheduleItem = styled.div`
  display: flex;
  flex-flow: wrap;
  width: 100%;
  > div {
    width: 25%;
    text-align: center;
    padding: 0 10px;
    .scheduleInformation {
      width: 100%;
      padding: 2px 5px;
      border: 1px solid #69a1ea;
      display: inline-block;
      border-radius: 5px;
      cursor: pointer;
    }
  }
`;
