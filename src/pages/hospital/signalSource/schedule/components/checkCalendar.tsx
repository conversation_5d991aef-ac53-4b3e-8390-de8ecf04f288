import { Calendar, Col, Row, Select, CalendarProps } from 'antd';
import React from 'react';
import './index.less';

export default (props: CalendarProps<any>) => {
  return (
    <Calendar
      {...props}
      headerRender={({ value, onChange }) => {
        const start = 0;
        const end = 12;
        const monthOptions: any[] = [];
        const current = value.clone();
        const localeData = value.localeData();
        const months: any[] = [];
        for (let i = 0; i < 12; i++) {
          current.month(i);
          months.push(localeData.monthsShort(current));
        }
        for (let i = start; i < end; i++) {
          monthOptions.push(
            <Select.Option key={i} value={i} className='month-item'>
              {months[i]}
            </Select.Option>
          );
        }
        const year = value.year();
        const month = value.month();
        const options: any[] = [];
        for (let i = year - 10; i < year + 10; i += 1) {
          options.push(
            <Select.Option key={i} value={i} className='year-item'>
              {i + '年'}
            </Select.Option>
          );
        }
        return (
          <div style={{ padding: 8 }}>
            <Row gutter={8}>
              <Col>
                <Select
                  size='small'
                  dropdownMatchSelectWidth={false}
                  className='my-year-select'
                  value={year}
                  onChange={newYear => {
                    const now = value.clone().year(newYear);
                    onChange(now);
                  }}>
                  {options}
                </Select>
              </Col>
              <Col>
                <Select
                  size='small'
                  dropdownMatchSelectWidth={false}
                  value={month}
                  onChange={newMonth => {
                    const now = value.clone().month(newMonth);
                    onChange(now);
                  }}>
                  {monthOptions}
                </Select>
              </Col>
            </Row>
          </div>
        );
      }}
      className={'dateBox'}
      fullscreen={false}
    />
  );
};
