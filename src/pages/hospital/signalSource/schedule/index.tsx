import { But<PERSON>, Tabs } from 'antd';
import React, { useState, useRef, useMemo, useEffect } from 'react';
import { BlankLayout, handleSubmit } from 'parsec-admin';
import BasicSettin from '@pages/hospital/signalSource/schedule/components/basicSettin';
import { useHistory, useParams } from 'react-router';
import styled from 'styled-components';
import apis from '../apis';
import SchedulingManagement from '@pages/hospital/signalSource/schedule/components/schedulingManagement';
import apis2 from '@pages/hospital/doctorSmart/api';
import { enumType } from '@pages/hospital/signalSource';

export default () => {
  const history = useHistory();
  const [key, setKey] = useState('1');
  const { id, type } = useParams<{ id: string; type: 'string' }>();
  const isBatch = useMemo(() => {
    return id === 'false';
  }, [id]);
  useEffect(() => {
    if (isBatch) {
      setKey('2');
    }
  }, [isBatch]);
  const signalSourceName = useMemo(() => {
    if (isBatch) {
      return enumType[type] + '批量';
    }
    return '';
  }, [isBatch, type]);
  const formRef = useRef<any>();
  const {
    data: { data: doctor }
  } = apis2.详情主键ID({
    needInit: !isBatch,
    params: id,
    initValue: { data: {} }
  });
  const {
    data: { data: serviceSettin }
  } = apis.获取服务配置信息({
    params: { id },
    needInit: !isBatch,
    initValue: { data: {} }
  });
  const { loading, request } = apis.服务设置修改({
    needInit: false
  });
  const submit = async () => {
    const { list } = await formRef.current.submit();
    const arr = (serviceSettin?.inquiryList || []).filter(
      item => item.type === type
    );
    if (arr?.length && list?.length) {
      arr.forEach(item => {
        list.forEach(item2 => {
          if (item.id === item2.id) {
            if (JSON.stringify(item) !== JSON.stringify(item2)) {
              item2.manualStatus = 1;
            } else {
              item2.manualStatus = 0;
            }
          }
        });
      });
    }
    // console.log(list);
    if (list && list?.length) {
      list.forEach(item => {
        if (typeof item.isOnDuty === 'boolean') {
          if (item.isOnDuty) {
            item.isOnDuty = 1;
          } else {
            item.isOnDuty = 0;
          }
        }
        item.remune = item.remune * 100;
        if (item?.amount) {
          item.amount = item.amount * 100;
        }
      });
    }
    const oldArr = (serviceSettin?.inquiryList || []).filter(
      item => item.type !== type
    );
    if (oldArr?.length) {
      oldArr.forEach(item => {
        item.manualStatus = 0;
      });
    }
    handleSubmit(() =>
      request({
        id: id,
        moNames: encodeURIComponent(serviceSettin?.moNames || ''),
        inquirys: JSON.stringify([...list, ...oldArr]),
        hisTypeList: serviceSettin?.hisTypeList || []
      })
    );
  };
  const selectOption = useMemo(() => {
    if (
      doctor?.hisTypeList?.length &&
      doctor?.hisTypeList[0]?.deptList?.length
    ) {
      return doctor?.hisTypeList
        .filter(v => v.hisType === 1)?.[0]
        ?.deptList.map(item => {
          return { label: item.name, value: item.no || '' };
        });
    }
    return [];
  }, [doctor]);
  return (
    <>
      <BlankLayout>
        <HeaderAction>
          <div>
            {!isBatch
              ? enumType[type] +
                '问诊排班' +
                '-' +
                (doctor?.name || '') +
                ' | ' +
                `${selectOption?.length ? selectOption[0].label + ' | ' : ''}` +
                (doctor?.level || '')
              : '批量排班管理'}
          </div>
          {!isBatch && (
            <div className={'action'}>
              <Button onClick={() => history.goBack()}>取消</Button>
              <Button loading={loading} type={'primary'} onClick={submit}>
                确认
              </Button>
            </div>
          )}
        </HeaderAction>
        <Tabs activeKey={key} onChange={setKey}>
          {!isBatch && (
            <Tabs.TabPane tab='基础设置' key='1'>
              <BasicSettin
                ref={formRef}
                serviceSettin={serviceSettin}
                type={type}
              />
            </Tabs.TabPane>
          )}
          <Tabs.TabPane tab={`${signalSourceName}排班管理`} key='2'>
            <SchedulingManagement
              id={doctor.doctorId || ''}
              type={type}
              isBatch={isBatch}
              selectOption={selectOption}
            />
          </Tabs.TabPane>
        </Tabs>
      </BlankLayout>
    </>
  );
};
const HeaderAction = styled.div`
  text-align: center;
  position: relative;
  > div {
    &:first-child {
      display: inline-block;
      font-weight: bold;
      font-size: 17px;
    }
  }
  .action {
    position: absolute;
    top: -4px;
    right: 0;
    > button {
      margin: 0 5px 5px 5px;
    }
  }
`;
