import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ApiResponse } from '@pages/login/apis';
import { ScheduleIds } from '@configs/d';
import { Docinfo, docType } from '@pages/hospital/doctorSmart/d';
import {
  ListApiRequestParams,
  ListApiResponseData
} from '@pages/hospital/doctorSmart/api';
interface getSchedule {
  doctorId: string;
  deptNo: string | number;
  visitDate: string; //日期 yyyy-MM-dd
}

interface createSchedule {
  doctor: [
    {
      doctorId: '@word(4)'; //医生编号
      deptNo: '@word(6)'; //科室编号
    }
  ]; //医生信息，支持多个医生同时排班
  endTime: '2020-09-18 18:00:00'; // 开始排班时间
  startTime: '2020-09-18 12:00:00'; // 结束排班时间
  minuteInterval: 60; // 间隔时间
  totalResourceNum: 10; // 排班时间内放号数量
  visitDate: '2020-09-18'; // 排班日期
  type: '@pick(1,2,3)'; //号源类别 1图文，2电话，3视频
  cover?: 1 | 0; //是否覆盖医生已有排班 1是，0否
}
interface ScheduleDetail {
  id: '@natural'; //主键ID
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //更新时间
  doctorId: '@word(5)'; //医生编码
  deptNo: '@word(5)'; //科室编码
  deptName: '@cword(2)科室'; //科室名称
  visitDate: '2020-09-18'; //问诊日期
  startTime: '2020-09-18 12:00:00'; //号源开始时间
  endTime: '2020-09-18 13:00:00'; //号源结束时间
  totalResourceNum: 10; //该时段总的号源数量
  leftResourceNum: 10; //该时段剩余号源数量
  isPublish: 0; //发布状态 0未发布 1已发布
  type: '@pick(1,2,3)'; //号源类别 1图文，2电话，3视频
}
interface BatchScheduleDetail {
  startTime: '2020-09-18 12:00:00'; //号源开始时间
  endTime: '2020-09-18 13:00:00'; //号源结束时间
  totalNum: 10; //该时段总的号源数量
  leftNum: 10; //该时段剩余号源数量
  scheduleList: {
    id: '@natural'; //主键ID
    createTime: '@datetime'; //创建时间
    updateTime: '@datetime'; //更新时间
    doctorId: '@word(5)'; //医生编码
    deptNo: '@word(5)'; //科室编码
    deptName: '@cword(2)科室'; //科室名称
    visitDate: '2020-09-18'; //问诊日期
    startTime: '2020-09-18 12:00:00'; //号源开始时间
    endTime: '2020-09-18 13:00:00'; //号源结束时间
    totalResourceNum: 10; //该时段总的号源数量
    leftResourceNum: 10; //该时段剩余号源数量
    isPublish: 0; //发布状态 0未发布 1已发布
    type: '@pick(1,2,3)'; //号源类别 1图文，2电话，3视频
  }[];
}
interface ServiceSettin {
  id: string; //医生主键ID
  moNames: string; //'门诊加号|1,报告解读|1,在线处方|1,检验检查|0,远程会诊|0,双向转诊|0,预约挂号|0,医联体会诊|0,医联体转诊|0'; 医生服务权限 多个权限用‘,’分隔，1开启权限  0关闭权限 服务配置 urlencode编码
  inquiryList?: {
    id: '@natural'; //问诊配置主键ID
    doctorId: '123'; //医生编号
    hisId: 40009;
    isOnDuty: '0'; //在线状态 0离线 1在线
    maxInquiry: 50; //每日最大问诊量
    amount: 100; //原价
    price: 0;
    remune: 1; //现价,
    type: string;
    manualStatus: 1 | 0; //手动修改状态：1修改过，0未修改过
  }[]; //医生问诊配置 需要转换为json字符串传给后端
  inquirys: string;
  hisTypeList: {
    hisType: '@pick(1,2,4)'; //业务平台编码 1互联网医院，2智慧医院，4智能随访
    deptList: [
      {
        id: '@natural'; //科室主键ID
        name: '@cword科室'; //科室名称（添加或修改时可不传）
        specialty: '@csentence'; //医生在这个科室的擅长
        introduction: '@csentence'; //医生在这个科室的介绍
      }
    ];
    recommend: '@pick(0,1)'; //是否推荐 0不推荐，1推荐
    sortNo: '@natural'; //排序序号
  }[]; //医生开通的业务平台列表
}
interface Batch {
  doctor: ScheduleIds[];
  type: string; //号源类别 1图文，2电话，3视频
}
export default {
  医生列表: createApiHooks(
    async (
      params: ListApiRequestParams & {
        hisId?: string;
        deptName?: string;
        level?: string;
        name?: string;
        hisType?: string | number;
        type: docType;
        scheduleState?: 0 | 1; //排班状态 1已排班 0未排班
        scheduleDate?: string; //排班时间，默认为当天
        showInquiry?: boolean;
      }
    ) => {
      const res = await request.get<ListApiResponseData<Docinfo>>(
        `/mch/his/schedule/doctor-list`,
        {
          params
        }
      );
      return res;
    }
  ),
  创建排班: createApiHooks((params: createSchedule) =>
    request.post<ApiResponse<any>>('/mch/his/schedule', params, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  ),
  批量创建排班: createApiHooks((params: createSchedule) =>
    request.post<ApiResponse<any>>('/mch/his/schedule/batch-create', params, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  ),
  日历高亮: createApiHooks(
    (params: { doctorId: string; deptNo: string | number; type: string }) =>
      request.get<ApiResponse<string[]>>('/mch/his/schedule/high-light', {
        params
      })
  ),
  批量排班日历高亮: createApiHooks((params: Batch) =>
    request.post<ApiResponse<string[]>>(
      '/mch/his/schedule/batch-high-light',
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  排班详情: createApiHooks((params: getSchedule & { type: string }) =>
    request.get<ApiResponse<ScheduleDetail[]>>('/mch/his/schedule/one-day', {
      params
    })
  ),
  批量排班详情: createApiHooks((params: Batch & { visitDate: string }) =>
    request.post<ApiResponse<BatchScheduleDetail[]>>(
      '/mch/his/schedule/batch-one-day',
      params,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  ),
  发布排班: createApiHooks((params: { ids: string }) =>
    request.put<ApiResponse<any>>('/mch/his/schedule/publish', params)
  ),
  删除排班: createApiHooks((params: { ids: string }) =>
    request.delete<ApiResponse<any>>('/mch/his/schedule', {
      data: params
    })
  ),
  复用排班: createApiHooks(
    (params: {
      sourceDate: string; //源日期
      targetDate: string; //目标日期，多个日期已","分割
      type: string; //号源类别 1图文，2电话，3视频
      doctorId: string; //医生编号
      deptId: '0001'; //科室编号
    }) =>
      request.post<ApiResponse<any>>('/mch/his/schedule/copy', params, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
  ),
  批量复用排班: createApiHooks(
    (
      params: {
        sourceDate: string; //源日期
        targetDate: string; //目标日期，多个日期已","分割
      } & Batch
    ) =>
      request.post<ApiResponse<any>>('/mch/his/schedule/batch-copy', params, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
  ),
  服务设置修改: createApiHooks((params: ServiceSettin) =>
    request.put<ApiResponse<any>>('/mch/his/doctorMain/serviceModify', params)
  ),
  获取服务配置信息: createApiHooks((params: { id: string }) =>
    request.get<ApiResponse<ServiceSettin>>('/mch/his/doctorMain/serviceList', {
      params
    })
  ),
  同步指定医生挂号费: createApiHooks(() =>
    request.get<ApiResponse<any>>('/mch/his/doctorMain/syncRegistrationFee')
  ),
  排班统计: createApiHooks((params: getSchedule | { type: string }) =>
    request.get<
      ApiResponse<{
        totalResourceNum: 10; //该时段总的号源数量
        leftResourceNum: 10; //该时段剩余号源数量
        usedResourceNum: 10; //已经使用号源数
        canceledResourceNum: 10; //已经取消号源数
      }>
    >('/mch/his/schedule/stats', { params })
  )
};
