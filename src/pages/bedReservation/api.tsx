import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ApiResponse,
  Dictionaries,
  ListApiRequestParams,
  ListApiResponseData
} from '@configs/d';
export default {
  查询预约信息列表: createApiHooks(
    (params: {
      regName?: string;
      regIdNo?: string;
      startTime?: string;
      endTime?: string;
      status?: string; //状态（成功-SUCCESS；失败-FAILE；待审核-WAITE）
      page?: number;
      limit?: number;
    }) =>
      request.get<
        ListApiResponseData<{
          id: '@natural'; //主键ID
          hisId: '@natural'; //机构ID
          hisName: '@cword(6)'; //机构名称
          writeName: '@cname'; //填写人姓名
          relation: ''; //关系（用数据字典）
          wirtePhone: '1@integer(3000000000, 9900000000)'; //填写人电话
          regName: '@cname'; //预约人姓名
          regPhone: '1@integer(3000000000, 9900000000)'; //预约人电话
          regIdNo: ''; //预约人证件号
          nationCode: '@word(16)'; //民族编码（用数据字典）
          nationName: '@cword(6)'; //民族（用数据字典）
          sex: ''; //性别（男-M；女-F）
          status: ''; //状态（成功-SUCCESS；失败-FAIL；待审核-WAITE）
          createTime: '@datetime'; //创建时间
          updateTime: '@datetime'; //修改时间
        }>
      >('/intelligent/mch/intelligent/bed/register', {
        params
      })
  ),

  预约信息审核: createApiHooks(
    (params: {
      id: '@natural'; //主键ID
      hisId: '@natural'; //机构ID
      status: '';
    }) =>
      request.put<ApiResponse<any>>(
        `/intelligent/mch/intelligent/bed/register`,
        params
      )
  ),
  查询字典组列表: createApiHooks((params: ListApiRequestParams) =>
    request.get<
      ListApiResponseData<{
        id: 1; //Id
        code: '@word'; //医院Id
        name: string; // 字典组名称
        remark: '@ctitle'; // 备注
        groupType: 'query.groupType'; // 字典组类型
      }>
    >('/kaiqiao/his/dictGroup/page', { params })
  ),
  查询字典列表: createApiHooks(
    (
      params: { groupCode: string; dictValue?: string } & ListApiRequestParams
    ) =>
      request.get<ListApiResponseData<Dictionaries>>(
        `/kaiqiao/his/dictItem/page/${params.groupCode}`,
        { params }
      )
  ),
  本地查询字典列表: createApiHooks(
    (
      params: {
        groupCode: string;
        dictValue?: string;
        targetHisId?: string;
      } & ListApiRequestParams
    ) =>
      request.get<ListApiResponseData<Dictionaries>>(
        `/mch/his/ls-main/mainDiagnosis`,
        { params }
      )
  )
};
