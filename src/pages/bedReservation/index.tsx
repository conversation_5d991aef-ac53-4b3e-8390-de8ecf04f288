import MyTableList from '@components/myTableList';
import {
  ActionsWrap,
  ArrSelect,
  DayRangePicker,
  handleSubmit,
  LinkButton
} from 'parsec-admin';
import React, { useEffect, useState } from 'react';
import apis from './api';
import permisstion from '@utils/permisstion';

const Status = {
  SUCCESS: '成功',
  FAIL: '失败',
  WAITE: '待审核'
};
export default () => {
  const {
    data: { data: enmu }
  } = apis.查询字典组列表();
  // const [ethnicGroup, setEthnicGroup] = useState<{ [key: string]: string }>({});
  const [relationship, setRelationship] = useState<{ [key: string]: string }>(
    {}
  );
  useEffect(() => {
    if (enmu?.recordList && enmu?.recordList?.length) {
      // const arr1 = enmu?.recordList.filter(item => item.name === '民族字典');
      const arr2 = enmu?.recordList.filter(item => item.name === '关系字典');
      // if (arr1?.length && arr1?.[0]?.code) {
      //   apis.查询字典列表
      //     .request({
      //       pageNum: 1,
      //       numPerPage: 999,
      //       groupCode: arr1?.[0]?.code
      //     })
      //     .then(({ data }) => {
      //       if (data?.recordList?.length) {
      //         const obj: { [key: string]: string } = {};
      //         data?.recordList.forEach(item => {
      //           obj[item.dictKey || ''] = item.dictValue.value;
      //         });
      //         setEthnicGroup(obj);
      //       }
      //     });
      // }
      if (arr2?.length && arr2?.[0]?.code) {
        apis.查询字典列表
          .request({
            pageNum: 1,
            numPerPage: 999,
            groupCode: arr2?.[0]?.code
          })
          .then(({ data }) => {
            if (data?.recordList?.length) {
              const obj: { [key: string]: string } = {};
              data?.recordList.forEach(item => {
                obj[item.dictKey || ''] = item.dictValue.value;
              });
              setRelationship(obj);
            }
          });
      }
    }
  }, [enmu]);
  const save = (status, record) => {
    handleSubmit(() =>
      apis.预约信息审核.request({
        status: status,
        id: record.id,
        hisId: record.hisId
      })
    );
  };
  return (
    <MyTableList
      tableTitle='床位预约列表'
      getList={({ pagination: { current = 1 }, params }) => {
        delete params.sort;
        return apis.查询预约信息列表.request({
          ...params,
          page: current as number,
          limit: 10
        });
      }}
      exportExcelButton
      columns={[
        {
          title: '填写人姓名',
          dataIndex: 'writeName',
          width: 150
        },
        {
          title: '关系',
          dataIndex: 'relation',
          width: 120,
          render: v => relationship[v]
        },
        {
          title: '填写人手机号',
          dataIndex: 'writePhone',
          width: 150
        },
        {
          title: '预约人姓名',
          dataIndex: 'regName',
          width: 120,
          search: true
        },
        {
          title: '证件号',
          dataIndex: 'regIdNo',
          search: true,
          width: 200
        },
        {
          title: '性别',
          dataIndex: 'sex',
          width: 80,
          render: v => (!v ? '-' : v === 'M' ? '男' : '女')
        },
        {
          title: '民族',
          dataIndex: 'nationName',
          width: 80
        },
        {
          title: '预约人手机号',
          dataIndex: 'regPhone',
          width: 150
        },
        {
          title: '预约状态',
          dataIndex: 'status',
          search: <ArrSelect options={Status} />,
          width: 100,
          render: v => Status[v] || '-'
        },
        {
          title: '填写时间',
          dataIndex: 'createTime',
          search: (
            <DayRangePicker
              placeholder={['开始时间', '结束时间']}
              valueFormat={'YYYY-MM-DD'}
            />
          ),
          searchIndex: ['startTime', 'endTime'],
          width: 120
        },
        {
          title: '操作',
          fixed: 'right',
          width: 120,
          render: ({ status }, record: any) =>
            status === 'WAITE' &&
            permisstion.BedReservationOperation && (
              <ActionsWrap>
                <LinkButton
                  onClick={() => {
                    save('SUCCESS', record);
                  }}>
                  接收
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    save('FAIL', record);
                  }}>
                  拒绝
                </LinkButton>
              </ActionsWrap>
            )
        }
      ]}
    />
  );
};
