import React, { useState } from 'react';
import apis from './api';
import styled from 'styled-components';
import WaterMarkWrap from '@src/components/waterMarkWrap';
import { DayRangePicker } from 'parsec-admin';
import moment from 'moment';
import { message } from 'antd';
export default () => {
  // const hisId = env.hisId;
  // const paramsRef = useRef<any>();
  const [dates, setDates] = useState<any[]>([]);
  return (
    <WaterMarkWrap>
      <Wrapper>
        <div className='title'>互联网医院医保明细导出</div>
        <div className='datePicker'>
          选择结算日期：
          <DayRangePicker
            valueFormat={'YYYY-MM-DD HH:mm:ss'}
            placeholder={['请选择日期范围', '']}
            stringValue={true}
            disabledDate={d => {
              return d.isAfter(moment());
            }}
            onCalendarChange={(date, dateString) => {
              const [start, end] = dateString;

              setDates([start, end]);
            }}
            hideDisabledOptions
          />
        </div>
        <div
          className='btn'
          onClick={async () => {
            if (dates.length === 0) {
              message.destroy();
              message.error('请先选择日期范围');
              return;
            }
            console.log('dates', dates);
            apis.exportMedical({
              dateTimeStart: encodeURIComponent(
                moment(dates[0]).format('YYYY-MM-DD HH:mm:ss')
              ),
              dateTimeEnd: encodeURIComponent(
                moment(dates[1])
                  .endOf('day')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
            });
          }}>
          导出
        </div>
      </Wrapper>
    </WaterMarkWrap>
  );
};

const Wrapper = styled.div`
  width: 1200px;
  margin-right: 50px;
  color: #666;
  padding: 25px 0;
  font-size: 20px;
  background: linear-gradient(85deg, #f8f5ff, #e9f3ff, #ebf3ff);
   .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 33px;
    padding-left: 30px;
    padding-right: 30px;
    }
    .datePicker {
      margin-left: 30px;
    }
    .btn {
      width: 100px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      border-radius: 4px;
      background: #4f9aff;
      color: #fff;
      margin-left: 70px;
      margin-top: 40px;
      cursor: pointer;
    }
  }
`;
