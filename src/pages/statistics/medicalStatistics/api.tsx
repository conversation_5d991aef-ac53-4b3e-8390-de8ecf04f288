import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import env from '@configs/env';

export default {
  medicare: createApiHooks(
    (params: { dateTimeStart: string; dateTimeEnd: string }) =>
      request.get<any>('/mch/order/export/medicare', {
        params
      })
  ),
  exportMedical: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${env.apiHost}/mch/order/export/medicare?dateTimeStart=${p.dateTimeStart}&dateTimeEnd=${p.dateTimeEnd}`;
    window.open(url);
  }
};
