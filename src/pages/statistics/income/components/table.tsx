import React, { useEffect, useMemo, useRef, useState } from 'react';
import MyTableList from '@components/myTableList';
import { Button, Form, Tooltip } from 'antd';
import { DayRangePicker } from 'parsec-admin';
import moment from 'moment';
import useApi from '@pages/statistics/income/api';
import SearchSelect from '@components/SearchSelect';
import apis from '@apiHooks';
import { QuestionCircleOutlined } from '@ant-design/icons';
import env from '@src/configs/env';
import styled from 'styled-components';

const formatMoney = (x: number) => {
  return (x / 100).toFixed(2);
};

export default ({
  type,
  initialParams
}: {
  type: 'doctor' | 'dept';
  toDoctor?: (v: any) => void;
  initialParams?: Record<string, string>;
}) => {
  const [form] = Form.useForm();
  const [deptId, setDeptId] = useState('');
  const hisId = env.hisId;
  const paramsRef = useRef<any>();
  const [showTotal, setShowTotal] = useState({
    refundTotalFee: 0,
    totalFee: 0,
    totalLeftFee: 0
  });

  useEffect(() => {
    if (type === 'doctor' && initialParams) {
      const value = initialParams?.searchStartTime
        ? {
            deptId: initialParams.deptId,
            searchStartTime: [
              initialParams?.searchStartTime,
              initialParams?.searchEndTime
            ]
          }
        : {
            deptId: initialParams.deptId,
            searchStartTime: undefined
          };
      form.setFieldsValue(value);
      setDeptId(initialParams.deptId);
      form.submit();
    }
  }, [initialParams, form, type]);

  return (
    <TableBox>
      <MyTableList
        name={`/statics/${type}`}
        searchFormProps={{
          onReset: () => {
            setDeptId('');
          }
        }}
        tableTitle={
          <div>
            收入统计
            <span style={{ marginLeft: 30 }}>
              合计(元): {formatMoney(showTotal.totalLeftFee)}
            </span>
            <span style={{ marginLeft: 30 }}>
              收入(元): {formatMoney(showTotal.totalFee)}
            </span>
            <span style={{ marginLeft: 30 }}>
              退费(元): {formatMoney(showTotal.refundTotalFee)}
            </span>
          </div>
        }
        action={
          <>
            <Button
              onClick={() => {
                if (type === 'doctor') {
                  useApi.exportDoctor(paramsRef.current);
                } else {
                  useApi.exportDept(paramsRef.current);
                }
              }}>
              导出
            </Button>
          </>
        }
        form={form}
        getList={({ params, sorter }: { params: any; sorter: any }) => {
          const submitParams = { ...params };
          if (sorter.field && sorter.order) {
            submitParams.sort = sorter.order === 'ascend' ? 'ASC' : 'DESC';
            submitParams.orderBy = sorter.field;
          }
          paramsRef.current = submitParams;
          if (type === 'doctor') {
            return useApi.getDoctorList.request(submitParams).then(res => {
              setShowTotal(res?.data?.countResultMap);
              return res;
            });
          }
          return useApi.getDeptList.request(submitParams).then(res => {
            setShowTotal(res?.data?.countResultMap);
            return res;
          });
        }}
        columns={useMemo(
          () =>
            [
              {
                title: '起止日期',
                dataIndex: 'date',
                show: false,
                render: false,
                excelRender: false,
                search: (
                  <DayRangePicker
                    placeholder={['开始时间', '结束时间']}
                    valueFormat={'YYYY-MM-DD HH:mm:ss'}
                    disabledDate={d => {
                      return d.isAfter(moment());
                    }}
                    hideDisabledOptions
                  />
                ),
                searchIndex: ['searchStartTime', 'searchEndTime']
              },
              {
                title: ' 所属科室',
                show: false,
                render: false,
                searchIndex: 'deptId',
                search: (
                  <SearchSelect
                    allowClear={true}
                    onChange={setDeptId}
                    getList={async key => {
                      const res = await apis.deptSearch.request({
                        searchKey: key,
                        hisId
                      });
                      return res.map(x => {
                        return {
                          label: `${x.name}`,
                          value: x.no
                        };
                      });
                    }}
                  />
                )
              },
              {
                title: ' 医生姓名',
                show: false,
                render: false,
                searchIndex: 'doctorId',
                search:
                  type === 'doctor' ? (
                    <SearchSelect
                      allowClear={true}
                      getList={async key => {
                        const res = await apis.doctorSearch.request({
                          searchKey: key,
                          deptId,
                          hisId
                        });
                        return res.map(x => {
                          return {
                            label: `${x.name}-${x.doctorId}`,
                            value: x.doctorId
                          };
                        });
                      }}
                    />
                  ) : null
              },
              {
                title: '所属科室',
                dataIndex: 'deptName',
                searchIndex: 'deptName'
              },
              {
                title: '医生姓名',
                dataIndex: 'doctorName',
                hide: type === 'dept'
              },
              {
                title: '职称',
                dataIndex: 'level',
                hide: type === 'dept'
              },
              {
                title: (
                  <div>
                    医疗服务收入
                    <Tooltip title='互联网医院平台的问诊费和处方费的收入'>
                      <QuestionCircleOutlined style={{ marginLeft: 10 }} />
                    </Tooltip>
                  </div>
                ),
                children: [
                  {
                    title: '问诊收入',
                    dataIndex: 'inquiryFee',

                    render: formatMoney
                  },
                  {
                    title: '处方收入',
                    dataIndex: 'ppFee',

                    render: formatMoney
                  },
                  {
                    title: '其他收入',
                    dataIndex: 'otherMedicalFee',

                    render: formatMoney
                  },
                  {
                    title: '小计',
                    dataIndex: 'totalMedicalFee',

                    render: formatMoney
                  }
                ]
              },
              {
                title: '医疗服务退费',
                children: [
                  {
                    title: '问诊退费',
                    dataIndex: 'refundInquiryFee',

                    render: formatMoney
                  },
                  {
                    title: '处方退费',
                    dataIndex: 'refundPpFee',

                    render: formatMoney
                  },
                  {
                    title: '其他退费',
                    dataIndex: 'refundOtherMedicalFee',

                    render: formatMoney
                  },
                  {
                    title: '小计',
                    dataIndex: 'refundTotalMedicalFee',

                    render: formatMoney
                  }
                ]
              },
              {
                title: '医疗服务实际收入',
                children: [
                  {
                    title: '合计',
                    width: 140,
                    dataIndex: 'totalMedicalLeftFee',
                    render: formatMoney
                  }
                ]
              },
              {
                title: (
                  <div>
                    非医疗服务收入(含税)
                    <Tooltip title='互联网医院平台的咨询费和快递费的收入 '>
                      <QuestionCircleOutlined style={{ marginLeft: 10 }} />
                    </Tooltip>
                  </div>
                ),
                children: [
                  {
                    title: '咨询收入',
                    dataIndex: 'consultFee',

                    render: formatMoney
                  },
                  {
                    title: '快递收入',
                    dataIndex: 'deliveryFee',

                    render: formatMoney
                  },
                  {
                    title: '其他收入',
                    dataIndex: 'otherNonMedicalFee',

                    render: formatMoney
                  },
                  {
                    title: '小计',
                    dataIndex: 'totalNonMedicalFee',
                    render: formatMoney
                  }
                ]
              },
              {
                title: <div>非医疗服务收入(不含税)</div>,
                children: [
                  {
                    title: '咨询费',
                    dataIndex: 'consultFeeNoTax',
                    render: formatMoney
                  },
                  {
                    title: '税金',
                    dataIndex: 'consultFeeTaxes',
                    render: formatMoney
                  },
                  {
                    title: '快递费',
                    dataIndex: 'deliveryFeeNoTax',
                    render: formatMoney
                  },
                  {
                    title: '税金',
                    dataIndex: 'deliveryFeeTaxes',
                    render: formatMoney
                  },
                  {
                    title: '合计',
                    dataIndex: 'totalNonMedicalFeeNoTax',
                    render: formatMoney
                  }
                ]
              },
              {
                title: '非医疗服务退费',
                children: [
                  {
                    title: '咨询退费',
                    dataIndex: 'refundConsultFee',

                    render: formatMoney
                  },
                  {
                    title: '快递退费',
                    dataIndex: 'refundDeliveryFee',

                    render: formatMoney
                  },
                  {
                    title: '其他退费',
                    dataIndex: 'refundOtherNonMedicalFee',

                    render: formatMoney
                  },
                  {
                    title: '小计',
                    dataIndex: 'refundTotalNonMedicalFee',

                    render: formatMoney
                  }
                ]
              },
              {
                title: '非医疗服务实际收入',
                children: [
                  {
                    title: '合计',
                    width: 140,
                    dataIndex: 'totalNonMedicalLeftFee',

                    render: formatMoney
                  }
                ]
              },
              {
                title: '总收入合计',
                dataIndex: 'totalLeftFee',
                fixed: 'right',
                render: formatMoney
              }
            ]
              .filter(item => !item.hide)
              .map(item => {
                if (item.children && Array.isArray(item.children)) {
                  // @ts-ignore
                  item.children = item.children.filter(
                    (subItem: any) => !subItem.hide
                  );
                }
                return item;
              }) as any,
          [type, hisId, deptId]
        )}
      />
    </TableBox>
  );
};
const TableBox = styled.div`
  .ant-table-container {
    > .ant-table-content {
      border-color: #cacaca !important;
      > table {
        min-width: 2085px !important;
        text-align: center;
        .ant-table-tbody {
          > tr {
            > td {
              border-color: #cacaca;
            }
          }
        }
        .ant-table-thead {
          > tr {
            &:first-child {
              > th {
                &:first-child {
                  border-left: 0;
                }
              }
            }
            > th {
              border-left: 1px solid #cacaca;
              border-bottom: 1px solid #cacaca;
              ::before {
                display: none;
              }
              text-align: center;
              padding: 0 0 5px 0;
            }
          }
        }
      }
    }
  }
`;
