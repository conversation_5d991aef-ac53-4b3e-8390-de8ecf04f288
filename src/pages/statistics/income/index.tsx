import React, { useState } from 'react';
import Table from './components/table';
import { Tabs } from 'antd';
import styled from 'styled-components';

const { TabPane } = Tabs;

enum tabsEnum {
  dept = '1',
  doctor = '2'
}

export default () => {
  const [tabKey, setTabKey] = useState<string>(tabsEnum.dept);
  const [initialParams, setInitialParams] = useState<Record<string, string>>();

  // const handleToDoctor = useCallback((record: any) => {
  //   setInitialParams(record);
  //   setTabKey(tabsEnum.doctor);
  // }, []);

  return (
    <Wrap>
      <Tabs
        defaultActiveKey='1'
        className={'tabs'}
        activeKey={tabKey}
        size={'large'}
        onChange={v => {
          setTabKey(v);
          setInitialParams(undefined);
        }}
        tabBarStyle={{ padding: '0 20px' }}>
        <TabPane tab='科室统计' key={tabsEnum.dept} className={'tabs-item'}>
          <Table type={'dept'} />
        </TabPane>
        <TabPane
          tab='医生统计'
          key={tabsEnum.doctor}
          className={'tabs-item'}
          forceRender>
          <Table type={'doctor'} initialParams={initialParams} />
        </TabPane>
      </Tabs>
    </Wrap>
  );
};

const Wrap = styled.div`
  padding: 20px;
  > .tabs {
    background: #fff;
    .tabs-item {
      background: #e9f1f9;
    }
    .ant-card {
      margin-bottom: 20px !important;
    }
  }
`;
