import env from '@src/configs/env';
import createApiHooks from 'create-api-hooks';
import { ListApiRequestParams } from 'parsec-admin';
import { request } from 'parsec-admin';
import QueryString from 'qs';

export interface ApiResponse<D> {
  code: 0 | 200 | 999; // 999用户未登录
  msg: string | null;
  data: D;
}

export type ListApiResponseData<D> = ApiResponse<{
  currentPage: number;
  totalCount: number;
  countResultMap: {
    totalLeftFee: number;
    refundTotalFee: number;
    totalFee: number;
  };
  recordList: D[];
}>;

type CommonParams = ListApiRequestParams & {
  hisId: string;
  orderBy?: string;
  orderSeq?: string;
};

export default {
  getDeptList: createApiHooks((params: CommonParams) =>
    request.get<ListApiResponseData<any>>(
      '/mch/order/statistic/dept-income-sttt',
      {
        params
      }
    )
  ),
  getDoctorList: createApiHooks((params: CommonParams) =>
    request.get<ListApiResponseData<any>>(
      '/mch/order/statistic/doctor-income-sttt',
      {
        params
      }
    )
  ),
  exportDept: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${
      env.apiHost
    }/mch/order/statistic/dept-income-sttt?${QueryString.stringify({
      ...p,
      isExport: '1'
    })}`;
    window.open(url);
  },
  exportDoctor: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${
      env.apiHost
    }/mch/order/statistic/doctor-income-sttt?${QueryString.stringify({
      ...p,
      isExport: '1'
    })}`;
    window.open(url);
  }
};
