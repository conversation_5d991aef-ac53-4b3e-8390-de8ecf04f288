import { But<PERSON>, Col, Form, Input, Row } from 'antd';
import { <PERSON>Layout, DayRangePicker, LinkButton } from 'parsec-admin';
import React, { useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import { UpOutlined } from '@ant-design/icons';
import { useWindowResize } from 'parsec-hooks';
import { ColProps } from 'antd/lib/col';
import DoctorPanel from '@src/pages/statistics/hospital/components/doctorPanel';
import PrescriptionPanel from '@src/pages/statistics/hospital/components/prescriptionPanel';
import QueryPanel from '@src/pages/statistics/hospital/components/queryPanel';
import DealPanel from '@src/pages/statistics/hospital/components/dealPanel';
import moment from 'moment';

const FormItem = Form.Item;

export default () => {
  const [form] = Form.useForm();
  const [expand, setExpand] = useState(false);
  const getTime = useMemo(() => {
    try {
      return JSON.parse(
        sessionStorage.getItem('hospitalDistrictAnalysis') || '{}'
      );
    } catch (e) {
      console.log(e);
    }
    return {};
  }, []);
  const [params, setParams] = useState<{
    beginTime?: string;
    endTime?: string;
  }>(getTime);
  useEffect(() => {
    if (Object.keys(getTime)?.length) {
      form.setFieldValue('time', [getTime.beginTime, getTime.endTime]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const searchColumns = [
    // {
    //   title: '查询院区',
    //   dataIndex: 'hospital',
    //   render: (
    //     <ArrSelect options={['院区1', '院区2', '院区3', '院区4', '院区5']} />
    //   )
    // },
    {
      title: '起止时间',
      dataIndex: 'time',
      render: (
        <DayRangePicker
          disabledDate={current => current && current > moment().endOf('day')}
        />
      )
    }
  ];
  const { clientWidth } = useWindowResize();

  const ExpandBtns = (
    <SearchItemCol style={{ maxWidth: 'none', flex: 1 }}>
      <FormItem className={'expand'}>
        <Row
          justify={expand ? 'end' : 'start'}
          align={'middle'}
          style={{
            lineHeight: '32px',
            whiteSpace: 'nowrap',
            justifyContent: 'flex-end'
          }}>
          <Button
            htmlType={'submit'}
            type={'primary'}
            onClick={() => {
              console.log('chaxun');
              form
                .validateFields()
                .then(data => {
                  try {
                    if (data?.time) {
                      const time = {
                        beginTime: data.time?.[0].format('YYYY-MM-DD 00:00:00'),
                        endTime: data.time?.[1].format('YYYY-MM-DD 23:59:59')
                      };
                      setParams(time);
                      sessionStorage.setItem(
                        'hospitalDistrictAnalysis',
                        JSON.stringify(time)
                      );
                    } else {
                      setParams({});
                      sessionStorage.setItem('hospitalDistrictAnalysis', '{}');
                    }
                  } catch (e) {
                    console.log(e);
                  }
                })
                .catch(e => {
                  console.log(e);
                });
            }}>
            查询
          </Button>
          <MarginButton
            onClick={() => {
              form.resetFields();
              setParams({});
              sessionStorage.setItem('hospitalDistrictAnalysis', '{}');
            }}>
            重置
          </MarginButton>
          {searchColumns.length >=
            4 - (clientWidth < 990 ? 2 : clientWidth < 1600 ? 1 : 0) && (
            <LinkButton
              onClick={e => {
                e.preventDefault();
                setExpand(!expand);
              }}>
              {expand ? '收起' : '展开'}{' '}
              <UpOutlined
                style={{
                  transform: `rotate(${expand ? 0 : -180}deg)`,
                  transition: 'transform .3s'
                }}
              />
            </LinkButton>
          )}
        </Row>
      </FormItem>
    </SearchItemCol>
  );

  return (
    <Wrapper>
      <CardLayout>
        <Form
          className={'tableList-search-form'}
          form={form}
          style={{ display: 'flex' }}
          layout={'inline'}
          onFinish={value => {
            // setParams({
            //   beginTime: value.time?.[0].format('YYYY-MM-DD'),
            //   endTime: value.time?.[1].format('YYYY-MM-DD')
            // });
          }}>
          {searchColumns.map((item, i) => {
            return (
              <SearchItemCol key={i} show={expand || i <= 2 ? 1 : 0}>
                <FormItem name={item.dataIndex} label={item.title}>
                  {!item.render ? (
                    <Input placeholder={`请输入${item.title}`} />
                  ) : (
                    item.render
                  )}
                </FormItem>
              </SearchItemCol>
            );
          })}
          {ExpandBtns}
        </Form>
      </CardLayout>
      <CardLayout>
        <h5>互联网医院数据</h5>
        <PanelWrap>
          <DoctorPanel params={params} />
          <QueryPanel params={params} />
          <PrescriptionPanel params={params} />
          <DealPanel params={params} />
        </PanelWrap>
      </CardLayout>
    </Wrapper>
  );
};

const Wrapper = styled.div`
  width: 100%;
  height: 100%;
  h5 {
    font-size: 20px;
    color: black;
    margin-bottom: 26px;
    font-weight: bolder;
    position: relative;
    //&::before {
    //  position: absolute;
    //  height: 13px;
    //  top: 0;
    //  bottom: 0;
    //  left: -24px;
    //  content: '';
    //  width: 5px;
    //  background-color: #2780d9;
    //  margin: auto;
    //}
  }
`;
const MarginButton = styled(Button)`
  margin: 0 8px;
`;

const PanelWrap = styled.div`
  display: flex;
  flex-wrap: wrap;
`;

const SearchItemCol = styled((props: ColProps & { show?: 1 | 0 }) => (
  <Col
    xs={{ span: 24 }}
    sm={{ span: 12 }}
    md={{ span: 12 }}
    lg={{ span: 8 }}
    xxl={{ span: 6 }}
    {...props}
  />
))`
  && {
    padding: 4px 8px;
    margin-bottom: 24px;
    display: ${({ show = 1 }) => (show ? 'block' : 'none')};
    @media (max-width: 1600px) {
      &.SearchItem2 {
        display: none;
      }
    }
    @media (max-width: 990px) {
      &.SearchItem1 {
        display: none;
      }
    }
  }
`;
