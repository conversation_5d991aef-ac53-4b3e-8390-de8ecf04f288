import { useEffect, useMemo } from 'react';
import useApi from '@pages/index/Charts/api';
import { ChartItem, ObjChatItem } from '@src/pages/index/Charts/d';

export default (params: any, id: number): any => {
  const { data, request, loading } = useApi.公共统计接口({
    initValue: [],
    needInit: false,
    params: {
      configId: id,
      params
    }
  });

  useEffect(() => {
    request();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params]);

  function JudeTypeIsChartItem(item: any): item is ChartItem {
    return Boolean(item.point);
  }

  function judgeKeyType(item: any): item is keyof ObjChatItem {
    return true;
  }

  const formatData = useMemo(() => {
    const res: { [key: string]: any } = {};
    data.forEach(item => {
      if (!JudeTypeIsChartItem(item)) {
        Object.keys(item).forEach(subItem => {
          //类型会丢失
          if (judgeKeyType(subItem)) {
            res[subItem] = item[subItem];
          }
        });
      }
    });
    return res;
  }, [data]);

  return { formatData, loading };
};
