import React from 'react';
import HospStatics, { PanelProps } from './hospStatics';
import { formatNumber } from '@utils/tools';
import useRequestData from './useRequestData';
import { useHistory } from 'react-router';
import qs from 'qs';

export default (props: PanelProps) => {
  const { params } = props;
  const { push } = useHistory();
  const { formatData, loading } = useRequestData(params, 41);

  return (
    <HospStatics
      title='医生数据'
      loading={loading}
      imgUrl={require('../images/doctor.png')}
      subTitle='入网医生数'
      count={formatData.doctor}
      countAfter='人'
      goTitleRoute={() => {
        push('/doctorSmart?' + qs.stringify({ hisType: 1 }));
      }}
      tooltipText={'统计互联网医院平台医生数据'}
      items={[
        {
          title: '入网科室数',
          count: `${formatNumber(formatData.dept)}个`,
          goDetail: () => {
            push('/hospital/deptSmart');
          }
        },
        {
          title: '在线医生数',
          count: `${formatNumber(formatData.doctorOnline)}人`,
          goDetail: () => {
            push('/onlineDetails/doctor?' + qs.stringify(params));
          }
        },

        {
          title: '在线科室数',
          count: `${formatNumber(formatData.deptOnline)}个`,
          goDetail: () => {
            push('/onlineDetails/dept?' + qs.stringify(params));
          }
        }
      ]}
    />
  );
};
