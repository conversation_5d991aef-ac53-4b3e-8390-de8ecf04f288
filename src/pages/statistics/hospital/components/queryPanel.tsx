import formatTime from '@src/utils/formatTime';
import React from 'react';
import HospStatics, { PanelProps } from './hospStatics';
import useRequestData from './useRequestData';
import { useHistory } from 'react-router';
import qs from 'qs';

export default (props: PanelProps) => {
  const { params } = props;
  const { push } = useHistory();
  const { formatData, loading } = useRequestData(params, 33);
  return (
    <HospStatics
      title='问诊数据'
      loading={loading}
      imgUrl={require('../images/query.png')}
      subTitle='问诊量'
      count={formatData.inquiry}
      goTitleRoute={() => {
        push('/order/inquiryOnLine');
      }}
      goSubTitleRoute={() => {
        push('/operate/consult/list?' + qs.stringify(params));
      }}
      countAfter='次'
      tooltipText={'统计互联网医院平台问诊数据，包含咨询量和问诊量'}
      items={[
        {
          title: '回复率',
          count: `${((formatData.replyRate ?? 0) * 100).toFixed(2)}%`
        },
        {
          title: '好评率',
          count: `${((formatData.praiseRate ?? 0) * 100).toFixed(2)}%`
        },
        {
          title: '响应时长',
          count: `${formatTime((formatData.responseTime || 0) * 1000)}`
        }
      ]}
    />
  );
};
