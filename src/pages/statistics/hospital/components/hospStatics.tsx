import React from 'react';
import styled from 'styled-components';
import { Spin, Tooltip } from 'antd';
import { formatNumber } from '@utils/tools';
import { InfoCircleOutlined } from '@ant-design/icons';

export interface PanelProps {
  params: {
    beginTime?: string;
    endTime?: string;
  };
}
export interface StaticProps {
  title: string;
  imgUrl: string;
  subTitle: string;
  count: number;
  loading?: boolean;
  countAfter: string;
  items: {
    title: string;
    count: string;
    goDetail?: () => void;
  }[];
  tooltipText?: string;
  goSubTitleRoute?: () => void;
  goTitleRoute?: () => void;
}

export default (props: StaticProps) => {
  const getStyle = (fn?: () => void) => {
    return typeof fn === 'function' ? { cursor: 'pointer' } : {};
  };
  return (
    <Spin spinning={props.loading ?? false}>
      <Wrapper>
        <div
          style={getStyle(props?.goTitleRoute)}
          onClick={props?.goTitleRoute}>
          <img src={props.imgUrl} alt={props.title} />
          <span>{props.title}</span>
          <Tooltip
            className={'TooltipBox'}
            placement='right'
            color={'#3f3f3f'}
            title={props?.tooltipText || ''}>
            <InfoCircleOutlined />
          </Tooltip>
        </div>
        <div
          style={getStyle(props?.goSubTitleRoute)}
          onClick={props?.goSubTitleRoute}>
          <div>{props.subTitle}</div>
          <div>
            {formatNumber(props.count)}
            {props.countAfter}
          </div>
        </div>

        <div>
          {props.items.map(item => (
            <div>
              <div>{item.title}</div>
              <div style={getStyle(item?.goDetail)} onClick={item?.goDetail}>
                {item.count}
              </div>
            </div>
          ))}
        </div>
      </Wrapper>
    </Spin>
  );
};

const Wrapper = styled.div`
  width: 700px;
  margin-right: 50px;
  color: #666;
  padding: 25px 0;
  font-size: 20px;
  margin-bottom: 33px;
  background: linear-gradient(85deg, #f8f5ff, #e9f3ff, #ebf3ff);
  & > div:nth-child(1) {
    display: flex;
    align-items: center;
    justify-content: left;
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 33px;
    padding-left: 30px;
    > img {
      width: 46px;
      height: 46px;
      margin-right: 11px;
    }
    .TooltipBox {
      margin-left: 10px;
      font-size: 14px;
      color: #9f9e9e;
    }
  }
  & > div:nth-child(2) {
    > div {
      &:nth-child(1) {
        text-align: center;
        margin-bottom: 20px;
      }
      &:nth-child(2) {
        text-align: center;
        font-size: 40px;
        font-weight: bold;
        color: #2780d9;
        margin-bottom: 45px;
      }
    }
  }
  & > div:nth-child(3) {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    > div {
      flex: 1;
      text-align: center;
      border-right: 1px solid #e2e2e2;
      & > div:nth-child(1) {
        margin-bottom: 5px;
      }
      & > div:nth-child(2) {
        color: #333;
        font-weight: bold;
      }
    }
    & > div:last-child {
      border-right: none;
    }
  }
`;
