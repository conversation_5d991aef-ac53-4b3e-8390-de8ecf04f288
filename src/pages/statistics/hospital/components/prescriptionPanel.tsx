import React, { useMemo } from 'react';
import HospStatics, { PanelProps } from './hospStatics';
import { formatNumber } from '@utils/tools';
import useRequestData from './useRequestData';
import { useHistory } from 'react-router';

export default (props: PanelProps) => {
  const { params } = props;

  const { formatData, loading } = useRequestData(params, 45);

  const passRatio = useMemo(() => {
    if (
      !formatData.prescriptionPass ||
      !formatData.prescription ||
      formatData.prescription === 0
    ) {
      return '0%';
    }
    return (
      Math.round(
        (formatData.prescriptionPass * 100) / formatData.prescription
      ) + '%'
    );
  }, [formatData]);
  const { push } = useHistory();
  return (
    <HospStatics
      title='处方数据'
      loading={loading}
      imgUrl={require('../images/prescription.png')}
      subTitle='处方数量'
      count={formatData.prescription}
      countAfter='张'
      goTitleRoute={() => {
        push('/order/medicine');
      }}
      tooltipText={'统计互联网医院平台医生开方的数据'}
      items={[
        {
          title: '审核通过数',
          count: `${formatNumber(formatData.prescriptionPass)}张`
        },
        {
          title: '处方缴费数',
          count: `${formatNumber(formatData.prescriptionPaid)}张`
        },
        {
          title: '审核通过率',
          count: passRatio
        },
        {
          title: '审核不通过',
          count: `${formatNumber(formatData.prescriptionFailPass)}张`
        }
      ]}
    />
  );
};
