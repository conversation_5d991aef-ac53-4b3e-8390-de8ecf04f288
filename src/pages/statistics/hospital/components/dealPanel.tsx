import React, { useMemo } from 'react';
import HospStatics, { PanelProps } from './hospStatics';
import { formatNumber } from '@utils/tools';
import useRequestData from './useRequestData';

export default (props: PanelProps) => {
  const { params } = props;

  const { formatData, loading } = useRequestData(params, 47);

  const refundRatio = useMemo(() => {
    if (
      !formatData.transactionAmount ||
      !formatData.refoundAmount ||
      formatData.transactionAmount === 0
    ) {
      return '0%';
    }
    return (
      Math.round(
        (formatData.refoundAmount * 100) / formatData.transactionAmount
      ) + '%'
    );
  }, [formatData]);

  return (
    <HospStatics
      title='交易数据'
      loading={loading}
      imgUrl={require('../images/deal.png')}
      subTitle='总交易额'
      count={Number((formatData.transactionMoney / 100).toFixed(2))}
      countAfter='元'
      tooltipText={
        '统计互联网医院平台所有交易的数据，包含问诊费、咨询费、处方费、邮费。'
      }
      items={[
        {
          title: '总交易笔数',
          count: `${formatNumber(formatData.transactionAmount)}笔`
        },
        {
          title: '退款金额',
          count: `${formatNumber(
            Number((formatData.refoundMoney / 100).toFixed(2))
          )}元`
        },
        {
          title: '退款笔数',
          count: `${formatNumber(formatData.refoundAmount)}笔`
        },
        {
          title: '退款率',
          count: refundRatio
        }
      ]}
    />
  );
};
