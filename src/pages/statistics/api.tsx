import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiResponseData,
  ListApiRequestParams,
  ApiResponse
} from '@apiHooks';

export const accountStatusObj: any = {
  '': '全部',
  '0': '禁用',
  '1': '启用',
  '2': '冻结',
  '3': '锁定'
};

export const roleStateObj: any = {
  '': '全部',
  '0': '停用',
  '1': '启用'
};

export interface OperationSettingResp {
  hisId: '@integer(1,100)'; //医院ID
  restrictImageInquiryConfig: '1,2,3'; //限制医生图文问诊配置，医生id主键，以逗号隔开
  followAppointDirector: '@pick(0,1)'; //智能随访任务指定负责人规则，0:按病种与科室查询负责人   1：取诊疗记录中的主治医生
  followSurveyPushAgain: '@integer(1,10)'; //问卷调查患者未反馈消息的重复信息推送，单位天，0表示不需要重复推送
  followArticlePushAgain: '@integer(1,10)'; //健康宣教患者未反馈消息的重复信息推送，单位天，0表示不需要重复推送
}

export interface RoleListItem {
  id?: number;
  hisId?: number;
  roleName?: string;
  describe?: string;
  status?: string | number;
  createTime?: string;
  updateTime?: string;
  clientType?: string;
  dataRight?: 'SELF' | 'DEPT' | 'HIS';
}

export interface MenuItem {
  childMenu: MenuItem[];
  code: string;
  id: number;
  name: string;
  operatePurview: string;
  parentId: number;
  sort: number;
  type: number;
  url: string;
  userId: number;
}

export default {
  docLoginlog: createApiHooks(
    (
      params: ListApiRequestParams & {
        startTime?: string;
        endTime?: string;
        account?: string;
        doctorName?: string;
        deptName?: string;
      }
    ) =>
      request.get<
        ListApiResponseData<{
          id: string;
          createTime: string;
          updateTime: string;
          account: string; // 账号
          doctorName: string; // 医生姓名
          districtName: string; // 所属院区
          deptName: string; // 科室
          level: string; // 医生职称
          openId: string; // OpenID
        }>
      >('/mch/user/doctor/login/log', { params })
  ),
  exportOrder: createApiHooks(
    (params: {
      pageNum?: string;
      numPerPage?: string;
      startTime?: string;
      endTime?: string;
      account?: string;
      doctorName?: string;
      deptName?: string;
    }) =>
      request.get<Blob>('/mch/user/doctor/login/log/export', {
        responseType: 'blob',
        params
      })
  ),
  doconline: createApiHooks(
    (
      params: ListApiRequestParams & {
        hisId?: string;
        startDate?: string;
        endDate?: string;
        hospital?: string;
        account?: string;
      }
    ) =>
      request.get<
        ListApiResponseData<{
          doctorName: string;
          doctorId: string;
          deptName: string;
          level: string;
          isOnline: string;
          textInquiryStatus: string;
          phoneInquiryStatus: string;
          videoInquiryStatus: string;
          healthConsultationStatus: string;
        }>
      >('/mch/his/doctorMain/onlineSituation', { params })
  ),
  exportOnline: createApiHooks(
    (params: {
      pageNum?: string;
      numPerPage?: string;
      startTime?: string;
      endTime?: string;
      account?: string;
      doctorName?: string;
      deptName?: string;
    }) =>
      request.get<Blob>('/mch/his/doctorMain/exportOnlineSituation', {
        responseType: 'blob',
        params
      })
  ),
  docstats: createApiHooks(
    (
      params: ListApiRequestParams & {
        hisId?: string;
        startDate?: string;
        endDate?: string;
        hospital?: string;
        account?: string;
      }
    ) =>
      request.get<
        ApiResponse<{
          doctorCount: string;
          onlineCount: string;
        }>
      >('/mch/his/doctorMain/onlineSituationStatistics', { params })
  ),
  funlog: createApiHooks(
    (
      params: ListApiRequestParams & {
        startTime?: string;
        endTime?: string;
        inquiryType?: string;
        onDuty?: string;
        deptName?: string;
        level?: string;
      }
    ) =>
      request.get<
        ListApiResponseData<{
          id: string;
          createTime: string;
          updateTime: string;
          account: string; // 账号
          doctorName: string; // 医生姓名
          districtName: string; // 所属院区
          deptName: string; // 科室
          level: string; // 医生职称
          inquiryType: string; // 功能名称 "1":"图文问诊", "2":"电话问诊", "3":"视频问诊", "11":"健康咨询", "21":"随访咨询", "7":"新冠咨询", "31":"护理咨询"
          onDuty: string; // 操作状态 1: 开启 (提供服务), 0: 关闭(停止服务)
        }>
      >('/mch/his/doctor/inquiry-config/log', { params })
  ),
  funlogOrder: createApiHooks(
    (params: {
      startTime?: string;
      endTime?: string;
      inquiryType?: string;
      onDuty?: string;
      deptName?: string;
      level?: string;
    }) =>
      request.get<Blob>('/mch/his/doctor/inquiry-config/log/export', {
        responseType: 'blob',
        params
      })
  ),
  deptSearch: createApiHooks((params: { searchKey: string }) => {
    return request.get<{ id: number; name: string; no: string }[]>(
      '/mch/his/dept/briefs',
      {
        params
      }
    );
  })
};
