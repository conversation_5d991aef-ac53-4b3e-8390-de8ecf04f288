import React, { useMemo, useRef } from 'react';
import useApi from './api';
import OldTableList from '@components/oldTableList';
import formatTime from '@src/utils/formatTime';
import SearchSelect from '@components/SearchSelect';
import { ArrSelect, DayRangePicker } from 'parsec-admin';
import {
  doctorlevels,
  druglevels,
  nurselevels,
  docTypes
} from '@src/pages/hospital/doctor/d';
import { Button, Modal } from 'antd';
import apis from '@configs/apis';
import moment from 'moment';
import env from '@src/configs/env';

import WaterMarkWrap from '@src/components/waterMarkWrap';

export default () => {
  const hisId = env.hisId;
  const paramsRef = useRef<any>();

  return (
    <WaterMarkWrap>
      <OldTableList
        tableTitle='医生分析'
        action={
          <>
            <Button
              onClick={() => {
                Modal.confirm({
                  title: '确认导出?',
                  content: '当前导出信息，请勿非法传阅',
                  onOk: () => {
                    useApi.export(paramsRef.current);
                  }
                });
              }}>
              导出
            </Button>
          </>
        }
        getList={async ({
          pagination: { current, pageSize = 10 },
          params,
          sorter
        }) => {
          const {
            order,
            column: { dataIndex } = { dataIndex: undefined }
          } = sorter as any;
          const p = {
            hisId,
            ...params,
            pageNum: current,
            numPerPage: pageSize,
            orderBy: dataIndex,
            orderSeq: dataIndex
              ? order === 'ascend'
                ? 'asc'
                : 'desc'
              : undefined
          };
          paramsRef.current = p;
          const res = await useApi.list.request(p);
          console.log('sorter', sorter);
          return {
            list: res?.data?.recordList ?? [],
            total: res?.data?.totalCount
          };
        }}
        // tableTitle={false}
        columns={useMemo(
          () => [
            {
              title: '医生编号',
              dataIndex: 'doctorId',
              fixed: 'left',
              width: 140,
              excelRender: t => t + ''
            },
            {
              title: '起止日期',
              dataIndex: 'date',
              show: false,
              render: false,
              excelRender: false,
              search: (
                <DayRangePicker
                  valueFormat={'YYYY-MM-DD HH:mm:ss'}
                  placeholder={['开始时间', '结束时间']}
                  disabledDate={d => {
                    return d.isAfter(moment());
                  }}
                  hideDisabledOptions
                />
              ),
              searchIndex: ['startDate', 'endDate']
            },
            {
              title: '医生姓名',
              dataIndex: 'doctorName',
              width: 100,
              searchIndex: 'doctorId',
              search: (
                <SearchSelect
                  allowClear={true}
                  getList={async key => {
                    const res = await apis.doctorSearch.request({
                      searchKey: key,
                      hisId
                    });
                    return res.map(x => {
                      return {
                        label: `${x.name}-${x.doctorId}`,
                        value: x.doctorId
                      };
                    });
                  }}
                />
              )
            },
            {
              title: '医生类型',
              dataIndex: 'doctorType',
              show: false,
              width: 120,
              render: x => docTypes.find(y => y.value === x)?.label,
              search: (
                <ArrSelect
                  allowClear={true}
                  optionFilterProp='children'
                  options={(docTypes || []).map(x => ({
                    value: x.value,
                    children: x.label
                  }))}
                />
              )
            },
            ({ doctorType }) => {
              let levels: any[] = [];
              if (doctorType === '1') {
                levels = doctorlevels;
              }
              if (doctorType === '2') {
                levels = nurselevels;
              }
              if (doctorType === '4') {
                levels = druglevels;
              }
              return {
                title: '职称',
                dataIndex: 'level',
                width: 120,
                search:
                  levels.length >= 0 ? (
                    <ArrSelect
                      allowClear={true}
                      optionFilterProp='children'
                      notFoundContent='请选择医生类型'
                      options={levels.map(x => ({
                        value: x.label,
                        children: x.label
                      }))}
                    />
                  ) : (
                    false
                  )
              };
            },
            {
              title: '所属科室',
              dataIndex: 'deptName',
              width: 100,
              searchIndex: 'deptId',
              search: (
                <SearchSelect
                  allowClear={true}
                  getList={async key => {
                    const res = await apis.deptSearch.request({
                      searchKey: key,
                      hisId
                    });
                    return res.map(x => {
                      return {
                        label: `${x.name}`,
                        value: x.no
                      };
                    });
                  }}
                />
              )
            },
            {
              title: '问诊类型',
              dataIndex: 'inquiryType',
              show: false,
              render: false,
              excelRender: false,
              search: (
                <ArrSelect
                  allowClear={true}
                  options={[
                    {
                      value: '1',
                      children: '图文问诊'
                    },
                    {
                      value: '2',
                      children: '视频问诊'
                    },
                    {
                      value: '3',
                      children: '电话问诊'
                    }
                  ]}
                />
              )
            },
            {
              title: '问诊量',
              dataIndex: 'inquiryAmount',
              sorter: true,
              width: 100,
              render: x => x || '0'
            },
            {
              title: '接诊量',
              dataIndex: 'receptionAmount',
              sorter: true,
              width: 100,
              render: x => x || '0'
            },
            {
              title: '回复率',
              dataIndex: 'replyRate',
              sorter: true,
              width: 100,
              render: x => (x * 100).toFixed(2) + '%'
            },
            {
              title: '响应时长',
              dataIndex: 'responseAvgTime',
              sorter: true,
              width: 150,
              render: x => formatTime(x * 1000)
            },
            {
              title: '处方开单量',
              dataIndex: 'prescriptionAmount',
              sorter: true,
              width: 140,
              render: x => x || '0'
            },
            {
              title: '交易金额（元）',
              dataIndex: 'transactionMoney',
              sorter: true,
              width: 160,
              render: x => (x / 100).toFixed(2)
            },
            {
              title: '主动退款笔数',
              dataIndex: 'doctorRefundAmount',
              sorter: true,
              width: 140
            },
            {
              title: '上线时长',
              dataIndex: 'onlineTime',
              sorter: true,
              width: 150,
              render: x => formatTime(x * 1000)
            },
            {
              title: '好评率',
              dataIndex: 'praiseRate',
              sorter: true,
              width: 100,
              render: x => (x * 100).toFixed(2) + '%'
            },
            {
              title: '写病例数',
              dataIndex: 'medicalRecordAmount',
              sorter: true,
              width: 120
            },
            {
              title: '审核通过率',
              dataIndex: 'prescriptionPassRate',
              sorter: true,
              width: 140,
              render: x => (x * 100).toFixed(2) + '%'
            },
            {
              title: '主动退金额（元）',
              dataIndex: 'doctorRefundMoney',
              sorter: true,
              width: 170,
              render: x => (x / 100).toFixed(2)
            },
            {
              title: '主动退款率',
              dataIndex: 'doctorRefundRate',
              sorter: true,
              width: 140,
              render: x => (x * 100).toFixed(2) + '%'
            },
            {
              title: '推荐人数',
              dataIndex: 'sharePersonTimes',
              sorter: true,
              width: 140
            },
            {
              title: '推荐人次',
              dataIndex: 'shareTimes',
              sorter: true,
              width: 140
            }
          ],
          [hisId]
        )}
      />
    </WaterMarkWrap>
  );
};
