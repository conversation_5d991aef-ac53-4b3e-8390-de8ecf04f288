import createApiHooks from 'create-api-hooks';
import { ListApiRequestParams, ListApiResponseData } from '@configs/d';
import { request } from 'parsec-admin';
import { StatisticsDoctor } from './d';
import QueryString from 'qs';
import env from '@src/configs/env';
export default {
  list: createApiHooks(
    (
      params: ListApiRequestParams & {
        hisId: string;
        orderBy?: string;
        orderSeq?: string;
      }
    ) =>
      request.get<ListApiResponseData<StatisticsDoctor>>(
        '/mch/his/statistics/doctors',
        {
          params
        }
      )
  ),
  export: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${
      env.apiHost
    }/mch/his/statistics/doctors?${QueryString.stringify({
      ...p,
      isExport: '1'
    })}`;
    window.open(url);
  }
};
