import { ApiResponse } from '@src/pages/login/apis';
import createApiHooks from 'create-api-hooks';
import {
  request,
  ListApiResponseData,
  ListApiRequestParams
} from 'parsec-admin';

export default {
  配置列表: createApiHooks(
    async (params: ListApiRequestParams & { hisId?: string }) => {
      const resp = await request.get<
        ListApiResponseData<{
          id: number;
          chartId: number;
          chartName: string;
          hisId: string;
          chart: Array<{
            title: string;
            chartType: string;
            config: string;
            reqParams: string;
            resParmas: string;
            sqlTemplate: string;
          }>;
          createAt: string;
          updateAt: string;
          remark: string;
        }>
      >('/mch/his/statistics/config', {
        params
      });
      const list = resp.data.list.map(x => {
        return {
          ...x
          // 美化一下显示
          // chart: JSON.stringify(x.chart, null, 2)
        };
      });
      return {
        ...resp,
        data: {
          ...resp.data,
          list
        }
      };
    }
  ),
  删除配置: createApiHooks((params: { id: number }) =>
    request.delete(`/mch/his/statistics/config/${params.id}`, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  ),
  新增配置: createApiHooks(
    (data: {
      chartId: string;
      chartName: string;
      hisId: string;
      chart: string;
      remark: string;
    }) =>
      request.post<ApiResponse<Record<string, unknown>>>(
        '/mch/his/statistics/config',
        {
          ...data,
          chart: JSON.parse(data.chart)
        },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
  ),
  修改配置: createApiHooks(
    (data: { id: number; chartName: string; chart: string; remark: string }) =>
      request.put<ApiResponse<Record<string, unknown>>>(
        '/mch/his/statistics/config',
        {
          ...data,
          chart: JSON.parse(data.chart)
        },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
  )
};
