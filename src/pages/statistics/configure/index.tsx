import React, { useMemo } from 'react';
import {
  actionConfirm,
  ActionsWrap,
  DateShow,
  handleSubmit,
  LinkButton,
  useModal
} from 'parsec-admin';
import useApi from './api';
import OldTableList from '@components/oldTableList';
import { Button, Input } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import env from '@configs/env';

const { TextArea } = Input;

export default () => {
  const hisId = env.hisId;

  const configureModalVisible = useModal(
    values => ({
      title: '新增/编辑配置',
      onSubmit: ({ ...params }: any) => {
        params.hisId = hisId;
        if (!params.hisId) {
          return Promise.reject();
        }
        return handleSubmit(
          () => (params.id ? useApi.修改配置 : useApi.新增配置).request(params),
          `${params.id ? '修改' : '新增'}`
        );
      },
      items: [
        {
          name: 'id',
          render: false
        },
        {
          label: '前端图表id',
          name: 'chartId',
          required: true,
          render: v => (
            <Input
              placeholder={'前端图表id'}
              disabled={values.id ? true : false}
            />
          )
        },
        {
          label: '图表名称',
          name: 'chartName',
          required: true
        },
        {
          label: '图表',
          name: 'chart',
          rules: [
            {
              required: true,
              message: '请输入图表JSON'
            },
            {
              validator: (_: any, v: any) => {
                try {
                  JSON.parse(v);
                  return Promise.resolve();
                } catch (error) {
                  return Promise.reject(new Error('JSON格式错误'));
                }
              }
            }
          ],
          render: <TextArea rows={10} />,
          required: true
        },
        {
          label: '说明',
          name: 'remark',
          render: <TextArea />,
          required: true
        }
      ]
    }),
    [hisId]
  );
  return (
    <OldTableList
      tableTitle='配置管理'
      action={
        <>
          <Button
            type={'default'}
            icon={<PlusOutlined />}
            onClick={() => configureModalVisible()}>
            添加
          </Button>
        </>
      }
      getList={({ pagination: { current }, params }) => {
        return useApi.配置列表.request({ ...params, page: current, limit: 10 });
      }}
      columns={useMemo(
        () => [
          {
            title: 'id',
            dataIndex: 'id',
            width: 110
          },
          {
            title: '前端图表id',
            dataIndex: 'chartId',
            width: 110
          },
          {
            title: '图表名称',
            dataIndex: 'chartName',
            width: 110,
            search: true
          },
          {
            title: '创建时间',
            dataIndex: 'createAt',
            width: 110,
            render: v => <DateShow>{v}</DateShow>
          },
          {
            title: '说明',
            dataIndex: 'remark',
            width: 110
          },
          {
            title: '操作',
            fixed: 'right',
            width: 110,
            render: record => (
              <ActionsWrap max={99}>
                <LinkButton
                  onClick={() => {
                    configureModalVisible({
                      ...record,
                      chart: JSON.stringify(record.chart)
                    });
                  }}>
                  编辑
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        useApi.删除配置.request({
                          id: record.id
                        }),
                      '删除'
                    );
                  }}>
                  删除
                </LinkButton>
              </ActionsWrap>
            )
          }
        ],
        [configureModalVisible]
      )}
    />
  );
};
