import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { LinkButton, actionConfirm, DayRangePicker } from 'parsec-admin';
import useApi from '../api';
import { Button } from 'antd';
import MyTableList from '@components/myTableList';
import permisstion from '@utils/permisstion';
import { ExportOutlined } from '@ant-design/icons';
import saveAs from 'file-saver';

export default () => {
  const { request: handleExport, loading: exportLoading } = useApi.exportOrder({
    needInit: false
  });
  const [queryParams, setQueryParams] = useState({} as any);
  return (
    <MyTableList
      tableTitle={'医生登录日志'}
      getList={({ params }) => {
        setQueryParams({ ...params });
        return useApi.docLoginlog.request(params);
      }}
      action={
        <Button
          type={'default'}
          loading={exportLoading}
          icon={<ExportOutlined />}
          onClick={() =>
            handleExport({ ...queryParams, isExport: 1 }).then(data =>
              saveAs(data, `医生登录日志.xls`)
            )
          }>
          导出
        </Button>
      }
      columns={useMemo(
        () => [
          {
            title: '账号',
            dataIndex: 'account',
            width: 100,
            search: true
          },
          {
            title: '医生姓名',
            dataIndex: 'doctorName',
            width: 100,
            search: true
          },
          {
            title: '所属院区',
            dataIndex: 'districtName',
            width: 100
          },
          {
            title: '科室',
            dataIndex: 'deptName',
            width: 150,
            search: true
          },
          {
            title: '医生职称',
            dataIndex: 'level',
            width: 100
          },
          {
            title: 'OpenID',
            dataIndex: 'openId',
            width: 100
          },
          {
            title: '登陆时间',
            dataIndex: 'createTime',
            width: 200,
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                valueFormat={'YYYY-MM-DD HH:mm:ss'}
              />
            ),
            searchIndex: ['startTime', 'endTime']
          }
        ],
        []
      )}
    />
  );
};
