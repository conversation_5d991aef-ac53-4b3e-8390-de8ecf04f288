import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import QueryString from 'qs';
import env from '@src/configs/env';
import moment from 'moment';
import { ApiResponse, ListApiResponseData } from '@src/api/request';

// 1(微信智慧医院) 2(支付宝智慧医院) 3(自助机) 4(互联网) 5(融合版项目)
export type TPlatformSource = 1 | 2 | 3 | 4 | 5;

export type TDateRange = [moment.Moment, moment.Moment];

// 1：点击，2浏览，3：接口，4：搜索
export type TEventType = 1 | 2 | 3 | 4;

export interface IEventStatisticsResData {
  eventCode: string;
  eventName: string;
  count: number;
  percentage: string;
}

export default {
  // 概览统计
  getOverviewStatistics: createApiHooks(
    (params: {
      hisId: string;
      startTime: string;
      endTime: string;
      platformSource: TPlatformSource;
    }) =>
      request.get<
        ApiResponse<{
          totalCount: number;
          newUserCount: number;
          addPatientCount: number;
        }>
      >('/mch/statistics/burial-point/overviewStatistics', {
        params
      })
  ),
  // 点击事件统计 / 页面统计数据
  getEventStatistics: createApiHooks(
    (params: {
      startTime: string;
      endTime: string;
      hisId: string;
      platformSource: TPlatformSource;
      eventType: TEventType;
      eventName?: string;
      pageNum?: number;
      numPerPage?: number;
    }) =>
      request.get<ListApiResponseData<IEventStatisticsResData>>(
        '/mch/statistics/burial-point/burialEventInfo',
        { params }
      )
  ),
  // 导出点击事件统计
  exportEventStatistics: (params: {
    hisId: string;
    startTime: string;
    endTime: string;
    eventType: TEventType;
    platformSource: TPlatformSource;
  }) => {
    const url = `${
      env.apiHost
    }/mch/statistics/burial-point/click/export?${QueryString.stringify({
      ...params,
      isExport: '1'
    })}`;
    window.open(url);
  },
  // 导出页面统计
  exportPvStatistics: (params: {
    startTime: string;
    endTime: string;
    hisId: string;
    platformSource: TPlatformSource;
    eventName: string;
    pageNum: number;
    numPerPage: number;
    eventType: TEventType;
  }) => {
    const url = `${
      env.apiHost
    }/mch/statistics/burial-point/visit/export?${QueryString.stringify({
      ...params,
      isExport: '1'
    })}`;
    window.open(url);
  }
};
