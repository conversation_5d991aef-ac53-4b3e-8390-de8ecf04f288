import ClientCountIcon from '@src/images/buriedPointStatistics/<EMAIL>';
import FreshCountIcon from '@src/images/buriedPointStatistics/<EMAIL>';
import VistCountIcon from '@src/images/buriedPointStatistics/<EMAIL>';

export const DATE_FORMAT = 'YYYY-MM-DD';

export const STATISTICS_DATA_ITEMS = [
  {
    label: '访问次数',
    icon: VistCountIcon,
    id: 1,
    tips: '每增加访问一次，计数一次',
    value: 0
  },
  {
    label: '新增用户',
    icon: FreshCountIcon,
    id: 2,
    tips: '每增加新用户一人，计数一次',
    value: 0
  },
  {
    label: '新增就诊人',
    icon: ClientCountIcon,
    id: 3,
    tips: '每增加新就诊人一人，计数一次',
    value: 0
  }
];

export const SHORTCUT_ITEMS = ['昨日', '最近7天', '最近30天'];

export const PLATFORM_OPTIONS = [
  {
    value: 1,
    label: '智慧医院-微信'
  },
  {
    value: 2,
    label: '智慧医院-支付宝'
  },
  {
    value: 3,
    label: '自助机'
  },
  {
    value: 4,
    label: '互联网医院'
  },
  {
    value: 5,
    label: '融合版项目'
  }
];

export const INIT_PLATFORM_VALUE = 1;

export const EVENT_STATISTICS_FIRST_COLUMN = [
  {
    title: '功能模块',
    key: 'functionModule',
    render: () => '点击次数',
    width: 90,
    fixed: 'left'
  }
];

export const PV_STATISTICS_COLUMNS = [
  {
    title: '序号',
    dataIndex: 'index',
    fixed: 'left',
    width: 60,
    render: (_, record, index) => {
      if (record.eventCode === 'total') return '';
      return index + 1;
    }
  },
  {
    title: '访问页面备注名称',
    dataIndex: 'eventName'
  },
  {
    title: '页面访问次数',
    dataIndex: 'count'
  },
  {
    title: '访问次数占比',
    dataIndex: 'percentage'
  }
];
