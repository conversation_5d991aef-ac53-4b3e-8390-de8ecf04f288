import { useState, useRef } from 'react';
import { Button, Select } from 'antd';
import WaterMarkWrap from '@src/components/waterMarkWrap';
import GeneralStatistics from './components/GeneralStatistics';
import EventStatistics from './components/EventStatistics';
import PvStatistics from './components/PvStatistics';
import { TPlatformSource } from './api';
import { PLATFORM_OPTIONS, INIT_PLATFORM_VALUE } from './consts';
import './index.less';

export default () => {
  const [platform, setPlatform] = useState<TPlatformSource>(
    INIT_PLATFORM_VALUE
  );
  const generalStatisticsRef = useRef<{
    onConfirm: (isRest?: boolean) => void;
  }>();
  const eventStatisticsRef = useRef<{
    onConfirm: (isRest?: boolean) => void;
  }>();
  const pvStatisticsRef = useRef<{
    onConfirm: (isRest?: boolean) => void;
  }>();

  const onSearch = () => {
    generalStatisticsRef.current?.onConfirm();
    eventStatisticsRef.current?.onConfirm();
    pvStatisticsRef.current?.onConfirm();
  };

  const onReset = () => {
    setPlatform(INIT_PLATFORM_VALUE);

    generalStatisticsRef.current?.onConfirm(true);
    eventStatisticsRef.current?.onConfirm(true);
    pvStatisticsRef.current?.onConfirm(true);
  };

  return (
    <WaterMarkWrap>
      <div className='buried-point-statistics-container'>
        <div className='platform-select-container'>
          <div className='platform-select-selector-wrapper'>
            <span className='statistics-picker--label'>平台选择</span>
            <Select
              placeholder='请选择平台'
              options={PLATFORM_OPTIONS}
              onChange={value => setPlatform(value)}
              value={platform}
              className='platform-select--selector'
            />
          </div>
          <div className='platform-select-btn-wrapper'>
            <Button type='primary' onClick={onSearch}>
              查询
            </Button>
            <Button type='ghost' onClick={onReset}>
              重置
            </Button>
          </div>
        </div>
        <div className='statistics-container'>
          <div className='statistics-title'>
            <span>事件统计</span>
          </div>
          <div className='statistics-content-wrapper'>
            <div className='statistics-content'>
              {/* 概览统计 */}
              <GeneralStatistics
                platformSource={platform}
                ref={generalStatisticsRef}
              />
              {/* 时间统计 */}
              <EventStatistics
                platformSource={platform}
                ref={eventStatisticsRef}
              />
              {/* 页面统计 */}
              <PvStatistics platformSource={platform} ref={pvStatisticsRef} />
            </div>
          </div>
        </div>
      </div>
    </WaterMarkWrap>
  );
};
