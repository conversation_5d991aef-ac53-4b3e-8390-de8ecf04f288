.buried-point-statistics-container {
  padding: 10px 20px;

  .statistics-picker-wrapper() {
    display: flex;
    align-items: center;

    .statistics-picker--label {
      font-weight: 400;
      font-size: 16px;
      color: #666666;
    }

    .statistics-picker {
      margin-left: 14px;
      width: 210px;
      height: 40px;
      border-radius: 10px;
      // background-color: #ecf3fa;
    }
  }

  .statistics-btn() {
    margin-left: 40px;
    width: 120px;
    height: 40px;
    border-radius: 10px;
    background-color: #2780d9;
    font-weight: 700;
    font-size: 16px;
    color: #ffffff;
  }

  .platform-select-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #fff;

    .platform-select-selector-wrapper {
      .statistics-picker-wrapper();

      .platform-select--selector {
        margin-left: 14px;

        .ant-select-selector {
          width: 210px;
          height: 40px;
          border-radius: 10px;
          // background-color: #ecf3fa;

          input {
            height: 100%;
          }

          .ant-select-selection-placeholder,
          .ant-select-selection-item {
            line-height: 40px;
          }
        }
      }
    }

    .platform-select-btn-wrapper {
      .ant-btn {
        width: 120px;
        height: 40px;
        border-radius: 10px;
        font-weight: 700;
        font-size: 16px;

        &-primary {
          background-color: #2780d9;
          color: #fff;
        }

        &-ghost {
          margin-left: 14px;
          border-color: #2780d9;
          color: #2780d9;
        }
      }
    }
  }

  .statistics-container {
    margin-top: 10px;
    background-color: #fff;

    .statistics-title {
      display: flex;
      align-items: center;
      padding: 20px 0;

      &::before {
        content: '';
        width: 5px;
        height: 15px;
        background-color: #2780d9;
      }

      span {
        margin-left: 15px;
        font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
        font-weight: 400;
        font-size: 20px;
        color: #333333;
      }
    }

    .statistics-content-wrapper {
      padding: 0 20px;

      .statistics-content {
        .general-statistics {
          border-top: 1px solid #e2e2e2;
          padding: 15px 0 20px;

          &--function {
            display: flex;
            align-items: center;

            .general-statistics-picker-wrapper {
              .statistics-picker-wrapper();
            }

            .general-statistics-shortcut-container {
              display: flex;
              height: 40px;
              border-radius: 10px;
              border: 1px solid #e2e2e2;
              margin-left: 40px;

              .ant-btn {
                padding: 9px 10px;
                height: 100%;
                border: none;
                border-radius: 10px;
                font-weight: 400;
                font-size: 16px;
                color: #333;
                line-height: 0;

                &.active {
                  background-color: #2780d9;
                  color: #fff;
                }
              }
            }

            .general-statistics-confirm-btn {
              .statistics-btn();
            }
          }

          &--data {
            margin-top: 15px;
            display: flex;
            justify-content: space-between;
            gap: 40px;

            &-item {
              display: flex;
              justify-content: center;
              align-items: center;
              flex-direction: column;
              flex: 1;
              width: 513px;
              height: 140px;
              border-radius: 10px;
              background: linear-gradient(180deg, #ecf3fa 0%, #b2d9ff 100%),
                #ffffff;

              .label-wrapper {
                display: flex;
                align-items: center;

                .icon {
                  width: 40px;
                  height: 40px;
                }

                .label {
                  margin-left: 5px;
                  font-weight: 400;
                  font-size: 16px;
                  color: #666666;
                }

                .tool-tips {
                  cursor: pointer;
                  margin-left: 5px;
                  width: 16px;
                  height: 16px;
                }
              }

              .count {
                margin-top: 4px;
                font-weight: 900;
                font-size: 40px;
                color: #333333;
                line-height: 56px;
              }
            }
          }
        }

        .click-statistics {
          border-top: 1px solid #e2e2e2;
          padding: 25px 0 20px;

          &--function {
            display: flex;
            align-items: center;

            .click-statistics-picker-wrapper {
              .statistics-picker-wrapper();
            }

            .click-statistics--confirm-btn {
              .statistics-btn();
            }

            .click-statistics-export-btn {
              .statistics-btn();
              margin-left: auto;
            }
          }

          &--data {
            margin-top: 15px;
          }
        }

        .pv-statistics {
          border-top: 1px solid #e2e2e2;
          padding: 25px 0 28px;

          &--function {
            display: flex;
            align-items: center;

            .pv-statistics-search-input {
              width: 288px;
              height: 40px;
              border-radius: 10px;
              // background-color: #ecf3fa;
              // border: none;

              input {
                // background-color: transparent;
              }
            }

            .pv-statistics-picker-wrapper {
              .statistics-picker-wrapper();
              margin-left: 40px;
            }

            .pv-statistics-confirm-btn {
              .statistics-btn();
            }

            .indicator-tooltips {
              display: flex;
              align-items: center;
              margin-left: 20px;
              cursor: pointer;

              .indicator-tooltips-text {
                font-weight: 400;
                font-size: 16px;
                color: #666666;
              }

              .indicator-tooltips-icon {
                margin-left: 6px;
                width: 15px;
                height: 15px;
                flex-shrink: 0;
              }
            }

            .pv-statistics-export-btn {
              .statistics-btn();
              margin-left: auto;
            }
          }

          &--data {
            margin-top: 33px;
          }
        }
      }
    }
  }
}
