import {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useCallback
} from 'react';
import moment from 'moment';
import { DayRangePicker } from 'parsec-admin';
import { Button, Tooltip, Table, Input } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import env from '@src/configs/env';
import TipsIcon from '@src/images/buriedPointStatistics/<EMAIL>';
import { SearchOutlined } from '@ant-design/icons';
import {
  DATE_FORMAT,
  PV_STATISTICS_COLUMNS,
  INIT_PLATFORM_VALUE
} from '../../consts';
import useApi, {
  TPlatformSource,
  TDateRange,
  TEventType,
  IEventStatisticsResData
} from '../../api';

export default forwardRef((props: { platformSource: TPlatformSource }, ref) => {
  const { platformSource } = props;

  const [searchKey, setSearchKey] = useState('');
  const [dateRange, setDateRange] = useState<TDateRange>([
    moment().startOf('day'),
    moment()
  ]);
  const [pvStatisticsData, setPvStatisticsData] = useState<
    Array<IEventStatisticsResData>
  >([]);
  const [isLoading, setIsLoading] = useState(false);
  const [pagination, setPagination] = useState<{
    current: number;
    total: number;
  }>({
    current: 1,
    total: 0
  });

  const onDateRangeChange = (dates: TDateRange) => {
    setDateRange(dates);
  };

  const onConfirm = useCallback(
    async (isReset = false, pageNum = null) => {
      const params = {
        eventType: 2 as TEventType,
        hisId: env.hisId,
        startTime: dateRange[0].format(DATE_FORMAT),
        endTime: dateRange[1].format(DATE_FORMAT),
        platformSource,
        eventName: searchKey,
        pageNum: pageNum ?? pagination.current,
        numPerPage: 10
      };

      if (isReset) {
        const newDateRange: TDateRange = [moment().startOf('day'), moment()];

        setDateRange(newDateRange);
        setSearchKey('');
        setPagination({
          current: 1,
          total: 0
        });

        params.startTime = newDateRange[0].format(DATE_FORMAT);
        params.endTime = newDateRange[1].format(DATE_FORMAT);
        params.platformSource = INIT_PLATFORM_VALUE;
        params.eventName = '';
        params.pageNum = 1;
      }

      setIsLoading(true);
      const res = await useApi.getEventStatistics.request(params).catch(err => {
        console.log('获取页面访问统计接口报错：', err);
      });
      setIsLoading(false);

      setPvStatisticsData(res?.data?.recordList || []);
      setPagination({
        ...pagination,
        total: res?.data?.totalCount || 0
      });
    },
    [dateRange, platformSource, pagination, searchKey]
  );

  useImperativeHandle(
    ref,
    () => ({
      onConfirm
    }),
    [onConfirm]
  );

  useEffect(() => {
    onConfirm();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onPageChange = (page: number) => {
    setPagination({
      ...pagination,
      current: page
    });

    onConfirm(false, page);
  };

  const onExport = () => {
    useApi.exportPvStatistics({
      eventType: 2 as TEventType,
      hisId: env.hisId,
      startTime: dateRange[0].format(DATE_FORMAT),
      endTime: dateRange[1].format(DATE_FORMAT),
      platformSource,
      eventName: searchKey,
      pageNum: 1,
      numPerPage: 10
    });
  };

  return (
    <div className='pv-statistics'>
      <div className='pv-statistics--function'>
        <Input
          placeholder='请输入页面名称'
          suffix={<SearchOutlined className='pv-statistics-search-icon' />}
          className='pv-statistics-search-input'
          value={searchKey}
          onChange={e => setSearchKey(e.target.value)}
        />
        <div className='pv-statistics-picker-wrapper'>
          <span className='statistics-picker--label'>页面访问统计</span>
          <DayRangePicker
            valueFormat={DATE_FORMAT}
            placeholder={['开始时间', '结束时间']}
            disabledDate={d => {
              return d.isAfter(moment());
            }}
            value={dateRange}
            onCalendarChange={value => {
              onDateRangeChange(value as TDateRange);
            }}
            allowClear={false}
          />
        </div>
        <Button
          type='primary'
          className='pv-statistics-confirm-btn'
          onClick={() => onConfirm()}>
          确定
        </Button>
        <Tooltip title='页面点击进入的次数' className='indicator-tooltips'>
          <span className='indicator-tooltips-text'>指标说明</span>
          <img src={TipsIcon} className='indicator-tooltips-icon' alt='' />
        </Tooltip>
        <Button
          type='primary'
          className='pv-statistics-export-btn'
          onClick={onExport}>
          导出
        </Button>
      </div>
      <div className='pv-statistics--data'>
        <Table
          dataSource={pvStatisticsData}
          loading={isLoading}
          columns={PV_STATISTICS_COLUMNS as ColumnsType<any>}
          pagination={{
            ...pagination,
            pageSize: 10,
            onChange: onPageChange,
            size: 'small',
            showTotal: total => `共 ${total} 条`,
            showQuickJumper: true
          }}
          scroll={{ x: '100%' }}
          className='pv-statistics-table'
        />
      </div>
    </div>
  );
});
