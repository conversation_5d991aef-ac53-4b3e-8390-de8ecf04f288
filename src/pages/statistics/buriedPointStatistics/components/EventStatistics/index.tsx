import {
  useState,
  useEffect,
  forwardRef,
  useImperative<PERSON>andle,
  useCallback
} from 'react';
import moment from 'moment';
import { DayRangePicker } from 'parsec-admin';
import { Button, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import env from '@src/configs/env';
import {
  DATE_FORMAT,
  EVENT_STATISTICS_FIRST_COLUMN,
  INIT_PLATFORM_VALUE
} from '../../consts';
import useApi, {
  TPlatformSource,
  TDateRange,
  TEventType,
  IEventStatisticsResData
} from '../../api';

export default forwardRef((props: { platformSource: TPlatformSource }, ref) => {
  const { platformSource } = props;

  const [dateRange, setDateRange] = useState<TDateRange>([
    moment().startOf('day'),
    moment()
  ]);
  const [eventStatisticsColumns, setEventStatisticsColumns] = useState<
    ColumnsType<any>
  >([]);
  const [eventStatisticsData, setEventStatisticsData] = useState<
    [Record<string, number>?]
  >([]);
  const [isLoading, setIsLoading] = useState(false);

  const onDateRangeChange = (dates: TDateRange) => {
    setDateRange(dates);
  };

  const transformResData = useCallback(
    (respData?: Array<IEventStatisticsResData>) => {
      if (!respData || !respData.length) {
        return {
          columns: [],
          data: []
        };
      }

      const columns: ColumnsType<any> = [];
      const record = {};

      respData.forEach(item => {
        const { eventCode, count, eventName } = item;

        record[eventCode] = count;
        columns.push({
          title: eventName,
          dataIndex: eventCode
        });
      });

      return {
        columns: [...EVENT_STATISTICS_FIRST_COLUMN, ...columns],
        data: [record]
      };
    },
    []
  );

  const onConfirm = useCallback(
    async (isReset = false) => {
      const params = {
        eventType: 1 as TEventType,
        hisId: env.hisId,
        startTime: dateRange[0].format(DATE_FORMAT),
        endTime: dateRange[1].format(DATE_FORMAT),
        platformSource
      };

      if (isReset) {
        const newDateRange: TDateRange = [moment().startOf('day'), moment()];

        setDateRange(newDateRange);

        params.startTime = newDateRange[0].format(DATE_FORMAT);
        params.endTime = newDateRange[1].format(DATE_FORMAT);
        params.platformSource = INIT_PLATFORM_VALUE;
      }

      setIsLoading(true);
      const res = await useApi.getEventStatistics.request(params).catch(err => {
        console.log('获取事件统计接口报错：', err);
      });
      setIsLoading(false);

      const { columns, data: validData } = transformResData(
        res?.data?.recordList
      );

      setEventStatisticsColumns(columns as ColumnsType<any>);
      setEventStatisticsData(validData as [Record<string, number>?]);
    },
    [dateRange, platformSource, transformResData]
  );

  const onExport = () => {
    useApi.exportEventStatistics({
      eventType: 1 as TEventType,
      hisId: env.hisId,
      startTime: dateRange[0].format(DATE_FORMAT),
      endTime: dateRange[1].format(DATE_FORMAT),
      platformSource
    });
  };

  useImperativeHandle(
    ref,
    () => ({
      onConfirm
    }),
    [onConfirm]
  );

  useEffect(() => {
    onConfirm();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className='click-statistics'>
      <div className='click-statistics--function'>
        <div className='click-statistics-picker-wrapper'>
          <span className='statistics-picker--label'>点击事件统计</span>
          <DayRangePicker
            valueFormat={DATE_FORMAT}
            placeholder={['开始时间', '结束时间']}
            disabledDate={d => {
              return d.isAfter(moment());
            }}
            value={dateRange}
            onCalendarChange={value => {
              onDateRangeChange(value as TDateRange);
            }}
            allowClear={false}
          />
        </div>
        <Button
          type='primary'
          className='click-statistics--confirm-btn'
          onClick={() => {
            onConfirm();
          }}>
          确定
        </Button>
        <Button
          type='primary'
          className='click-statistics-export-btn'
          onClick={onExport}>
          导出
        </Button>
      </div>
      <div className='click-statistics--data'>
        <Table
          dataSource={eventStatisticsData}
          loading={isLoading}
          columns={eventStatisticsColumns}
          pagination={false}
          scroll={{ x: '100%' }}
          className='click-statistics-table'
        />
      </div>
    </div>
  );
});
