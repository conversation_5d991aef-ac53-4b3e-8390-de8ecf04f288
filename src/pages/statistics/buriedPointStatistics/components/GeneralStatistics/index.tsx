import {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useCallback
} from 'react';
import moment from 'moment';
import { DayRangePicker } from 'parsec-admin';
import { Button, Tooltip } from 'antd';
import env from '@src/configs/env';
import TipsIcon from '@src/images/buriedPointStatistics/<EMAIL>';
import {
  STATISTICS_DATA_ITEMS,
  SHORTCUT_ITEMS,
  DATE_FORMAT,
  INIT_PLATFORM_VALUE
} from '../../consts';
import useApi, { TPlatformSource, TDateRange } from '../../api';

export default forwardRef((props: { platformSource: TPlatformSource }, ref) => {
  const { platformSource } = props;

  const [dateRange, setDateRange] = useState<TDateRange>([
    moment().startOf('day'),
    moment()
  ]);
  const [activeShortcut, setActiveShortcut] = useState<number>();
  const [generalStatisticsData, setGeneralStatisticsData] = useState(
    STATISTICS_DATA_ITEMS
  );

  const onDateRangeChange = (dates: TDateRange) => {
    setDateRange(dates);
    setActiveShortcut(undefined);
  };

  const onActiveShortcutChange = (idx: number) => () => {
    setActiveShortcut(idx);

    switch (idx) {
      // 昨天
      case 0:
        setDateRange([
          moment().subtract(1, 'day'),
          moment().subtract(1, 'day')
        ]);
        break;
      // 最近七天
      case 1:
        setDateRange([moment().subtract(7, 'day'), moment()]);
        break;
      // 最近30天
      case 2:
        setDateRange([moment().subtract(30, 'day'), moment()]);
        break;
      default:
        break;
    }
  };

  const onConfirm = useCallback(
    async (isReset = false) => {
      const params = {
        hisId: env.hisId,
        startTime: dateRange[0].format(DATE_FORMAT),
        endTime: dateRange[1].format(DATE_FORMAT),
        platformSource
      };

      if (isReset) {
        const newDateRange: TDateRange = [moment().startOf('day'), moment()];

        setDateRange(newDateRange);
        setActiveShortcut(undefined);

        params.startTime = newDateRange[0].format(DATE_FORMAT);
        params.endTime = newDateRange[1].format(DATE_FORMAT);
        params.platformSource = INIT_PLATFORM_VALUE;
      }

      const res = await useApi.getOverviewStatistics
        .request(params)
        .catch(err => {
          console.log('获取概况统计接口报错：', err);
        });

      const { totalCount, newUserCount, addPatientCount } = res?.data || {};

      setGeneralStatisticsData([
        {
          ...STATISTICS_DATA_ITEMS[0],
          value: totalCount ?? 0
        },
        {
          ...STATISTICS_DATA_ITEMS[1],
          value: newUserCount ?? 0
        },
        {
          ...STATISTICS_DATA_ITEMS[2],
          value: addPatientCount ?? 0
        }
      ]);

      console.log('getOverviewStatistics res==>', res);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    },
    [dateRange, platformSource]
  );

  useImperativeHandle(
    ref,
    () => ({
      onConfirm
    }),
    [onConfirm]
  );

  useEffect(() => {
    onConfirm();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className='general-statistics'>
      <div className='general-statistics--function'>
        <div className='general-statistics-picker-wrapper'>
          <span className='statistics-picker--label'>概况统计</span>
          <DayRangePicker
            valueFormat={DATE_FORMAT}
            placeholder={['开始时间', '结束时间']}
            disabledDate={d => {
              return d.isAfter(moment());
            }}
            value={dateRange}
            onCalendarChange={value => {
              onDateRangeChange(value as TDateRange);
            }}
            allowClear={false}
          />
        </div>
        <div className='general-statistics-shortcut-container'>
          {SHORTCUT_ITEMS.map((text, idx) => (
            <Button
              className={activeShortcut === idx ? 'active' : ''}
              onClick={onActiveShortcutChange(idx)}
              key={idx}>
              {text}
            </Button>
          ))}
        </div>
        <Button
          type='primary'
          className='general-statistics-confirm-btn'
          onClick={() => {
            onConfirm();
          }}>
          确定
        </Button>
      </div>
      <div className='general-statistics--data'>
        {generalStatisticsData.map(({ id, label, icon, tips, value }) => (
          <div key={id} className='general-statistics--data-item'>
            <div className='label-wrapper'>
              <img className='icon' src={icon} alt='' />
              <span className='label'>{label}</span>
              <Tooltip title={tips} className='tool-tips'>
                <img src={TipsIcon} alt='' />
              </Tooltip>
            </div>
            <span className='count'>{value}</span>
          </div>
        ))}
      </div>
    </div>
  );
});
