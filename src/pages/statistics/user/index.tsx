import React, { useState } from 'react';
import NavItem from './components/navItem';
import UserStatics from './components/userStatics';
import UserProfile from './components/userProfile';
import DistrictStatics from './components/districtStatics';
import { CardLayout } from 'parsec-admin';
import useApi from '@pages/index/Charts/api';
import styled from 'styled-components';

export default () => {
  const [type, setType] = useState<1 | 2 | 3>(1);

  const { data } = useApi.公共统计接口({
    initValue: [],
    params: {
      configId: 43
    }
  });

  const getImgUrl = (title: string) => {
    switch (title) {
      case '访问人数':
        return require('./images/access.png');
      case '注册人数':
        return require('./images/register.png');
      case '咨询人数':
        return require('./images/zixun.jpg');
      case '问诊人数':
        return require('./images/wenzhen.jpg');
      default:
        return require('./images/access.png');
    }
  };

  return (
    <div style={{ overflow: 'hidden' }}>
      <CardLayout>
        <PageTitle>医院用户分析</PageTitle>
        <Wrapper>
          <h5>用户概况</h5>
          <div className='head'>
            {data.map((item: any) => (
              <div>
                <NavItem
                  key={item.title}
                  img={getImgUrl(item.title)}
                  title={item.title}
                  number={
                    item.visitor ||
                    item.resiger ||
                    item.inquiryUser ||
                    item?.consultUser
                  }
                />
              </div>
            ))}
          </div>
          <UserStatics />
          <h5>就诊人画像</h5>
          <UserProfile type={type} setType={setType} />
          <DistrictStatics type={type} />
        </Wrapper>
      </CardLayout>
    </div>
  );
};

export const Wrapper = styled.div`
  > h5 {
    font-size: 22px;
    margin-bottom: 26px;
    font-weight: bolder;
    position: relative;
  }
  > .head {
    margin: 26px 0 30px;
    width: calc(100%);
    display: flex;
    flex-wrap: wrap;
    > div {
      flex: 1;
      width: 20%;
      padding: 0 20px;
    }
  }
  .divider {
    height: 10px;
    background-color: #ecf3fa;
  }
`;
const PageTitle = styled.h5`
  font-size: 22px;
  margin-bottom: 26px;
  font-weight: bolder;
  position: relative;
  &::before {
    position: absolute;
    height: 13px;
    top: 0;
    bottom: 0;
    left: -24px;
    content: '';
    width: 5px;
    background-color: #2780d9;
    margin: auto;
  }
`;
