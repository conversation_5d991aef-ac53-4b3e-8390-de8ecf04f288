import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';

interface UserOverViewParams {
  hisId: string;
  type: 1 | 2; //1 新增用户  2 累计用户
  beginTime: string;
  endTime: string;
}
export interface UserOverViewItem {
  date: string;
  visitCount: number;
  registerCount: number;
  patientCount: number;
  consultCount: number; // 咨询人数
  inquiryCount: number; // 问诊人数
}
export interface UserAgeStatics {
  manCount: number; // 男性人数
  womanCount: number; // 女性人数
  ageData: {
    ageGroup: string;
    count: number;
  }[];
}

export interface UserStaticsData {
  consultCount: 7;
  date: null;
  inquiryCount: 15;
  registerCount: 0;
  visitCount: 5;
}

export default {
  getCommonStatics: createApiHooks((params: { hisId: string }) =>
    request.get<Record<string, unknown>>('/mch/his/statistics/common', {
      params
    })
  ),
  getUserOverView: createApiHooks((params: UserOverViewParams) =>
    request.get<{ dateData: UserOverViewItem[]; countData: UserStaticsData }>(
      '/mch/statistics/user/survey',
      {
        params
      }
    )
  ),
  getUserAge: createApiHooks((params: { hisId: string; type: 1 | 2 | 3 }) =>
    request.get<UserAgeStatics>('/mch/statistics/user/portraitAge', {
      params
    })
  ),
  getUserDistrict: createApiHooks(
    (params: { hisId: string; type: 1 | 2 | 3; level: 1 | 2 }) =>
      request.get<{ value: string; count: number }[]>(
        '/mch/statistics/user/portraitArea',
        {
          params
        }
      )
  )
};
