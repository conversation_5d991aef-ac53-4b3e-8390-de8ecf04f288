import React, { FC } from 'react';
import styled from 'styled-components';

interface NavItemProps {
  className?: string;
  img?: string;
  title?: string;
  number: number | string;
  unit?: React.ReactNode;
}

const NavItem: FC<NavItemProps> = props => {
  const formatNumber = (value: number | null | undefined | string): string => {
    if (!value) {
      return '0';
    }
    if (typeof value === 'string') {
      return value;
    }
    return value.toLocaleString('en-US');
  };
  return (
    <div className={props.className}>
      {props.img && <img src={props.img} alt={props.title} />}
      <div>
        <h6>
          {typeof props.number === 'string'
            ? props.number
            : formatNumber(props.number)}
          {props.unit || '人'}
        </h6>
        <div className='num'>{props.title}</div>
      </div>
    </div>
  );
};

export default styled(NavItem)`
  width: 100%;
  box-shadow: 0px 0px 30px rgba(39, 128, 217, 0.16);
  height: 160px;
  display: inline-flex;
  align-items: center;
  font-size: 18px;
  color: #666;
  padding-left: 20px;
  border-radius: 15px;
  > img {
    width: 54px;
    height: 54px;
    margin-right: 48px;
  }
  h6 {
    font-size: 26px;
    color: #2780d9;
    margin-bottom: 5px;
    font-weight: bold;
  }
`;
