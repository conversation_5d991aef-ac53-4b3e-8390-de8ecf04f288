import React, { useCallback, useState, useMemo } from 'react';
import StyledComponent from '@pages/index/Charts/CommChart.style';
import classnames from 'classnames';
import useApi, { UserOverViewItem, UserStaticsData } from '../apis';
import Charts from '@components/charts';
import SelectDate from '@pages/index/Charts/SelectDate';
import styled from 'styled-components';
import moment from 'moment';
import env from '@src/configs/env';

export default () => {
  const [type, setType] = useState<1 | 2>(1); //1 新增， 2 累计
  const [params, setParams] = useState<any>();
  const hisId = env.hisId;

  const { data, loading } = useApi.getUserOverView({
    initValue: [],
    params: {
      hisId: hisId,
      type,
      beginTime: params?.beginTime,
      endTime: params?.endTime
    },
    needInit: !!params
  });
  const dataToOptionConfig = (datas: UserOverViewItem[]) => {
    if (!datas) {
      return {
        yData1: [],
        yData2: [],
        yData3: [],
        yData4: [],
        xData: []
      };
    }
    const res: {
      yData1: any[];
      yData2: any[];
      yData3: any[];
      yData4: any[];
      xData: any[];
    } = {} as any;
    res.yData1 = datas.map(data => data.visitCount || 0);
    res.yData2 = datas.map(data => data.registerCount || 0);
    res.yData3 = datas.map(data => data.consultCount || 0);
    res.yData4 = datas.map(data => data.inquiryCount || 0);
    res.xData = datas.map(data => moment(data.date).format('YYYY-MM-DD'));
    return res;
  };
  const { yData1, yData2, yData3, yData4, xData } = dataToOptionConfig(
    data.dateData
  );

  return (
    <Wrapper>
      <div>
        <div
          className={classnames({ selected: type === 1 })}
          onClick={() => {
            setType(1);
          }}>
          新增用户
        </div>
        <div
          className={classnames({ selected: type === 2 })}
          onClick={() => {
            setType(2);
          }}>
          累计用户
        </div>
      </div>
      <div className='user-statics'>
        <StyledComponent>
          <div className='head'>
            <div className='title'>时间</div>
            <div className='date'>
              <SelectDate
                hideToday
                isUserAnalysis={true}
                onChange={useCallback(v => {
                  setParams({
                    beginTime: v[0].format('YYYY-MM-DD 00:00:00'),
                    endTime: v[1].format('YYYY-MM-DD 23:59:59')
                  });
                }, [])}
              />
            </div>
          </div>
          <div className='container'>
            <LineChart
              xData={xData}
              title={{
                show: type === 1,
                text: `访问人数： ${data?.countData?.visitCount ??
                  ''}     注册人数： ${data?.countData?.registerCount ??
                  ''}     咨询人数： ${data?.countData?.consultCount ??
                  ''}     问诊人数： ${data?.countData?.inquiryCount ?? ''}`,
                bottom: 10,
                left: 'center'
              }}
              options={useMemo(
                () => [
                  {
                    name: '访问人数',
                    data: yData1,
                    color: 'rgb(84,126,249)'
                  },
                  {
                    name: '注册人数',
                    data: yData2,
                    color: 'rgb(86,213,153)'
                  },
                  {
                    name: '咨询人数',
                    data: yData3,
                    color: 'rgb(83,98,134)'
                  },
                  {
                    name: '问诊人数',
                    data: yData4,
                    color: 'rgb(64,128,187)'
                  }
                ],
                [yData1, yData2, yData3, yData4]
              )}
              loading={loading}
            />
          </div>
        </StyledComponent>
      </div>
    </Wrapper>
  );
};

export const LineChart = ({
  title,
  xData,
  options,
  loading
}: {
  xData: any[];
  data?: { countData: UserStaticsData };
  title?: echarts.EChartTitleOption;
  loading: boolean;
  options: { name: string; data: any[]; color: string }[];
}) => {
  const echartsOption: echarts.EChartOption = useMemo(() => {
    return {
      title,
      legend: {
        data: options.map(({ name }) => ({ name, icon: 'rect' })),
        itemGap: 24,
        top: 5,
        right: 10
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        backgroundColor: '#fff',
        textStyle: {
          color: '#333'
        }
      },
      color: options.map(({ color }) => color),
      grid: {
        left: '3%',
        right: '4%',
        top: 35,
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: xData
        }
      ],
      yAxis: [
        {
          type: 'value',
          splitLine: {
            show: false
          }
        }
      ],
      series: options.map(({ data, name }) => ({
        name: name,
        type: 'line',
        // areaStyle: {},
        smooth: true,
        data: data
      }))
    };
  }, [options, xData, title]);
  return (
    <Charts
      loading={loading}
      option={echartsOption}
      style={{ width: '100%', height: '100%' }}
    />
  );
};

const Wrapper = styled.div`
  > :nth-child(1) {
    display: flex;
    margin-bottom: 15px;
    > div {
      cursor: pointer;
      min-width: 130px;
      line-height: 42px;
      height: 42px;
      box-sizing: border-box;
      border-radius: 25px;
      font-size: 18px;
      font-weight: bold;
      border: 1px solid #ffffff;
      text-align: center;
      padding: 0 28px;
      margin-right: 20px;
    }
    > div.selected {
      color: #2780d9;
      border: 1px solid #2780d9;
    }
  }
  > :nth-child(2) {
    width: 100%;
    height: 450px;
  }
`;
