import React, { useState, useMemo } from 'react';
import { Table, Row, Col } from 'antd';
import classnames from 'classnames';
import Charts from '@components/charts';
import useApi from '../apis';
import styled from 'styled-components';
import echarts from 'echarts';
import 'echarts/map/js/china';
import chongqing from 'echarts/map/json/province/chongqing.json';
import { ColumnsType } from 'antd/lib/table';
import env from '@src/configs/env';

echarts.registerMap('chongqing', chongqing);

export default (props: { type: 1 | 2 | 3 }) => {
  const { type } = props;
  const [level, setLevel] = useState<1 | 2>(1);
  const hisId = env.hisId;
  const { data, loading } = useApi.getUserDistrict({
    params: {
      level,
      type,
      hisId
    },
    needInit: !!hisId,
    initValue: []
  });

  const columns: ColumnsType<any> = [
    {
      title: '地区',
      dataIndex: 'district',
      key: 'district',
      align: 'center'
    },
    {
      title: '人数',
      dataIndex: 'count',
      key: 'count',
      align: 'center'
    },
    {
      title: '占比',
      dataIndex: 'percent',
      key: 'percent',
      align: 'center'
    }
  ];

  //总人数
  const totalNum = useMemo(() => {
    return data.reduce((prev, now) => prev + now.count, 0);
  }, [data]);

  const dataSource = useMemo(() => {
    return data
      .map(item => ({
        district: item.value,
        count: item.count,
        percent:
          totalNum === 0
            ? '0%'
            : ((item.count * 100) / totalNum).toFixed(2) + '%',
        key: item.value
      }))
      .sort((prev, now) => now.count - prev.count);
  }, [data, totalNum]);

  const cqOptions: echarts.EChartOption = useMemo(() => {
    return {
      tooltip: {
        triggerOn: 'click',
        formatter: function(e: any) {
          return e.seriesName + '<br />' + e.name + '：' + (e.value || 0);
        }
      },
      visualMap: [
        {
          show: true,
          left: '5%',
          bottom: '5%',
          max: 500,
          min: 0,
          z: 999,
          calculable: false,
          text: ['高', '低'],
          inRange: {
            color: ['#EBF8FF', '#3389E1']
          },
          textStyle: {
            color: '#333'
          },
          seriesIndex: 0
        }
      ],
      geo: {
        map: 'chongqing',
        roam: true,
        scaleLimit: {
          min: 1,
          max: 12
        },
        zoom: 1,
        top: 50,
        left: '25%',
        label: {
          normal: {
            show: false,
            fontSize: '14',
            color: 'rgba(0,0,0,0.7)'
          }
        },
        itemStyle: {
          normal: {
            //shadowBlur: 50,
            //shadowColor: 'rgba(0, 0, 0, 0.2)',
            borderColor: 'rgba(0, 0, 0, 0.2)'
          },
          emphasis: {
            areaColor: '#f2d5ad',
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            borderWidth: 0
          }
        }
      },
      series: [
        {
          name: '重庆分布',
          type: 'map',
          geoIndex: 0,
          data: data.map(item => ({
            name: item.value,
            value: item.count
          }))
        }
      ]
    };
  }, [data]);

  const mapOptions: echarts.EChartOption = useMemo(() => {
    return {
      tooltip: {
        triggerOn: 'click',
        formatter: function(e: any) {
          return e.seriesName + '<br />' + e.name + '：' + (e.value || 0);
        }
      },
      visualMap: [
        {
          show: true,
          left: '5%',
          bottom: '5%',
          max: 500,
          min: 0,
          z: 999,
          calculable: false,
          text: ['高', '低'],
          inRange: {
            color: ['#EBF8FF', '#3389E1']
          },
          textStyle: {
            color: '#333'
          },
          seriesIndex: 0
        }
      ],
      geo: {
        map: 'china',
        roam: false,
        scaleLimit: {
          min: 1,
          max: 2
        },
        zoom: 1,
        top: 80,
        left: '20%',
        label: {
          normal: {
            show: !0,
            fontSize: '14',
            color: 'rgba(0,0,0,0.7)'
          }
        },
        itemStyle: {
          normal: {
            //shadowBlur: 50,
            //shadowColor: 'rgba(0, 0, 0, 0.2)',
            borderColor: 'rgba(0, 0, 0, 0.2)'
          },
          emphasis: {
            areaColor: '#f2d5ad',
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            borderWidth: 0
          }
        }
      },
      series: [
        {
          name: '地域分布',
          type: 'map',
          geoIndex: 0,
          data: data.map(item => ({
            name: item.value,
            value: item.count
          }))
        }
      ]
    };
  }, [data]);

  const pieOptions: echarts.EChartOption = useMemo(() => {
    return {
      color: [
        '#0984CE',
        '#19AAC8',
        '#47D99D',
        '#B0EA8C',
        '#DEEB8E',
        '#C9D097',
        '#AEA9BA',
        '#6B3B9F',
        '#6551E2',
        '#273FC0',
        '#0984CE'
      ],
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)'
      },
      series: [
        {
          name: '地域分布',
          type: 'pie',
          radius: '65%',
          // left: '30%',
          center: ['44%', '50%'],
          data: data.map(item => ({
            name: item.value,
            value: item.count
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
  }, [data]);

  return (
    <Wrapper>
      <div>
        <span>地域分布</span>
        <div
          className={classnames({ selected: level === 1 })}
          onClick={() => {
            setLevel(1);
          }}>
          全国分布
        </div>
        <div
          className={classnames({ selected: level === 2 })}
          onClick={() => {
            setLevel(2);
          }}>
          省级分布
        </div>
      </div>
      <Row>
        <Col xs={{ span: 24 }} lg={{ span: 14 }}>
          {level === 1 ? (
            <div>
              <div className='map-title'>地域分布</div>
              <Charts
                loading={loading}
                option={mapOptions}
                style={{ width: '100%', height: '533px' }}
              />
            </div>
          ) : (
            <div>
              <div className='map-title'>重庆分布</div>
              <Charts
                loading={loading}
                option={cqOptions}
                style={{ width: '100%', height: '533px' }}
              />
            </div>
          )}

          <Charts
            loading={loading}
            option={pieOptions}
            style={{ width: '100%', height: '533px' }}
          />
        </Col>
        <Col xs={{ span: 24 }} lg={{ span: 9, push: 1 }}>
          <Table columns={columns} dataSource={dataSource} pagination={false} />
        </Col>
      </Row>
    </Wrapper>
  );
};

const Wrapper = styled.div`
  > :nth-child(1) {
    display: flex;
    margin-bottom: 15px;

    > span {
      font-size: 20px;
      color: #767676;
      font-weight: bolder;
      padding: 7px 20px 0 0;
    }

    > div {
      cursor: pointer;
      min-width: 130px;
      line-height: 42px;
      height: 42px;
      box-sizing: border-box;
      border-radius: 25px;
      font-size: 18px;
      font-weight: bold;
      border: 1px solid #ffffff;
      text-align: center;
      padding: 0 28px;
      margin-right: 20px;
    }

    > div.selected {
      color: #2780d9;
      border: 1px solid #2780d9;
    }
  }

  .bar {
    width: 100%;
    height: 25px;
    background-color: #87d4d6;
    position: relative;
    border-radius: 6px;
    margin-bottom: 60px;

    .bar-male {
      background-color: #5897d7;
      transition: all 1s ease-in;
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      height: 25px;
      border-radius: 6px 0 0 6px;
    }
  }

  > h6 {
    font-size: 20px;
    color: #666;
  }

  .map-title {
    color: #333;
    font-size: 19px;
    text-align: center;
    font-weight: bold;
  }
`;
