import React, { useMemo } from 'react';
import { Table, Row, Col } from 'antd';
import classnames from 'classnames';
import Charts from '@components/charts';
import useApi from '../apis';

import styled from 'styled-components';
import { ColumnsType } from 'antd/lib/table';
import env from '@src/configs/env';

export default (props: {
  type: 1 | 2 | 3;
  setType: (num: 1 | 2 | 3) => void;
}) => {
  const { type, setType } = props;
  const hisId = env.hisId;
  const { data, loading } = useApi.getUserAge({
    params: {
      type,
      hisId
    },
    needInit: !!hisId,
    initValue: { ageData: [], manCount: 0, womanCount: 0 }
  });
  //总人数
  const totalNum = useMemo(() => {
    return data.ageData.reduce((prev, now) => prev + now.count, 0);
  }, [data]);

  const womanPercentage = useMemo(() => {
    return (
      (data.manCount + data.manCount === 0
        ? 0
        : ((data.womanCount * 100) / (data.manCount + data.womanCount)).toFixed(
            2
          )) + '%'
    );
  }, [data]);

  const manPercentage = useMemo(() => {
    return (
      (data.manCount + data.manCount === 0
        ? 0
        : ((data.manCount * 100) / (data.manCount + data.womanCount)).toFixed(
            2
          )) + '%'
    );
  }, [data]);

  const columns: ColumnsType<any> = [
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      align: 'center'
    },
    {
      title: '人数',
      dataIndex: 'count',
      key: 'count',
      align: 'center'
    },
    {
      title: '占比',
      dataIndex: 'percent',
      key: 'percent',
      align: 'center'
    }
  ];
  const dataSource = useMemo(() => {
    return data.ageData.map(item => ({
      age: item.ageGroup,
      count: item.count,
      percent:
        totalNum === 0
          ? '0%'
          : ((item.count * 100) / totalNum).toFixed(2) + '%',
      key: item.ageGroup
    }));
  }, [data, totalNum]);

  const formatNumber = (value: number | null | undefined | string): string => {
    if (!value) {
      return '0';
    }
    if (typeof value === 'string') {
      return value;
    }
    return value.toLocaleString('en-US');
  };

  const echartsOptions: echarts.EChartOption = useMemo(() => {
    return {
      color: ['#3398DB'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
        },
        formatter: (obj: any) => {
          const params = obj?.[0];
          if (!params) {
            return '';
          }
          const { marker, data, seriesName } = params;
          return `${seriesName}<br />${marker}${(data * 100).toFixed(2)}%`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: data.ageData.map(item => item.ageGroup),
          axisTick: {
            alignWithLabel: true
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: function(value: number) {
              return (value * 100).toFixed(2) + '%';
            }
          }
        }
      ],
      series: [
        {
          name: '年龄分布',
          type: 'bar',
          barWidth: '35%',
          data: data.ageData.map(item => item.count / totalNum)
        }
      ]
    };
  }, [data, totalNum]);

  return (
    <Wrapper>
      <div>
        <div
          className={classnames({ selected: type === 1 })}
          onClick={() => {
            setType(1);
          }}>
          全部就诊人
        </div>
        <div
          className={classnames({ selected: type === 2 })}
          onClick={() => {
            setType(2);
          }}>
          咨询用户
        </div>
        <div
          className={classnames({ selected: type === 3 })}
          onClick={() => {
            setType(3);
          }}>
          问诊用户
        </div>
      </div>
      <SexTitle>
        <h6>性别比例</h6>
        <div>
          <div>
            <div className='circle' />
            <span>
              女：{formatNumber(data.womanCount)}人（{womanPercentage}）
            </span>
          </div>
          <div>
            <div className='circle circle2' />
            <span>
              男：{formatNumber(data.manCount)}人（{manPercentage}）
            </span>
          </div>
        </div>
      </SexTitle>
      <div className='bar'>
        <div className='bar-male' style={{ width: manPercentage }} />
      </div>

      <h6>年龄分布</h6>
      <Row>
        <Col xs={{ span: 24 }} lg={{ span: 14 }}>
          <Charts loading={loading} option={echartsOptions} />
        </Col>
        <Col xs={{ span: 24 }} lg={{ span: 9, push: 1 }}>
          <Table columns={columns} dataSource={dataSource} pagination={false} />
        </Col>
      </Row>
    </Wrapper>
  );
};

const Wrapper = styled.div`
  margin-bottom: 30px;
  > :nth-child(1) {
    display: flex;
    margin-bottom: 15px;
    > div {
      cursor: pointer;
      min-width: 130px;
      line-height: 42px;
      height: 42px;
      box-sizing: border-box;
      border-radius: 25px;
      font-size: 18px;
      font-weight: bold;
      border: 1px solid #ffffff;
      text-align: center;
      padding: 0 28px;
      margin-right: 20px;
    }
    > div.selected {
      color: #2780d9;
      border: 1px solid #2780d9;
    }
  }

  .bar {
    width: 100%;
    height: 25px;
    background-color: #87d4d6;
    position: relative;
    border-radius: 6px;
    margin-bottom: 60px;
    .bar-male {
      background-color: #5897d7;
      transition: all 0.5s ease-in-out;
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      height: 25px;
      border-radius: 6px 0 0 6px;
    }
  }
  > h6 {
    font-size: 20px;
    color: #666;
  }
`;

//性别占比样式
const SexTitle = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 21px 0 19px;
  > h6 {
    font-size: 20px;
    color: #666;
  }
  > div {
    font-size: 16px;
    color: #333;
    display: flex;
    > div {
      display: flex;
      align-items: center;
      margin-right: 20px;
      > .circle {
        width: 19px;
        height: 19px;
        background-color: #87d4d6;
        border-radius: 50%;
        margin-right: 13px;
      }
      > .circle2 {
        background-color: #5897d7;
      }
    }
  }
`;
