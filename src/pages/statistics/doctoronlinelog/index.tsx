import React, { useMemo, useCallback, useState, useEffect } from 'react';
import {
  LinkButton,
  actionConfirm,
  DayRangePicker,
  ArrSelect
} from 'parsec-admin';
import useApi from '../api';
import { Button } from 'antd';
import MyTableList from '@components/myTableList';
import permisstion from '@utils/permisstion';
import { ExportOutlined } from '@ant-design/icons';
import SearchSelect from '@components/SearchSelect';
import saveAs from 'file-saver';
import { TransferChange } from '@kqinfo/ui';

export default () => {
  const { request: handleExport, loading: exportLoading } = useApi.exportOnline(
    {
      needInit: false
    }
  );
  const [deptId, setDeptId] = useState('');
  const [queryParams, setQueryParams] = useState({} as any);
  const { data: statis } = useApi.docstats({
    needInit: true,
    params: queryParams
  });
  return (
    <MyTableList
      tableTitle={'医生在线情况'}
      getList={({ params }) => {
        setQueryParams({ ...params });
        return useApi.doconline.request(params);
      }}
      action={
        <Button
          type={'default'}
          loading={exportLoading}
          icon={<ExportOutlined />}
          onClick={() =>
            handleExport({ ...queryParams, isExport: 1 }).then(data =>
              saveAs(data, `医生在线情况.xls`)
            )
          }>
          导出
        </Button>
      }
      columns={useMemo(
        () => [
          {
            title: '医生姓名',
            dataIndex: 'doctorName',
            width: 100,
            searchIndex: 'name',
            search: true
          },
          {
            title: '医生职称',
            dataIndex: 'level',
            width: 100
          },
          {
            title: '科室',
            dataIndex: 'deptName',
            width: 150,
            searchIndex: 'deptId',
            search: (
              <SearchSelect
                allowClear={true}
                getList={async key => {
                  const res = await useApi.deptSearch.request({
                    searchKey: key
                  });
                  return res.map(x => {
                    return {
                      label: `${x.name}`,
                      value: x.no
                    };
                  });
                }}
              />
            )
          },
          {
            title: '是否登录',
            dataIndex: 'isOnline',
            width: 150
          },
          {
            title: '开启图文咨询',
            dataIndex: 'textInquiryStatus',
            width: 100
          },
          {
            title: '开启视频咨询',
            dataIndex: 'videoInquiryStatus',
            width: 100
          },
          {
            title: '开启健康咨询',
            dataIndex: 'healthConsultationStatus',
            width: 100
          }
        ],
        []
      )}
      footer={() => (
        <div>
          当前入网医生{statis.data?.doctorCount}人，登录医生
          {statis.data?.onlineCount}人
        </div>
      )}
    />
  );
};
