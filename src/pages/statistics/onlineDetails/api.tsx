import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ListApiRequestParams, ListApiResponseData } from '@configs/d';
import env from '@configs/env';
import QueryString from 'qs';
type SearchParams = ListApiRequestParams & {
  deptId?: string;
  searchStartTime?: string;
  searchEndTime?: string;
};
export default {
  分页查询医生在线记录: createApiHooks((params: SearchParams) =>
    request.get<
      ListApiResponseData<{
        id: '@natural'; // 配置id
        hisId: '@natural(1000, 9999)'; //医院id
        doctorId: '@string(6)'; //配送规则名称
        doctorName: '@cname(0,1)'; //状态，0：禁用，1：启用
        level: "@pick('主任医师','副主医师')"; //医生职称
        deptId: '@string(6)'; //科室编号
        deptName: '@string(4)科室'; //科室名称
        createTime: '@@datetime'; //创建时间
        startTime: string; //在线时间
      }>
    >('/mch/his/doctor-online-log/by-doctor', {
      params
    })
  ),
  分页查询科室在线记录: createApiHooks((params: SearchParams) =>
    request.get<
      ListApiResponseData<{
        id: '@natural'; // 配置id
        hisId: '@natural(1000, 9999)'; //医院id
        deptId: '@string(6)'; //科室编号
        deptName: '@string(4)科室'; //科室名称
        createTime: '@@datetime'; //创建时间
        startTime: string; //在线时间
      }>
    >('/mch/his/doctor-online-log/by-dept', {
      params
    })
  ),
  exportDept: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${
      env.apiHost
    }/mch/his/doctor-online-log/by-dept?${QueryString.stringify({
      ...p,
      isExport: '1'
    })}`;
    window.open(url);
  },
  exportDoctor: (p: any) => {
    // 本发模式配置代理不能用这种直接下载，测试/正式环境是正常的
    const url = `${
      env.apiHost
    }/mch/his/doctor-online-log/by-doctor?${QueryString.stringify({
      ...p,
      isExport: '1'
    })}`;
    window.open(url);
  }
};
