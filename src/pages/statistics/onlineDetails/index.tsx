import useApi from './api';
import MyTableList from '@components/myTableList';
import React, { useEffect, useMemo, useRef } from 'react';
import { Tag } from '@kqinfo/ui';
import env from '@configs/env';
import SearchSelect from '@components/SearchSelect';
import apis from '@apiHooks';
import qs from 'qs';
// import moment from 'moment';
import { DayRangePicker } from 'parsec-admin';
import { Button, Form } from 'antd';

export default ({ type }: { type: 'dept' | 'doctor' }) => {
  const hisId = env.hisId;
  const { beginTime, endTime } = qs.parse(
    window.location.href.split('?')[1]
  ) as {
    beginTime?: string;
    endTime?: string;
  };
  const paramsRef = useRef<any>();
  const form = Form.useForm()[0];
  useEffect(() => {
    if (endTime && beginTime) {
      form.setFieldsValue({
        searchStartTime: [beginTime, endTime]
      });
    }
  }, [endTime, beginTime, form]);
  const columns = useMemo(() => {
    const arr = [
      {
        title: '选择时间',
        search: (
          <DayRangePicker
            placeholder={['开始时间', '结束时间']}
            valueFormat={'YYYY-MM-DD HH:mm:ss'}
            disabledDate={current => {
              return current && current.valueOf() > Date.now();
            }}
          />
        ),
        searchIndex: ['searchStartTime', 'searchEndTime']
      }
    ];
    return type === 'doctor'
      ? [
          {
            title: '医生姓名',
            dataIndex: 'doctorName'
          },
          {
            title: '医生编号',
            dataIndex: 'doctorId'
          },
          {
            title: '医生职称',
            dataIndex: 'level'
          },
          {
            title: '科室',
            dataIndex: 'deptName',
            width: (
              <SearchSelect
                allowClear={true}
                getList={async key => {
                  const res = await apis.deptSearch.request({
                    searchKey: key,
                    hisId
                  });
                  return res.map(x => {
                    return {
                      label: `${x.name}`,
                      value: x.no
                    };
                  });
                }}
              />
            )
          },
          {
            title: '科室名称',
            searchIndex: 'deptId',
            search: true
          },
          {
            title: '业务平台',

            render: () => (
              <Tag
                style={{
                  width: '90px',
                  height: '16px',
                  fontSize: '12px'
                }}
                ghost
                block
                color={'#108EE9'}>
                互联网医院
              </Tag>
            )
          },
          {
            title: '在线时间',
            dataIndex: 'startTime'
          },
          ...arr
        ]
      : [
          {
            title: '科室名称及编码',
            dataIndex: 'deptName',
            width: 120,
            render: (v, record: any) => {
              return v + (record?.deptId || '');
            }
          },
          {
            title: '所属业务平台',
            width: 120,
            render: () => (
              <Tag
                style={{
                  width: '90px',
                  height: '16px',
                  fontSize: '12px'
                }}
                ghost
                block
                color={'#108EE9'}>
                互联网医院
              </Tag>
            )
          },
          {
            title: '状态',
            dataIndex: 'status',
            width: 120,
            render: v =>
              !v
                ? '-'
                : v === '0'
                ? '已删除'
                : v === '1'
                ? '正常'
                : v === '2'
                ? '已停用'
                : '-'
          },
          {
            title: '科室名称',
            searchIndex: 'deptId',
            search: (
              <SearchSelect
                allowClear={true}
                getList={async key => {
                  const res = await apis.deptSearch.request({
                    searchKey: key,
                    hisId
                  });
                  return res.map(x => {
                    return {
                      label: `${x.name}`,
                      value: x.no
                    };
                  });
                }}
              />
            )
          },
          {
            title: '在线时间',
            dataIndex: 'startTime',
            width: 120
          },
          ...arr
        ];
  }, [hisId, type]);
  return (
    <MyTableList
      tableTitle={`在线${type === 'doctor' ? '医生' : '科室'}详情`}
      form={form}
      action={
        <>
          <Button
            onClick={() => {
              if (type === 'doctor') {
                useApi.exportDoctor(paramsRef.current);
              } else {
                useApi.exportDept(paramsRef.current);
              }
            }}>
            导出EXCEL
          </Button>
        </>
      }
      getList={({ params }) => {
        let searchParams: any = { ...params };
        delete searchParams.sort;
        const { searchStartTime } = form.getFieldsValue();
        if (Array.isArray(searchStartTime)) {
          searchParams = {
            ...searchParams,
            searchStartTime: searchStartTime[0],
            searchEndTime: searchStartTime[1]
          };
        }
        paramsRef.current = searchParams;
        return type === 'doctor'
          ? useApi.分页查询医生在线记录.request({
              ...searchParams
            })
          : useApi.分页查询科室在线记录.request({
              ...searchParams
            });
      }}
      columns={columns}
    />
  );
};
