import React, { useMemo, useCallback, useState, useEffect } from 'react';
import {
  LinkButton,
  actionConfirm,
  DayRangePicker,
  ArrSelect
} from 'parsec-admin';
import useApi from '../api';
import { Button } from 'antd';
import MyTableList from '@components/myTableList';
import permisstion from '@utils/permisstion';
import { ExportOutlined } from '@ant-design/icons';
import saveAs from 'file-saver';
const inquiryType: any = {
  '1': '图文问诊',
  '2': '电话问诊',
  '3': '视频问诊',
  '11': '健康咨询',
  '21': '随访咨询',
  '7': '新冠咨询',
  '31': '护理咨询'
};
const onDuty: any = {
  '': '全部',
  '0': '停用',
  '1': '启用'
};
export default () => {
  const { request: handleExport, loading: exportLoading } = useApi.funlogOrder({
    needInit: false
  });
  const [queryParams, setQueryParams] = useState({} as any);

  return (
    <MyTableList
      tableTitle={'功能开启日志'}
      getList={({ params }) => {
        setQueryParams({ ...params });
        return useApi.funlog.request(params);
      }}
      action={
        <Button
          type={'default'}
          loading={exportLoading}
          icon={<ExportOutlined />}
          onClick={() =>
            handleExport({ ...queryParams, isExport: 1 }).then(data =>
              saveAs(data, `功能开启日志.xls`)
            )
          }>
          导出
        </Button>
      }
      columns={useMemo(
        () => [
          {
            title: '账号',
            dataIndex: 'account',
            width: 100
          },
          {
            title: '医生姓名',
            dataIndex: 'doctorName',
            width: 100,
            search: true
          },
          {
            title: '所属院区',
            dataIndex: 'districtName',
            width: 100
          },
          {
            title: '科室',
            dataIndex: 'deptName',
            width: 150,
            search: true
          },
          {
            title: '医生职称',
            dataIndex: 'level',
            width: 100,
            search: true
          },
          {
            title: '功能名称',
            dataIndex: 'inquiryType',
            render: (text: string) => inquiryType[text],
            width: 100,
            search: <ArrSelect options={inquiryType} />
          },
          {
            title: '操作状态',
            dataIndex: 'onDuty',
            render: (text: string) => onDuty[text],
            width: 100,
            search: <ArrSelect options={onDuty} />
          },
          {
            title: '操作时间',
            dataIndex: 'createTime',
            width: 200,
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                valueFormat={'YYYY-MM-DD HH:mm:ss'}
              />
            ),
            searchIndex: ['startTime', 'endTime']
          }
        ],
        []
      )}
    />
  );
};
