import React, { useMemo, useRef } from 'react';
import useApi from './api';
import OldTableList from '@components/oldTableList';
import formatTime from '@src/utils/formatTime';
import SearchSelect from '@components/SearchSelect';
import { ArrSelect, DayRangePicker } from 'parsec-admin';
import { Button, Modal } from 'antd';
import apis from '@configs/apis';
import moment from 'moment';
import env from '@src/configs/env';

import WaterMarkWrap from '@src/components/waterMarkWrap';

export default () => {
  const hisId = env.hisId;
  const paramsRef = useRef<any>();

  return (
    <WaterMarkWrap>
      <OldTableList
        tableTitle='科室分析'
        action={
          <>
            <Button
              onClick={() => {
                Modal.confirm({
                  title: '确认导出?',
                  content: '当前导出信息，请勿非法传阅',
                  onOk: () => {
                    useApi.export(paramsRef.current);
                  }
                });
              }}>
              导出
            </Button>
          </>
        }
        getList={async ({
          pagination: { current, pageSize = 10 },
          params,
          sorter
        }) => {
          const {
            order,
            column: { dataIndex } = { dataIndex: undefined }
          } = sorter as any;
          const p = {
            hisId,
            ...params,
            pageNum: current,
            numPerPage: pageSize,
            orderBy: dataIndex,
            orderSeq: dataIndex
              ? order === 'ascend'
                ? 'asc'
                : 'desc'
              : undefined
          };
          paramsRef.current = p;
          const res = await useApi.list.request(p);
          console.log('sorter', sorter);
          return {
            list: res?.data?.recordList ?? [],
            total: res?.data?.totalCount
          };
        }}
        // tableTitle={false}
        columns={useMemo(
          () => [
            // {
            //   title: '科室编号',
            //   dataIndex: 'deptId',
            //   fixed: 'left',
            //   width: 140,
            //   excelRender: t => t + ''
            // },
            {
              title: '起止日期',
              dataIndex: 'date',
              show: false,
              render: false,
              excelRender: false,
              search: (
                <DayRangePicker
                  valueFormat={'YYYY-MM-DD HH:mm:ss'}
                  placeholder={['开始时间', '结束时间']}
                  disabledDate={d => {
                    return d.isAfter(moment());
                  }}
                />
              ),
              searchIndex: ['startDate', 'endDate']
            },
            {
              title: '科室名称',
              dataIndex: 'deptName',
              width: 100,
              searchIndex: 'deptId',
              search: (
                <SearchSelect
                  allowClear={true}
                  getList={async key => {
                    const res = await apis.deptSearch.request({
                      searchKey: key,
                      hisId
                    });
                    return res.map(x => {
                      return {
                        label: `${x.name}`,
                        value: x.no
                      };
                    });
                  }}
                />
              )
            },
            {
              title: '问诊类型',
              dataIndex: 'inquiryType',
              show: false,
              render: false,
              excelRender: false,
              search: (
                <ArrSelect
                  allowClear={true}
                  options={[
                    {
                      value: '1',
                      children: '图文问诊'
                    },
                    {
                      value: '2',
                      children: '电话问诊'
                    },
                    {
                      value: '3',
                      children: '视频问诊'
                    }
                  ]}
                />
              )
            },
            {
              title: '上线医生数',
              dataIndex: 'onlineAmount',
              sorter: true,
              width: 140,
              render: x => x || '0'
            },
            {
              title: '医生上线率',
              dataIndex: 'onlineRate',
              sorter: true,
              width: 140,
              render: x => (x * 100).toFixed(2) + '%'
            },
            {
              title: '问诊量',
              dataIndex: 'inquiryAmount',
              sorter: true,
              width: 100,
              render: x => x || '0'
            },
            {
              title: '接诊量',
              dataIndex: 'receptionAmount',
              sorter: true,
              width: 100,
              render: x => x || '0'
            },
            {
              title: '回复率',
              dataIndex: 'replyRate',
              sorter: true,
              width: 100,
              render: x => (x * 100).toFixed(2) + '%'
            },
            {
              title: '响应时长',
              dataIndex: 'responseAvgTime',
              sorter: true,
              width: 150,
              render: x => formatTime(x * 1000)
            },
            {
              title: '好评率',
              dataIndex: 'praiseRate',
              sorter: true,
              width: 140,
              render: x => (x * 100).toFixed(2) + '%'
            },
            {
              title: '处方开单量',
              dataIndex: 'prescriptionAmount',
              sorter: true,
              width: 140,
              render: x => x || '0'
            },
            {
              title: '交易金额（元）',
              dataIndex: 'transactionMoney',
              sorter: true,
              width: 160,
              render: x => (x / 100).toFixed(2)
            },
            {
              title: '医生退款率',
              dataIndex: 'doctorRefundRate',
              sorter: true,
              width: 140,
              render: x => (x * 100).toFixed(2) + '%'
            },
            {
              title: '写病例数',
              dataIndex: 'medicalRecordAmount',
              sorter: true,
              width: 120,
              render: x => x || '0'
            }
          ],
          [hisId]
        )}
      />
    </WaterMarkWrap>
  );
};
