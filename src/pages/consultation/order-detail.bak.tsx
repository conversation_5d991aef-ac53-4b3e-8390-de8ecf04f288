import React from 'react';
import { CardLayout, FormDescriptions, PreviewImg } from 'parsec-admin';
import styled from 'styled-components';
import useApi, { ConsultationState, ReportOptions } from './api';
import { Table } from 'antd';
import { useParams } from 'react-router-dom';

const editSate = false;

export default () => {
  const { id: orderId } = useParams<any>();
  const columns = [
    {
      title: '时间',
      dataIndex: 'updateTime'
    },
    {
      title: '确认人',
      dataIndex: 'operaterName'
    },
    {
      title: '科室',
      dataIndex: 'deptName'
    },
    {
      title: '操作',
      dataIndex: 'operate'
    },
    {
      title: '确认意见',
      dataIndex: 'summary'
    }
  ];
  const {
    data: { data },
    loading: detailLoading
  } = useApi.会诊记录详情({
    params: { id: orderId || '' }
  });

  return (
    <Wrapper edit={false}>
      <CardLayout title={'患者信息'} loading={detailLoading}>
        <FormDescriptions
          data={data}
          edit={editSate}
          column={4}
          loading={detailLoading}
          items={[
            {
              label: '姓名',
              name: 'patientName'
            },
            {
              label: '性别',
              name: 'patientSex'
            },
            {
              label: '年龄',
              name: 'patientAge'
            },
            {
              label: '就诊卡号',
              name: 'patientCardNo'
            },
            {
              label: '患者病史资料',
              name: 'medicalHistory',
              span: 4
            },
            {
              label: '其它相关资料',
              name: 'images',
              span: 4,
              render: (v: any, record: any) => {
                if (!v) {
                  return undefined;
                }
                return v.map((item, index) => {
                  if (!item.imageUrls?.length) {
                    return undefined;
                  }
                  return (
                    <PreviewImg
                      key={index}
                      width={'200px'}
                      src={item.imageUrls[0]}
                      className='imgPreview'
                    />
                  );
                });
              }
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'会诊信息'} loading={detailLoading}>
        <FormDescriptions
          data={data}
          edit={editSate}
          column={4}
          loading={detailLoading}
          items={[
            {
              label: '经治医生',
              name: 'patientDoctorName'
            },
            {
              label: '申请时间',
              name: 'applyTime'
            },
            {
              label: '会诊状态',
              name: 'status',
              render: v => {
                return ConsultationState[v as string] || '-';
              }
            },
            {
              label: '订单状态',
              name: 'orderStatusName'
            },
            {
              label: '会诊成员',
              name: 'doctors',
              span: 4,
              render: v => {
                if (v instanceof Array) {
                  return v.map((item, index) => {
                    return (
                      <span style={{ color: '#999999' }} key={item.id}>
                        {item.deptName}[{item.name}({item.title})]
                        {v.length - 1 === index ? '' : '、'}
                      </span>
                    );
                  });
                }
              }
            },
            {
              label: '会诊时间',
              name: 'consultationTime'
            },
            {
              label: '会诊类型',
              name: 'vField',
              render: v => '远程会诊'
            },
            {
              label: '金额（元）',
              name: 'amount'
            },
            {
              label: '会诊报告状态',
              name: 'reportStatus',
              render: (v, record: any) => {
                return ReportOptions[v as string] || '-';
              }
            },
            {
              label: '会诊意见',
              name: 'tempField',
              span: 4,
              render: (v, record) => {
                return record.doctors?.map(item => {
                  return (
                    <div style={{ marginBottom: 14 }} key={item.id}>
                      {item.name}({item.deptName})：{item.summary}
                    </div>
                  );
                });
              }
            }
          ]}
        />
      </CardLayout>
      <CardLayout title={'确认记录'} loading={detailLoading}>
        <Table
          style={{ marginTop: 30 }}
          pagination={false}
          bordered={true}
          columns={columns}
          rowKey='id'
          dataSource={data?.confirmRecords || []}
        />
      </CardLayout>
    </Wrapper>
  );
};

const Wrapper = styled.div<{ edit: boolean }>`
  .ant-descriptions-item {
    padding-bottom: ${({ edit }) => edit && 0};
  }
  .imgPreview {
    margin-right: 20px;
    margin-bottom: 20px;
  }
`;
