import { Button, InputNumber, Modal, Space, Form, message } from 'antd';
import createApiHooks from 'create-api-hooks';
import { request, useReloadTableList } from 'parsec-admin';
import styled from 'styled-components';

export interface ConfirmPriceModalProps {
  open: boolean;
  raw: any;
  onFinished: () => void;
}

const api = (params: {
  id: number;
  data: { amount: number; shouldAmount: number; amountStatus: number };
}) => {
  return request.post(
    `/mch/cooperate/api/kq/mdt/room/modifyAmount/${params.id}`,
    params.data
  );
};

export const ConfirmPriceModal = ({
  open,
  raw,
  onFinished
}: ConfirmPriceModalProps) => {
  const [form] = Form.useForm();

  const reload = useReloadTableList();

  const setAPI = createApiHooks(api)({ needInit: false });

  const handleSet = async (cache = false) => {
    try {
      // 验证表单
      const values = await form.validateFields();

      await setAPI.request({
        id: raw.id,
        data: {
          amount: values.amount,
          shouldAmount: values.realAmount,
          amountStatus: cache ? -1 : 1
        }
      });

      message.success(cache ? '暂存成功' : '确认成功');

      reload();
      onFinished?.();
    } catch (error) {
      // 验证失败会在这里捕获
      console.error('Validate Failed:', error);
    }
  };
  return (
    <Modal
      visible={open}
      title='确认价格'
      onCancel={onFinished}
      footer={
        <Space>
          <Button onClick={() => handleSet(true)}>暂存</Button>
          <Button type='primary' onClick={() => handleSet()}>
            确认
          </Button>
        </Space>
      }>
      <Form form={form} initialValues={{ amount: 594, realAmount: 594 }}>
        <ContentWrapper>
          <div className='info-item'>
            <label>患者信息：</label>
            <span>{`${raw?.patientName || ''} ${raw?.patientSex ||
              ''} ${raw?.patientAge || ''}`}</span>
          </div>

          <div className='info-item'>
            <label>申请医生：</label>
            <span>{`${raw?.patientDoctorName ||
              ''} | ${raw?.patientDoctorTitle || ''} | ${raw?.deptName ||
              ''}`}</span>
          </div>

          <div className='info-item'>
            <label>会诊医生：</label>
            <div className='doctor-list'>
              {raw?.doctors?.map((doctor: any, index: number) => (
                <div key={index}>
                  {`${doctor.name} | ${doctor.title} | ${doctor.deptName}`}
                </div>
              )) || '暂无'}
            </div>
          </div>

          <Form.Item
            label='订单金额'
            name='amount'
            rules={[
              { required: true, message: '请输入订单金额' },
              { type: 'number', min: 0, message: '金额必须大于0' }
            ]}>
            <InputNumber
              prefix='¥'
              precision={2}
              min={0}
              style={{ width: '200px' }}
            />
          </Form.Item>
          <Form.Item
            label='应收金额'
            name='realAmount'
            rules={[
              { required: true, message: '请输入订单金额' },
              { type: 'number', min: 0, message: '金额必须大于0' }
            ]}>
            <InputNumber
              prefix='¥'
              precision={2}
              min={0}
              style={{ width: '200px' }}
            />
          </Form.Item>
        </ContentWrapper>
      </Form>
    </Modal>
  );
};

const ContentWrapper = styled.div`
  padding: 20px 0;

  .info-item {
    margin-bottom: 16px;
    display: flex;
    align-items: flex-start;

    label {
      width: 84px;
      color: #666;
      flex-shrink: 0;
    }

    .doctor-list {
      flex: 1;

      > div {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    &.price {
      margin-top: 24px;

      .price-input {
        color: #f5222d;

        input {
          width: 120px;
          margin-left: 4px;
          padding: 4px 8px;
          border: 1px solid #d9d9d9;
          border-radius: 2px;

          &:focus {
            border-color: #40a9ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
      }
    }
  }
`;
