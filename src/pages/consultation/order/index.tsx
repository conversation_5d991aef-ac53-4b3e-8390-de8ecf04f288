import React, { useMemo, useRef, Fragment, useState } from 'react';
import {
  LinkButton,
  DayRangePicker,
  ArrSelect,
  RouteComponentProps,
  handleSubmit,
  ActionsWrap,
  actionConfirm,
  useModal,
  getPrice,
  useReloadTableList
} from 'parsec-admin';
import useApi, {
  ConsultationState,
  ReportOptions,
  OrderStatus,
  ReportOptionsSearch,
  ConsultationStateSearch
} from '../api';
import MyTableList from '@components/myTableList';
import permisstion from '@utils/permisstion';
import { useMutative } from 'use-mutative';

import styled from 'styled-components';
import { ConfirmPriceModal } from '../components/confirm-price-modal';
import { Button, Input, Modal, Space } from 'antd';
import { useConfig } from '@src/store/hisConfig';

export default ({ history }: RouteComponentProps) => {
  const paramsRef = useRef<any>();
  const { TextArea } = Input;
  const reload = useReloadTableList();

  const rejectAPI = useApi.useReject({ needInit: false });
  const cancelAPI = useApi.useCancel({ needInit: false });
  const { isICU } = useConfig();
  const { data: statistics, request: fetchStatistics } = useApi.useStatistics({
    initValue: {},
    needInit: false
  });

  // 退款modal
  const showModal = useModal({
    onSubmit: values => {
      return handleSubmit(() =>
        useApi.会诊退款接口.request({
          ...values
        })
      );
    },
    title: '退款原因',
    items: [
      {
        name: 'id',
        render: false
      },
      {
        label: '原因',
        name: 'reason',
        required: true
      }
    ]
  });

  const [open, setOpen] = useMutative({
    confirmPrice: false
  });

  const startAPI = useApi.useStart({ needInit: false });

  const [selectedRecord, setSelectedRecord] = useState(null);
  const switchModalVisible = useModal(({ id }) => {
    return {
      title: '拒绝审批',
      onSubmit: values =>
        handleSubmit(() => {
          return rejectAPI.request({
            id,
            reason: values.reason
          });
        }),
      items: [
        {
          name: 'id',
          render: false
        },
        {
          name: 'reason',
          formItemProps: {
            hasFeedback: true,
            rules: [
              {
                required: true,
                message: '请输入驳回理由'
              }
            ]
          },
          render: <TextArea placeholder='请输入驳回理由' />
        }
      ]
    };
  });
  return (
    <Fragment>
      <MyTableList
        tableTitle='订单列表'
        pageHeaderProps={false}
        rowKey='id'
        getList={({ params }) => {
          const p = { ...params, businessType: '1' } as any;

          if (params.orderStatus === 4) {
            p.status = '2';
            p.amountStatus = '0';
            p.orderStatus = void 0;
          }

          paramsRef.current = p;
          isICU && fetchStatistics(p);
          return useApi.会诊记录分页列表.request(p);
        }}
        columns={useMemo(
          () => [
            {
              title: '单据号',
              dataIndex: 'id',
              width: 120
            },
            {
              title: '申请时间',
              dataIndex: 'applyTime',
              width: 220,
              searchIndex: ['applyStartDate', 'applyEndDate'],
              search: (
                <DayRangePicker
                  placeholder={['开始时间', '结束时间']}
                  valueFormat={'YYYY-MM-DD'}
                  disabledDate={current => {
                    return current && current.valueOf() > Date.now();
                  }}
                />
              )
            },
            {
              title: '会诊类型',
              dataIndex: 'vField',
              width: 120,
              render: v => '远程会诊'
            },
            // {
            //   title: '发起人',
            //   dataIndex: 'patientDoctorName'
            // },
            {
              title: '会诊成员',
              width: 180,
              dataIndex: 'doctors',
              render: (v: any[]) => {
                return v?.map(dr => `${dr.name} | ${dr.title}`).join(',');
              }
            },
            {
              title: '患者信息',
              dataIndex: 'patientName',
              width: 180,
              search: true,
              render: (v, record: any) => {
                return `${record.patientName} ${record.patientSex} ${record.patientAge}`;
              }
            },
            {
              title: '会诊状态',
              dataIndex: 'status',
              width: 120,
              render: (v, record: any) => {
                return ConsultationState[record.status];
              }
            },
            {
              title: '会诊状态',
              dataIndex: 'status',
              search: <ArrSelect options={ConsultationStateSearch} />,
              render: false
            },
            {
              title: '会诊时间',
              searchIndex: ['startDateTime', 'endDateTime'],
              search: (
                <DayRangePicker
                  placeholder={['开始时间', '结束时间']}
                  disabledDate={current => {
                    return current && current.valueOf() > Date.now();
                  }}
                />
              ),
              width: 220,
              dataIndex: 'consultationTime'
            },
            // {
            //   title: '金额(元)',
            //   dataIndex: 'amount',
            //   // render: v => `${getPrice(v, 2, true)}`
            //   render: (v, record: any) => {
            //     if (record.status === '2' && !record.amountStatus) {
            //       return '待确认';
            //     }
            //     // return `${getPrice(v, 2, true)}`;
            //     return v;
            //   }
            // },
            {
              title: '订单金额(元)',
              width: 180,
              dataIndex: 'amount',
              render: v => (v ? `¥${getPrice(v * 100, 2, true)}` : '-')
            },
            {
              title: '应收金额(元)',
              width: 180,
              dataIndex: 'shouldAmount',
              render: v => (v ? `¥${getPrice(v * 100, 2, true)}` : '-')
            },
            {
              title: '会诊报告状态',
              dataIndex: 'reportStatusRender',
              width: 180,
              render: (v, record: any) => {
                return ReportOptions[record.reportStatus];
              }
            },
            {
              title: '会诊报告状态',
              dataIndex: 'reportStatus',
              search: <ArrSelect options={ReportOptionsSearch} />,
              render: false
            },
            {
              title: '会诊医生',
              dataIndex: 'cooperateDoctor',
              width: 180,
              search: true,
              render: isICU ? v => v || '-' : false
            },
            ...(isICU
              ? [
                  {
                    title: '上级医院',
                    dataIndex: 'cooperateMember',
                    width: 180,
                    search: (
                      <ArrSelect
                        options={{
                          重医儿童医院: '重医儿童医院',
                          成都妇女儿童中心医院: '成都妇女儿童中心医院'
                        }}
                      />
                    )
                  }
                ]
              : []),
            {
              title: '操作',
              fixed: 'right',
              width: 300,
              render: record => (
                <Space>
                  <LinkButton
                    onClick={() => {
                      record.id &&
                        history.push('/consultation/order/' + record.id);
                    }}>
                    详情
                  </LinkButton>
                  {['1'].includes(record.status) && (
                    <>
                      <LinkButton
                        onClick={() => {
                          setSelectedRecord(record);
                          setOpen(draft => {
                            draft.confirmPrice = true;
                          });
                        }}>
                        同意
                      </LinkButton>
                      <LinkButton
                        onClick={() => {
                          switchModalVisible({
                            id: record.id
                          });
                        }}>
                        拒绝
                      </LinkButton>
                    </>
                  )}
                  {['3'].includes(record.status) && (
                    <LinkButton
                      onClick={() => {
                        actionConfirm(
                          () =>
                            startAPI.request({
                              id: record.id
                            }),
                          '开始会诊'
                        );
                      }}>
                      开始会诊
                    </LinkButton>
                  )}
                  {['1', '3', '4'].includes(record.status) && (
                    <Button
                      type='link'
                      danger
                      onClick={() => {
                        actionConfirm(
                          () =>
                            cancelAPI.request({
                              id: record.id
                            }),
                          '取消会诊'
                        );
                      }}>
                      取消会诊
                    </Button>
                  )}
                  {/* {record.orderStatus === 1 && permisstion.canOrderRefund && (
                    <LinkButton
                      onClick={() => {
                        showModal(record);
                      }}>
                      退款
                    </LinkButton>
                  )} */}
                </Space>
              )
            }
          ],
          [history, showModal]
        )}
      />
      <ConfirmPriceModal
        open={open.confirmPrice}
        onFinished={() => {
          setOpen(draft => {
            draft.confirmPrice = false;
          });
          setSelectedRecord(null);
        }}
        raw={selectedRecord}
      />
      {isICU && (
        <div
          style={{
            position: 'relative',
            marginTop: '-80px',
            marginLeft: '50px',
            color: 'red',
            fontWeight: 'bold',
            pointerEvents: 'none'
          }}>
          <span style={{ marginRight: '6px' }}>
            会诊单总计数量：{statistics?.data?.total || 0};
          </span>
          <span style={{ marginRight: '6px' }}>
            重医儿童医院参与数量：
            {statistics?.data?.cqChildrenNum || 0};
          </span>
          <span>
            成都妇女儿童中心医院参与数量：{statistics?.data?.cdChildrenNum || 0}
          </span>
        </div>
      )}
    </Fragment>
  );
};

export const Form = styled.div`
  > .formItem {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 10px 0;
    > .label {
      padding-right: 10px;
    }
    > .value {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }
`;
