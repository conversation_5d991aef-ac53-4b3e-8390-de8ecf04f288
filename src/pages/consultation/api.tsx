import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import {
  ListApiRequestParams,
  ListApiResponseData,
  ApiResponse
} from '@src/configs/apis';

// 会诊状态-查询
export const ConsultationStateSearch: {
  [key: string]: string;
} = {
  '0': '会诊结束',
  '1': '待审核',
  '3': '待会诊',
  '4': '会诊中',
  '8': '会诊取消'
};
// 会诊状态
export const ConsultationState: {
  [key: string]: string;
} = {
  '0': '会诊结束',
  '1': '待审核',
  '3': '待会诊',
  '4': '会诊中',
  '8': '会诊取消'
};
//报告状态
export const ReportOptions: {
  [key: string]: string;
} = {
  0: '待出报告',
  1: '待发布',
  2: '已出报告'
};
//报告状态-查询
export const ReportOptionsSearch: {
  [key: string]: string;
} = {
  0: '待出报告',
  1: '待发布',
  2: '已出报告'
};
//订单状态
export const OrderStatus: {
  [key: string]: string;
} = {
  0: '未支付',
  1: '已支付',
  2: '已退款',
  3: '退款失败',
  4: '待确认'
};

export interface OrderInfo {
  id: string; // 主键ID
  userId: string; // 用户ID
  patientId: string; // 患者ID
  patientName: string; // 患者名称
  patientAge: string; // 患者年龄
  patientSex: string; // 患者性别
  patientCardNo: string; // 患者就诊卡号
  patientIdNo: string; // 身份证
  patientBirthDay: string; // 生日
  patientPhone: string; // 电话
  guardianRelation: string; // 监护人关系
  guardianName: string; // 监护人姓名
  guardianIdNo: string; // 监护人身份证
  patientDoctorId: string; // 患者主治医生ID
  patientDoctorName: string; // 患者主治医生名称
  patientDoctorTitle: string; // 患者主治医生职称
  diseaseDesc: string; // 病情介绍
  medicalHistory: string; // 病史
  mainDiagnosis: string; // 主要诊断
  purpose: string; // 会诊目的
  consultationTime: string; // 患者会诊时间
  supplement: string; // 病情补充描述
  applyTime: string; // 会诊申请时间
  amount: string; // 金额
  orderId: string; // 订单ID
  hisId: string; // 医院ID
  inquiryId: string; // 问诊ID
  status: string; // 会诊状态 (0会诊完,已出报告 1申请待审核 2申请已审核,待支付 3已支付,待会诊 4会诊中,未出报告 5会诊完,待出报告  6申请审核未通过 7报告审核未通过 8会诊取消 9缴费失败 10缴费异常 11已退款 12退款失败)
  updateTime: string; // 更新时间
  createTime: string; // 创建时间
  images: string; // 会诊图片json字符串
  report: string; // 报告json
  reportStatus: string; // 会诊报告状态 (0未出报告 1已出报告未确定 2已确定未审核 3报告已审核)
  reportSummary: string; // 会诊报告总结
  reason: string; // 取消订单原因
  doctors?: [
    // 会诊医生
    {
      id: string; // ID
      hisId: string; // 医院ID
      hisName: string; // 医院名称
      deptId: string; // 会诊科室ID
      deptName: string; // 会诊科室名称
      doctorId: string; // 医生ID
      name: string; // 会诊人员名称
      title: string; // 会诊人员职称
      image: string; // 会诊人员头像
      type: string; // 1普通会诊医生 2团队领队
      status: string; // 状态 2待确认 3确认参加 4拒绝参加
      summary: string; // 医生会诊意见
    }
  ];
  confirmRecords?: [
    // 确认记录
    {
      id: string; // 医生确认记录主键ID
      roomId: string; // mdt会诊室ID
      hisId: string; // 医院ID
      operaterId: string; // 操作人id
      operaterName: string; // 操作人姓名
      operate: string; // 操作内容（如：变更科室，变更医生，确认会诊，拒绝会诊
      deptId: string; // 科室id
      deptName: string; // 科室名称
      result: string; // 结果（1-同意；2-拒绝）
      status: string; // 状态-标准版用（0-默认值；1-待审核；2-已审核）
      summary: string; // 操作意见
    }
  ];
  audits?: [
    {
      auditPersonAccount: string;
      auditPersonId: string;
      auditPersonName: string;
      auditStatus: number;
      auditTime: string;
      createTime: string;
      id: number;
      remark: string;
      roomId: number;
      updateTime: string;
    }
  ];
}

export default {
  会诊记录分页列表: createApiHooks(
    (
      params: ListApiRequestParams & {
        applyStartDate?: string;
        applyEndDate?: string;
        status?: string;
        reportStatus?: string;
      }
    ) => {
      return request.get<ListApiResponseData<OrderInfo>>(
        `/mch/cooperate/api/kq/mdt/room/page`,
        { params }
      );
    }
  ),
  会诊记录详情: createApiHooks((params: { id: string }) => {
    return request.get<ApiResponse<OrderInfo>>(
      `/mch/cooperate/api/kq/mdt/room/${params.id}`
    );
  }),
  会诊退款接口: createApiHooks((params: { id: string; reason: string }) => {
    return request.post<ApiResponse<OrderInfo>>(
      `/mch/cooperate/api/kq/mdt/room/refund/${params.id}`,
      { reason: params.reason }
    );
  }),
  useReject: createApiHooks((params: { id: string; reason: string }) => {
    return request.post<ApiResponse<OrderInfo>>(
      `/mch/cooperate/api/kq/mdt/room/refuse/${params.id}`,
      { reason: params.reason }
    );
  }),
  useStart: createApiHooks((params: { id: string }) => {
    return request.post<ApiResponse<OrderInfo>>(
      `/mch/cooperate/api/kq/mdt/room/startRoom/${params.id}`
    );
  }),
  useCancel: createApiHooks((params: { id: string }) => {
    return request.post<ApiResponse<OrderInfo>>(
      `/mch/cooperate/api/kq/mdt/room/manageCancel/${params.id}`
    );
  }),
  useDetail: createApiHooks((data: { id: string }) =>
    request.get<ApiResponse<any>>(`/mch/cooperate/api/kq/mdt/room/${data.id}`)
  ),
  useReportPDF: createApiHooks((data: { id: string }) =>
    request.get<Blob>(
      `/mch/cooperate/api/kq/mdt/pdf/generate?roomId=${data.id}`,
      {
        responseType: 'blob'
      }
    )
  ),
  // 会诊订单统计
  useStatistics: createApiHooks(params =>
    request.get<any>(`/mch/cooperate/api/kq/mdt/room/statistics`, { params })
  )
};
