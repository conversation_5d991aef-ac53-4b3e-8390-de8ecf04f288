.detail-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 24px;
}

.description-supplement {
  margin-top: 16px;

  .description-text {
    margin-bottom: 16px;
    white-space: pre-wrap;
  }

  .visit-record {
    margin-bottom: 16px;

    .visit-date {
      margin-bottom: 8px;
      color: #1890ff;
    }

    .image-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;

      .image-item {
        border: 1px solid #d9d9d9;
        padding: 4px;
        border-radius: 2px;

        img {
          width: 100%;
          height: auto;
          display: block;
        }
      }
    }
  }

  .documents {
    margin-top: 16px;

    .documents-title {
      font-weight: 500;
      margin-bottom: 8px;
    }

    .document-link {
      color: #1890ff;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.audit-records {
  margin-top: 16px;

  table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
      padding: 8px 16px;
      text-align: left;
    }

    th {
      background-color: #fafafa;
      font-weight: 500;
    }

    tr {
      border-bottom: 1px solid #f0f0f0;
    }

    tbody tr:hover {
      background-color: #fafafa;
    }
  }
}

.iconamoon--eye-off-fill {
  display: inline-block;
  width: 24px;
  height: 24px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M4.707 3.293a1 1 0 0 0-1.414 1.414l1.67 1.671C3.23 7.716 1.889 9.538 1.07 11.636a1 1 0 0 0 0 .728C2.803 16.806 6.884 20 12 20c1.935 0 3.73-.459 5.31-1.276l1.983 1.983a1 1 0 0 0 1.414-1.414l-2.501-2.501l-.038-.038l-3.328-3.328l-.011-.012l-.012-.011l-4.22-4.22l-.023-.023l-3.329-3.33l-.038-.037zm4.585 7.414a3 3 0 0 0 4.001 4.001zm1.554-4.64Q11.409 6 12 6c4.074 0 7.38 2.443 8.919 6c-.34.787-.768 1.52-1.271 2.184a1 1 0 1 0 1.594 1.208a12.6 12.6 0 0 0 1.69-3.028a1 1 0 0 0 0-.728C21.197 7.194 17.116 4 12 4q-.705 0-1.386.08a1 1 0 0 0 .232 1.986' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.mdi--eye {
  display: inline-block;
  width: 24px;
  height: 24px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M12 9a3 3 0 0 0-3 3a3 3 0 0 0 3 3a3 3 0 0 0 3-3a3 3 0 0 0-3-3m0 8a5 5 0 0 1-5-5a5 5 0 0 1 5-5a5 5 0 0 1 5 5a5 5 0 0 1-5 5m0-12.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}
