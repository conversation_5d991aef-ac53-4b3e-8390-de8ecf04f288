import React, { useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Button, Card, Descriptions, Image } from 'antd';
import api from '../api';
import './index.less';
import dayjs from 'dayjs';
import { useHistory } from 'react-router';
import { CONS_REPORT_STATUS } from '../constant';
import { useConfig } from '@src/store/hisConfig';
// 状态映射
const statusMap = {
  '0': '待审核',
  '1': '审核通过',
  '2': '审核拒绝',
  '3': '进行中'
};
const CONS_STATUS = {
  '0': '会诊结束',
  '1': '待审核',
  // '2': '待支付',
  '3': '待会诊',
  '4': '会诊中',
  '5': '报告待出',
  '6': '审核被拒绝',
  '7': '报告已出',
  '8': '会诊取消'
  // '0': '会诊已结束',
  // '1': '申请待审核',
  // '2': '申请已审核,待支付',
  // '3': '已支付,待会诊',
  // '4': '会诊中,未出报告',
  // '5': '会诊完,待出报告',
  // '6': '申请审核未通过',
  // '7': '报告已发布,待结束',
  // '8': '会诊取消',
  // '9': '缴费失败',
  // '10': '缴费异常',
  // '11': '已退款',
  // '12': '退款失败'
} as const;

// 监护人关系映射
const guardianRelationMap = {
  MOTHER: '母亲',
  FATHER: '父亲',
  OTHER: '其他'
};

export default function Detail() {
  const { id } = useParams<{ id: string }>();
  const {
    data: { data: detail }
  } = api.useDetail({ params: { id } });
  const pdfAPI = api.useReportPDF({ params: { id }, needInit: false });

  const history = useHistory();
  const depts = useMemo(() => {
    return Array.from(
      new Set(detail?.doctors?.map((doctor: any) => doctor.deptName))
    ).join(', ');
  }, [detail?.doctors]);

  const drs = useMemo(() => {
    return detail?.doctors
      ?.map(dr => `${dr.name} (${dr.deptName} | ${dr.title})`)
      .join('、');
  }, [detail?.doctors]);

  const hosps = useMemo(() => {
    const top = detail?.doctors?.filter(dr => dr.type === '1');
    const bottom = detail?.doctors?.filter(dr => dr.type === '2');

    return {
      top: top?.map(dr => dr.name).join('、'),
      bottom: bottom?.map(dr => dr.name).join('、')
    };
  }, [detail?.doctors]);

  const audits = useMemo(() => detail?.audits || [], [detail?.audits]);

  const handleReportPDF = async () => {
    const res = await pdfAPI.request();

    const url = window.URL.createObjectURL(res);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'report.pdf';
    window.open(a.href);
  };
  const { isICU } = useConfig();

  const renderDocuments = () => {
    if (isICU) {
      if (typeof detail?.documents !== 'string') return '暂无数据';
      try {
        const res = JSON.parse(detail?.documents);
        return (
          <a href={res.url} target='_blank' rel='noreferrer'>
            {res.name}
          </a>
        );
      } catch (error) {
        return '暂无数据';
      }
    } else {
      return detail?.documents ? (
        <a href={detail?.documents?.url} target='_blank' rel='noreferrer'>
          {detail?.documents?.name}
        </a>
      ) : (
        '暂无数据'
      );
    }
  };

  return (
    <div className='detail-wrapper'>
      {/* 会诊信息卡片 */}
      <Card
        title='会诊信息'
        extra={
          <div
            style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
            <Button
              type='primary'
              className='mr-2'
              disabled={
                CONS_REPORT_STATUS[detail?.reportStatus] !==
                CONS_REPORT_STATUS[2]
              }
              onClick={handleReportPDF}>
              查看报告
            </Button>
            {/* <Button
              disabled={
                CONS_REPORT_STATUS[detail?.reportStatus] !==
                CONS_REPORT_STATUS[2]
              }>
              下载报告
            </Button> */}
          </div>
        }>
        <Descriptions column={3}>
          <Descriptions.Item label='会诊状态'>
            <Field value={CONS_STATUS[detail?.status]} />
          </Descriptions.Item>
          <Descriptions.Item label='报告状态'>
            <Field value={CONS_REPORT_STATUS[detail?.reportStatus]} />
          </Descriptions.Item>
          <Descriptions.Item label='年龄'>
            <Field value={detail?.patientAge} />
          </Descriptions.Item>

          <Descriptions.Item label='姓名'>
            <Field value={detail?.patientName} />
          </Descriptions.Item>
          <Descriptions.Item label='性别'>
            <Field value={detail?.patientSex} />
          </Descriptions.Item>
          <Descriptions.Item label='联系电话'>
            <MaskWrapper
              origin={detail?.patientPhone}
              value={
                detail?.patientPhone?.substring(0, 3) +
                '****' +
                detail?.patientPhone?.substring(7)
              }
            />
          </Descriptions.Item>

          <Descriptions.Item label='证件类型'>
            <Field value={detail?.patientIdType || '身份证'} />
          </Descriptions.Item>
          <Descriptions.Item label='证件号'>
            <MaskWrapper
              origin={detail?.patientIdNo}
              value={
                detail?.patientIdNo?.substring(0, 4) +
                '********' +
                detail?.patientIdNo?.substring(detail?.patientIdNo.length - 4)
              }
            />
          </Descriptions.Item>
          {detail?.guardianRelation && (
            <>
              <Descriptions.Item label='监护人证件类型'>
                <Field value={detail?.guardianIdType || '身份证'} />
              </Descriptions.Item>
              <Descriptions.Item label='监护人关系'>
                <Field value={guardianRelationMap[detail?.guardianRelation]} />
              </Descriptions.Item>
              <Descriptions.Item label='监护人姓名'>
                <Field value={detail?.guardianName} />
              </Descriptions.Item>
              <Descriptions.Item label='监护人证件号'>
                <MaskWrapper
                  origin={detail?.guardianIdNo}
                  value={
                    detail?.guardianIdNo?.substring(0, 4) +
                    '********' +
                    detail?.guardianIdNo?.substring(
                      detail?.guardianIdNo.length - 4
                    )
                  }
                />
              </Descriptions.Item>
            </>
          )}
          <Descriptions.Item label='会诊目的'>
            <Field value={detail?.purpose} />
          </Descriptions.Item>
          <Descriptions.Item label='会诊时间'>
            <Field value={detail?.consultationTime} />
          </Descriptions.Item>
          <Descriptions.Item label='会诊类型'>
            <Field value={detail?.consultationType || '远程会诊'} />
          </Descriptions.Item>
          {/* <Descriptions.Item label='主治医生'>
            <Field value={detail?.patientDoctorName} />
          </Descriptions.Item> */}
          <Descriptions.Item label='会诊医院'>
            <Field value={hosps.top} />
          </Descriptions.Item>
          <Descriptions.Item label='受邀科室'>
            <Field value={depts} />
          </Descriptions.Item>
          {/* <Descriptions.Item label='会诊专业'>
            <Field value={depts} />
          </Descriptions.Item> */}
          <Descriptions.Item label='发起医院'>
            <Field value={hosps.bottom} />
          </Descriptions.Item>
          <Descriptions.Item label='订单金额'>
            <Field value={detail?.amount} type='price' />
          </Descriptions.Item>
          <Descriptions.Item label='应收金额'>
            <Field value={detail?.shouldAmount} type='price' />
          </Descriptions.Item>
          <Descriptions.Item label='会诊团队' span={3}>
            <Field value={drs} />
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 患者病情卡片 */}
      <Card title='患者病情' className='mt-4'>
        <Descriptions column={2}>
          <Descriptions.Item label='主诉'>
            <Field value={detail?.complaints} />
          </Descriptions.Item>
          <Descriptions.Item label='主要诊断'>
            <Field value={detail?.mainDiagnosis} />
          </Descriptions.Item>
          <Descriptions.Item label='现病史'>
            <Field value={detail?.presentIllness} />
          </Descriptions.Item>
          <Descriptions.Item label='体格检查'>
            <Field value={detail?.physique} />
          </Descriptions.Item>
          <Descriptions.Item label='既往史'>
            <Field value={detail?.anamnesis} />
          </Descriptions.Item>
          <Descriptions.Item label='体重（kg）'>
            <Field value={detail?.weight} />
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 病情描述补充卡片 */}
      <Card title='病情描述补充' className='description-supplement'>
        <div className='description-text'>{detail?.medicalHistory}</div>
        <div className='description-text'>{detail?.supplement}</div>

        {/* 就诊图片列表 */}
        {detail?.images?.map((record: any) => (
          <div key={record.date} className='visit-record'>
            <div className='visit-date'>{record.title}</div>
            <div className='image-grid'>
              {record.imageUrls?.map((img: string, index: number) => (
                <div
                  key={index}
                  className='image-item'
                  style={{ display: 'flex', gap: '8px' }}>
                  <Image
                    src={img}
                    alt={`就诊图片${index + 1}`}
                    style={{ width: '100px', height: '100px' }}
                  />
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* 文档资料 */}
        <div className='documents'>
          <div className='documents-title'>文档资料</div>
          {renderDocuments()}
        </div>
      </Card>

      {/* 审核记录卡片 */}
      <Card title='审核记录' className='audit-records'>
        <table>
          <thead>
            <tr>
              <th>审核时间</th>
              <th>审核人</th>
              <th>审核结果</th>
              <th>审核意见</th>
            </tr>
          </thead>
          <tbody>
            {audits?.map((record: any, index: number) => (
              <tr key={index}>
                <td>{dayjs(record.auditTime).format('YYYY-MM-DD HH:mm:ss')}</td>
                <td>{record.auditPersonName}</td>
                <td>{record.auditResult}</td>
                <td>{record.remark}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </Card>

      {detail?.isLeader && (
        <div className=''>
          <Button
            type='primary'
            onClick={() => {
              //
              history.push(`/manage-cons-create?id=${id}`);
              // setStep(3);
            }}>
            重新发起会诊
          </Button>
        </div>
      )}
    </div>
  );
}

function MaskWrapper({ origin, value }) {
  const [show, setShow] = useState(false);

  if (!origin) return <>暂无数据</>;

  return (
    <div style={{ display: 'flex', gap: '8px' }}>
      <div>{show ? origin : value}</div>
      <i
        className={`${show ? 'iconamoon--eye-off-fill' : 'mdi--eye'}`}
        onClick={() => setShow(!show)}
      />
    </div>
  );
}

function Field({ value, defaultValue = '暂无数据', type = '' }) {
  if (!value) return <>{defaultValue}</>;

  if (type === 'price') {
    return <>{`¥${value}`}</>;
  }

  return <>{value}</>;
}
