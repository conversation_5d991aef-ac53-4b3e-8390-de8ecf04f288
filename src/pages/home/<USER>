import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { ApiResponse } from '@apiHooks';

export interface OrderPannelData {
  totalUserCount?: number; //总用户数
  onlinePutRecordCount?: number; // 在线建档数
  onlineVisitCount?: number; // 就诊人数
  onlineBindCardCount?: number; //在线绑卡数
  totalBusinessFee?: number; //交易总金额
  totalBusinessCount?: number; //交易总笔数
  refundBusinessFee?: number; //交易统计退款金额
  netFee?: number; //交易统计净交易金额
  businessType?: string; //业务类型名称
  businessTypeCount?: number; //业务类型数量
  payChannel?: string; //支付类型名称
  payChannelCount?: number; //支付类型数量
  onlineType?: string; //在线问诊名称
  onlineTypeCount?: number; //在线问诊数量
  date?: string; //日期
  cumulativeNumber?: number; //累计用户数量
  newAddNumber?: number; //新增用户数量
  rate?: string; //百分比
  businessFee?: number; //交易统计交易总金额
}

export interface UserStatisticsData {
  totalUserCount?: number;
  onlinePutRecordCount?: number;
  onlineBindCardCount?: number;
  totalBusinessFee?: number;
  totalBusinessCount?: number;
  refundBusinessFee?: number;
  netFee?: number;
  businessType?: string;
  businessTypeCount?: number;
  payChannel?: string;
  payChannelCount?: number;
  onlineType?: string;
  onlineTypeCount?: number;
  date?: string;
  cumulativeNumber?: number;
  newAddNumber?: number;
  rate?: string;
  businessFee?: number;
}

export interface CharApiRatioParams {
  hisId: string;
  startDate: string;
  endDate: string;
}

export interface ChartApiParams extends CharApiRatioParams {
  granularity: string;
}

export type ChartApiResponse = ApiResponse<UserStatisticsData[]>;

export default {
  // 获取首页面板数据，总用户数 在线建档数 在线绑卡数
  getUserPanelData: createApiHooks((params: { hisId: string }) =>
    request.post<ApiResponse<OrderPannelData>>(
      '/mch/user/statistic/getPanelData',
      params
    )
  ),
  // 获取首页面板数据，交易总金额，交易总笔数
  getOrderPanelData: createApiHooks((params: { hisId: string }) =>
    request.post<ApiResponse<OrderPannelData>>(
      '/mch/order/statistic/getPanelData',
      params
    )
  ),
  // 获取首页用户统计数据
  getUserStaticsData: createApiHooks((params: ChartApiParams) =>
    request.post<ChartApiResponse>(
      '/mch/user/statistic/getUserStatisticsData',
      params
    )
  ),
  // 获取交易统计数据
  getOrderStaticsData: createApiHooks((params: ChartApiParams) =>
    request.post<ChartApiResponse>(
      '/mch/order/statistic/getOrderStatisticsData',
      params
    )
  ),
  // 获取业务类型占比数据
  getBusinessTypeRatio: createApiHooks((params: CharApiRatioParams) =>
    request.post<ChartApiResponse>(
      '/mch/order/statistic/getOrderBusinessTypeData',
      params
    )
  ),
  // 获取支付类型占比数据
  getPayTypeRatio: createApiHooks((params: CharApiRatioParams) =>
    request.post<ChartApiResponse>(
      '/mch/order/statistic/getOrderPayChannelData',
      params
    )
  ),
  // 获取在线问诊数据
  getOnlineQuery: createApiHooks((params: CharApiRatioParams) =>
    request.post<ChartApiResponse>(
      '/mch/order/statistic/getOrderOnlineTypeData',
      params
    )
  )
};
