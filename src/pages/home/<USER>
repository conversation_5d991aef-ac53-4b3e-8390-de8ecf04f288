import React, { useCallback } from 'react';
import MidStatics from './common/midStatics';
import useApi from './apis';

interface Iprops {
  className?: string;
}

export default () => {
  const setEchartsOption = useCallback(data => {
    return {
      color: ['#50A3E0'],
      xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: false
        }
      },
      series: [
        {
          data: [120, 200, 150, 80, 70, 110, 130],
          type: 'bar',
          barCategoryGap: '50%'
        }
      ]
    };
  }, []);

  return (
    <MidStatics
      id='prescription-bottom-statics'
      dateTitle='在线处方统计'
      style={{ height: '280px' }}
      setEchartsOption={setEchartsOption}
      getData={useCallback(params => useApi.getOnlineQuery.request(params), [])}
    />
  );
};
