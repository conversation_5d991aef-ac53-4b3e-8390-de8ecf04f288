import React, { useCallback } from 'react';
import MidStatics from './common/midStatics';
import moment from 'moment';
import useApi, { UserStatisticsData } from './apis';

interface Iprops {
  className?: string;
}

export default () => {
  const dataToOptionConfig = (datas: UserStatisticsData[]) => {
    if (!datas) {
      return {};
    }
    const res: {
      yData1?: any[];
      yData2?: any[];
      xData?: any[];
    } = {};
    res.yData1 = datas.map(data => data?.newAddNumber || 0);
    res.yData2 = datas.map(data => data?.cumulativeNumber || 0);
    res.xData = datas.map(data =>
      moment(data?.date, 'YYYY-MM-DD HH:mm:ss').format('M月DD日')
    );
    return res;
  };

  const setEchartsOption = useCallback(data => {
    const { yData1 = [], yData2 = [], xData = [] } = dataToOptionConfig(data);
    return {
      legend: {
        data: [
          { name: '新增人数', icon: 'rect' },
          { name: '累计用户', icon: 'rect' }
        ],
        itemGap: 24,
        bottom: 25,
        left: '6%'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        backgroundColor: '#fff',
        textStyle: {
          color: '#333'
        }
      },
      color: ['#e0f0ff', '#ffe6c3'],
      grid: {
        left: '3%',
        right: '4%',
        bottom: 62,
        top: 15,
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: xData
          // data: [
          //   '7月1日',
          //   '7月2日',
          //   '7月3日',
          //   '7月4日',
          //   '7月5日',
          //   '7月6日',
          //   '7月7日'
          // ]
        }
      ],
      yAxis: [
        {
          type: 'value',
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '新增人数',
          type: 'line',
          // stack: '总量',
          areaStyle: {},
          data: yData1
          // data: [120, 132, 101, 134, 90, 230, 210]
        },
        {
          name: '累计用户',
          type: 'line',
          // stack: '总量',
          areaStyle: {},
          data: yData2
          // data: [220, 182, 191, 234, 290, 330, 310]
        }
      ]
    };
  }, []);

  return (
    <MidStatics
      id='user-mid-statics'
      showGranularity
      dateTitle='用户统计'
      style={{ height: '385px' }}
      setEchartsOption={setEchartsOption}
      getData={useCallback(
        params => useApi.getUserStaticsData.request(params),
        []
      )}
    />
  );
};
