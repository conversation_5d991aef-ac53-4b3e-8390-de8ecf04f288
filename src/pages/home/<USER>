import React, { useCallback } from 'react';
import MidStatics from './common/midStatics';
import moment from 'moment';
import useApi, { UserStatisticsData } from './apis';

interface Iprops {
  className?: string;
}

export default () => {
  const dataToOptionConfig = (datas: UserStatisticsData[]) => {
    if (!datas) {
      return {};
    }
    const res: {
      yData1?: any[];
      yData2?: any[];
      yData3?: any[];
      xData?: any[];
    } = {};
    res.yData1 = datas.map(data => (data.businessFee || 0) / 100);
    res.yData2 = datas.map(data => (data.netFee || 0) / 100);
    res.yData3 = datas.map(data => (data.refundBusinessFee || 0) / 100);
    res.xData = datas.map(data =>
      moment(data?.date, 'YYYY-MM-DD HH:mm:ss').format('M月DD日')
    );
    return res;
  };

  const setEchartsOption = useCallback(data => {
    const {
      yData1 = [],
      yData2 = [],
      yData3 = [],
      xData = []
    } = dataToOptionConfig(data);
    return {
      legend: {
        data: [
          { name: '总交易额', icon: 'rect' },
          { name: '净交易', icon: 'rect' },
          { name: '退款金额', icon: 'rect' }
        ],
        itemGap: 24,
        bottom: 25,
        left: '6%'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        backgroundColor: '#fff',
        textStyle: {
          color: '#333'
        }
      },
      color: ['#e0f0ff', '#C7FBE8', '#ffe6c3'],
      grid: {
        left: '3%',
        right: '4%',
        bottom: 62,
        top: 15,
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: xData
        }
      ],
      yAxis: [
        {
          type: 'value',
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '总交易额',
          type: 'line',
          // stack: '总量',
          areaStyle: {},
          data: yData1
        },
        {
          name: '净交易',
          type: 'line',
          // stack: '总量',
          areaStyle: {},
          data: yData2
        },
        {
          name: '退款金额',
          type: 'line',
          // stack: '总量',
          areaStyle: {},
          data: yData3
        }
      ]
    };
  }, []);

  return (
    <MidStatics
      id='order-mid-statics'
      showGranularity
      dateTitle='交易统计'
      style={{ height: '385px' }}
      setEchartsOption={setEchartsOption}
      getData={useCallback(
        params => useApi.getOrderStaticsData.request(params),
        []
      )}
    />
  );
};
