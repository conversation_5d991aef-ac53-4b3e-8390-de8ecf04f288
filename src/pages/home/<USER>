import React, { useCallback } from 'react';
import MidStatics from './common/midStatics';
import useApi, { UserStatisticsData } from './apis';
export default () => {
  const dataToOptionConfig = (datas: UserStatisticsData[]) => {
    if (!datas) {
      return [];
    }
    return datas.map(data => ({
      name: data.businessType,
      value: data.businessTypeCount
    }));
  };
  const setEchartsOption = useCallback(data => {
    const showData = dataToOptionConfig(data);
    return {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      color: ['#3876CF', '#50A3E0'],
      legend: {
        orient: 'vertical',
        left: 10,
        data: showData.map(x => x.name)
      },
      series: [
        {
          name: '业务类型占比',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            position: 'inner'
          },
          emphasis: {
            label: {
              show: true,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: showData
        }
      ]
    };
  }, []);

  return (
    <MidStatics
      id='business-bottom-statics'
      dateTitle='业务类型占比'
      style={{ height: '280px' }}
      setEchartsOption={setEchartsOption}
      getData={useCallback(
        params => useApi.getBusinessTypeRatio.request(params),
        []
      )}
    />
  );
};
