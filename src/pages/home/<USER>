import React from 'react';
import styled from 'styled-components';
import { Row, Col, Spin } from 'antd';
import NavItem from './common/navItem';
import useApi from './apis';
import { useSessionStorage } from 'react-use';
import { card, doc, money, pie, user } from './images';
import UserStatics from './userStatics';
import OrderStatics from './orderStatics';
import BusinessType from './businessType';
import PayType from './payType';
// import OnlinePrescriptionStatics from './onlinePrescriptionStatics';
import OnlineQueryStatics from './onlineQueryStatics';
import env from '@configs/env';

export default function Home() {
  const hisId = env.hisId;

  // 顶部用户数据
  const {
    data: { data: userData },
    loading: UserLoading
  } = useApi.getUserPanelData({
    params: { hisId: hisId }
  });

  // 顶部订单数据
  const {
    data: { data: orderData },
    loading: OrderLoading
  } = useApi.getOrderPanelData({
    params: { hisId: hisId }
  });

  return (
    <Wrapper>
      <Spin spinning={UserLoading || OrderLoading} tip={'加载中...'}>
        <Row>
          <Col xl={{ span: 10 }} xs={{ span: 24 }}>
            <Row>
              <Col sm={{ span: 24 }} md={{ span: 12 }}>
                <NavItem
                  lable='总用户数'
                  value={userData?.totalUserCount}
                  img={user}
                  after='人'
                />
              </Col>
              <Col sm={{ span: 24 }} md={{ span: 12 }}>
                <NavItem
                  lable='就诊人数'
                  value={userData?.onlineVisitCount}
                  img={doc}
                  after='人'
                />
              </Col>
            </Row>
            <Row>
              <Col sm={{ span: 24 }} md={{ span: 24 }}>
                <UserStatics />
              </Col>
            </Row>
          </Col>
          <Col xl={{ span: 14 }} xs={{ span: 24 }}>
            <Row>
              <Col sm={{ span: 24 }} md={{ span: 8 }}>
                <NavItem
                  lable='在线绑卡数'
                  value={userData?.onlineBindCardCount}
                  img={card}
                  after='人'
                />
              </Col>
              <Col sm={{ span: 24 }} md={{ span: 8 }}>
                <NavItem
                  lable='交易总金额'
                  value={
                    orderData?.totalBusinessFee
                      ? (orderData?.totalBusinessFee / 100).toFixed(2)
                      : '0.00'
                  }
                  img={money}
                  after='元'
                />
              </Col>
              <Col sm={{ span: 24 }} md={{ span: 8 }}>
                <NavItem
                  lable='交易总笔数'
                  value={orderData?.totalBusinessCount}
                  img={pie}
                  after='笔'
                />
              </Col>
            </Row>
            <Row>
              <Col sm={{ span: 24 }} md={{ span: 24 }}>
                <OrderStatics />
              </Col>
            </Row>
          </Col>
        </Row>
        <Row>
          <Col xs={{ span: 24 }} lg={{ span: 12 }} xl={{ span: 8 }}>
            <BusinessType />
          </Col>
          <Col xs={{ span: 24 }} lg={{ span: 12 }} xl={{ span: 8 }}>
            <PayType />
          </Col>
          <Col xs={{ span: 24 }} lg={{ span: 12 }} xl={{ span: 8 }}>
            <OnlineQueryStatics />
          </Col>
          {/* <Col xs={{ span: 24 }} lg={{ span: 12 }} xl={{ span: 6 }}>
            <OnlinePrescriptionStatics />
          </Col> */}
        </Row>
      </Spin>
    </Wrapper>
  );
}

const Wrapper = styled.div`
  padding: 15px 0 15px 15px;
  .ant-col {
    width: 100%;
  }
`;
