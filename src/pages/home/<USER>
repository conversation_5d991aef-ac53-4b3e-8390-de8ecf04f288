import React, { useCallback } from 'react';
import MidStatics from './common/midStatics';
import useApi, { UserStatisticsData } from './apis';

export default () => {
  const dataToOptionConfig = (datas: UserStatisticsData[]) => {
    if (!datas) {
      return {};
    }
    const res: {
      yData?: any[];
      xData?: any[];
    } = {};
    res.yData = datas.map(data => data.onlineTypeCount);
    res.xData = datas.map(data => data.onlineType || '');
    return res;
  };

  const setEchartsOption = useCallback(data => {
    const { yData = [], xData = [] } = dataToOptionConfig(data);
    return {
      color: ['#50A3E0'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: xData
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: false
        }
      },
      series: [
        {
          data: yData,
          type: 'bar',
          barCategoryGap: '50%'
        }
      ]
    };
  }, []);

  return (
    <MidStatics
      id='query-bottom-statics'
      dateTitle='在线问诊统计'
      style={{ height: '280px' }}
      setEchartsOption={setEchartsOption}
      getData={useCallback(params => useApi.getOnlineQuery.request(params), [])}
    />
  );
};
