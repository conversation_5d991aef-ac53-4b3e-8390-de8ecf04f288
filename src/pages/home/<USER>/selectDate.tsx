import React, { useMemo, useEffect } from 'react';
import styled from 'styled-components';
import { ArrSelect, DayRangePicker } from 'parsec-admin';
import { useForceUpdate } from 'parsec-hooks';
import moment from 'moment';
import { Form } from 'antd';
import { ChartApiParams } from '../apis';
import { useSessionStorage } from 'react-use';
import env from '@configs/env';

interface Iprops<D> {
  className?: string;
  dateTitle: React.ReactNode;
  showGranularity?: boolean;
  onChange: (params: ChartApiParams) => Promise<D>;
}

const selectType = [
  {
    label: '近7日',
    date: [
      moment()
        .subtract(6, 'days')
        .startOf('day'),
      moment().endOf('day')
    ]
  },
  {
    label: '近15日',
    date: [
      moment()
        .subtract(14, 'days')
        .startOf('day'),
      moment().endOf('day')
    ]
  },
  {
    label: '上月',
    date: [
      moment()
        .month(moment().month() - 1)
        .startOf('month'),
      moment()
        .month(moment().month() - 1)
        .endOf('month')
    ]
  },
  {
    label: '上季度',
    date: [
      moment()
        .quarter(moment().quarter() - 1)
        .startOf('quarter'),
      moment()
        .quarter(moment().quarter() - 1)
        .endOf('quarter')
    ]
  },
  {
    label: '自定义时间',
    date: [
      moment()
        .subtract(6, 'days')
        .startOf('day'),
      moment().endOf('day')
    ]
  }
];

const selectDateType = [
  {
    children: '日',
    value: 'day'
  },
  {
    children: '周',
    value: 'week'
  },
  {
    children: '月',
    value: 'month'
  }
];
function SelectDate<D>({
  onChange,
  className,
  dateTitle,
  showGranularity = false
}: Iprops<D>) {
  const [form] = Form.useForm();
  const initialValues = useMemo(
    () => ({
      type: '近7日',
      granularity: 'day',
      date: [
        moment()
          .subtract(6, 'days')
          .startOf('day'),
        moment().endOf('day')
      ]
    }),
    []
  );
  const {
    type = initialValues.type,
    date: [startDate, endDate] = initialValues.date,
    granularity = initialValues.granularity
  } = form.getFieldsValue();
  const { forceUpdate } = useForceUpdate();
  const hisId = env.hisId;
  useEffect(() => {
    const format = `YYYY-MM-${
      form.getFieldValue('granularity') === 'month' ? '01' : 'DD'
    }`;
    onChange({
      hisId,
      startDate: moment(startDate).format(format),
      endDate: moment(endDate).format(format),
      granularity
    });
  }, [endDate, hisId, onChange, startDate, granularity, form]);

  return (
    <Form
      initialValues={initialValues}
      onValuesChange={forceUpdate}
      form={form}
      className={className}
      layout='inline'>
      <Form.Item label={dateTitle} name='type' style={{ marginBottom: '15px' }}>
        <ArrSelect
          allowClear={false}
          options={selectType.map(({ label }) => ({
            children: label,
            value: label
          }))}
          className='select-all'
          onChange={value =>
            form.setFieldsValue({
              date: selectType.find(({ label }) => label === value)?.date
            })
          }
        />
      </Form.Item>

      <Form.Item
        label='自定义'
        name='date'
        style={{ display: type === '自定义时间' ? 'flex' : 'none' }}>
        <DayRangePicker
          picker={
            selectDateType.find(
              ({ value }) => value === form.getFieldValue('granularity')
            )?.value as 'date'
          }
        />
      </Form.Item>
      <Form.Item
        label='粒度'
        name='granularity'
        style={{
          display: type === '自定义时间' && showGranularity ? 'flex' : 'none'
        }}>
        <ArrSelect
          allowClear={false}
          options={selectDateType}
          className='select-all'
          onChange={v => {
            if (v === 'month') {
              form.setFieldsValue({
                date: [moment(), moment().subtract(1, 'M')]
              });
            }
          }}
        />
      </Form.Item>
    </Form>
  );
}

export default styled(SelectDate)`
  & {
    padding: 15px !important;
    z-index: 1;
  }
  .select-all {
    width: 150px;
    margin-right: 20px;
  }
  .ant-col {
    width: auto !important;
  }
`;
