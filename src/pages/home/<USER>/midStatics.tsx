import React, { useEffect, useCallback, useMemo, useRef } from 'react';
import styled from 'styled-components';
import { usePromise } from 'parsec-hooks';
import { ChartApiParams, UserStatisticsData } from '../apis';
import { useResize } from '../hooks';
import SelectDate from './selectDate';
import * as echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/line';
import 'echarts/lib/chart/bar';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';
import 'echarts/lib/chart/pie';
import 'echarts/lib/component/title';
import 'echarts/lib/component/toolbox';

export interface Iprops {
  className?: string;
  style?: React.CSSProperties;
  dateTitle: string;
  showGranularity?: boolean;
  formatResult?: (res: any) => any;
  id: string;
  setEchartsOption: (data: UserStatisticsData[] | undefined) => any;
  getData: (params: ChartApiParams) => Promise<any>;
}

const Statics = (props: Iprops) => {
  const {
    className,
    getData,
    setEchartsOption,
    id,
    style,
    dateTitle,
    formatResult,
    showGranularity = false
  } = props;
  const echartInstance = useRef<echarts.ECharts | null>(null);
  const { data, handle, loading } = usePromise(
    useCallback((params: ChartApiParams) => getData(params), [getData]),
    { needInit: false }
  );

  const option = useMemo(() => {
    if (formatResult) {
      return setEchartsOption(formatResult(data));
    }
    return setEchartsOption(data?.data);
  }, [data, setEchartsOption, formatResult]);

  // 加入了debounce
  const size = useResize();

  useEffect(() => {
    const staticsDom = document.getElementById(id) as HTMLDivElement;
    if (!staticsDom) {
      return;
    }
    if (!echartInstance.current) {
      echartInstance.current = echarts.init(staticsDom);
    }
    echartInstance.current.setOption(option);
  }, [option, id]);

  useEffect(() => {
    if (!echartInstance.current) {
      return;
    }
    echartInstance.current.resize();
  }, [size]);

  useEffect(() => {
    if (!echartInstance.current) {
      return;
    }
    if (loading) {
      echartInstance.current.showLoading('', {
        text: '正在加载数据',
        color: '#50A3E0',
        textColor: '#333',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        zlevel: 0
      });
    } else {
      echartInstance.current.hideLoading();
    }
  }, [loading]);

  return (
    <div className={className}>
      <div className='date-wrap'>
        <SelectDate
          showGranularity={showGranularity}
          dateTitle={dateTitle}
          onChange={useCallback((params: ChartApiParams) => handle(params), [
            handle
          ])}
        />
      </div>
      <div id={id} style={style} />
    </div>
  );
};

export default styled(Statics)`
  background-color: #fff;
  margin: 0 13px 13px 0;
  .date-wrap {
    height: 80px;
    display: flex;
    align-content: center;
  }
`;
