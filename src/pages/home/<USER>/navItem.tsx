import React from 'react';
import styled from 'styled-components';

export interface Iprops {
  className?: string;
  img?: any;
  lable: string;
  value?: number | string;
  after: string;
}

const NavItem = (props: Iprops) => {
  const { className, lable, value, after, img } = props;

  const formatNumber = (value: number | null | undefined | string): string => {
    if (!value) {
      return '0';
    }
    if (typeof value === 'string') {
      return value;
    }
    return value.toLocaleString('en-US');
  };
  return (
    <div className={className}>
      <img src={img} alt='测试图片'></img>
      <div className='content'>
        <h3>
          {formatNumber(value)}
          {after}
        </h3>
        <span>{lable}</span>
      </div>
    </div>
  );
};

export default styled(NavItem)`
  background-color: #fff;
  display: flex;
  align-items: center;
  height: 126px;
  margin: 0 13px 12px 0;
  > img {
    width: 54px;
    height: 54px;
    margin: 0 23px;
    border-radius: 50% 50%;
  }
  > .content > h3 {
    font-size: 26px;
    margin: 0 0 17px 0;
  }
  > .content > span {
    color: #666666;
    font-size: 18px;
  }
`;
