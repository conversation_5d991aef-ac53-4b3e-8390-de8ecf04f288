import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';
import { LoginInfo, UserHisListItem } from './d';

export interface ApiResponse<D> {
  code: 0 | 200 | 999; // 999用户未登录
  msg: string | null;
  data?: D;
}

export interface LoginData {
  token: string;
}

export default {
  verificationCode: createApiHooks(() =>
    request
      .post<{
        result: {
          client_code: string;
          code: string;
          image_url: string;
          imgUrl: string;
        };
      }>('https://third.parsec.com.cn/piccapt/fetch')
      .then(response => {
        response.data.result.imgUrl = `https://third.parsec.com.cn${response.data.result.image_url}`;
        response.data.result.code = response.data.result.client_code;
        return Promise.resolve(response);
      })
  ),
  getCode: createApiHooks(
    (params: {
      phone: string;
      imageCodeKey: string;
      hisId: number;
      imageCode: string;
    }) =>
      request.post<ApiResponse<[]>>('/mch/user/doctorAccount/valicode', params)
  ),
  getResetCode: createApiHooks((params: { phone: string; hisId: number }) =>
    request.post<ApiResponse<[]>>(
      '/mch/user/doctorAccount/sendForgetPasswordCode',
      params
    )
  ),
  gethisList: createApiHooks((params: { phone: string }) =>
    request.post<{
      code: number;
      data: Array<UserHisListItem>;
      msg: any;
    }>('/mch/user/doctorAccount/getUserByPhone', params)
  ),
  login: createApiHooks((params: { // username: string;
    password: string; phone: string; hisId: number }) =>
    request.post<LoginInfo>('/mch/user/doctorAccount/login', params)
  ),
  codeLogin: createApiHooks(
    (params: { code: string; hisId: number | string }) =>
      request.post<LoginInfo>('/mch/user/chuAuth/login', params)
  ),
  resetPWD: createApiHooks(
    (params: {
      password: string;
      phone: string;
      hisId: string;
      code: string;
      imageCode: string;
      imageCodeKey: string;
    }) =>
      request.post<LoginInfo>('/mch/user/doctorAccount/forgetPassword', params)
  ),
  logout: createApiHooks(() =>
    request.post<LoginInfo>('/mch/user/doctorAccount/logout')
  ),
  getnurseInfo: createApiHooks((params: { doctorId: string; hisId: number }) =>
    request.get('/mch/his/doctor/getByDoctorId', { params })
  ),
  getaccount: createApiHooks(params =>
    request.post('/mch/user/doctorAccount/getSessionUser', params)
  )
};
