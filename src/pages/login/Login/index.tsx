import React, { useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { Form, Input, Button, message, Row, Modal } from 'antd';
import useApi from '../apis';
import { useDownCount, useEffectState } from 'parsec-hooks';
import { useLocalStorage, useSessionStorage } from 'react-use';
import storage from '@utils/storage';
import MyIcon from '@components/myIcon';
import RoleIdsStore from '@src/store/RoleIdsStore';
import ConfigStore from '@src/store/ConfigStore';
import { AES } from '@kqinfo/ui';
import qs from 'qs';
import { v4 } from 'uuid';
import env from '@src/configs/env';
import styled from 'styled-components';
import './index.less';
const FormItem = Form.Item;

export default (props: { onReset: () => void; hisId: string }) => {
  //图形验证码表单
  const [imgForm] = Form.useForm();
  const [open, setOpen] = useState(false);
  useEffect(() => {
    const main = document.getElementsByTagName('main')[0];
    if (open && main) {
      const mask = document.createElement('div');
      mask.style.position = 'fixed';
      mask.style.top = '0';
      mask.style.right = '0';
      mask.style.bottom = '0';
      mask.style.left = '0';
      mask.style.zIndex = '19';
      mask.style.height = '100%';
      mask.style.backgroundColor = 'rgba(0, 0, 0, 0.45)';
      mask.id = 'myMask';
      main.appendChild(mask);
      mask.onclick = () => {
        setOpen(false);
      };
    } else {
      const mask = document.getElementById('myMask');
      if (mask) {
        main.removeChild(mask);
      }
      setImageCodeKey(v4());
    }
  }, [open]);
  //本地数据化管理
  const [phone, setPhone] = useLocalStorage('phone');
  const [password, setPassword] = useLocalStorage('password');
  const [, setHisName] = useSessionStorage('hisName');
  const [, setAccount] = useSessionStorage('account');
  const [, setDoctor] = useSessionStorage('doctor');
  const [, setAdminId] = useSessionStorage('adminId');
  const [, setAdminName] = useSessionStorage('adminName');
  const [, setDept] = useSessionStorage('dept');
  const { setMenuData } = RoleIdsStore.useContainer();
  const { flushConfig } = ConfigStore.useContainer();
  const { countdown, setCountdown } = useDownCount();
  const history = useHistory();
  const [form] = Form.useForm();
  const [imageCodeKey, setImageCodeKey] = useState(() => {
    return v4();
  });
  // 登录请求
  const { request: requestLogin, loading: requestLoginLoading } = useApi.login({
    needInit: false,
    initValue: {}
  });
  const [loginLoading, setLoginLoading] = useEffectState(requestLoginLoading);

  // 获取短信验证码请求
  const {
    request: requestGetCode,
    loading: requestGetCodeLoading,
    data: requestGetCodeData
  } = useApi.getCode({
    needInit: false,
    initValue: ''
  });
  const [getCodeLoading] = useEffectState(requestGetCodeLoading);
  // 获取护士信息请求
  const {
    request: requestNurseInfo,
    loading: requestNurseInfoLoading
  } = useApi.getnurseInfo({
    needInit: false
  });
  //医生的账户信息
  const {
    request: requestGetAccount,
    loading: requestGetAccountLoading
  } = useApi.getaccount({
    needInit: false
  });
  const phoneInputFocusRef = useRef(false);
  const phoneInputChangedRef = useRef(true);
  useEffect(() => {
    phoneInputFocusRef.current = false;
    phoneInputChangedRef.current = true;
    form.validateFields(['phone']);
  }, [form]);

  useEffect(() => {
    const { code } = qs.parse(window.location.href.split('?')[1]);
    if (code && typeof code === 'string') {
      useApi.codeLogin
        .request({ code, hisId: props.hisId })
        .then(async ({ code, msg, data }) => {
          if (code !== 0 && !data) {
            message.error(msg || '登录失败');
            // 您第一次登录或密码安全系数较低，为保障您的账号安全，请重置密码！
            if (code === 2000) {
              props.onReset();
            }
            return;
          }
          const [doctor, menuData] = data;
          setDoctor(doctor);
          const { userFlag, account, hisId, id, name, hisName, dept } = doctor;
          setAdminId(id);
          setAdminName(name);
          setHisName(hisName);
          setDept(dept);
          if (userFlag === '5') {
            const { data: NurseInfo }: any = await requestNurseInfo({
              doctorId: account,
              hisId
            });
            setAccount(NurseInfo.doctorId);
          } else {
            const { data: account }: any = await requestGetAccount();
            setAccount(account.account);
          }
          setMenuData(menuData as any);
          history.replace('/home');
        });
    }
  }, [
    history,
    props,
    requestGetAccount,
    requestNurseInfo,
    setAccount,
    setAdminId,
    setAdminName,
    setDept,
    setDoctor,
    setHisName,
    setMenuData
  ]);
  const getCode = () => {
    imgForm
      .validateFields()
      .catch(() => {
        message.error('请输入图形验证码');
      })
      .then(({ imageCode }) => {
        const { phone, hisId } = form.getFieldsValue();
        requestGetCode({
          phone,
          imageCodeKey,
          imageCode,
          hisId
        })
          .then(() => {
            message.success('发送验证码成功');
            setCountdown(120);
            setOpen(false);
          })
          .catch(() => {
            setImageCodeKey(v4());
            imgForm.resetFields();
          });
      });
  };
  // 键盘事件
  const onKeyDown = e => {
    switch (e.keyCode) {
      case 13:
        open && getCode();
        break;
    }
  };
  useEffect(() => {
    window.addEventListener('keydown', onKeyDown); // 添加全局事件

    return () => {
      window.removeEventListener('keydown', onKeyDown); // 销毁
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, imageCodeKey]);
  return (
    <>
      <Form
        form={form}
        onFinish={({ remember, phone, password, hisId, ...values }: any) => {
          //测试开发环境免校验获取过正确的验证码
          // if (
          //   (!requestGetCodeData || requestGetCodeData?.code !== 0) &&
          //   env.env === 'prod'
          // ) {
          //   message.error('请先获取正确的验证码');
          //   return Promise.reject();
          // }
          setLoginLoading(true);
          return requestLogin({
            ...values,
            username: phone,
            phone,
            password: AES.Encrypt(password),
            hisId
          })
            .then(async ({ code, msg, data }) => {
              if (code !== 0) {
                setImageCodeKey(v4());
              }
              if (code !== 0 && !data) {
                message.error(msg || '登录失败');
                // 您第一次登录或密码安全系数较低，为保障您的账号安全，请重置密码！
                if (code === 2000) {
                  props.onReset();
                }
                return;
              }
              console.log('🔥🔥🔥data===>', data);
              const [doctor, menuData] = data;
              setDoctor(doctor);
              if (remember) {
                setPassword(password);
                setPhone(phone);
              } else {
                setPassword(undefined);
                setPhone(undefined);
              }
              const {
                userFlag,
                account,
                hisId,
                id,
                name,
                hisName,
                dept
              } = doctor;
              setAdminId(id);
              setAdminName(name);
              setHisName(hisName);
              setDept(dept);
              if (userFlag === '5') {
                const { data: NurseInfo }: any = await requestNurseInfo({
                  doctorId: account,
                  hisId
                });
                setAccount(NurseInfo.doctorId);
              } else {
                const { data: account }: any = await requestGetAccount();
                setAccount(account.account);
              }
              setMenuData(menuData as any);
              storage.set('userInfo', doctor as any);
              storage.set('token', '23333');
              flushConfig();
              history.replace('/home');
            })
            .catch(res => {
              console.log(res);
              setImageCodeKey(v4());
              // 您第一次登录或密码安全系数较低，为保障您的账号安全，请重置密码！
              if (res.data.code === 2000) {
                props.onReset();
              }
            });
        }}
        onFinishFailed={({
          errorFields: [
            {
              errors: [error]
            }
          ]
        }) => message.error(error)}
        initialValues={{
          phone,
          password,
          hisId: props.hisId,
          remember: !!password
        }}>
        <FormItem
          name={'phone'}
          validateFirst
          rules={[
            {
              required: true,
              message: '请输入手机号'
            },
            {
              pattern: /^1[0-9]{10}$/,
              message: '请输入正确的手机号'
            }
          ]}>
          <Input
            onFocus={() => {
              phoneInputFocusRef.current = true;
            }}
            onBlur={() => {
              phoneInputFocusRef.current = false;
              phoneInputChangedRef.current = true;
              form.validateFields(['phone']);
            }}
            size={'large'}
            placeholder={'请输入手机号'}
            suffix={<MyIcon type={'iconuser'} />}
          />
        </FormItem>
        <FormItem
          name={'password'}
          rules={[
            {
              required: true,
              message: '请输入您的密码'
            }
          ]}>
          <Input
            size={'large'}
            placeholder={'请输入您的密码'}
            type={'password'}
            suffix={<MyIcon type={'iconpassword'} />}
          />
        </FormItem>
        <FormItem
          style={{
            display: 'none'
          }}
          name={'hisId'}
          rules={[
            {
              required: true,
              message: '没有医院id'
            }
          ]}>
          <Input size={'large'} placeholder={'没有医院id'} />
        </FormItem>
        <Row justify='space-between' align='top'>
          <Form.Item
            className='leftItem'
            name={'valicode'}
            rules={[
              {
                required: true,
                message: '请输入短信验证码'
              }
            ]}>
            <Input size={'large'} placeholder={'请输入短信验证码'} />
          </Form.Item>
          {countdown > 0 ? (
            <Button
              style={{
                borderRadius: '10px'
              }}
              type={'primary'}
              disabled={true}
              loading={getCodeLoading}>
              {`${countdown} s后重试`}
            </Button>
          ) : (
            <Button
              style={{
                backgroundColor: '#2059ff',
                borderColor: '#2059ff',
                borderRadius: '10px'
              }}
              type={'primary'}
              onClick={() => {
                form
                  .validateFields(['phone', 'hisId'])
                  .then(() => {
                    setOpen(true);
                  })
                  .catch(
                    ({
                      errorFields: [
                        {
                          errors: [error]
                        }
                      ]
                    }) => {
                      message.error(error);
                      setOpen(false);
                    }
                  );
              }}
              loading={getCodeLoading}>
              获取验证码
            </Button>
          )}
        </Row>
        <FormItem style={{ textAlign: 'center' }}>
          <Button
            className='loginbtn'
            htmlType={'submit'}
            loading={
              loginLoading ||
              requestNurseInfoLoading ||
              requestGetAccountLoading
            }
            // onClick={() => setLoginLoading(true)}
            style={{ fontWeight: 300 }}>
            登录
          </Button>

          {props.hisId === '40005' && (
            <Button
              className='loginbtn'
              style={{ marginTop: 15 }}
              onClick={() => {
                window.location.href = `/api/mch/user/chuAuth/auth?hisId=${props.hisId}`;
              }}>
              统一登录
            </Button>
          )}
        </FormItem>
      </Form>
      <Modal
        destroyOnClose={true}
        getContainer={false}
        visible={open}
        maskStyle={{ borderRadius: '10px' }}
        footer={false}
        onCancel={() => setOpen(false)}
        className={'imageModal'}>
        <ImgCodeBox>
          <div
            onClick={() => {
              setImageCodeKey(v4());
            }}>
            <img
              style={{ width: '60%' }}
              src={`${env.apiHost}/mch/user/doctorAccount/imageCode?imageCodeKey=${imageCodeKey}&hisId=${props.hisId}`}
              alt='图形验证码'
            />
          </div>
          <div className={'formItemBox'}>
            <Form form={imgForm}>
              <Form.Item
                className='leftItem'
                name={'imageCode'}
                rules={[
                  {
                    required: true,
                    message: ''
                  }
                ]}>
                <Input placeholder={'请输入图形验证码'} autoFocus />
              </Form.Item>

              <Button
                type={'primary'}
                loading={requestGetCodeLoading}
                onClick={getCode}>
                确认
              </Button>
            </Form>
          </div>
        </ImgCodeBox>
      </Modal>
    </>
  );
};
const ImgCodeBox = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 20px;
  .formItemBox {
    margin-top: 20px;
    > .ant-form {
      width: 100%;
      display: flex;
      align-items: center;
      .leftItem {
        margin-right: 10px;
        margin-bottom: 0;
      }
    }
  }
`;
