.logo-box {
  width: 454px;
  min-height: 482px;
  background: rgba(63, 118, 255, 0.2);
  box-shadow: 0px 10px 28px 0px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  position: absolute;
  padding: 40px 62px;
  top: 50%;
  left: 50%;
  margin-left: 97px;
  transform: translateY(-50%);
  z-index: 20;

  .ant-form-item-has-error,
  .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled),
  .ant-form-item-has-error,
  .ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless).ant-input,
  .ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless).ant-input:hover,
  .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover,
  .ant-form-item-has-error .ant-input:not(.ant-input-disabled),
  .ant-form-item-has-error .ant-input:not(.ant-input-disabled):hover {
    background-color: transparent;
  }

  @media (max-width: 990px) {
    width: 320px;
    top: 50%;
    left: 50%;
    margin-left: 0;
    padding: 40px 30px;
    transform: translate(-50%, -50%);
  }

  .title {
    color: #fff;
    text-align: center;
    margin-bottom: 40px;
    font-family: 'PingFang SC ', 'PingFang SC';
    font-weight: 650;
    font-style: normal;
    font-size: 26px;
    letter-spacing: 1.5px;

    @media (max-width: 990px) {
      font-size: 18px;
    }
  }

  .ant-input-affix-wrapper,
  .ant-form-item-control-input-content {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    line-height: 1.5715;
    background-color: transparent;
    background-image: none;
    border: none;

    input {
      background: none;
    }
  }

  .ant-form-item-control-input {
    min-height: 50px;
  }

  .ant-input-affix-wrapper-lg {
    padding: 6.5px 0;
    font-size: 16px;
  }

  .ant-input-affix-wrapper-focused {
    box-shadow: none;
  }

  .ant-input-suffix {
    margin-left: 0;
    padding: 5px 20px 0px 20px;
    border-bottom: 1px solid #2a4593;
    position: relative;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      height: 20px;
      width: 1px;
      background: #2a4593;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .ant-input-suffix {
    .anticon {
      width: 14px;
      color: #2158e1;
    }
  }

  .ant-input {
    color: rgba(255, 255, 255, 0.5);
    // width: 100%;
    min-width: 0;
    padding: 4px 0;
    font-size: 14px;
    line-height: 2;
    background-color: transparent;
    background-image: none;
    border: none;
    border-radius: 0px;
    border-bottom: 1px solid #2a4593 !important;

    &:focus {
      box-shadow: none;
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-form-item-explain,
  .ant-form-item-extra {
    display: none;
  }

  input:-webkit-autofill {
    background: none !important;
    -webkit-text-fill-color: #fff !important;
    transition: background-color 99999s ease-in-out 0s;
  }

  .leftItem {
    margin-right: 11px;
    flex: 1;
  }
  .CAPTCHA {
    width: 110px;
    border-radius: 7px;
    border: 1px solid rgb(32, 89, 255);
    > img {
      width: 100%;
    }
  }

  .ant-checkbox-inner {
    border-radius: 50%;
  }

  .ant-checkbox:hover::after,
  .ant-checkbox-wrapper:hover .ant-checkbox::after {
    border-radius: 50%;
    visibility: visible;
  }

  .ant-checkbox-wrapper {
    font-size: 16px;
    color: #fff;
  }

  .send {
    cursor: pointer;
    border-radius: 7px;
    color: #fff;
    font-weight: 550 !important;
    font-size: 14px;
    width: 108px;
    height: 40px;
    text-align: center;
    background-color: rgb(32, 89, 255);
    border-color: rgb(32, 89, 255);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ant-select {
    .ant-select-selector {
      background-color: transparent;
      border-color: transparent !important;
      border: 1px solid #2a4593 !important;
      color: #fff;
    }
    .ant-select-arrow {
      color: #2158e1;
    }
  }

  .loginbtn {
    width: 212px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    border-radius: 100px;
    font-size: 24px;
    border: none;
    font-weight: 550 !important;
    background: rgb(32, 89, 255);
    color: #fff;
    margin: 0 auto;
  }

  .his-select {
    .ant-select-selector {
      background-color: transparent !important;
    }
  }
}
.imageModal {
  width: 90% !important;
  .ant-modal-content {
    background: rgb(44, 72, 202);
    box-shadow: 0 10px 28px 0 rgba(0, 0, 0, 0.4);
    border-radius: 10px;
  }
}
