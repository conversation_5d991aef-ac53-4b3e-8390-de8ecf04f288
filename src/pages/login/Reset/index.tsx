import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, Button, message, Row } from 'antd';
import useApi from '../apis';
import { useDownCount, useEffectState } from 'parsec-hooks';
import MyIcon from '@components/myIcon';
import checkPwd from '@src/utils/checkPwd';
import { AES } from '@kqinfo/ui';
import { v4 } from 'uuid';
import env from '@src/configs/env';
const FormItem = Form.Item;

const isDev = process.env.NODE_ENV !== 'production';

export default (props: { onLogin: () => void; hisId: string }) => {
  const { countdown, setCountdown } = useDownCount();
  const [form] = Form.useForm();
  // 登录请求
  const {
    request: requestResetPWD,
    loading: requestLoginLoading
  } = useApi.resetPWD({
    needInit: false,
    initValue: {}
  });
  const [loginLoading, setLoginLoading] = useEffectState(requestLoginLoading);

  // 获取短信验证码请求
  const {
    request: requestGetCode,
    loading: requestGetCodeLoading
  } = useApi.getResetCode({
    needInit: false
  });
  const [getCodeLoading, setGetCodeLoading] = useEffectState(
    requestGetCodeLoading
  );

  const phoneInputFocusRef = useRef(false);
  const phoneInputChangedRef = useRef(true);
  useEffect(() => {
    phoneInputFocusRef.current = false;
    phoneInputChangedRef.current = true;
    form.validateFields(['phone']);
  }, [form]);

  const [imageCodeKey, setImageCodeKey] = useState(() => {
    return v4();
  });

  return (
    <Form
      form={form}
      onFinish={(values: any) => {
        setLoginLoading(true);
        return requestResetPWD({
          phone: values.phone,
          imageCode: values.imageCode,
          imageCodeKey,
          password: AES.Encrypt(values.password),
          code: values.code,
          hisId: values.hisId
        })
          .then(async ({ code, msg, data }) => {
            if (code !== 0) {
              setImageCodeKey(v4());
            }
            if (code !== 0 && !data) {
              message.error(msg || '重置失败');
              return;
            }
            message.success('重置成功，请重新登录');
            props.onLogin();
          })
          .catch(() => {
            setImageCodeKey(v4());
          });
      }}
      onFinishFailed={({
        errorFields: [
          {
            errors: [error]
          }
        ]
      }) => {
        message.error(error);
        // setLoginLoading(false);
      }}
      initialValues={{
        ...(isDev
          ? {
              // phone: '18716573962',
              // password: '000000'
            }
          : {}),
        hisId: props.hisId
      }}>
      <FormItem
        name={'phone'}
        validateFirst
        rules={[
          {
            required: true,
            message: '请输入手机号'
          }
        ]}>
        <Input
          size={'large'}
          placeholder={'请输入手机号'}
          suffix={<MyIcon type={'iconuser'} />}
        />
      </FormItem>

      <FormItem
        style={{
          display: 'none'
        }}
        name={'hisId'}
        rules={[
          {
            required: true,
            message: '没有医院id'
          }
        ]}>
        <Input size={'large'} placeholder={'没有医院id'} />
      </FormItem>

      <Row justify='space-between' align='top'>
        <FormItem
          className='leftItem'
          name={'imageCode'}
          rules={[
            {
              required: true,
              message: '请输入图形验证码'
            }
          ]}>
          <Input size={'large'} placeholder={'请输入图形验证码'} />
        </FormItem>
        <div
          className='CAPTCHA'
          onClick={() => {
            setImageCodeKey(v4());
          }}>
          <img
            src={`${env.apiHost}/mch/user/doctorAccount/imageCode?imageCodeKey=${imageCodeKey}&hisId=${props.hisId}`}
            alt='图形验证码'
          />
        </div>
      </Row>

      <Row justify='space-between' align='top'>
        <FormItem
          className='leftItem'
          name={'code'}
          rules={[
            {
              required: true,
              message: '请输入短信验证码'
            }
          ]}>
          <Input size={'large'} placeholder={'请输入短信验证码'} />
        </FormItem>
        <Button
          className='send'
          onClick={() => {
            setGetCodeLoading(true);
            form
              .validateFields(['phone', 'hisId'])
              .then(({ phone, hisId }) =>
                requestGetCode({
                  phone,
                  hisId
                }).then(() => {
                  message.success('发送验证码成功');
                  setCountdown(120);
                })
              )
              .catch(
                ({
                  errorFields: [
                    {
                      errors: [error]
                    }
                  ]
                }) => {
                  setGetCodeLoading(false);
                  message.error(error);
                }
              );
          }}
          disabled={countdown > 0}
          loading={getCodeLoading}>
          {countdown > 0 ? `${countdown} s后重试` : '获取验证码'}
        </Button>
      </Row>
      <FormItem
        name={'password'}
        rules={[
          {
            required: true,
            message: '请输入您的新密码'
          },
          {
            required: true,
            message: '密码格式错误！',
            validator: (rule, value, callback) => {
              if (checkPwd(value).success) {
                callback();
              } else {
                callback('密码格式错误！');
              }
            }
          }
        ]}>
        <Input
          size={'large'}
          placeholder={'请输入您的新密码'}
          type={'password'}
          suffix={<MyIcon type={'iconpassword'} />}
        />
      </FormItem>
      <FormItem name='remember' valuePropName={'checked'}>
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'space-between',
            alignContent: 'center',
            color: '#c1c1c1'
          }}>
          请设置8-20位密码，至少包含数字、字母、特殊符号中2种，不能有空格
        </div>
      </FormItem>
      <FormItem style={{ textAlign: 'center' }}>
        <Button
          className='loginbtn'
          htmlType={'submit'}
          loading={loginLoading}
          // onClick={() => setLoginLoading(true)}
          style={{ fontWeight: 300 }}>
          重置密码
        </Button>
      </FormItem>
    </Form>
  );
};
