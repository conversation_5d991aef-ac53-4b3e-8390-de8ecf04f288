// 根据手机号查询出的所在的医院列表
export interface UserHisListItem {
  account: '900';
  createTime: '2018-08-07 16:08:10';
  departmentId: '2018008';
  dept: '皮肤科';
  expiresIn: null;
  freezetime: '2019-01-11T02:44:02.000+0000';
  hisName: '重庆凯桥虚拟医院互联网医院';
  hisId: 2218;
  id: 33133;
  loginNum: null;
  name: '潘龙测试';
  password: null;
  passwordErrorNum: null;
  passwordErrorTime: null;
  passwordValidateEnd: null;
  phone: '***********';
  roleId: null;
  signaturePassword: null;
  status: '1';
  unionId: 'ozulNwVqeINp8ZO7IfT7ytFjuJnA';
  updateTime: '2020-04-15 22:01:37';
  userFlag: '3';
  userType: 'doc_platform';
  validateEnd: '2019-12-12T06:16:59.000+0000';
  validateStart: '2018-08-07T08:08:10.000+0000';
}

interface LoginInfoMenuItem {
  id: 110;
  hisId: 2218;
  type: '1';
  name: '医院管理';
  code: 'HOSPTIAL_MANAGE';
  parentId: null;
  url: '';
  icon: '';
  sort: 1;
  createTime: '2019-05-29T07:16:19.000+0000';
  updateTime: '2019-05-29T07:16:19.000+0000';
  roleId: null;
  childMenu: LoginInfoMenuItem[] | null;
}
//登录后返回的信息 包含菜单权限
export interface LoginInfo {
  code: number;
  msg: '登录成功';
  data: [
    {
      id: 33133;
      hisId: 2218;
      hisName: string;
      account: '900';
      password: 'ecd0bd90f90410eddf3b051cc1362992';
      signaturePassword: 'e1e2abe5646f51b5a452c5fe0c326e7b';
      status: '1';
      userType: 'doc_platform';
      name: '潘龙测试';
      dept: '皮肤科';
      departmentId: '2018008';
      freezetime: '2019-01-11T02:44:02.000+0000';
      phone: '***********';
      validateStart: '2018-08-07T08:08:10.000+0000';
      validateEnd: '2019-12-12T06:16:59.000+0000';
      passwordErrorTime: null;
      passwordErrorNum: 0;
      passwordValidateEnd: '2019-12-12T06:17:46.000+0000';
      loginNum: 0;
      userFlag: string;
      createTime: '2018-08-07 16:08:10';
      updateTime: '2020-04-15 22:31:53';
      unionId: 'ozulNwVqeINp8ZO7IfT7ytFjuJnA';
      roleId: null;
      expiresIn: null;
    },
    LoginInfoMenuItem[],
    {
      id: 1;
      hisId: 2218;
      hisName: '重庆医科大学附属儿童医院';
      doctorId: '900';
      deptId: '2018008';
      deptName: '皮ac';
      name: '潘龙测试';
      sex: 'M';
      level: '2';
      grade: '2';
      image: string;
      specialty: '肺炎、腹泻等儿内科常见病；免疫缺陷、过敏性紫癜、川崎病等疾病诊治肺炎、腹泻等儿内科常见病；免疫缺陷、过敏性紫癜、川崎病等疾病诊治222222222222';
      introduction: '女，硕士，主治医师。2013年重庆医科大学儿科系毕业后留校工作，从事风湿免疫专业临床与研究工作。多次参加国际及全国学术会议，并大会发言，发表SCI论文2篇。擅长风湿免疫性疾病（川崎病，过敏性紫癜，原发性免疫缺陷病等）的诊治，对儿内科常见病（呼吸、消化方向）、儿童保健也有较深见解。4444444';
      pdeptName: '';
      pdeptId: ' 2018008';
      sortNo: 9;
      status: '1';
      workingLife: 10;
      mobile: '***********';
      createTime: '2018-08-07 16:08:10';
      updateTime: '2020-04-02 17:18:28';
      extFields: '{"view":"person","operate":"person","followed":"off"}';
      hisDoctorName: 'ZJR';
      qrTicket: 'gQE48TwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyS1BHQWdldVFhZlAxMDAwMDAwN0cAAgQDlGNcAwQAAAAA';
      qrContent: 'http://weixin.qq.com/q/02KPGAgeuQafP10000007G';
      qrUrl: string;
      circleImage: string;
      type: '1';
      deptmentId: 190829000000888;
      deptmentName: '下级医院职能部门';
      consultationAuthority: '1';
      recommend: null;
      educationTitle: null;
      consultCount: null;
      doctorInfoId: 1;
      practiceScope: '外科专业';
      category: 'data:image/jpg;base64,R0lGODlhoAA8APcAAP8CAv4BAf4BAf4CAv4EBP4NDf4mJv5vb/7z8/7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v////7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v////7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v////7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v////7+/v7+/v7+/v///yH5BAlkAPAALAAAAACgADwAAAj+AOEJHEiwoMGDCBMqXMiwocOHECNKnEixosWLGDNq3Mixo8ePIEOKHEmypMmTKFOqXMmypcuXMGPKnEmzps2bOHPq3Mmzp8+fQIMKHUq0qNGjSJMqXcq0qdOnNgMEADmVqsOqIadixbhVqtetHrFK7Tj261iCYMNq1XgWrcC2ZNGmvbh2INi5EuG6hae3oti3gK3uxUuxa2DCEfH+ZWuXr+ONZr/yNcuxal3LjCdHluzXruTNYQNDtowZ8+OslUVPPv3RdOrVjk0jzjg7MevLqE/3tXgWN+vCu3/Tbqy7dl7iwQs/9m28oVitXuPqlit9edzShzMTF85bc3TK1eujpsZuffhe7p1FL678PHTs7OiPn2/OMLr67ZDPs+1d3jVd+LBx9Rxor2lWEH0KGXYQgglO9158D623Hm2yHWheefp1N+Bb4gG33YR0uXYXgwa1BSKJCJXVl31+DRiZdiWCxxt01Bl4YULJTTQXiiWqNBuP6UEl5JBEFmnkkUgmqeSSTDbp5JNQRinllFRWaeWVWGap5ZZcdunll2CGKeaYZHYUEAA7';
      practiceLevel: '执业医师';
      practiceNumber: 110500000011900;
      position: '单曲';
      idNumber: '420302197712110937';
      auditTime: '2000-01-01 03:00:00';
      practiceMedicalInstitution: '重庆医科大学附属儿童医院';
      title: '';
      prescriptionQualification: '普通处方';
      policyNumber: 'PZEM201950010000000039';
      underwritingUnit: '中国人民财产保险股份有限公司';
      insuranceStartDate: '2020-01-02 19:05:08';
      insuranceEndDate: '2020-01-02 19:05:12';
      insuranceDueDate: '2019年1月1日至2019年12月31日';
      remark: '';
      startValidDate: '2020-01-02 19:05:04';
      endValidDate: '2020-01-02 19:05:06';
      doctorInfoVo: null;
      reviewAdmin: null;
      reviewDoctor: null;
      inquirys: null;
      inquiryConfigParamList: null;
      ableType: null;
      score: 0.0;
      favoriteRate: null;
      serviceTimes: 0;
      completed: 0;
      favourTimes: 0;
      evaluated: 0;
      replyTime: null;
      timelyReply: '0%';
      evaluationLabel: null;
      replyLabel: null;
      onDuty: '1';
      isFull: '0';
      fans: 0;
      signatureImg: null;
    }
  ];
}

//查询医生详情
// export interface doctorInfo {}
