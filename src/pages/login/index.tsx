import React, { useState } from 'react';
import { KqLoginLayout } from 'parsec-admin';
import Login from './Login';
import Reset from './Reset';
import env from '@src/configs/env';

export default () => {
  const [modalType, setModalType] = useState<'login' | 'reset'>('login');

  const hisId = env.hisId;

  return (
    <KqLoginLayout>
      {modalType === 'login' && (
        <Login
          hisId={hisId}
          onReset={() => {
            setModalType('reset');
          }}
        />
      )}
      {modalType === 'reset' && (
        <Reset
          hisId={hisId}
          onLogin={() => {
            setModalType('login');
          }}
        />
      )}
    </KqLoginLayout>
  );
};
