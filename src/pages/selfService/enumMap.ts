import {
  convertMapToOptions,
  convertMapToOptionsForArrSelect
} from '@src/utils/common';

export const indexPageLayoutMap = {
  1: '综合服务',
  2: '多项服务',
  3: '专项服务'
};
export const pageGroupMap = {
  1: '门诊',
  2: '住院'
};

export const pageGroupOptions = convertMapToOptions(pageGroupMap);

export const functionStatusMap = {
  0: '停用',
  1: '启用',
  2: '维护'
};
export const functionStatusOptions = convertMapToOptionsForArrSelect(
  functionStatusMap
);

export const moduleSizeMap = {
  1: '小尺寸',
  2: '中尺寸'
};
export const moduleSizeOptions = convertMapToOptions(moduleSizeMap);

export const moduleLocationMap = {
  1: '门诊',
  2: '住院'
};
export const moduleLocationOptions = convertMapToOptions(moduleLocationMap);

export const identityTypeMap = {
  idCard: '身份证',
  medicalCard: '医保卡',
  eMedicalCode: '电子医保凭证',
  barCode: '条形码',
  hospitalCard: '健康公众通卡（医院实体卡）',
  inputPatientId: '手动输入就诊号',
  eHealthCode: '电子健康卡'
};
export const identityTypeOptions = convertMapToOptions(identityTypeMap);

export const payTypeMap = {
  wechatPay: '微信',
  alipay: '支付宝',
  bankCard: '银行卡',
  medicalCard: '医保卡',
  eMedicalCode: '电子医保凭证'
};
export const payTypeOptions = convertMapToOptions(payTypeMap);
export const moduleStatusMap = {
  //功能状态 0停用 1启用 2维护

  0: '停用',
  1: '启用',
  2: '维护'
};
export const moduleStatusOptions = convertMapToOptions(moduleStatusMap);
export const hospitalMap = {
  '0001': '渝中院区',
  '0002': '礼嘉院区'
};
