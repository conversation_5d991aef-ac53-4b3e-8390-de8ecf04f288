export const convertArrayToOneZeroObject = (
  oneArray: string[],
  allArray: string[]
) => {
  const result: Record<string, number> = {};
  allArray.forEach(item => {
    result[item] = 0;
  });
  oneArray.forEach(item => {
    result[item] = 1;
  });
  return result;
};

export const convertOneZeroObjectToArrayValue = (
  oneZeroObject: Record<string, number>,
  oneZeroMap: Record<string, string>
) => {
  if (!oneZeroObject) return [];
  return Object.keys(oneZeroObject)
    .filter(key => oneZeroObject[key] === 1)
    .map(key => oneZeroMap[key]);
};
export const convertOneZeroObjectToArrayKey = (
  oneZeroObject: Record<string, number>,
  oneZeroMap: Record<string, string>
) => {
  if (!oneZeroObject) return [];
  return Object.keys(oneZeroObject).filter(key => oneZeroObject[key] === 1);
};
