import { useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON>on,
  DayRangePicker,
  ArrSelect,
  ActionsWrap,
  actionConfirm,
  useModal,
  handleSubmit
} from 'parsec-admin';
import MyTableList from '@components/myTableList';
import { useRequest } from 'ahooks';
import { Button, Checkbox, InputNumber, Radio } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

import {
  apiGetKaiqiaoHisConfigCommonGetModuleConfig,
  apiGetKaiqiaoHisConfigCommonGetModuleList,
  apiPutKaiqiaoHisConfigCommonAddModuleConfig,
  apiPutKaiqiaoHisConfigCommonChargeModuleConfig
} from '@src/api/公共分类';
import {
  identityTypeMap,
  identityTypeOptions,
  moduleLocationMap,
  moduleLocationOptions,
  moduleSizeMap,
  moduleSizeOptions,
  moduleStatusMap,
  moduleStatusOptions,
  payTypeMap,
  payTypeOptions
} from '@src/pages/selfService/enumMap';
import {
  convertArrayToOneZeroObject,
  convertOneZeroObjectToArrayKey,
  convertOneZeroObjectToArrayValue
} from '@src/pages/selfService/util';
import { SelectDevice } from '../..';
export type FunctionalConfigurationTable = {
  selectDevice: SelectDevice;
};
export default ({ selectDevice }: FunctionalConfigurationTable) => {
  const { data: moduleList } = useRequest(async () => {
    const {
      data: { recordList }
    } = await apiGetKaiqiaoHisConfigCommonGetModuleList({
      pageNum: '1',
      numPerPage: '99999'
    } as any);
    return recordList;
  });

  const switchModalVisible = useModal(
    ({ id }) => {
      const isEdit = !!id;
      return {
        title: isEdit ? '编辑功能' : '添加功能',
        onSubmit: values =>
          handleSubmit(() => {
            const val = {
              ...values,
              indexPageId: selectDevice.pageTypeId,
              id
            };
            if (values.payType) {
              val.payType = convertArrayToOneZeroObject(
                values.payType,
                payTypeOptions.map(item => item.value)
              );
            }
            if (values.identityType) {
              val.identityType = convertArrayToOneZeroObject(
                values.identityType,
                identityTypeOptions.map(item => item.value)
              );
            }
            if (isEdit) {
              return apiPutKaiqiaoHisConfigCommonChargeModuleConfig(val);
            } else {
              delete val.id;
              return apiPutKaiqiaoHisConfigCommonAddModuleConfig(val);
            }
          }),
        items: [
          {
            label: '功能',
            name: 'moduleId',
            required: true,
            render: () => (
              <ArrSelect
                options={(moduleList || [])?.map(item => ({
                  value: item.id,
                  children: item.moduleName
                }))}
              />
            )
          },
          {
            label: '功能状态',
            name: 'moduleStatus',
            required: true,
            render: () => <ArrSelect options={moduleStatusOptions} />
          },
          {
            label: '功能尺寸',
            name: 'moduleSize',
            render: () => (
              <Radio.Group
                options={moduleSizeOptions}
                optionType='button'
                buttonStyle='solid'
              />
            ),
            formItemProps: {
              rules: [
                {
                  required: true
                }
              ]
            }
          },
          {
            label: '功能序号',
            name: 'moduleSerialNo',
            render: () => (
              <InputNumber
                placeholder='请输入功能序号（0~99）'
                min={0}
                max={100}
              />
            ),
            required: true
          },
          {
            label: '功能位置',
            name: 'moduleLocaltion',
            required: true,
            render: () => (
              <Radio.Group
                options={moduleLocationOptions}
                optionType='button'
                buttonStyle='solid'
              />
            )
          },
          {
            label: '身份识别方式',
            name: 'identityType',
            render: <Checkbox.Group options={identityTypeOptions} />
          },
          {
            label: '支付方式',
            name: 'payType',
            render: <Checkbox.Group options={payTypeOptions} />
          }
        ]
      };
    },
    [moduleList]
  );

  return (
    <MyTableList
      tableTitle={false}
      action={
        <Button
          type={'default'}
          icon={<PlusOutlined />}
          onClick={() => {
            switchModalVisible({});
          }}>
          添加功能
        </Button>
      }
      getList={({ params }) => {
        return apiGetKaiqiaoHisConfigCommonGetModuleConfig(
          params as any
        ) as any;
      }}
      params={{
        pageTypeId: selectDevice?.pageTypeId
      }}
      columns={useMemo(
        () => [
          {
            width: 100,
            title: '功能名称',
            dataIndex: 'moduleName',
            search: true
          },
          {
            width: 100,
            title: '功能尺寸',
            dataIndex: 'moduleSize',
            render: v => moduleSizeMap[v]
          },
          {
            width: 100,
            title: '功能状态',
            dataIndex: 'moduleStatus',
            render: v => moduleStatusMap[v],
            searchIndex: 'status',
            search: <ArrSelect options={moduleStatusMap} />
          },
          {
            width: 100,
            title: '修改时间',
            dataIndex: 'updateTime',
            render: false,
            search: (
              <DayRangePicker
                placeholder={['开始时间', '结束时间']}
                valueFormat={'YYYY-MM-DD HH:mm:ss'}
              />
            ),
            searchIndex: ['updateStartTime', 'updateEndTime']
          },
          {
            width: 100,
            title: '功能位置',
            dataIndex: 'moduleLocaltion',
            render: v => moduleLocationMap[v]
          },
          {
            width: 100,
            title: '功能序号',
            dataIndex: 'serialNo'
          },
          {
            title: '身份识别方式',
            dataIndex: 'identityType',
            width: 300,
            render: v =>
              convertOneZeroObjectToArrayValue(
                v,
                identityTypeMap
              ).map((item, index) => <div key={index}>{item}</div>)
          },
          {
            title: '支付方式',
            dataIndex: 'payWay',
            width: 300,
            render: v =>
              convertOneZeroObjectToArrayValue(
                v,
                payTypeMap
              ).map((item, index) => <div key={index}>{item}</div>)
          },
          {
            title: '最后修改时间',
            dataIndex: 'payWay',
            render: v => '-'
          },
          {
            title: '最后修改人',
            dataIndex: 'payWay',
            render: v => '-'
          },
          {
            title: '操作',
            width: 200,
            fixed: 'right',
            render: record => (
              <ActionsWrap>
                <LinkButton
                  onClick={() => {
                    switchModalVisible({
                      ...record,
                      payType: convertOneZeroObjectToArrayKey(
                        record.payWay,
                        identityTypeMap
                      ),
                      identityType: convertOneZeroObjectToArrayKey(
                        record.identityType,
                        payTypeMap
                      )
                    });
                  }}>
                  编辑
                </LinkButton>

                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        apiPutKaiqiaoHisConfigCommonChargeModuleConfig({
                          ...record,
                          isDelete: 1
                        }),
                      '删除'
                    );
                  }}>
                  删除
                </LinkButton>
              </ActionsWrap>
            )
          }
        ],
        [switchModalVisible]
      )}
    />
  );
};
