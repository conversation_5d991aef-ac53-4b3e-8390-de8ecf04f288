import React, { useRef, useState } from 'react';

import MyTableList from '@src/components/myTableList';
import {
  apiGetKaiqiaoHisConfigCommonGetDeviceList,
  apiPutKaiqiaoHisConfigCommonChargeDevice,
  ApiPutKaiqiaoHisConfigCommonChargeDeviceRequest
} from '@src/api/公共分类';
import { Button, Row, Space } from 'antd';
import { useReloadTableList } from 'parsec-admin';
import { SelectDevice } from '../..';

type DeviceListAddModalType = {
  onClose: () => void;
  selectDevice: SelectDevice;
};
const DeviceListAddModal = ({
  onClose,
  selectDevice
}: DeviceListAddModalType) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const reloadTableList = useReloadTableList();
  const selectedRowsData = useRef<
    ApiPutKaiqiaoHisConfigCommonChargeDeviceRequest
  >();
  const handleOnOk = async () => {
    await apiPutKaiqiaoHisConfigCommonChargeDevice(
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      (selectedRowsData.current! as any).map(item => ({
        ...item,
        indexPageId: selectDevice.pageTypeId
      }))
    );
    reloadTableList();
    onClose();
  };
  return (
    <>
      <MyTableList
        rowKey='deviceCode'
        tableTitle=''
        columns={[
          {
            title: '设备编号',
            dataIndex: 'deviceCode'
          },
          {
            title: '设备位置',
            dataIndex: 'deviceLocaltion'
          },
          {
            title: '院区',
            dataIndex: 'hospitalCode'
          }
        ]}
        getList={({ params }) =>
          apiGetKaiqiaoHisConfigCommonGetDeviceList(params as any) as any
        }
        rowSelection={{
          preserveSelectedRowKeys: true,
          selectedRowKeys: selectedRowKeys,
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRowKeys(selectedRowKeys);
            selectedRowsData.current = selectedRows as ApiPutKaiqiaoHisConfigCommonChargeDeviceRequest;
          }
        }}
        paginationExtra={
          <div>
            {!!selectedRowKeys.length && (
              <>
                <span> 已选择{selectedRowKeys.length}条</span>
              </>
            )}
          </div>
        }
      />
      <Row justify='end'>
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button type='primary' onClick={handleOnOk}>
            确定
          </Button>
        </Space>
      </Row>
    </>
  );
};

export default DeviceListAddModal;
