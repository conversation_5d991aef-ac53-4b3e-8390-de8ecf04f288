import { useMemo, useState } from 'react';
import {
  <PERSON><PERSON>utton,
  DayRangePicker,
  ArrSelect,
  ActionsWrap,
  actionConfirm,
  useModal,
  handleSubmit
} from 'parsec-admin';
import MyTableList from '@components/myTableList';
import { useRequest } from 'ahooks';

import { Button, Modal } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import {
  apiGetKaiqiaoHisConfigCommonGetDeviceConfig,
  apiGetKaiqiaoHisConfigCommonGetIndexPageTypeList,
  apiPutKaiqiaoHisConfigCommonChargeDevice
} from '@src/api/公共分类';
import { hospitalMap } from '@src/pages/selfService/enumMap';

import { SelectDevice } from '../..';
import DeviceListAddModal from '../DeviceListAddModal';
import { showPlaceholderWhenEmpty } from '@src/utils/common';
export type DeviceListTable = {
  selectDevice: SelectDevice;
};
export default ({ selectDevice }: DeviceListTable) => {
  const [deviceListAddModalVisible, setDeviceListAddModalVisible] = useState(
    false
  );
  const { data: pageTypeList } = useRequest(async () => {
    const {
      data: { recordList }
    } = await apiGetKaiqiaoHisConfigCommonGetIndexPageTypeList({
      pageNum: '1',
      numPerPage: '99999',
      deviceType: selectDevice?.deviceTypeId
    } as any);
    return recordList;
  });

  const switchModalVisible = useModal(
    record => {
      return {
        title: '设置设备类型',
        onSubmit: values =>
          handleSubmit(() =>
            apiPutKaiqiaoHisConfigCommonChargeDevice([{ ...record, ...values }])
          ),
        items: [
          {
            name: 'indexPageId',
            label: '设备类型',
            render: (
              <ArrSelect
                options={(pageTypeList || [])?.map(item => ({
                  value: item.id,
                  children: item.indexPageName
                }))}
              />
            )
          }
        ]
      };
    },
    [pageTypeList]
  );
  return (
    <>
      <MyTableList
        tableTitle={false}
        action={
          <Button
            type={'default'}
            icon={<PlusOutlined />}
            onClick={() => {
              setDeviceListAddModalVisible(true);
            }}>
            添加设备
          </Button>
        }
        getList={({ params }) => {
          const { hospitalCode, ...restParams } = params as any;
          const finalParams: any = restParams;
          if (hospitalCode) {
            finalParams.hospitalCode = `000${hospitalCode}`;
          }
          return apiGetKaiqiaoHisConfigCommonGetDeviceConfig(
            finalParams as any
          ) as any;
        }}
        params={{
          pageTypeId: selectDevice?.pageTypeId
        }}
        columns={useMemo(
          () => [
            {
              title: '设备编号',
              dataIndex: 'deviceCode',
              search: true
            },
            {
              title: '设备位置',
              dataIndex: 'deviceLocaltion',
              search: true
            },
            {
              title: '院区',
              dataIndex: 'hospitalCode',
              render: v => {
                return hospitalMap[v];
              },
              search: <ArrSelect options={hospitalMap} />
            },
            {
              title: '添加时间',
              dataIndex: 'updateTime',
              render: false,
              search: (
                <DayRangePicker
                  placeholder={['开始时间', '结束时间']}
                  valueFormat={'YYYY-MM-DD HH:mm:ss'}
                />
              ),
              searchIndex: ['updateStartTime', 'updateEndTime']
            },
            {
              title: '添加人',
              dataIndex: 'operationUser',
              render: v => showPlaceholderWhenEmpty(v)
            },
            {
              title: '操作时间',
              dataIndex: 'operatorDateTime',
              render: v => showPlaceholderWhenEmpty(v)
            },
            {
              title: '操作',
              fixed: 'right',
              render: record => (
                <ActionsWrap>
                  <LinkButton
                    onClick={() => {
                      switchModalVisible(record);
                    }}>
                    设置类型
                  </LinkButton>

                  <LinkButton
                    onClick={() => {
                      actionConfirm(
                        () =>
                          apiPutKaiqiaoHisConfigCommonChargeDevice([
                            {
                              ...record,
                              indexPageId: null
                            }
                          ]),
                        '删除'
                      );
                    }}>
                    删除
                  </LinkButton>
                </ActionsWrap>
              )
            }
          ],
          [switchModalVisible]
        )}
      />
      <Modal
        title='添加设备'
        visible={deviceListAddModalVisible}
        destroyOnClose
        onCancel={() => setDeviceListAddModalVisible(false)}
        footer={null}
        width={800}>
        <DeviceListAddModal
          selectDevice={selectDevice}
          onClose={() => setDeviceListAddModalVisible(false)}
        />
      </Modal>
    </>
  );
};
