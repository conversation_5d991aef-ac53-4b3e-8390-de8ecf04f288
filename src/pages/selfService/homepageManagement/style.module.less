.index {
  display: flex;
}

.left {
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  padding: 20px;
  background-color: rgb(252, 251, 250);
  height: 900px;
  margin-right: 20px;

  :global {
    .ant-tabs-nav-list {
      width: 220px;
    }
  }
}

.left_top {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 0;
  margin-bottom: 20px;
}

.left_1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.left_2 {
  flex: 1;
  overflow: auto;
}

.left_3 {
  display: flex;
  align-items: center;
  justify-content: center;
}

.left_1_select {
  min-width: 100px;
}

.leftTabItem_1 {
  margin-bottom: 10px;
  display: flex;
}

.tag {
  margin-left: 10px !important;
}

.right {
  flex: 1 0 0;
  width: 0;
}

.tabs {
  :global {
    .ant-tabs-nav-list {
      overflow-x: scroll;
    }

    .ant-tabs-tab-active {
      background-color: #e7f0fa;
    }
  }
}
