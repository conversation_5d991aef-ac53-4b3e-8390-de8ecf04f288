import { CardLayout } from 'parsec-admin';
import styles from './style.module.less';
import { Button, Select, Tabs, Tag } from 'antd';
import { FlagOutlined, SettingOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { useHistory } from 'react-router';
import { useState } from 'react';
import FunctionalConfigurationTable from './components/FunctionalConfigurationTable';
import DeviceListTable from './components/DeviceListTable';
import {
  apiGetKaiqiaoHisConfigCommonGetIndexPageTypeList,
  useApiGetKaiqiaoHisConfigCommonGetDeviceType
} from '@src/api/公共分类';
const rightTabs = [
  {
    tab: '功能配置',
    key: 'functionConfig',
    content: FunctionalConfigurationTable
  },
  {
    tab: '设备列表',
    key: 'deviceList',
    content: DeviceListTable
  }
];
export type SelectDevice = {
  deviceTypeId?: string;
  pageTypeId?: string;
};
const Index = () => {
  const [selectDevice, setSelectDevice] = useState<SelectDevice>();
  const history = useHistory();
  const { data: deviceList } = useApiGetKaiqiaoHisConfigCommonGetDeviceType({
    initValue: []
  });
  const { data: pageTypeList } = useRequest(
    async () => {
      const {
        data: { recordList }
      } = await apiGetKaiqiaoHisConfigCommonGetIndexPageTypeList({
        deviceType: selectDevice?.deviceTypeId,
        numPerPage: '99999',
        pageNum: '1'
      } as any);
      return recordList;
    },
    {
      refreshDeps: [selectDevice?.deviceTypeId],
      onSuccess: data => {
        if (data?.[0]) {
          setSelectDevice(selectDevice => ({
            ...selectDevice,
            pageTypeId: String(data[0].id)
          }));
        }
      }
    }
  );
  const deviceSelectOptions = deviceList?.map(device => ({
    value: device.id,
    label: device.name
  }));
  return (
    <CardLayout>
      <div className={styles.index}>
        <div className={styles.left}>
          <div className={styles.left_top}>
            <div className={styles.left_1}>
              <div>
                <FlagOutlined /> 设备类型
              </div>
              <Select
                className={styles.left_1_select}
                value={selectDevice?.deviceTypeId}
                onChange={option => {
                  setSelectDevice(d => ({
                    ...d,
                    deviceTypeId: option
                  }));
                }}
                options={deviceSelectOptions}
              />
            </div>
            <div className={styles.left_2}>
              <Tabs
                className={styles.tabs}
                tabPosition='left'
                activeKey={selectDevice?.pageTypeId}
                onChange={tab => {
                  setSelectDevice(d => ({
                    ...d,
                    pageTypeId: tab
                  }));
                }}>
                {pageTypeList?.map(pageType => (
                  <Tabs.TabPane
                    tab={
                      <div className={styles.leftTabItem}>
                        <div className={styles.leftTabItem_1}>
                          {pageType.indexPageName}
                          <Tag color='blue' className={styles.tag}>
                            {pageType.indexPageName}
                          </Tag>
                        </div>
                        <div>{pageType.nameCode}</div>
                      </div>
                    }
                    key={pageType.id}
                  />
                ))}
              </Tabs>
            </div>
          </div>
          <div className={styles.left_3}>
            <Button
              type='default'
              icon={<SettingOutlined />}
              onClick={() => {
                history.push('/selfService/homepageType');
              }}>
              首页类型管理
            </Button>
          </div>
        </div>
        <div className={styles.right} key={selectDevice?.pageTypeId}>
          <Tabs destroyInactiveTabPane defaultActiveKey='functionConfig'>
            {rightTabs.map(tab => {
              const TabContent = tab.content as any;
              return (
                <Tabs.TabPane tab={tab.tab} key={tab.key}>
                  <TabContent selectDevice={selectDevice} />
                </Tabs.TabPane>
              );
            })}
          </Tabs>
        </div>
      </div>
    </CardLayout>
  );
};

export default Index;
