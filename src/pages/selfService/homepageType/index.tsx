import { useMemo } from 'react';
import {
  LinkButton,
  RouteComponentProps,
  ActionsWrap,
  useModal,
  handleSubmit,
  ArrSelect,
  actionConfirm
} from 'parsec-admin';
import dxfwSrc from '@pages/selfService/images/dxfw.png';
import zhfwSrc from '@pages/selfService/images/zhfw.png';
import zxfwSrc from '@pages/selfService/images/zxfw.png';
import { Button, Form, Image, Radio } from 'antd';

import styles from './index.module.less';
import {
  apiGetKaiqiaoHisConfigCommonGetIndexPageTypeList,
  apiPutKaiqiaoHisConfigCommonAddIndexPage,
  apiPutKaiqiaoHisConfigCommonChargeIndexPage,
  useApiGetKaiqiaoHisConfigCommonGetDeviceType
} from '@src/api/公共分类';
import { PlusOutlined } from '@ant-design/icons';
import { indexPageLayoutMap, pageGroupMap, pageGroupOptions } from '../enumMap';
import UploadBase64Img from '@src/components/UploadBase64Img';
import MyTableList from '@src/components/myTableList';
import { showPlaceholderWhenEmpty } from '@src/utils/common';

const modalFormColProps = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 }
};
export default ({ history }: RouteComponentProps) => {
  const { data: deviceTypeList } = useApiGetKaiqiaoHisConfigCommonGetDeviceType(
    {
      initValue: []
    }
  );
  const switchEditModalVisible = useModal(
    ({ id }, { getFieldValue }) => {
      const isEdit = !!id;
      return {
        width: 800,
        title: isEdit ? '编辑首页' : '新增首页',
        myFormProps: {
          formProps: {
            ...modalFormColProps,
            initialValues: {
              indexPageLayout: 1
            }
          }
        },
        onSubmit: values =>
          handleSubmit(() => {
            if (isEdit) {
              return apiPutKaiqiaoHisConfigCommonChargeIndexPage({
                ...values,
                id
              });
            } else {
              return apiPutKaiqiaoHisConfigCommonAddIndexPage({
                ...values,
                supportDeviceName: deviceTypeList?.find(
                  item => item.id === values.supportDevice
                )?.name
              });
            }
          }),
        items: [
          {
            label: '首页名称',
            name: 'indexPageName',
            formItemProps: {
              rules: [
                {
                  required: true
                }
              ]
            }
          },
          {
            label: '缩写代码',
            name: 'nameCode',
            formItemProps: {
              rules: [
                {
                  required: true
                }
              ]
            }
          },
          {
            label: '支持的设备',
            name: 'supportDevice',
            required: false,
            render: () => (
              <ArrSelect
                className={styles.select}
                options={deviceTypeList?.map(item => ({
                  value: item.id,
                  label: item.name
                }))}
              />
            )
          },
          {
            label: '首页布局',
            name: 'indexPageLayout',
            render: () => (
              <Radio.Group>
                <Radio value={1}>
                  <Image
                    preview={false}
                    src={zhfwSrc}
                    alt='综合服务'
                    width={120}
                  />
                  <div className={styles.layoutTip1}>综合服务</div>
                  <div className={styles.layoutTip2}>支付所有功能配置</div>
                </Radio>
                <Radio value={2}>
                  <Image
                    src={dxfwSrc}
                    alt='多项服务'
                    width={120}
                    preview={false}
                  />
                  <div className={styles.layoutTip1}>多项服务</div>
                  <div className={styles.layoutTip2}>支持4项功能配置</div>
                </Radio>
                <Radio value={3}>
                  <Image
                    src={zxfwSrc}
                    alt='专项服务'
                    width={120}
                    preview={false}
                  />
                  <div className={styles.layoutTip1}>专项服务</div>
                  <div className={styles.layoutTip2}>仅单功能，直达功能页</div>
                </Radio>
              </Radio.Group>
            ),
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '账号状态是必选的'
                }
              ]
            }
          },
          {
            renderShouldUpdate: (prevValues, curValues) =>
              prevValues.indexPageLayout !== curValues.indexPageLayout,
            render: () => {
              const showGroup = getFieldValue('indexPageLayout') === 1;
              return (
                showGroup && (
                  <>
                    <Form.Item
                      {...modalFormColProps}
                      name='pageGroup'
                      label='默认分组'
                      rules={[{ required: true, message: '请选择默认分组' }]}>
                      <Radio.Group
                        options={pageGroupOptions}
                        optionType='button'
                        buttonStyle='solid'
                      />
                    </Form.Item>
                    <Form.Item
                      {...modalFormColProps}
                      name='isGroup'
                      label='是否支持分组切换'
                      rules={[
                        { required: true, message: '请选择是否支持分组切换' }
                      ]}>
                      <Radio.Group
                        options={[
                          {
                            label: '是',
                            value: 1
                          },
                          {
                            label: '否',
                            value: 0
                          }
                        ]}
                        optionType='button'
                        buttonStyle='solid'
                      />
                    </Form.Item>
                  </>
                )
              );
            },
            formItemProps: {
              noStyle: true
            }
          },
          {
            render: () => {
              const show = getFieldValue('indexPageLayout') !== 3;
              return (
                show && (
                  <>
                    <Form.Item
                      {...modalFormColProps}
                      name='topImage'
                      label='顶部展示图'>
                      <UploadBase64Img arrValue={false} />
                    </Form.Item>
                    <Form.Item
                      {...modalFormColProps}
                      name='regularImage'
                      label='固定展示图'>
                      <UploadBase64Img arrValue={false} />
                    </Form.Item>
                  </>
                )
              );
            },
            formItemProps: {
              noStyle: true
            }
          }
        ]
      };
    },
    [deviceTypeList]
  );
  return (
    <MyTableList
      tableTitle={'首页类型管理'}
      action={
        <div className={styles.actionBtnContainer}>
          <Button
            type='primary'
            icon={<PlusOutlined />}
            onClick={() => {
              switchEditModalVisible();
            }}>
            添加新首页类型
          </Button>
        </div>
      }
      getList={({ params }) => {
        return apiGetKaiqiaoHisConfigCommonGetIndexPageTypeList(
          params as any
        ) as any;
      }}
      columns={useMemo(
        () => [
          {
            title: '首页名称',
            dataIndex: 'indexPageName'
          },
          {
            title: '支持设备',
            dataIndex: 'supportDeviceName'
          },
          {
            title: '缩写代码',
            dataIndex: 'nameCode'
          },
          {
            title: '首页布局',
            dataIndex: 'indexPageLayout',
            render: v => indexPageLayoutMap[v]
          },
          {
            title: '默认分组',
            dataIndex: 'pageGroup',
            render: v => pageGroupMap[v]
          },
          {
            title: '支持分组切换',
            dataIndex: 'isGroup',
            render: v => (v ? '是' : '否')
          },
          {
            title: '顶部展示图',
            align: 'center',
            render: v =>
              showPlaceholderWhenEmpty(v, <Image width={100} src={v} />),
            dataIndex: 'topImage'
          },
          {
            title: '固定展示图',
            align: 'center',
            render: v =>
              showPlaceholderWhenEmpty(v, <Image width={100} src={v} />),
            dataIndex: 'regularImage'
          },
          {
            title: '操作',
            fixed: 'right',
            width: 150,
            render: record => (
              <ActionsWrap>
                <LinkButton
                  onClick={() => {
                    switchEditModalVisible(record);
                  }}>
                  编辑
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        apiPutKaiqiaoHisConfigCommonAddIndexPage({
                          ...record,
                          indexPageName: `副本-${record.indexPageName}`
                        }),
                      `复制创建一条名为副本-${record.indexPageName}的新数据`
                    );
                  }}>
                  复制
                </LinkButton>
                <LinkButton
                  onClick={() => {
                    actionConfirm(
                      () =>
                        apiPutKaiqiaoHisConfigCommonChargeIndexPage({
                          ...record,
                          isDelete: 1
                        }),
                      '删除'
                    );
                  }}>
                  删除
                </LinkButton>
              </ActionsWrap>
            )
          }
        ],
        [switchEditModalVisible]
      )}
    />
  );
};
