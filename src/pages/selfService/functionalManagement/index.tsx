import { useMemo } from 'react';
import {
  LinkButton,
  DayRangePicker,
  ArrSelect,
  RouteComponentProps,
  ActionsWrap,
  useModal,
  handleSubmit
} from 'parsec-admin';
import MyTableList from '@components/myTableList';
import TextArea from 'antd/lib/input/TextArea';
import { Image, Input } from 'antd';
import {
  apiGetKaiqiaoHisConfigCommonGetModuleList,
  apiPutKaiqiaoHisConfigCommonChargeModuleList
} from '@src/api/公共分类';
import UploadBase64Img from '@src/components/UploadBase64Img';
import { functionStatusMap, moduleLocationMap } from '../enumMap';
import { showPlaceholderWhenEmpty } from '@src/utils/common';

export default ({ history }: RouteComponentProps) => {
  const switchModalVisible = useModal(() => {
    return {
      title: '编辑功能',
      onSubmit: values =>
        handleSubmit(() => {
          return apiPutKaiqiaoHisConfigCommonChargeModuleList(values);
        }),
      items: [
        {
          label: 'ID',
          name: 'id',
          render: v => v
        },
        {
          label: '功能场景',
          name: 'moduleType',
          render: v => moduleLocationMap[v]
        },
        {
          label: '全局状态',
          name: 'status',
          required: true,
          render: () => <ArrSelect options={functionStatusMap} />
        },
        {
          label: '功能名称',
          name: 'moduleName',
          required: true
        },
        {
          label: '功能简要介绍',
          name: 'moduleRemark',
          required: true,
          render: () => (
            <Input
              placeholder='请输入功能简要介绍 （16字以内）'
              maxLength={16}
            />
          )
        },
        {
          label: '维护说明',
          name: 'maintenanceRemark',
          required: true,
          render: () => <TextArea placeholder='输入内容' maxLength={100} />
        },
        {
          label: '中尺寸背景色',
          name: 'mediumBackground',
          required: true,
          formItemProps: {
            extra: '请上传1MB以内背景透明的PNG位图'
          },
          render: () => <Input placeholder='请输入十六进制颜色值' />
        },
        {
          label: '中尺寸图标',
          name: 'mediumImage',
          formItemProps: {
            extra: '请上传1MB以内背景透明的PNG位图'
          },
          render: () => <UploadBase64Img arrValue={false} />
        },
        {
          label: '小尺寸图标',
          name: 'smallImage',
          render: () => <UploadBase64Img arrValue={false} />
        }
      ]
    };
  }, []);

  return (
    <MyTableList
      tableTitle='功能管理'
      getList={({ params }) => {
        return apiGetKaiqiaoHisConfigCommonGetModuleList(params as any) as any;
      }}
      columns={useMemo(
        () => [
          {
            title: '功能ID',
            dataIndex: 'id'
          },
          {
            title: '功能名称',
            dataIndex: 'moduleName',
            search: true
          },
          {
            title: '功能简要描述',
            dataIndex: 'moduleRemark'
          },
          {
            title: '中尺寸图标',
            dataIndex: 'mediumImage',
            align: 'center',

            render: v =>
              showPlaceholderWhenEmpty(v, <Image width={100} src={v} />)
          },
          {
            title: '小尺寸图标',
            dataIndex: 'smallImage',
            align: 'center',
            render: v =>
              showPlaceholderWhenEmpty(v, <Image width={100} src={v} />)
          },
          {
            title: '中尺寸主题色',
            dataIndex: 'mediumBackground'
          },
          {
            title: '全局状态',
            dataIndex: 'status',
            render: v => functionStatusMap[v],
            search: <ArrSelect options={functionStatusMap} />,
            searchIndex: 'moduleStatus'
          },
          {
            title: '修改时间',
            dataIndex: 'updateTime',
            render: false,
            search: <DayRangePicker valueFormat={'YYYY-MM-DD'} />,
            searchIndex: ['updateStartTime', 'updateEndTime']
          },
          {
            title: '最后修改时间',
            dataIndex: 'updateTime'
          },
          {
            title: '维护说明',
            dataIndex: 'maintenanceRemark'
          },
          {
            title: '操作',
            render: record => (
              <ActionsWrap>
                <LinkButton
                  onClick={() => {
                    switchModalVisible({
                      ...record,
                      status: Number(record.status)
                    });
                  }}>
                  编辑
                </LinkButton>
              </ActionsWrap>
            )
          }
        ],
        [switchModalVisible]
      )}
    />
  );
};
