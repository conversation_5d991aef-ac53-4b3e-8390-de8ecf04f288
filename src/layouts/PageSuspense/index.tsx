import { PropsWithChildren, Suspense } from 'react';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

import cn from 'classnames';

export type PageSuspenseProps = PropsWithChildren<{
  className?: string;
}>;

export default function PageSuspense({
  children,
  className
}: PageSuspenseProps) {
  return (
    <Suspense
      fallback={
        <div
          className={cn(
            'size-full flex justify-center items-center',
            className
          )}>
          <Spin indicator={<LoadingOutlined />} size='large' />
        </div>
      }>
      {children}
    </Suspense>
  );
}
