import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { KqBasicLayout } from 'parsec-admin';
import { Avatar } from 'antd';
import ConfigStore from '@src/store/ConfigStore';
import { useSessionStorage } from 'react-use';
import { hidePhone } from '@utils/tools';
import useApi from '@src/pages/login/apis';
import useHosApi from '@src/pages/hospital/info/apis';
import { useConfig } from '@src/store/hisConfig';
import Cookies from 'js-cookie';
import env from '@configs/env';

export default () => {
  const [account] = useSessionStorage('account', '默认用户');
  const { logo } = useConfig();
  const [, setIsFlowUp] = useSessionStorage('isFlowUp', '0');
  const { config } = ConfigStore.useContainer();
  const hisId = env.hisId;
  const {
    data: { data }
  } = useHosApi.getHospitalInfoById({
    needInit: !env.isDoctor,
    params: { hisId: hisId },
    initValue: { data: {} }
  });

  // const [logo, setLogo] = useState('');
  // useEffect(() => {
  //   setLogo((data?.hisTopImg || '').split('|')[1] || '');
  // }, [data, setLogo]);
  useEffect(() => {
    setIsFlowUp(config?.enableFollow === 1 ? '1' : '0');
  }, [config, setIsFlowUp]);
  return (
    <KqBasicLayout
      autoTheme={false}
      logo={() => (
        <img
          src={logo || require('../../images/logo.png')}
          style={{ height: 41, objectFit: 'contain' }}
          height={41}
          alt=''
        />
      )}
      header={
        <UserInfo>
          <div className='userbar'>
            <Avatar
              size={40}
              style={{ backgroundColor: '#fff', marginBottom: 5 }}
              src='https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png'
            />
            <div className='username'>{hidePhone(account)}</div>
          </div>
          <span
            onClick={async () => {
              await useApi.logout.request({});
              sessionStorage.clear();
              localStorage.clear();
              Cookies.remove('SESSION');
              Cookies.remove('JSESSIONID');
              window.location.replace('#/login');
            }}>
            退出
          </span>
        </UserInfo>
      }
    />
  );
};

const UserInfo = styled.div`
  color: #fff;
  display: flex;
  align-items: center;
  margin-right: 20px;
  .userbar {
    text-align: center;
    > .username {
      display: inline-block;
      padding-left: 10px;
    }
  }
  > span {
    color: #fff;
    padding: 0 10px;
    text-decoration: underline;
    cursor: pointer;
  }
`;
