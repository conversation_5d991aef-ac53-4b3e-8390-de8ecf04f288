.TableListWrap {
  .ant-table-pagination {
    padding: 0;
    display: flex;
    width: 100%;
    text-align: right;
    .ant-pagination-total-text {
      flex: 1;
    }
  }
  > div {
    > div:first-child:not(:last-child) {
      .ant-form-item-label {
        min-width: 40px;
      }
    }
    > div:last-child {
      > .ant-card-body {
        padding: 0 20px 20px;
        > header {
          padding: 16px 0 0;
          position: relative;

          h3 {
            position: relative;
            color: #2780d9;
            &:not(:empty) {
              &:before {
                content: '';
                width: 6px;
                height: 15px;
                position: absolute;
                left: -20px;
                top: 50%;
                transform: translateY(-50%);
                background: #2780d9;
              }
            }
          }
        }
        .ant-table-wrapper {
          .ant-table-content {
            border: 1px solid #f0f0f0;
          }
        }
      }
    }
  }
}
.showHeaderExtra {
  > div {
    > div:last-child {
      > .ant-card-body {
        > header {
          padding-bottom: 50px;
        }
      }
    }
  }
  padding-bottom: 50px;
}
