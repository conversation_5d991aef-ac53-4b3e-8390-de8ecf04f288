import React from 'react';
import { TableList } from 'parsec-admin';
import { TableListProps } from 'parsec-admin/lib/templates/tableList/Types';
import styled from 'styled-components';
import { GetListParams } from 'parsec-admin/lib/components/baseTable';
import { ListApiResponseData } from '@apiHooks';
import env from '@configs/env';
import './index.less';

export default function<D, P>({
  paginationExtra = <div />,
  killHisId,
  getList,
  showHeaderExtra = false,
  ...props
}: Omit<TableListProps<D, P>, 'getList'> & {
  killHisId?: true;
  paginationExtra?: React.ReactNode;
  getList: (
    params: GetListParams<
      D,
      {
        [N in keyof P]?: P[N];
      } & {
        sort?: 'DESC';
        hisId?: string;
        numPerPage: number;
        pageNum: number;
        scheduleDate?: string;
      }
    >
  ) => Promise<ListApiResponseData<D>>;
  showHeaderExtra?: boolean;
}) {
  const hisId = env.hisId;
  return (
    <div
      className={`TableListWrap  ${showHeaderExtra ? 'showHeaderExtra' : ''}`}>
      <TableList
        pageHeaderProps={false}
        {...props}
        showTool={false}
        scroll={props?.scroll || { x: '100%' }}
        getList={({
          pagination,
          pagination: { current, pageSize },
          params,
          sorter
        }) => {
          const p: any = {
            ...params,
            sort: 'DESC',
            pageNum: current,
            numPerPage: pageSize
          };
          if (!killHisId) {
            p.hisId = !!hisId && hisId !== '""' ? hisId : undefined;
          }
          return getList({
            pagination,
            sorter,
            params: p
          } as any).then(res => ({
            list: res?.data?.recordList ?? [],
            total: res?.data?.totalCount
          }));
        }}
        pagination={{
          showQuickJumper: true,
          showSizeChanger: true,
          size: 'small',
          showTotal: total => (
            <PaginationExtra>
              {paginationExtra}共 {total} 条
            </PaginationExtra>
          )
        }}
      />
    </div>
  );
}

const PaginationExtra = styled.div`
  display: flex;
  justify-content: space-between;
`;
