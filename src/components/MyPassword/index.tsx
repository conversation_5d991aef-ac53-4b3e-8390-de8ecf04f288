import React, { useEffect, useMemo, useState } from 'react';
import { Button, Input } from 'antd';
import styled from 'styled-components';
import { InlineFormItemWrap, Form } from 'parsec-admin';
import classNames from 'classnames';
const strength = [
  {
    label: '弱',
    value: 0,
    color: 'red'
  },
  {
    label: '中',
    value: 1,
    color: '#dc52b1'
  },
  {
    label: '强',
    value: 2,
    color: 'green'
  }
];
function getPassword() {
  const amm = ['!', '@', '#', '$', '%', '&', '*', '=', '_', '.']; //定义特殊字符数组
  let tmp = Math.floor(Math.random() * 10); //生成0-9随机数
  let s: any = tmp;
  s += amm[tmp]; //取特殊字符
  //生成大写字母
  for (let i = 0; i < 4; i++) {
    tmp = Math.floor(Math.random() * 26);
    s = s + String.fromCharCode(65 + tmp);
  }
  //生成小写字母
  for (let i = 0; i < 4; i++) {
    tmp = Math.floor(Math.random() * 26);
    s = s + String.fromCharCode(97 + tmp);
  }
  return s;
}

export const validatePasswordStrength = (password: string) => {
  if (!password) return -1;
  const amount = {
    number: 0, // 数字
    lowcase: 0, // 小写
    upcase: 0, // 大写
    spec: 0 //特殊字符
  };
  for (let i = 0; i < password.length; i++) {
    if (/^\d$/.test(password[i])) {
      amount.number++;
    }
    if (/^[a-z]$/.test(password[i])) {
      amount.lowcase++;
    }
    if (/^[A-Z]$/.test(password[i])) {
      amount.upcase++;
    }
  }
  amount.spec =
    password.length - (amount.number + amount.lowcase + amount.upcase);
  if (amount.number > 0) amount.number = 1;
  if (amount.lowcase > 0) amount.lowcase = 1;
  if (amount.upcase > 0) amount.upcase = 1;
  if (amount.spec > 0) amount.spec = 1;
  const sum = amount.number + amount.lowcase + amount.upcase + amount.spec;
  if (sum >= 3) {
    if (password.length < 8) {
      return 1;
    }
    return 2;
  }
  if (sum >= 2) {
    if (password.length < 8) {
      return 0;
    }
    return 1;
  }
  return sum - 1;
};
export default (props: { value?: string; isEdit?: boolean; onChange }) => {
  const [activeIndex, setActiveIndex] = useState(-1);
  const [inputValue, setInputValue] = useState<string>();
  const active = useMemo(() => {
    return strength.find(x => x.value === activeIndex);
  }, [activeIndex]);
  useEffect(() => {
    setActiveIndex(validatePasswordStrength(props.value || ''));
    setInputValue(props?.value);
  }, [props]);
  return (
    <InlineFormItemWrap {...props}>
      <div style={{ display: 'flex' }}>
        <Form.Item
          style={{
            width: '90%'
          }}>
          <Input
            value={inputValue}
            onChange={e => setInputValue(e.target?.value)}
          />
        </Form.Item>
        <Button
          type={'link'}
          onClick={() => {
            props.onChange(getPassword());
          }}>
          {props?.isEdit ? '重置密码' : '生成随机码'}
        </Button>
      </div>
      <StyleComponent>
        <div className='strength'>
          {strength.map(item => {
            return (
              <div
                className={classNames('block', {
                  active: item.value <= activeIndex
                })}>
                <div
                  className='color'
                  style={{
                    backgroundColor:
                      active && item.value <= activeIndex
                        ? active.color
                        : undefined
                  }}
                />
                <div className='text'>{item.label}</div>
              </div>
            );
          })}
        </div>
      </StyleComponent>
    </InlineFormItemWrap>
  );
};
const StyleComponent = styled.div`
  > .strength {
    display: flex;
    padding-top: 10px;
    padding-left: 10px;
    > .block {
      float: left;
      margin-right: 10px;
      > .color {
        width: 60px;
        height: 5px;
        background-color: #ccc;
      }
      > .text {
        text-align: center;
        font-size: 12px;
      }
    }
  }
`;
