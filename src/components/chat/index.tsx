import React, { useState, useMemo } from 'react';
import { Form, Button, Input } from 'antd';
import { Items } from '@src/pages/operate/evaluate/api';
import * as StyleComp from './index.style';
interface Props {
  list: Items[];
  loading: boolean;
  submit?: (v?: any) => void;
  isNeedReplay?: boolean;
  isConsult?: boolean;
}
const { TextArea } = Input;
export default ({
  isNeedReplay = true,
  list,
  loading,
  submit,
  isConsult
}: Props) => {
  const [isVisible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const { validateFields } = form;
  const myList = useMemo(() => {
    const r = list.map(item => {
      let contentObj;
      try {
        contentObj = JSON.parse(item.content);
      } catch (error) {}
      return {
        ...item,
        contentObj
      };
    });
    r.reverse();
    console.log(r);
    return r;
  }, [list]);
  return (
    <StyleComp.Warp>
      <StyleComp.Toast>
        {myList.length > 0
          ? myList.map(item => {
              if (item.type === 'SYSTEM') {
                return (
                  <StyleComp.SystemTips>{item.content}</StyleComp.SystemTips>
                );
              }
              const Comp =
                item.direction === 'TO_DOCTOR'
                  ? StyleComp.Complaint
                  : StyleComp.Replay;
              const date =
                item.direction === 'TO_DOCTOR' ? (
                  <p>
                    {isConsult ? '【患者】' : `【${item.userName}】`}
                    <span>{item.createDate}</span>
                  </p>
                ) : (
                  <p>
                    <span>{item.createDate}</span>
                    {/* <span>{item.adminName}</span> */}
                    {isConsult ? '【医生】' : `【${item.userName}】`}
                  </p>
                );
              let content;
              if (item.action === 'img') {
                content = item.url.split(',').map(x => {
                  return (
                    <StyleComp.Thumb
                      key={x}
                      onClick={() => {
                        window.open(x);
                      }}>
                      <img src={x} alt={x} />
                    </StyleComp.Thumb>
                  );
                });
              }
              if (item.action) {
                if (item.action === 'replyContent') {
                  if (item.url) {
                    const imgsDom = item.url.split(',').map(x => {
                      return (
                        <StyleComp.Thumb
                          key={x}
                          onClick={() => {
                            window.open(x);
                          }}>
                          <img src={x} alt={x} />
                        </StyleComp.Thumb>
                      );
                    });
                    content = [<div>{item.content}</div>, imgsDom];
                  } else {
                    content = item.content;
                  }
                }
                if (item.action === 'payedChronic') {
                  const contentObj = item.contentObj as {
                    recipelList: {
                      createTime: 1591014644000;
                      deptName: '皮ac';
                      diagnosiDesOther: '5';
                      diagnosis: '高|糖尿病|无|E14.900x001';
                      diseaseDescribe: 'uhjkhkjhkjhkhkjgjhfhgfhgvhgv';
                      doctorName: '测试方法';
                      doctorTitle: '测试';
                      id: 77;
                      patCardNo: '465799';
                      patientAge: '26';
                      patientId: 668;
                      patientName: '符川';
                      patientSex: '男';
                      patientWeight: '100';
                      prescTime: 1591014644000;
                      updateTime: 1591014643000;
                    };
                    title: '处方申请';
                  };
                  console.log(item);
                  content = (
                    <StyleComp.ActionItem>
                      <div className='title'>{contentObj.title}</div>
                      <div className='kv'>
                        <div className='k'>就诊时间：</div>
                        <div className='v'>
                          {contentObj.recipelList.prescTime}
                        </div>
                      </div>
                      <div className='kv'>
                        <div className='k'>就诊科室：</div>
                        <div className='v'>
                          {contentObj.recipelList.deptName}
                        </div>
                      </div>
                      <div className='kv'>
                        <div className='k'>就这医生：</div>
                        <div className='v'>
                          {contentObj.recipelList.doctorName}
                        </div>
                      </div>
                      {(contentObj.recipelList.diagnosis || '')
                        .split('|')
                        .map((t, index) => {
                          return (
                            <div className='kv' key={index}>
                              <div className='k'>诊断：</div>
                              <div className='v'>{t}</div>
                            </div>
                          );
                        })}

                      {/* <div className="btn">查看详情</div> */}
                      {/* <div>{JSON.stringify(item.contentObj, null, 2)}</div> */}
                    </StyleComp.ActionItem>
                  );
                }
                if (item.action === 'receiveChronic') {
                  const contentObj = item.contentObj as {
                    recipelList: {
                      createTime: 1591014644000;
                      deptName: '皮ac';
                      diagnosiDesOther: '5';
                      diagnosis: '高|糖尿病|无|E14.900x001';
                      diseaseDescribe: 'uhjkhkjhkjhkhkjgjhfhgfhgvhgv';
                      doctorName: '测试方法';
                      doctorTitle: '测试';
                      id: 77;
                      patCardNo: '465799';
                      patientAge: '26';
                      patientId: 668;
                      patientName: '符川';
                      patientSex: '男';
                      patientWeight: '100';
                      prescTime: 1591014644000;
                      updateTime: 1591014643000;
                    };
                    title: '处方申请';
                  };
                  console.log(item);
                  content = (
                    <StyleComp.ActionItem
                      style={{
                        float: 'right'
                      }}>
                      <div className='title'>{contentObj.title}</div>
                      <div className='kv'>
                        <div className='k'>就诊时间：</div>
                        <div className='v'>
                          {contentObj.recipelList.prescTime}
                        </div>
                      </div>
                      <div className='kv'>
                        <div className='k'>就诊科室：</div>
                        <div className='v'>
                          {contentObj.recipelList.deptName}
                        </div>
                      </div>
                      <div className='kv'>
                        <div className='k'>就这医生：</div>
                        <div className='v'>
                          {contentObj.recipelList.doctorName}
                        </div>
                      </div>
                      {(contentObj.recipelList.diagnosis || '')
                        .split('|')
                        .map((t, index) => {
                          return (
                            <div className='kv' key={index}>
                              <div className='k'>诊断：</div>
                              <div className='v'>{t}</div>
                            </div>
                          );
                        })}

                      {/* <div className="btn">查看详情</div> */}
                      {/* <div>{JSON.stringify(item.contentObj, null, 2)}</div> */}
                    </StyleComp.ActionItem>
                  );
                }
                if (item.action === 'applyChronic') {
                  const contentObj = item.contentObj as {
                    content: 'mollit tempor et pariatu';
                    deptName: 'laboris sint';
                    diagnosInfo: '高压|肺动脉|中度|717;高压|脑血管|中度|720';
                    doctorName: 'magna incididunt';
                    doctorTitle: 'nostrud';
                    patientAge: 'mollit et dolor exercitation';
                    patientCardNo: '465757';
                    patientName: '成浩';
                    patientSex: 'culpa deserunt';
                    patientWeight: '80';
                    // recipelList: (2) [{…}, {…}]
                    title: '处方申请';
                    visitDate: '2020-06-05 10:39:20';
                  };
                  console.log(item);
                  content = (
                    <StyleComp.ActionItem>
                      <div className='title'>{contentObj.title}</div>
                      <div className='kv'>
                        <div className='k'>就诊时间：</div>
                        <div className='v'>{contentObj.visitDate}</div>
                      </div>
                      <div className='kv'>
                        <div className='k'>就诊科室：</div>
                        <div className='v'>{contentObj.deptName}</div>
                      </div>
                      <div className='kv'>
                        <div className='k'>就这医生：</div>
                        <div className='v'>{contentObj.doctorName}</div>
                      </div>
                      {(contentObj.diagnosInfo || '')
                        .split('|')
                        .map((t, index) => {
                          return (
                            <div className='kv' key={index}>
                              <div className='k'>诊断：</div>
                              <div className='v'>{t}</div>
                            </div>
                          );
                        })}

                      {/* <div className="btn">查看详情</div> */}
                      {/* <div>{JSON.stringify(item.contentObj, null, 2)}</div> */}
                    </StyleComp.ActionItem>
                  );
                }
              } else {
                if (item.content) {
                } else {
                  if (item.url && !item.voiceTime) {
                    content = item.url.split(',').map(x => {
                      return (
                        <StyleComp.Thumb
                          key={x}
                          onClick={() => {
                            window.open(x);
                          }}>
                          <img src={x} alt={x} />
                        </StyleComp.Thumb>
                      );
                    });
                  }
                  if (item.url && item.voiceTime > 0) {
                    content = <audio src={item.url} controls></audio>;
                  }
                }
              }
              return (
                <Comp>
                  {date}
                  <div className='content'>
                    {((!item.action && item.content) ||
                      item.action === 'text') && (
                      <div className='leftbox'>
                        <div
                          className='word'
                          dangerouslySetInnerHTML={{
                            __html: item.content
                          }}></div>
                      </div>
                    )}
                    <div>{content}</div>
                  </div>
                </Comp>
              );
            })
          : '暂无会话内容'}
      </StyleComp.Toast>
      {isVisible && isNeedReplay && (
        <div className='replaybox'>
          <Form form={form}>
            <Form.Item
              label=''
              name='content'
              rules={[{ required: true, message: '请输入要的回复内容' }]}>
              <TextArea rows={4} placeholder='请输入要的回复内容' />
            </Form.Item>
          </Form>
        </div>
      )}
      {!isVisible && isNeedReplay && (
        <div className='replaybar'>
          <Button type='primary' onClick={() => setVisible(true)}>
            回复
          </Button>
        </div>
      )}
      {isVisible && isNeedReplay && (
        <div className='replaybar'>
          <Button type='ghost' onClick={() => setVisible(false)}>
            取消
          </Button>
          <Button
            type='primary'
            loading={loading}
            onClick={() => {
              validateFields().then(v => {
                submit && submit(v);
              });
            }}>
            发送
          </Button>
        </div>
      )}
    </StyleComp.Warp>
  );
};
