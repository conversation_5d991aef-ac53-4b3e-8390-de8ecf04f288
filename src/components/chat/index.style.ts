import styled from 'styled-components';
export const Warp = styled.div`
  .replaybox {
    padding: 0;
  }
  .ant-form-item {
    margin-bottom: 0;
  }
  .replaybar {
    padding: 10px 0;
    display: flex;
    justify-content: flex-end;
    .ant-btn {
      margin-left: 5px;
    }
  }
`;
export const Toast = styled.div`
  border: 1px solid #eee;
  border-radius: 4px;
  background: #f3f3f3;
  padding: 20px;
  max-height: 450px;
  overflow-y: auto;
`;
export const SystemTips = styled.div`
  width: 100%;
  border-radius: 10px;
  background: #d8d8d8;
  color: #fff;
  padding: 10px;
  margin: 10px auto;
`;
export const Complaint = styled.div`
  margin-bottom: 20px;
  > p {
    width: 100%;
    margin-bottom: 5px;
    font-size: 14px;
    color: #a1a1a1;
  }
  > .content {
    display: flex;
    > .leftbox,
    .rightbox {
      width: 50%;
      overflow: hidden;
      word-break: break-all;
      .word {
        max-width: 100%;
        border-radius: 4px;
        background: #fff;
        padding: 10px;
        display: inline-block;
      }
      margin-right: 10px;
    }
    > .rightbox {
      text-align: right;
    }
  }
`;
export const Thumb = styled.div`
  width: 100px;
  /* height: 60px; */
  margin: 5px;
  display: inline-block;
  cursor: pointer;
  img {
    width: 100%;
    /* height: 100%; */
    object-fit: cover;
  }
`;
export const Replay = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-bottom: 20px;
  > p {
    width: 100%;
    margin-bottom: 5px;
    font-size: 14px;
    color: #a1a1a1;
    text-align: right;
  }
  > .content {
    width: 50%;
    overflow: hidden;
    word-break: break-all;
    text-align: right;
    .word {
      max-width: 100%;
      border-radius: 4px;
      background: #cdf2fe;
      padding: 10px;
      display: inline-block;
      text-align: left;
    }
  }
`;

export const ActionItem = styled.div`
  width: 280px;
  background: #ffffff;
  border-radius: 10px;
  padding-top: 5px;
  padding-bottom: 10px;
  > .title {
    font-size: 18px;
    padding: 5px 10px;
  }
  > .kv {
    font-size: 14px;
    display: flex;
    flex-wrap: nowrap;
    padding: 0 10px;
    > .k {
      white-space: nowrap;
    }
    > .v {
      word-break: break-all;
    }
  }
  > .btn {
    color: #0082e0;
    text-align: center;
    padding: 10px;
    border-top: 1px solid #ccc;
  }
`;
