import { Tabs } from 'antd';
import { ArrSelect } from 'parsec-admin';
import React, { useEffect, useState } from 'react';
import TabPane = Tabs.TabPane;
import './index.less';
import OrganizationListStore from '@src/store/organizationListStore';

export default ({
  headerExtraText = '',
  value,
  onChange
}: {
  headerExtraText?: string;
  value?: string | number;
  onChange: React.Dispatch<React.SetStateAction<any>>;
}) => {
  const {
    organizationList,
    getOrganizationList,
    localHIsId,
    otherList
  } = OrganizationListStore.useContainer();

  const [status, setStatus] = useState<any>();
  const [selectOther, setSelectOther] = useState<any>();
  useEffect(() => {
    if (!organizationList?.length) {
      getOrganizationList();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setStatus(value === localHIsId ? value : 'OTHER');
    if (value !== localHIsId && value) {
      setSelectOther(value);
    }
  }, [localHIsId, value]);
  useEffect(() => {
    if (status === localHIsId) {
      onChange?.(status);
    } else if (selectOther) {
      onChange?.(selectOther);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status, selectOther, localHIsId]);
  return (
    <div className={'organizationWrap'}>
      <Tabs
        activeKey={status}
        type='card'
        onChange={activeKey => {
          setStatus(activeKey);
        }}>
        <TabPane tab={`本院${headerExtraText}`} key={localHIsId}></TabPane>
        <TabPane tab={`其他机构${headerExtraText}`} key='OTHER'></TabPane>
      </Tabs>
      {status === 'OTHER' && (
        <ArrSelect
          allowClear={false}
          showSearch={false}
          style={{ marginLeft: '10px', minWidth: '120px' }}
          placeholder={'请选择机构'}
          value={selectOther}
          onChange={value => setSelectOther(value)}
          options={otherList?.map(item => {
            return { label: item.institutionName, value: item.targetHisId };
          })}
        />
      )}
    </div>
  );
};
