import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts/lib/echarts';
import ResizeObserver from 'resize-observer-polyfill';
// import { useResize } from './hooks';

import 'echarts/lib/chart/line';
import 'echarts/lib/chart/bar';
import 'echarts/lib/chart/pie';
import 'echarts/lib/chart/map';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/title';
import 'echarts/lib/component/toolbox';

interface ChartsProps {
  option: echarts.EChartOption;
  loading: boolean;
  style?: React.CSSProperties;
}

export default (props: ChartsProps) => {
  const { option, loading, style = { width: '100%', height: '300px' } } = props;
  const domRef = useRef<HTMLDivElement>(null);
  const echartInstance = useRef<echarts.ECharts>();

  useEffect(() => {
    if (!domRef.current) {
      return;
    }
    if (!echartInstance.current) {
      echartInstance.current = echarts.init(domRef.current);
    }
    echartInstance.current.setOption(option);
  }, [option]);

  // 加入了debounce
  // const size = useResize();

  // useEffect(() => {
  //   if (!echartInstance.current) {
  //     return;
  //   }
  //   echartInstance.current.resize();
  // }, [size]);

  useEffect(() => {
    if (!echartInstance.current) {
      return;
    }
    if (loading) {
      echartInstance.current.showLoading('', {
        text: '正在加载数据',
        color: '#50A3E0',
        textColor: '#333',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        zlevel: 0
      });
    } else {
      echartInstance.current.hideLoading();
    }
  }, [loading]);

  // 视图大小变化时
  useEffect(() => {
    if (!domRef.current) {
      return;
    }
    const observer = new ResizeObserver(mutations => {
      if (!echartInstance.current) {
        return;
      }
      echartInstance.current.resize();
    });
    observer.observe(domRef.current);
    return () => {
      observer.disconnect();
    };
  }, []);

  return <div ref={domRef} style={style} />;
};
