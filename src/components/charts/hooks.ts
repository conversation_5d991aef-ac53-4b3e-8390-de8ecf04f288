import { useState, useEffect, useRef } from 'react';

export function useResize(timeout = 500) {
  const timerRef = useRef<any>();
  const [size, setSize] = useState(() => ({
    width: document.documentElement.clientWidth,
    height: document.documentElement.clientHeight
  }));

  const onResize = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    timerRef.current = setTimeout(() => {
      setSize({
        width: document.documentElement.clientWidth,
        height: document.documentElement.clientHeight
      });
    }, timeout);
  };

  useEffect(() => {
    window.addEventListener('resize', onResize, false);
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      window.removeEventListener('resize', onResize, false);
    };
  });

  return size;
}
