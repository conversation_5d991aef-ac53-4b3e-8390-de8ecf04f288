import { View } from 'remax/one';
import { Image } from 'remax/one';
import React, { useEffect, useRef, useState } from 'react';
import r1 from './r1.png';
import r2 from './r2.png';
import r3 from './r3.png';
import style from './index.module.less';
import classNames from 'classnames';
const styleTrumpetImgs = [
  style.trumpetImg0,
  style.trumpetImg1,
  style.trumpetImg2
];

export default () => {
  const [activeIndex, setActiveIndex] = useState<number>(0);

  const isMounted = useRef<boolean>(false);

  useEffect(() => {
    return () => {
      isMounted.current = true;
    };
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      !isMounted.current && setActiveIndex((activeIndex + 1) % 3);
    }, 300);
    return () => clearTimeout(timer);
  }, [activeIndex, isMounted]);
  return (
    <View className={style.trumpet}>
      {[r1, r2, r3].map((item, index) => {
        return (
          <Image
            mode={'aspectFill'}
            key={item}
            className={classNames(styleTrumpetImgs[index])}
            style={{
              visibility: index <= activeIndex ? 'visible' : undefined
            }}
            src={item}
          />
        );
      })}
    </View>
  );
};
