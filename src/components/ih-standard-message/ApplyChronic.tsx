import { View } from 'remax/one';
import React from 'react';
import { ApplyChronicInfo } from './index.d';
import styleCard from './index.card.module.less';
export default ({
  goPrescriptionDetailPre,
  data
}: {
  data: ApplyChronicInfo;
  goPrescriptionDetailPre: () => void;
}) => {
  return (
    <View className={styleCard.main}>
      <View className={styleCard.title}>{data.title}</View>
      <View className={styleCard.info}>
        <View className={styleCard.infoDetail}>
          <View className={styleCard.infoDetailKv}>
            <View className={styleCard.infoDetailKvKey}>就诊时间：</View>
            <View className={styleCard.infoDetailKvValue}>
              {data.visitDate}
            </View>
          </View>
          <View className={styleCard.infoDetailKv}>
            <View className={styleCard.infoDetailKvKey}>就诊科室：</View>
            <View className={styleCard.infoDetailKvValue}>{data.deptName}</View>
          </View>
          <View className={styleCard.infoDetailKv}>
            <View className={styleCard.infoDetailKvKey}>就诊医生：</View>
            <View className={styleCard.infoDetailKvValue}>
              {data.doctorName}
            </View>
          </View>
          {data.diagnosInfoStr?.split(';').map(str => {
            return (
              <View className={styleCard.infoDetailKv} key={str}>
                <View className={styleCard.infoDetailKvKey}>诊断：</View>
                <View className={styleCard.infoDetailKvValue}>
                  {(str || '').split('|')[1]}
                </View>
              </View>
            );
          })}
        </View>
      </View>
      <View
        className={styleCard.button}
        onTap={() => {
          goPrescriptionDetailPre();
        }}>
        查看详情
      </View>
    </View>
  );
};
