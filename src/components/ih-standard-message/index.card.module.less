.main {
  background: #fff;
  border-radius: 20px;
  width: 450px;
}
.title {
  font-size: 30px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  line-height: 36px;
  padding: 20px 20px 0 20px;
  font-weight: 600;
}
.tongTitle {
  font-size: 30px;
  font-weight: 400;
  background-color: #277fd9;
  color: #fff;
  text-align: center;
  border-radius: 20px 20px 0 0;
  line-height: 36px;
  padding: 20px;
  font-weight: 600;
}
.center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.tongImg {
  width: 300px;
  height: 300px;
  margin: 0 auto;
}
.info {
  padding: 20px 20px 20px 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.infoAppoint {
  padding: 20px 20px 20px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.infoImage {
  width: 111px;
  height: 111px;
  margin-right: 20px;
  background: #bed9f4;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
}
.infoImageImage {
  width: 60px;
  height: 60px;
}

.infoAvatarImage {
  width: 120px;
  height: 120px;
  border-radius: 200px;
  overflow: hidden;
}

.infoAvatarImageImage {
  width: 120px;
  height: 120px;
}

.infoDetail {
  flex: 1;
  overflow: auto;
}

.infoAppointDetail {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 10px;

  >view{
    &:first-child{
      margin-bottom: 10px;
      font-size: 28px;
    }
    &:last-child{
      font-size: 26px;
    }
  }
}

.infoDetailPrice {
  font-size: 40px;
  font-weight: bold;
  color: rgba(217, 94, 56, 1);
}
.infoDetailKv {
  font-size: 26px;
  font-weight: 400;
  line-height: 36px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.infoDetailKvKey {
  color: rgba(102, 102, 102, 1);
  white-space: nowrap;
}
.infoDetailKvValue {
  color: #000;
}
.infoDetailKvEllipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.button {
  // prettier-ignore
  border-top: 1PX solid #e2e2e2;
  height: 70px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30px;
  font-weight: 400;
  color: rgba(39, 128, 217, 1);
  line-height: 36px;
  cursor: pointer;
}
