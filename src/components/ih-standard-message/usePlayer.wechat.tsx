import { useCallback, useState, useRef } from 'react';
import { createInnerAudioContext, showToast } from 'remax/wechat';

export default () => {
  const [innerSrc, setInnerSrc] = useState<string | undefined>();
  const [isPlaying, setIsPlaying] = useState(false);
  const { current: innerAudioContext } = useRef(createInnerAudioContext());
  innerAudioContext.obeyMuteSwitch = false;
  innerAudioContext.onPlay(() => {
    setIsPlaying(true);
    console.log('录音播放中');
  });

  innerAudioContext.onStop(() => {
    console.log('录音播放停止');
    setIsPlaying(false);
    setInnerSrc(undefined);
  });

  innerAudioContext.onEnded(() => {
    console.log('录音播放结束');
    setIsPlaying(false);
    setInnerSrc(undefined);
  });
  innerAudioContext.onError((res: any) => {
    setIsPlaying(false);
    setInnerSrc(undefined);
    showToast({
      duration: 2000,
      title: '语音播放失败, 错误代码：' + res.errCode,
      icon: 'none'
    });
  });

  return {
    src: innerSrc,
    isPlaying,
    play: useCallback(
      async (src: string) => {
        if (innerSrc === src && isPlaying) {
          innerAudioContext.stop();
        } else {
          console.log(src);
          setInnerSrc(src);
          innerAudioContext.src = src;
          innerAudioContext.play();
        }
        return;
      },
      [innerAudioContext, innerSrc, isPlaying]
    )
  };
};
