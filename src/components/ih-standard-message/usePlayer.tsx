import { useCallback, useEffect, useRef, useState } from 'react';

export default () => {
  const [innerSrc, setInnerSrc] = useState<string | undefined>();
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef(new Audio());
  useEffect(() => {
    const handle = () => {
      setIsPlaying(false);
      setInnerSrc(undefined);
    };
    const audioR = audioRef.current;
    audioR.addEventListener('ended', handle);
    return () => {
      audioR.removeEventListener('ended', handle);
    };
  });

  return {
    src: innerSrc,
    isPlaying,
    play: useCallback(
      (src: string) => {
        audioRef.current.pause();
        if (innerSrc === src && isPlaying) {
          setIsPlaying(false);
          setInnerSrc(undefined);
        } else {
          setIsPlaying(true);
          setInnerSrc(src);
          audioRef.current.currentTime = 0;
          audioRef.current.setAttribute('src', src);
          audioRef.current
            .play()
            .then(() => {
              setIsPlaying(true);
            })
            .catch(() => {
              console.log('播放错误');
            });
        }
      },
      [innerSrc, isPlaying]
    )
  };
};
