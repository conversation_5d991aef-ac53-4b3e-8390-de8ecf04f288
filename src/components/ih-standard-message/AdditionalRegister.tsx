import { View } from 'remax/one';
import React from 'react';
import { AdditionalRegisterType } from './index.d';
import styleCard from './index.card.module.less';
const timeFlag = ['', '上午', '下午', '晚上'];
export default ({
  goAdditionalRegisterDetail,
  data
}: {
  data: AdditionalRegisterType;
  goAdditionalRegisterDetail: () => void;
}) => {
  return (
    <View className={styleCard.main}>
      <View className={styleCard.title}>{data.title}</View>
      <View className={styleCard.info}>
        <View className={styleCard.infoDetail}>
          <View className={styleCard.infoDetailKv}>
            <View className={styleCard.infoDetailKvKey}>加号时间：</View>
            <View className={styleCard.infoDetailKvValue}>
              {`${data.additionalRegisterDate} ${
                data.timeFlag ? timeFlag[data.timeFlag] : ''
              }`}
            </View>
          </View>
          <View className={styleCard.infoDetailKv}>
            <View className={styleCard.infoDetailKvKey}>就诊科室：</View>
            <View className={styleCard.infoDetailKvValue}>{data.deptName}</View>
          </View>
          <View className={styleCard.infoDetailKv}>
            <View className={styleCard.infoDetailKvKey}>就诊医生：</View>
            <View className={styleCard.infoDetailKvValue}>
              {data.doctorName}
            </View>
          </View>
        </View>
      </View>
      <View
        className={styleCard.button}
        onTap={() => {
          goAdditionalRegisterDetail();
        }}>
        查看详情
      </View>
    </View>
  );
};
