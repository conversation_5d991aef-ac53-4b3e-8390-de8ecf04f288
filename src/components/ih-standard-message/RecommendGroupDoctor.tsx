import { View } from 'remax/one';
import React from 'react';
import { DoctorInfoType } from './index.d';
import styleCard from './index.card.module.less';
export default ({
  addNewChat,
  isShowBtn,
  data
}: {
  data: DoctorInfoType;
  isShowBtn: string;
  addNewChat: () => void;
}) => {
  return (
    <View className={styleCard.main}>
      <View className={styleCard.info}>
        {isShowBtn === 'TO_DOCTOR'
          ? `已为您推荐${data.name}(${data.businessTitle})，点击前往即可向他咨询`
          : `已为患者推荐${data.name}(${data.businessTitle})`}
      </View>
      {isShowBtn === 'TO_DOCTOR' && (
        <View
          className={styleCard.button}
          onTap={() => {
            addNewChat();
          }}>
          前往
        </View>
      )}
    </View>
  );
};
