import { View } from 'remax/one';
import React from 'react';
import styleCard from './index.card.module.less';
import { HospitalAppointmentType } from './index.d';
export default ({
  goHospitalAppointment,
  data
}: {
  data: HospitalAppointmentType;
  goHospitalAppointment: () => void;
}) => {
  return (
    <View className={styleCard.main}>
      <View className={styleCard.title}>{data.title}</View>
      <View className={styleCard.info}>
        <View className={styleCard.infoDetail}>
          <View className={styleCard.infoDetailKv}>
            <View className={styleCard.infoDetailKvKey}>入院科室：</View>
            <View className={styleCard.infoDetailKvValue}>{data.deptName}</View>
          </View>
          <View className={styleCard.infoDetailKv}>
            <View className={styleCard.infoDetailKvKey}>入院时间：</View>
            <View className={styleCard.infoDetailKvValue}>
              {data.inHospitalTime}
            </View>
          </View>
        </View>
      </View>
      <View
        className={styleCard.button}
        onTap={() => {
          goHospitalAppointment();
        }}>
        查看详情
      </View>
    </View>
  );
};
