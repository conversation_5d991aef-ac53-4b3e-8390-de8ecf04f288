import { View, Image } from 'remax/one';
import React from 'react';

import styleCard from './index.card.module.less';
export default ({
  url,
  onPreview,
  onUpload
}: {
  url: string;
  onPreview?: () => void;
  onUpload?: () => void;
}) => {
  return (
    <View className={styleCard.main}>
      <View className={styleCard.tongTitle}>舌象照片</View>
      <View className={styleCard.center}>
        <Image
          onTap={onPreview}
          src={url}
          className={styleCard.tongImg}
          mode='aspectFill'
        />
      </View>
      {onUpload && (
        <View className={styleCard.button} onTap={onUpload}>
          上传照片
        </View>
      )}
    </View>
  );
};
