import { View } from 'remax/one';
import React from 'react';
import { MdtChronicInfo } from './index.d';
import styleCard from './index.card.module.less';
export default ({
  goPrescriptionDetailPre,
  data
}: {
  data: MdtChronicInfo;
  goPrescriptionDetailPre: () => void;
}) => {
  return (
    <View className={styleCard.main}>
      <View className={styleCard.title}>{data.title}</View>
      <View className={styleCard.info}>
        <View className={styleCard.infoDetail}>
          <View className={styleCard.infoDetailKv}>
            <View className={styleCard.infoDetailKvKey}>就诊科室：</View>
            <View className={styleCard.infoDetailKvValue}>{data.dept}</View>
          </View>
        </View>
      </View>
      <View
        className={styleCard.button}
        onTap={() => {
          goPrescriptionDetailPre();
        }}>
        查看详情
      </View>
    </View>
  );
};
