.content {
  flex: 1;
  margin-left: 20px;
  //overflow: hidden;
  display: flex;
  justify-content: flex-start;
  position: relative;
  // 处方和各种卡片
}
.contentWrap {
  flex: 1;
  //overflow: hidden;
  display: flex;
  justify-content: flex-start;
  position: relative;
  flex-direction: column;
  // 处方和各种卡片
}
.userNameRight {
  margin-right: 0px;
  margin-left: 0;
  flex-direction: row-reverse;
}
.userName {
  margin-right: 20px;
  margin-left: 20px;
  display: flex;
  justify-content: flex-start;
  position: relative;
}
// 普通文本消息
.contentNormal {
  max-width: 80%;
  background: rgba(255, 255, 255, 1);
  border-radius: 20px;
  padding: 20px;

  font-size: 30px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  line-height: 45px;

  word-break: break-all;
}

// 音频
.contentVoice {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.contentVoiceImage {
  padding: 20px 40px;
  background: rgba(255, 255, 255, 1);
  border-radius: 20px;
  width: 100px;
  height: 40px;
}
.contentVoiceRotate {
  padding: 20px 40px;
  background: rgba(255, 255, 255, 1);
  border-radius: 20px;
  width: 100px;
  height: 40px;
  transform: rotate(180deg);
}

.contentVoiceImageImage {
  width: 20px;
  height: 28px;
}

.contentVoiceText {
  padding-left: 20px;
  font-size: 24px;
  color: #0082e0;
}
.contentVoiceTextRight {
  padding-left: 0;
  padding-right: 20px;
}

// 图片消息
.contentImage {
  width: 200px;
  height: 200px;
}
.contentRight {
  flex-direction: row-reverse;
  margin-right: 20px;
  margin-left: 0;
}
.fileItem {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  border-radius: 12px;
  background: #fff;
  font-size: 24px;
}
.fileImages {
  width: 135px;
  height: 135px;
  margin-bottom: 10px;
}
.fileText {
  width: 170px;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
