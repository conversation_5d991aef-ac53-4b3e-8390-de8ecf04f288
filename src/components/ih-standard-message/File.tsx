import { View, Image } from 'remax/one';
import Images from './images';
import React from 'react';
import styleMessage from './index.message.module.less';
export default ({ data, onTap }: { data: any; onTap?: () => void }) => {
  return (
    <View className={styleMessage.fileItem} onTap={onTap}>
      <Image
        className={styleMessage.fileImages}
        src={Images.file}
        mode='aspectFill'
      />
      <View className={styleMessage.fileText}>{data.content}</View>
    </View>
  );
};
