import React from 'react';
import { View, Image } from 'remax/one';
import Images from './images';
import style from './Actions.module.less';
import classNames from 'classnames';
export type Action =
  | 'copy'
  | 'del'
  | 'withdraw'
  | 'reEdit'
  | 'additionalRegister';
export default (props: { right: boolean; onTap: (action: Action) => void }) => {
  return (
    <View
      className={classNames(style.actions, {
        [style.actionsRight]: props.right
      })}>
      <View className={style.actionsWrap}>
        <View
          className={style.actionsItem}
          onTap={e => {
            e.stopPropagation();
            props.onTap('copy');
          }}>
          复制
        </View>
        <View
          className={style.actionsItem}
          onTap={e => {
            e.stopPropagation();
            props.onTap('del');
          }}>
          删除
        </View>
        <View
          className={style.actionsItem}
          onTap={e => {
            e.stopPropagation();
            props.onTap('withdraw');
          }}>
          撤回
        </View>
        <Image
          className={classNames(style.actionsArrow, {
            [style.actionsRightArrow]: props.right
          })}
          src={Images.arrow}
          mode={'widthFix'}
        />
      </View>
    </View>
  );
};
