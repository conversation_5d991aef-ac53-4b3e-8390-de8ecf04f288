import { Image, View } from 'remax/one';
import { ReceiveChronicInfo } from './index.d';
import styleCard from './index.card.module.less';
import React from 'react';
export default ({
  goPrescriptionDetail,
  data,
  showButton
}: {
  data: ReceiveChronicInfo & {
    name: string;
    deptName: string;
    image: string;
  };
  showButton: boolean;
  goPrescriptionDetail: () => void;
}) => {
  return (
    <View className={styleCard.main}>
      <View className={styleCard.title}>{data.title}</View>
      <View className={styleCard.infoAppoint}>
        <View className={styleCard.infoAvatarImage}>
          <Image
            className={styleCard.infoAvatarImageImage}
            src={data?.image}
            mode='aspectFill'
          />
        </View>
        <View className={styleCard.infoAppointDetail}>
          <View>{data?.name}</View>
          <View>{data?.deptName}</View>
        </View>
      </View>
      {showButton && (
        <View
          className={styleCard.button}
          onTap={() => {
            goPrescriptionDetail();
          }}>
          去挂号
        </View>
      )}
    </View>
  );
};
