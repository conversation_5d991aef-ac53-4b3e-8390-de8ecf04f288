import { Image, View } from 'remax/one';
import React from 'react';
import Images from './images';
import currency from 'currency.js';
import styleCard from './index.card.module.less';
import { InspectionType } from './index.d';
export default ({
  goInspection,
  data
}: {
  data: InspectionType;
  goInspection: () => void;
}) => {
  return (
    <View className={styleCard.main}>
      <View className={styleCard.title}>{data?.title}</View>
      <View className={styleCard.info}>
        <View className={styleCard.infoImage} style={{ background: 'none' }}>
          <Image
            className={styleCard.infoImageImage}
            src={data?.type === 'EXAM' ? Images.exam : Images.test}
            mode='aspectFill'
          />
        </View>
        <View className={styleCard.infoDetail}>
          <View className={styleCard.infoDetailPrice}>
            ￥
            {currency(data.amount, {
              fromCents: true,
              symbol: ''
            }).format()}
          </View>
          <View className={styleCard.inspectionInfo}>
            {data?.itemList?.join('，')}
          </View>
        </View>
      </View>
      <View
        className={styleCard.button}
        onTap={() => {
          goInspection();
        }}>
        查看详情
      </View>
    </View>
  );
};
