import React, { useState } from 'react';
import { ArrSelectProps } from 'parsec-admin/lib/components/arrSelect';
import { ArrSelect } from 'parsec-admin';
import { Divider, Input, message, Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import useApi from '@pages/operate/api';
import env from '@configs/env';
const MyArrSelect = (
  props: ArrSelectProps & {
    onUpdate?: () => void;
  }
) => {
  const hisId = env.hisId;
  const [inputValue, setInputValue] = useState('');
  return (
    <ArrSelect
      dropdownRender={menu => {
        return (
          <div>
            {menu}
            <Divider style={{ margin: '4px 0' }} />
            <div style={{ display: 'flex', flexWrap: 'nowrap', padding: 8 }}>
              <Input
                style={{ flex: 'auto' }}
                value={inputValue}
                onChange={e => {
                  setInputValue(e.target.value);
                }}
              />
              <Button
                style={{
                  marginLeft: '10px'
                }}
                onClick={() => {
                  if (!inputValue) {
                    message.warn('请输入选项');
                    return;
                  }
                  useApi.sampleTypeAdd
                    .request({
                      hisId,
                      name: inputValue,
                      status: '1'
                    })
                    .then(() => {
                      props.onUpdate && props.onUpdate();
                    });
                  setInputValue('');
                }}
                icon={<PlusOutlined />}>
                添加分类
              </Button>
            </div>
          </div>
        );
      }}
      {...Object.assign({}, props, { onUpdate: undefined })}
      options={props.options}
    />
  );
};
export default MyArrSelect;
