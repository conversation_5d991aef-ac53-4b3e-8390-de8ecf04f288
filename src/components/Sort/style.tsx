import styled from 'styled-components';
export const MARGIN = 5;
export default styled.div`
  display: flex;
  flex-wrap: wrap;
  position: relative;
  > .item {
    background: #fff;
    color: #000;
    border-radius: 5px;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: move;
    user-select: none;
    margin: ${MARGIN}px;

    font-size: 14px;
    height: 40px;
    &.selected {
      border-color: #277ace;
    }
    &:hover {
      border-color: #277ace;
    }
    &.active {
      opacity: 0.5;
    }
    &.handle {
      position: absolute;
      display: none;
      opacity: 0.5;
      &.active {
        visibility: visible;
        display: flex;
      }
    }
  }
`;
