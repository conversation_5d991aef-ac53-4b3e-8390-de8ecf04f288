import classNames from 'classnames';
import React, { useRef, MouseEvent, useCallback } from 'react';
import StyleComponent, { MARGIN } from './style';
const COLS = 7;

export interface ListItem {
  value: string;
  label: string;
}
export default ({
  list = [],
  selected,
  onSelect,
  onChange
}: {
  list?: ListItem[];
  selected?: string;
  onSelect?: (item: ListItem) => void;
  onChange?: (list: ListItem[]) => void;
}) => {
  const handleRef = useRef<HTMLDivElement>(null);
  const mainRef = useRef<HTMLDivElement>(null);

  const prePointRef = useRef<{ x: number; y: number; time: number }>();
  const handlePointRef = useRef<{
    x: number;
    y: number;
    index: number;
    item: ListItem;
    el: HTMLDivElement;
    rect: DOMRect;
    time: number;
  }>();
  const handleMouseDwon = useCallback(
    (e: MouseEvent) => {
      const el = e.target as HTMLDivElement;
      if (handleRef.current && el && el.parentElement) {
        const itemIndex = list.findIndex(x => x.value === el.dataset.value);
        if (itemIndex === -1) {
          return;
        }

        prePointRef.current = {
          x: e.pageX,
          y: e.pageY,
          time: Date.now()
        };

        const rect = el.getBoundingClientRect();
        el.classList.add('active');
        const parentRect = el.parentElement.getBoundingClientRect();
        handlePointRef.current = {
          y: rect.top - parentRect.top - MARGIN,
          x: rect.left - parentRect.left - MARGIN,
          index: itemIndex,
          el: el,
          item: list[itemIndex],
          rect,
          time: Date.now()
        };

        handleRef.current.classList.add('active');
        handleRef.current.innerHTML = el.innerHTML;
        handleRef.current.style.top = handlePointRef.current.y + 'px';
        handleRef.current.style.left = handlePointRef.current.x + 'px';

        console.log('handlePointRef', handlePointRef.current);
      }
    },
    [list]
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (
        prePointRef.current &&
        handleRef.current &&
        handlePointRef.current &&
        mainRef.current
      ) {
        const currentPoint = {
          x: e.pageX,
          y: e.pageY,
          time: handlePointRef.current.time
          // time: Date.now()
        };
        handlePointRef.current.y += currentPoint.y - prePointRef.current.y;
        handlePointRef.current.x += currentPoint.x - prePointRef.current.x;
        prePointRef.current = currentPoint;

        handleRef.current.style.top = handlePointRef.current.y + 'px';
        handleRef.current.style.left = handlePointRef.current.x + 'px';

        // 计算当前位置， 先计算第几行几列，再得到数组中的位置。
        // 目前认为每个元素都是一样大的 (rect+MARGIN*2)
        const itemSize = {
          height: handlePointRef.current.rect.height + MARGIN * 2,
          width: handlePointRef.current.rect.width + MARGIN * 2
        };
        // 计算行列，四舍五入
        const position = {
          row: Math.floor(handlePointRef.current.y / itemSize.height),
          col: Math.floor(handlePointRef.current.x / itemSize.width)
        };

        const itemIndex = Math.min(
          position.row * COLS + position.col,
          list.length
        );
        if (itemIndex !== handlePointRef.current.index) {
          // 交换位置
          // handlePointRef.current.el
          mainRef.current.insertBefore(
            handlePointRef.current.el,
            mainRef.current.children[
              itemIndex > handlePointRef.current.index
                ? itemIndex + 1
                : itemIndex + 1
            ]
          );

          handlePointRef.current.index = itemIndex;
          // console.log('itemIndex', itemIndex);
        }
      }
    },
    [list]
  );

  const handleMouseUp = useCallback(
    (e: MouseEvent) => {
      if (
        prePointRef.current &&
        handleRef.current &&
        mainRef.current &&
        handlePointRef.current
      ) {
        prePointRef.current = undefined;
        handleRef.current.classList.remove('active');
        handlePointRef.current.el.classList.remove('active');

        if (Date.now() - handlePointRef.current.time < 300) {
          onSelect?.(handlePointRef.current.item);
        } else {
          const re: ListItem[] = [];
          Array.from(mainRef.current.children).forEach(el => {
            const item = list.find(
              x => x.value === (el as HTMLDivElement).dataset.value
            );
            if (item) {
              re.push(item);
            }
          });

          onChange?.(re);
        }
      }
    },
    [list, onChange, onSelect]
  );
  const itemWidth = `calc(${100 / COLS}% - ${MARGIN * 2}px)`;
  return (
    <StyleComponent
      onMouseUp={handleMouseUp}
      onMouseMove={handleMouseMove}
      ref={mainRef}>
      {list.map(item => {
        return (
          <div
            className={classNames('item', {
              selected: item.value === selected
            })}
            data-value={item.value}
            key={item.value}
            style={{
              width: itemWidth
            }}
            onMouseDown={handleMouseDwon}>
            {item.label}
          </div>
        );
      })}
      <div
        ref={handleRef}
        className='item handle'
        style={{
          width: itemWidth
        }}
      />
    </StyleComponent>
  );
};
