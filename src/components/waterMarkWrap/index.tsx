import { WaterMark } from 'parsec-admin';
import moment from 'moment';
import { useMemo } from 'react';
import storage from '@src/utils/storage';
export default (props: { children: React.ReactNode }) => {
  const waterMarkContent = useMemo(() => {
    const userInfo: any = storage.get('userInfo');
    return `${userInfo.name}  ${moment().format('YYYY年MM月DD日 HH:MM')}`;
  }, []);
  return (
    <WaterMark
      fontSize={24}
      // zIndex={0}
      gapX={40}
      fontColor={'rgba(0,0,0,0.1)'}
      rotate={-30}
      width={250}
      height={0}
      gapY={340}
      content={waterMarkContent}>
      {props.children}
    </WaterMark>
  );
};
