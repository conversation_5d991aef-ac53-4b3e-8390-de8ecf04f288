import React, { useEffect, useState } from 'react';
import { Select } from 'antd';
import { SelectProps } from 'antd/lib/select';
import { useDebounce } from 'ahooks';
interface Props {
  getList: (
    key: string
  ) => Promise<
    {
      label: string;
      value: string;
    }[]
  >;
}
const Comp: React.FunctionComponent<Props & SelectProps<any>> = ({
  getList,
  ...rest
}) => {
  const [value, setValue] = useState<any>();

  const [options, setOptions] = useState<
    {
      label: string;
      value: string;
    }[]
  >([]);
  const [key, setKey] = useState('');
  const keyDebounce = useDebounce(key, { wait: 500 });
  useEffect(() => {
    getList(keyDebounce).then(res => {
      console.log(res);
      setOptions(res);
    });
  }, [keyDebounce, getList]);
  return (
    <Select
      showSearch
      style={{ width: '100%' }}
      value={value}
      //   showArrow={false}
      filterOption={false}
      onSearch={async v => {
        setKey(v);
      }}
      onChange={v => {
        setValue(v);
      }}
      options={options}
      {...rest}
    />
  );
};

export default Comp;
