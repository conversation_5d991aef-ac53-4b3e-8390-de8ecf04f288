import React, { useState } from 'react';
import { Select, Button, Row, Col } from 'antd';
import useApi from './api';
import styled from 'styled-components';
import { sexType1 } from '@src/pages/operation/api';
import env from '@configs/env';

const { Option } = Select;

interface Iprops {
  onChange?: (value: any) => void;
  onNext?: () => void;
}

export default (props: Iprops) => {
  const { onNext, onChange } = props;
  const [data, setData] = useState<any>([]);
  const [value, setValue] = useState<any>(undefined);
  const [patientDetail, setPatientDetail] = useState<any>({});
  const hisId = env.hisId;

  const getpatients = (value: string, callback: any) => {
    useApi.就诊人列表
      .request({
        hisId,
        queryName: value
      })
      .then(response => {
        callback(response.data || []);
      });
  };

  return (
    <ChoosePatientBody>
      就诊人：
      <Select
        style={{ width: 380 }}
        showSearch
        value={value}
        placeholder={'输入患者姓名、ID或者身份证查询'}
        defaultActiveFirstOption={false}
        showArrow={false}
        filterOption={false}
        onSearch={value => {
          if (value) {
            getpatients(value, (data: any) => setData(data));
          } else {
            setData([]);
          }
        }}
        onChange={value => {
          const patient = data.find((x: any) => x.id === value);
          setValue(value);
          setPatientDetail(patient);
          onChange && onChange(patient);
        }}
        notFoundContent={null}>
        {(data || []).map((d: any) => (
          <Option key={d.id} value={d.id}>
            {d.name + d.id}
          </Option>
        ))}
      </Select>
      <Row className='patientModalDetail'>
        <Col span={12}>姓名：{patientDetail.name || '-'}</Col>
        <Col span={12}>ID：{patientDetail.id || '-'}</Col>
        <Col span={12}>性别： {sexType1[patientDetail.sex] || '-'}</Col>
        <Col span={12}>年龄：{patientDetail.patientAge || '-'}</Col>
      </Row>
      <div className='patientModalButton'>
        <Button
          type='primary'
          disabled={patientDetail.id ? false : true}
          onClick={() => {
            patientDetail.id && onNext && onNext();
          }}>
          下一步
        </Button>
      </div>
    </ChoosePatientBody>
  );
};

export const ChoosePatientBody = styled.div`
  width: 520px;
  height: 338px;
  padding: 24px 40px;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 1);
  border-radius: 10px;
  margin: 30px auto 0;
  box-shadow: 0px 0px 9px rgba(39, 128, 218, 0.24);

  > .patientModalDetail {
    margin-top: 30px;
    margin-bottom: 120px;
    > div {
      margin-bottom: 20px;
    }
  }
  > .patientModalButton {
    text-align: center;
  }
`;
