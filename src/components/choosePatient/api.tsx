import createApiHooks from 'create-api-hooks';
import { request } from 'parsec-admin';

export default {
  就诊人列表: createApiHooks((params: { queryName?: string; hisId: string }) =>
    request.get<{
      data: {
        id: number;
        name: string;
        hisId: string;
        idNo: number;
        sex: string;
        mobile: string;
        patientAge: string;
        birthay: string;
        patHisNo: number;
        pathisId: string;
      }[];
    }>('/mch/user/patient/list', {
      params
    })
  )
};
