import React from 'react';
import { But<PERSON>, Row, Col } from 'antd';
import styled from 'styled-components';
import { operationType, sexType1, timeValType } from '@src/pages/operation/api';
import applicationOkImg from '@src/images/applicationOk.png';
import { timeFormat } from '@src/pages/operation/list';
import moment from 'moment';

interface Iprops {
  value: any;
  goBack?: () => void;
  goOn?: () => void;
}

export default (props: Iprops) => {
  const { value, goBack, goOn } = props;

  return (
    <ApplicationOkBody>
      <div
        style={{
          display: 'flex',
          padding: '20px 24px',
          borderBottom: '1px solid #E2E2E2'
        }}>
        <img
          src={applicationOkImg}
          alt=''
          style={{ width: 60, marginRight: 10 }}
        />
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between'
          }}>
          <div style={{ color: '#2780D9', fontSize: 20, marginTop: 10 }}>
            申请成功
          </div>
          <div style={{ color: '#999999', fontSize: 14 }}>
            该患者的手术成功提交预约申请
          </div>
        </div>
      </div>
      <Row className='result-box'>
        <Col span={12}>
          就诊人：
          {value.patientName +
            '|' +
            sexType1[value.patientSex] +
            '|' +
            value.patientAgeStr}
        </Col>
        <Col span={12}>就诊人ID：{value.patientId}</Col>
        <Col span={12}>手术名称：{value.optName}</Col>
        <Col span={12}>手术类型：{operationType[value.type]}</Col>
        <Col span={12}>
          手术预约时间：
          {moment(value.optBookedAt).format(timeFormat) +
            ' ' +
            timeValType[value.timeVal]}
        </Col>
        <Col span={12}>地址：{value.optAddress}</Col>
      </Row>
      <div className='patientModalButton'>
        <Button
          style={{ marginRight: 20 }}
          onClick={() => {
            goBack && goBack();
          }}>
          返回
        </Button>
        <Button
          type='primary'
          onClick={() => {
            goOn && goOn();
          }}>
          继续预约
        </Button>
      </div>
    </ApplicationOkBody>
  );
};

export const ApplicationOkBody = styled.div`
  width: 660px;
  height: 438px;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 1);
  border-radius: 10px;
  margin: 30px auto 0;
  box-shadow: 0px 0px 9px rgba(39, 128, 218, 0.24);
  > .result-box {
    margin-bottom: 80px;
    padding: 20px 24px;
    > div {
      margin-bottom: 20px;
      padding-left: 20px;
    }
  }
  > .patientModalButton {
    text-align: center;
  }
`;
