import React from 'react';
import { Button, Form, Input, DatePicker } from 'antd';
import styled from 'styled-components';
import { ArrSelect } from 'parsec-admin';
import {
  operationType,
  operationAddress,
  timeValType
} from '@src/pages/operation/api';
import moment from 'moment';

interface Iprops {
  onSubmit?: (value: any) => void;
  goBack?: () => void;
}

export default (props: Iprops) => {
  const { onSubmit, goBack } = props;
  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 }
  };
  const tailLayout = {
    wrapperCol: { offset: 8, span: 16 }
  };

  return (
    <FillInInfoBody>
      <Form
        {...layout}
        name='basic'
        onFinish={values => {
          const infomation = {
            ...values,
            optBookedAt: moment(values.optBookedAt).format('YYYY-MM-DD')
          };
          onSubmit && onSubmit(infomation);
        }}>
        <Form.Item
          label='手术名称'
          name='optName'
          rules={[{ required: true, message: '请输入手术名称!' }]}>
          <Input />
        </Form.Item>
        <Form.Item
          label='手术类型'
          name='type'
          rules={[{ required: true, message: '请输入手术类型!' }]}>
          <ArrSelect options={operationType} placeholder='请输入手术类型' />
        </Form.Item>
        <Form.Item label={<span className='required-icon'>手术预约时间</span>}>
          <div style={{ display: 'flex' }}>
            <Form.Item
              noStyle
              name='optBookedAt'
              rules={[{ required: true, message: '请选择手术预约时间!' }]}>
              <DatePicker
                style={{ width: 400 }}
                disabledDate={current => {
                  return current && current < moment().endOf('day');
                }}
              />
            </Form.Item>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <Form.Item
              noStyle
              name='timeVal'
              rules={[{ required: true, message: '请选择手术预约时间!' }]}>
              <ArrSelect options={timeValType} />
            </Form.Item>
          </div>
        </Form.Item>
        <Form.Item
          label='地址'
          name='optAddress'
          rules={[{ required: true, message: '请选择手术地址!' }]}>
          <ArrSelect options={operationAddress} placeholder='请选择手术地址' />
        </Form.Item>
        <Form.Item {...tailLayout}>
          <Button
            style={{ marginRight: 20 }}
            onClick={() => {
              goBack && goBack();
            }}>
            上一步
          </Button>
          <Button htmlType='submit' type='primary'>
            提交
          </Button>
        </Form.Item>
      </Form>
    </FillInInfoBody>
  );
};

export const FillInInfoBody = styled.div`
  width: 520px;
  height: 338px;
  padding: 24px 40px;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 1);
  border-radius: 10px;
  margin: 30px auto 0;
  box-shadow: 0px 0px 9px rgba(39, 128, 218, 0.24);

  .required-icon::before {
    display: inline-block;
    margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }
`;
