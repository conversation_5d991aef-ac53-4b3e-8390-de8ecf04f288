import React, { useCallback, useState } from 'react';
import styled from 'styled-components';
import { PlusOutlined } from '@ant-design/icons';
import { UploadFile } from 'antd/lib/upload/interface';
import { UploadRequestOption } from 'rc-upload/lib/interface';
import { PreviewImgModal, Upload } from 'parsec-admin';
import { MyUploadProps } from 'parsec-admin/lib/components/upload';
import { getPrefixCls } from 'parsec-admin/lib/_utils';

export default React.memo(
  ({ length = 1, value, ...props }: MyUploadProps) => {
    const [visible, setVisible] = useState(false);
    const [url, setUrl] = useState('');
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const customRequest = useCallback((options: UploadRequestOption) => {
      const { file, onSuccess, onError } = options;
      const reader = new FileReader();
      reader.onload = e => {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const base64String = e.target!.result as string;
        onSuccess?.({ url: base64String });
      };
      reader.onerror = error => {
        onError?.(error);
      };
      reader.readAsDataURL(file as any);
    }, []);
    return (
      <>
        <MyUpload
          listType='picture-card'
          onPreview={({ url, thumbUrl, response = {} }) => {
            setUrl(url || response.url || '');
            setVisible(true);
          }}
          customRequest={customRequest}
          value={value}
          length={length}
          onFileListChange={setFileList}
          {...props}>
          {fileList.filter(
            ({ response: { url } = {} }) =>
              !(typeof value === 'string'
                ? value === url
                : value?.includes(url))
          ).length +
            (typeof value === 'string' ? 1 : value?.length || 0) <
            length && <UploadButton />}
        </MyUpload>
        <PreviewImgModal visible={visible} setVisible={setVisible} img={url} />
      </>
    );
  },
  ({ value }, { value: v2 }) => JSON.stringify(value) === JSON.stringify(v2)
);

const MyUpload = styled(Upload)`
  padding: 4px 0;
`;
const UploadButton = styled(props => (
  <div {...props}>
    <PlusOutlined />
    <div className='ant-upload-text'>上传</div>
  </div>
))`
  .${getPrefixCls}-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .${getPrefixCls}-upload-select-picture-card .${getPrefixCls}-upload-text {
    margin-top: 8px;
    color: #666;
  }
`;
