import React from 'react';
import { TableList } from 'parsec-admin';
import { TableListProps } from 'parsec-admin/lib/templates/tableList/Types';
import styled from 'styled-components';

export default function<D, P>({
  paginationExtra = <div />,
  ...props
}: TableListProps<D, P> & {
  paginationExtra?: React.ReactNode;
}) {
  return (
    <TableListWrap>
      <TableList
        pageHeaderProps={false}
        {...props}
        showTool={false}
        scroll={{ x: '100%' }}
        pagination={{
          showQuickJumper: true,
          size: 'small',
          showTotal: total => (
            <PaginationExtra>
              {paginationExtra}共 {total} 条
            </PaginationExtra>
          )
        }}
      />
    </TableListWrap>
  );
}

const PaginationExtra = styled.div`
  display: flex;
  justify-content: space-between;
`;

const TableListWrap = styled.div`
  .ant-table-pagination {
    padding: 0;
    display: flex;
    width: 100%;
    text-align: right;
    .ant-pagination-total-text {
      flex: 1;
    }
  }
  > div {
    > div:first-child:not(:last-child) {
      .ant-form-item-label {
        min-width: 40px;
      }
    }
    > div:last-child {
      > .ant-card-body {
        padding: 0 20px 20px;
        > header {
          padding: 16px 0 0;
          position: relative;

          h3 {
            position: relative;
            color: #2780d9;
            &:not(:empty) {
              &:before {
                content: '';
                width: 6px;
                height: 15px;
                position: absolute;
                left: -20px;
                top: 50%;
                transform: translateY(-50%);
                background: #2780d9;
              }
            }
          }
        }
        .ant-table-wrapper {
          .ant-table-content {
            border: 1px solid #f0f0f0;
          }
        }
      }
    }
  }
`;
