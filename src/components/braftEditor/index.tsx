import 'braft-editor/dist/index.css';
import BraftEditor, { BraftEditorProps } from 'braft-editor';
import useApi from '@apiHooks';
import { message, Upload } from 'antd';
import { useCallback, useMemo } from 'react';
import { ContentUtils } from 'braft-utils';
import { useConfig } from '@src/store/hisConfig';

const BraftEditorComponent: any = ({
  value,
  onChange,
  ...props
}: BraftEditorProps) => {
  const innerValue = useMemo(
    () => value || BraftEditor.createEditorState(value || '<p></p>'),
    [value]
  );
  const { uploadApi } = useConfig();

  const uploadHandler = useCallback(
    async (params: { file?: File }) => {
      if (!params.file) {
        return;
      }

      try {
        message.loading({
          content: '上传中...'
        });
        const url = await uploadApi(params.file);
        onChange?.(
          ContentUtils.insertMedias(innerValue, [
            {
              type: 'IMAGE',
              url
            }
          ])
        );
      } finally {
        message.destroy();
      }
    },
    [innerValue, onChange]
  );

  const extendControls = [
    {
      key: 'antd-uploader',
      type: 'component',
      component: (
        <Upload
          accept='image/*'
          showUploadList={false}
          customRequest={uploadHandler as any}>
          <button
            type='button'
            className='control-item button upload-button'
            data-title='插入图片'>
            插入图片
          </button>
        </Upload>
      )
    }
  ];

  return (
    <BraftEditor
      value={innerValue}
      onChange={onChange}
      {...props}
      extendControls={extendControls as any}
      excludeControls={['media']}
    />
  );
};

Object.setPrototypeOf(BraftEditorComponent, BraftEditor);

export default BraftEditorComponent;
