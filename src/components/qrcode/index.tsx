import React, { useState, useEffect } from 'react';
const QRCode = require('qrcode');
interface Props {
  url: string;
  size?: number | string;
  onGetImgUrl?: (imgUrl: string) => void;
}
export default ({ url, size = 200, onGetImgUrl }: Props) => {
  const [imgurl, setImgurl] = useState('');
  useEffect(() => {
    QRCode.toDataURL(url)
      .then((qurl: any) => {
        setImgurl(qurl);
        onGetImgUrl && onGetImgUrl(qurl);
      })
      .catch((err: any) => {
        console.error(err);
      });
  }, [onGetImgUrl, url]);
  return <img src={imgurl} alt='' style={{ width: size, height: size }} />;
};
