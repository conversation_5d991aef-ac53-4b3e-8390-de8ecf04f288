import { createContainer } from 'unstated-next';
import api from '@apiHooks';
import { useEffect, useState } from 'react';
import env from '@configs/env';
export interface Organization {
  id: '@natural'; //主键
  createTime: '@datetime'; //创建时间
  updateTime: '@datetime'; //修改时间
  targetHisId: number | string; //his_id
  institutionName: '@cname'; //机构名称
  doctorNum: '@natural'; //医生数量
  superviseStatus: number; //监管状态 0关闭 1:开启
  environment: number; //0:内部 1:外部
}

export default createContainer(() => {
  const {
    request: getOrganizationList,
    data: { data: organizationList }
  } = api.获取机构列表({
    needInit: false,
    initValue: { data: [] }
  });
  const [localHIsId, setLocalHisId] = useState<string | number>(env.hisId);
  const [otherList, setOtherList] = useState<Organization[]>([]);
  useEffect(() => {
    if (organizationList?.length) {
      const local = organizationList.filter(item => item.environment === 0);
      setOtherList(organizationList.filter(item => item.environment === 1));
      if (local?.length) {
        setLocalHisId(local?.[0]?.targetHisId + '');
      }
    }
  }, [organizationList]);
  return {
    organizationList,
    getOrganizationList,
    localHIsId,
    otherList
  };
});
