import { createContainer } from 'unstated-next';
import apis, { HospitalizationEnum } from '@pages/organization/config/api';
import storage from '@utils/storage';
import { useEffect, useState } from 'react';

export default createContainer(() => {
  const {
    data: { data: config },
    request: flushConfig
  } = apis.基础配置详情({
    initValue: { data: undefined },
    needInit: !!storage.get('token')
  });
  const {
    data: dataHospitalizationEnum,
    request: getHospitalizationEnum
  } = apis.查询住院预约相关状态枚举({
    needInit: false
  });
  const {
    data: dataCheckEnum,
    request: getCheckEnum
  } = apis.查询检验检查主订单状态枚举({
    needInit: false
  });
  const {
    data: dataSingleCheckEnum,
    request: getSingleCheckEnum
  } = apis.查询单个检验检查记录状态枚举({
    needInit: false
  });
  const [hospitalizationEnum, setHospitalizationEnum] = useState<
    HospitalizationEnum
  >();
  const [checkEnum, setCheckEnum] = useState<HospitalizationEnum>();
  const [singleCheckEnum, setSingleCheckEnum] = useState<HospitalizationEnum>();
  useEffect(() => {
    if (dataSingleCheckEnum?.data) {
      setSingleCheckEnum(dataSingleCheckEnum.data);
    }
  }, [dataSingleCheckEnum]);
  useEffect(() => {
    if (dataHospitalizationEnum?.data) {
      setHospitalizationEnum(dataHospitalizationEnum.data);
    }
  }, [dataHospitalizationEnum]);
  useEffect(() => {
    if (dataCheckEnum?.data) {
      setCheckEnum(dataCheckEnum.data);
    }
  }, [dataCheckEnum]);
  useEffect(() => {
    if (config) {
      storage.set('hospital-config', config);
    }
  }, [config]);

  return {
    config,
    flushConfig,
    hospitalizationEnum,
    getHospitalizationEnum,
    checkEnum,
    getCheckEnum,
    getSingleCheckEnum,
    singleCheckEnum
  };
});
