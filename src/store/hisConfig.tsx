import { createContext, useContext, useState, ReactNode } from 'react';
import getHisConfig from '@configs/hospital';
import BaseConfig from '@configs/hospital/_BaseHosp';

const ConfigContext = createContext<BaseConfig | null>(null);

const HisConfigProvider = ({ children }: { children: ReactNode }) => {
  const [config] = useState<BaseConfig>(getHisConfig());

  return (
    <ConfigContext.Provider value={config}>{children}</ConfigContext.Provider>
  );
};

const useConfig = () => {
  const context = useContext(ConfigContext);
  if (!context)
    throw new Error('useConfig must be used within a ConfigProvider');
  return context;
};

export { HisConfigProvider, useConfig };
