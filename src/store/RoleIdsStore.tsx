import { createContainer } from 'unstated-next';
import { useState, useCallback, useEffect } from 'react';
import { useSessionStorage } from 'react-use';
import storage from '@utils/storage';
import env from '@configs/env';

interface MenuItem {
  childMenu: MenuItem[];
  code: string;
  id: number;
  name: string;
  operatePurview: string;
  parentId: number;
  sort: number;
  type: string;
  url: string;
  userId: number;
}

export default createContainer(() => {
  const [menuData, setMenuData] = useSessionStorage<MenuItem[]>('role');

  //用于生成{url: ["id1", "id2"]}这样的数据类型， 作为后面判断权限的依据
  const generateAuthData = useCallback(
    (roleData: MenuItem[], res: string[]) => {
      if (roleData && roleData.length > 0) {
        roleData.forEach(item => {
          //type为2说明是权限
          if (item.type === '2') {
            res.push(item.code);
          }
          if (item.childMenu && item.childMenu.length > 0) {
            generateAuthData(item.childMenu, res);
          }
        });
      }
    },
    []
  );

  useEffect(() => {
    if (!menuData && !env.isDoctor) {
      storage.del('auth');
      if (!window.location.href.includes('#/login')) {
        window.location.replace('#/login');
      }
    }
    // 保存权限数据
    const res = [] as string[];
    generateAuthData(menuData, res);
    storage.set('auth', JSON.stringify(res));
  }, [menuData, generateAuthData]);
  const getRoleIds = useCallback(
    (data: typeof menuData) =>
      data
        ?.map(({ id, childMenu }) => [id, childMenu.map(({ id }) => id)])
        .flat(2) || [],
    []
  );
  const [roleIds, setRoleIds] = useState<Array<number | string>>(
    getRoleIds(menuData)
  );
  return {
    roleIds,
    setMenuData: useCallback(
      (data: typeof menuData) => {
        setRoleIds(getRoleIds(data));
        setMenuData(data);
      },
      [getRoleIds, setMenuData]
    )
  };
});
