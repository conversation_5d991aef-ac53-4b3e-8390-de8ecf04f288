const uuList: any[] = [];

export default (v?: string) => {
  const myDate = new Date();
  const ossPath = v ? `${v}/` : '';
  const year = myDate.getFullYear();
  let month;
  let day;
  if (myDate.getMonth() + 1 < 10) {
    const m = myDate.getMonth() + 1;
    month = '0' + m;
  } else {
    month = myDate.getMonth() + 1;
  }
  if (myDate.getDate() < 10) {
    const d = myDate.getDate() + 1;
    day = '0' + d;
  } else {
    day = myDate.getDate();
  }
  const m = ossPath + year + '/' + month + '/' + day + '/';
  uuList[0] = m;
  return m;
};
