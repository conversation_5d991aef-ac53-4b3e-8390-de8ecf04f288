import { LoginData } from '@apiHooks';
import { RelationIds, ScheduleIds } from '@configs/d';
import { Configuration } from '@pages/organization/config/api';

type keys = 'token' | 'auth' | 'hospital-config';

interface Storage {
  get: {
    (key: keys): string | null;
    (key: 'userInfo'): LoginData | { [N in keyof LoginData]?: LoginData[N] };
    (key: 'scheduleIds'): ScheduleIds[];
    (key: 'relationIds'): RelationIds[];
    (key: 'hospital-config'): Configuration;
  };
  set: {
    (key: keys, value: string): void;
    (key: 'userInfo', value: LoginData): void;
    (key: 'hospital-config', value: Configuration): void;
    (key: 'scheduleIds', value: ScheduleIds[]): void;
    (key: 'relationIds', value: RelationIds[]): void;
  };
  del: {
    (key: keys): void;
    (key: 'userInfo'): void;
  };
  clear: () => void;
}

export const prefix = window.location.pathname;
// const prefix = 'parsec-app';

const storage: Storage = {
  get: (key: string) => {
    const data = localStorage.getItem(`${prefix}-${key}`);
    switch (key) {
      case 'userInfo':
        return data ? JSON.parse(data) : {};
      case 'scheduleIds':
        return data ? JSON.parse(data) : {};
      case 'hospital-config':
        return data ? JSON.parse(data) : {};
      default:
        return localStorage.getItem(`${prefix}-${key}`);
    }
  },
  set: (
    key: string,
    value: string | LoginData | ScheduleIds[] | Configuration | RelationIds[]
  ) => {
    localStorage.setItem(`${prefix}-${key}`, JSON.stringify(value));
  },
  del: (key: string) => localStorage.removeItem(`${prefix}-${key}`),
  clear: () => localStorage.clear()
};

export default storage;
