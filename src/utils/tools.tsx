import { jsPDF as JSPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { message } from 'antd';
import BigNumber from 'bignumber.js';
/*
  获取随机数
*/
export const getRandom: (max: number, min: number, num: number) => number[] = (
  max,
  min,
  num
) => {
  const arr: number[] = [];
  while (num--) {
    const asciiNum = ~~(Math.random() * (max - min + 1) + min);
    arr.push(asciiNum);
  }
  return arr;
};
/*
隐藏手机号中间4位
*/
export const hidePhone = (phone?: string) => {
  const reg = /^(\d{3})\d{4}(\d{4})$/;
  if (phone) {
    return phone.replace(reg, '$1****$2');
  } else {
    return '_';
  }
};
/*
金额显示
*/
export const cash = (moeny: number | string) => {
  if (moeny) {
    return (+moeny / 100).toFixed(2);
  } else {
    return '0';
  }
};

/**
 *
 * @param value 格式化数字为1,000格式
 */
export const formatNumber = (
  value: number | null | undefined | string
): string => {
  if (!value) {
    return '0';
  }
  if (typeof value === 'string') {
    return value;
  }
  return value.toLocaleString('en-US');
};
/*
生日换算年龄
*/
export const age = (birth: string) => {
  if (birth !== '' && birth !== null) {
    const b: any = new Date(birth);
    const t: any = new Date();
    return Math.floor((t - b) / (365.25 * 24 * 60 * 60 * 1000));
  } else {
    return '0';
  }
};

// 病案复印相关打印代码
// 调用打印机API打印某个元素
export const print = (
  el: HTMLElement | null,
  fileName: string,
  type: 'a4' | 'a5' | 'ems1' | 'ems2' = 'a5'
) => {
  window.scrollTo({
    top: 0
  });
  if (el) {
    html2canvas(el).then(canvas => {
      const contentWidth = canvas.width;
      const contentHeight = canvas.height;

      let printWidth = 592.28;
      let printHeight = 841.89;
      if (type === 'a5') {
        printWidth = 419.58;
        printHeight = 595.35;
      }
      if (type === 'ems1') {
        printWidth = 300;
        printHeight = 680;
      }
      if (type === 'ems2') {
        printWidth = 300;
        printHeight = 580;
      }
      //一页pdf显示html页面生成的canvas高度;
      const pageHeight = (contentWidth / printWidth) * printHeight - 55;
      //未生成pdf的html页面高度
      let leftHeight = contentHeight;
      //页面偏移
      let position = 0;
      //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
      const imgWidth = printWidth;
      const imgHeight = (printWidth / contentWidth) * contentHeight;

      const pageData = canvas.toDataURL('image/jpeg', 1.0);

      const pdf = new JSPDF('p', 'pt', 'a4');

      //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
      //当内容未超过pdf一页显示的范围，无需分页
      if (leftHeight < pageHeight) {
        pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight);
      } else {
        while (leftHeight > 0) {
          pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight);
          leftHeight -= pageHeight;
          position -= printHeight;
          //避免添加空白页
          if (leftHeight > 0) {
            pdf.addPage();
          }
        }
      }

      // pdf.save(`${fileName}.pdf`);
      pdf.autoPrint();

      const hiddFrame: any = document.createElement('iframe');
      hiddFrame.style.position = 'fixed';
      // "visibility: hidden" would trigger safety rules in some browsers like safari，
      // in which the iframe display in a pretty small size instead of hidden.
      // here is some little hack ~
      hiddFrame.style.width = '1px';
      hiddFrame.style.height = '1px';
      hiddFrame.style.opacity = '0.01';
      const isSafari = /^((?!chrome|android).)*safari/i.test(
        window.navigator.userAgent
      );
      if (isSafari) {
        // fallback in safari
        hiddFrame.onload = () => {
          try {
            hiddFrame.contentWindow.document.execCommand('print', false, null);
          } catch (e) {
            hiddFrame.contentWindow.print();
          }
        };
      }
      hiddFrame.src = pdf.output('bloburl');
      document.body.appendChild(hiddFrame);
    });
  } else {
    message.error('请传入需要生成PDF的内容');
  }
};

export const calcGetPrice = (price: number) => {
  return new BigNumber(price || 0).toFixed(2, BigNumber.ROUND_DOWN);
};
