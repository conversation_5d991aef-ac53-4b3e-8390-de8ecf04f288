export default (str: string) => {
  if (str.length < 8 || str.length > 20) {
    return { success: false, message: '密码长度8-20位' };
  }

  let num = 0;
  let char = 0;
  let spec = 0;
  let space = 0;

  for (let index = 0; index < str.length; index++) {
    const element = str[index];
    if (element === ' ') {
      space++;
      break;
    }

    if (/\d/.test(element)) {
      num++;
    } else if (/a-z/.test(element)) {
      char++;
    } else {
      spec++;
    }
  }

  if (space !== 0) {
    return { success: false, message: '不能有空格' };
  }

  if ((num ? 1 : 0) + (char ? 1 : 0) + (spec ? 1 : 0) < 2) {
    return { success: false, message: '至少包含数字、字母、特殊符号中2种' };
  }

  return { success: true, message: '成功' };
};
