import _provinces from 'china-division/dist/provinces.json';
import _cities from 'china-division/dist/cities.json';
import _areas from 'china-division/dist/areas.json';

// import { PickerData } from 'antd-mobile/lib/picker/PropsType';

const provinces = JSON.parse(JSON.stringify(_provinces)) as typeof _provinces;
const cities = JSON.parse(JSON.stringify(_cities)) as typeof _cities;
const areas = JSON.parse(JSON.stringify(_areas)) as typeof _areas;

export interface CascaderOptionType {
  value?: string;
  label?: React.ReactNode;
  children?: Array<CascaderOptionType>;
  [key: string]: any;
}

areas.forEach(area => {
  const matchCity: CascaderOptionType = cities.filter(
    city => city.code === area.cityCode
  )[0];
  if (matchCity) {
    matchCity.children = matchCity.children || [];
    matchCity.children.push({
      label: area.name,
      value: area.code
    });
  }
});

cities.forEach((city: CascaderOptionType) => {
  const matchProvince: CascaderOptionType = provinces.filter(
    province => province.code === city.provinceCode
  )[0];
  if (matchProvince) {
    matchProvince.children = matchProvince.children || [];
    matchProvince.children.push({
      label: city.name,
      value: city.code,
      children: city.children
    });
  }
});

export const getAddress = (arr: Array<string | number> = []) => {
  const strArr: string[] = [];
  let index = 0;
  const fn = (options: CascaderOptionType[]) =>
    options.find(({ value, label, children }) => {
      const is = value === arr[index];
      if (is) {
        strArr.push(label + '');
        index++;
      }
      if (children) {
        fn(children);
      }
      return is;
    });
  fn(options);
  return strArr.join('-');
};

export const options: CascaderOptionType[] = JSON.parse(
  JSON.stringify(
    provinces.map((province: CascaderOptionType) => ({
      label: province.name,
      value: province.code,
      children: province.children
    }))
  )
);

export default options as any[];

// 根据value获取城市
export function getCascaderLabel(
  values: string[],
  address: CascaderOptionType[]
) {
  const labels: any[] = [];
  let curAddress = address;
  values.forEach(val => {
    const tempAddress = each(val, curAddress);
    curAddress = tempAddress?.children || [];
    tempAddress?.label && labels.push(tempAddress.label);
  });
  return labels;
  function each(value: string, address: CascaderOptionType[]) {
    return address.find(item => item.value === value);
  }
}
// 根据城市获取value
export function getCascaderValue(
  labels: string[],
  address: CascaderOptionType[]
) {
  const values: any[] = [];
  let curAddress = address;
  labels.forEach(val => {
    const tempAddress = each(val, curAddress);
    curAddress = tempAddress?.children || [];
    tempAddress?.value && values.push(tempAddress.value);
  });
  return values;
  function each(value: string, address: CascaderOptionType[]) {
    return address.find(item => item.label === value);
  }
}
