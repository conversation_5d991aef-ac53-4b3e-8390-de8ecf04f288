/* eslint-disable no-prototype-builtins */
// export default (str: string | null | undefined) => {
//   if (str === 'M') {
//     return '男';
//   }
//   if (str === 'F') {
//     return '女';
//   }
//   if (str === null || str === undefined) {
//     return '-';
//   }
//   return str;
// };
/**
 * 性别转换函数
 * 将英文性别标识转换为中文表述
 * - 'M' 转换为 '男'
 * - 'F' 转换为 '女'
 * - null 或 undefined 转换为 '-'
 * - 其他值不变
 */
export default (str: string | null | undefined): string => {
  // 使用对象映射来管理性别转换，便于扩展和维护
  const genderMap: { [key: string]: string } = {
    M: '男',
    F: '女'
  };

  // 检查是否为预定义的性别值
  // 检查是否为预定义的性别值
  if (('M' in genderMap && str === 'M') || ('F' in genderMap && str === 'F')) {
    return genderMap[str as 'M' | 'F'];
  }
  // 处理 null 或 undefined 的情况
  if (str === null || str === undefined) {
    return '-';
  }

  // 对于未定义的性别值，返回原值
  return str;
};
