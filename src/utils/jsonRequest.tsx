import { request } from 'parsec-admin';

const jsonheaders = {
  'Content-Type': 'application/json; charset=UTF-8'
};

const jsonRequest: Partial<typeof request> = {
  get: (url, config) =>
    request.get(url, {
      ...config,
      headers: {
        ...jsonheaders,
        ...config?.headers
      }
    }),
  post: (url, data, config) =>
    request.post(url, data, {
      ...config,
      headers: {
        ...jsonheaders,
        ...config?.headers
      }
    }),
  put: (url, data, config) =>
    request.put(url, data, {
      ...config,
      headers: {
        ...jsonheaders,
        ...config?.headers
      }
    }),
  delete: (url, config) =>
    request.delete(url, {
      ...config,
      headers: {
        ...jsonheaders,
        ...config?.headers
      }
    }),
  patch: (url, data, config) =>
    request.patch(url, data, {
      ...config,
      headers: {
        ...jsonheaders,
        ...config?.headers
      }
    })
};

export default jsonRequest as typeof request;
