/*
 * @Author: hpengfei <EMAIL>
 * @Date: 2024-04-22 15:44:15
 * @LastEditors: hpengfei <EMAIL>
 * @LastEditTime: 2024-06-05 15:36:20
 * @FilePath: \ih-standard\src\utils\common.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import moment from 'moment';

export const formatParamsWithMoment = (data: any) => {
  if (!data || typeof data !== 'object') {
    return data;
  }
  const res: any = {};
  Object.keys(data).forEach(key => {
    if (moment.isMoment(data[key])) {
      res[key] = data[key].format('YYYY-MM-DD HH:mm:ss');
    } else {
      res[key] = data[key];
    }
  });
  return res;
};

export const isProd = window.location.hostname === 'ihs.cqkqinfo.com';

export const isLocal = () => {
  return window.location.hostname === 'localhost';
};

export const getApiVersion = () => {
  if (isProd) {
    return;
  }
  // if (isLocal()) {
  //   return '4.0.29';
  // }
  return window.location.pathname.match(/\d+\.\d+\.\d+/)?.[0];
};
export const convertMapToOptions = (map: {
  [key: string]: string | number;
}) => {
  return Object.keys(map).map(key => ({
    label: map[key],
    value: key
  }));
};
export const convertMapToOptionsForArrSelect = (map: {
  [key: string]: string | number;
}) => {
  return Object.keys(map).map(key => ({
    children: map[key],
    value: key
  }));
};
/**
 * 显示占位符或数据值。
 * 如果 `v` 为假（false, null, undefined, 0, "" 等），函数返回占位符。
 * 如果 `v` 为真，并且 `data` 已定义，函数返回 `data`。
 * 否则，返回占位符。
 *
 * @param v 用于确定是否显示占位符的值。
 * @param data 需要显示的数据。
 * @param placeholder 当 `v` 为假或 `data` 未定义时显示的占位符，默认为 '-'。
 * @returns 返回 `data` 或占位符。
 */
export const showPlaceholderWhenEmpty = (
  v: any,
  data?: any,
  placeholder = '-'
) => {
  if (!v) return placeholder;

  return data ?? v;
};
