// 输入毫秒 ,输出时间长度 多少小时
export default (s: number) => {
  let offset = s;
  const day = Math.floor(offset / (1000 * 60 * 60 * 24));

  offset = offset - day * 1000 * 60 * 60 * 24;
  const hour = Math.floor(offset / (1000 * 60 * 60));

  offset = offset - hour * 1000 * 60 * 60;
  const minutes = Math.floor(offset / (1000 * 60));

  offset = offset - minutes * 1000 * 60;
  const seconds = Math.floor(offset / 1000);

  let re = '';
  if (day) {
    re += `${day}日`;
  }
  if (hour || !!re) {
    re += `${hour}时`;
  }
  if (minutes || !!re) {
    re += `${minutes}分`;
  }
  if (seconds || !!re) {
    re += `${seconds}秒`;
  }

  return re ? (isNaN(Number(re)) ? re : '-') : '-';
};
