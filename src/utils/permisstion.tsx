import storage from '@utils/storage';
import env from '@src/configs/env';

class Permission {
  get permissions(): string[] {
    const res = storage.get('auth') || '[]';
    return JSON.parse(res);
  }
  /**
   * 能否查看订单详情
   */

  get canLookOrderSmartDetail() {
    return this.permissions.includes('ORDER_DETAIL_LOOK_SMART');
    // return this.permissions.includes('ORDER_LIST_SMART');
  }

  /**
   * 能否查看挂缴订单退费
   */

  get canLookOrderSmartrefund() {
    return this.permissions.includes('PAYMENT_ORDER_REFUND');
  }

  /**
   * 能否新增医疗机构
   */

  get canAddOrgnization() {
    return this.permissions.includes('ORGANIZATION_ADD');
  }

  /**
   * 能否更新医疗机构
   */
  get canUpdateOrgnization() {
    return this.permissions.includes('ORGANIZATION_UPDATE');
  }

  /**
   * 是否生成住院宣教二维码
   */
  get canInHospitalQr() {
    return this.permissions.includes('CONTENT_INHOSPITAL_QR_IMAGE');
  }

  /**
   * 能否重置医疗机构密码
   */
  get canResetOrgnization() {
    return this.permissions.includes('DOTOOR_ACCOUNT_RESET');
  }

  /**
   * 是否显示医疗机构操作
   */
  get showEditOrgnization() {
    return this.canUpdateOrgnization || this.canResetOrgnization;
  }

  /**
   * 能否更新医院信息
   */
  get canUpdateHospital() {
    return this.permissions.includes('HOSPTIAL_UPDATE');
  }

  /**
   * 能否新增医生/护士/药师
   */
  public canAddDoctor(type: string) {
    // 1医生 2护士 3职员 4药师
    return {
      1: this.permissions.includes('DOCTOR_MANAGE_ADD'),
      2: this.permissions.includes('NURSE_MANAGE_ADD'),
      4: this.permissions.includes('PHARMACIST_MANAGE_ADD')
    }[type];
  }

  /**
   * 能否导入医生/护士/药师
   */
  public canImportDoctor(type: string) {
    return {
      1: this.permissions.includes('DOCTOR_MANAGE_IMPORT'),
      2: this.permissions.includes('NURSE_MANAGE_IMPORT'),
      4: this.permissions.includes('PHARMACIST_MANAGE_IMPORT')
    }[type];
  }
  /**
   * 能否排序医生
   */
  get canSortDoctor() {
    return this.permissions.includes('DOCTOR_MANAGE_SORT');
  }
  /**
   * 能否同步医生
   */
  get canSyncDoctor() {
    return this.permissions.includes('DOCTOR_MANAGE_SYNC');
  }

  /**
   * 能否更新医生/护士/药师
   */
  public canUpdateDoctor(type: string) {
    return {
      1: this.permissions.includes('DOCTOR_MANAGE_MODIFY'),
      2: this.permissions.includes('NURSE_MANAGE_MODIFY'),
      4: this.permissions.includes('PHARMACIST_MANAGE_MODIFY')
    }[type];
  }
  /**
   * 能否删除医生/护士/药师
   */
  public canDelDoctor(type: string) {
    return {
      1: this.permissions.includes('DOCTOR_MANAGE_BATCH_DELETE'),
      2: this.permissions.includes('NURSE_MANAGE_BATCH_DELETE'),
      4: this.permissions.includes('PHARMACIST_MANAGE_BATCH_DELETE')
    }[type];
  }
  /**
   * 能否批量停用\启用 医生/护士/药师
   */
  public canDoctorStatus(type: string) {
    return {
      1: this.permissions.includes('DOCTOR_MANAGE_BATCH_STATUS'),
      2: this.permissions.includes('NURSE_MANAGE_BATCH_STATUS'),
      4: this.permissions.includes('PHARMACIST_MANAGE_BATCH_STATUS')
    }[type];
  }

  /**
   * 能否重置医生密码
   */
  get canResetDoctor() {
    return this.permissions.includes('DOCTOR_RESETPASSWORD');
  }

  /**
   * 是否显示医生操作按钮
   */
  get showEditDoctor() {
    return this.canResetDoctor || this.canUpdateDoctor;
  }

  /**
   * 能否新增科室
   */
  get canAddDept() {
    return this.permissions.includes('DEPT_ADD');
  }

  /**
   * 能否更新科室
   */
  get canUpdateDept() {
    return this.permissions.includes('DEPT_UPDATE');
  }

  /**
   * 能否查看问诊监管详情
   */
  get canQueryDetail() {
    return this.permissions.includes('INQUIRY_INFO');
  }

  /**
   * 能否评价管理下架
   */
  get canUpadateAppraisal() {
    return this.permissions.includes('APPRAISAL_UPDATE');
  }

  /**
   * 意见反馈忽略
   */
  get canUpdateComplants() {
    return this.permissions.includes('COMPLAINTS_UPDATE');
  }

  /**
   * 常用语添加
   */
  get canAddReply() {
    return this.permissions.includes('REPLYTEMPLATE_SAVE');
  }

  /**
   * 常用语编辑
   */
  get canUpdateReply() {
    return this.permissions.includes('REPLYTEMPLATE_UPDATE');
  }

  /**
   * 常用语删除
   */
  get canDeleteReply() {
    return this.permissions.includes('REPLYTEMPLATE_DELETE');
  }

  /**
   * 是否显示常用语操作界面
   */
  get showEditReply() {
    return this.canDeleteReply || this.canUpdateReply;
  }

  /**
   * 加号登记 -> 加号登记详情
   */
  get canRegDetail() {
    return this.permissions.includes('ADD_REG_DETAIL');
  }
  /**
   * 药品处方 -> 处方详情
   */
  get canPrescDetail() {
    return this.permissions.includes('PRESC_DETAIL');
  }
  /**
   * 在线问诊退费
   */
  get canOrderRefund() {
    return this.permissions.includes('ORDER_REFUND');
  }

  /**
   * 添加账号
   */
  get canAddAccount() {
    return this.permissions.includes('DOTOOR_ACCOUNT_SAVE');
  }

  /**
   * 编辑账号
   */
  get canUpdateAccount() {
    return this.permissions.includes('DOTOOR_ACCOUNT_UPDATE');
  }

  /**
   * 重置密码
   */
  get canRestAccount() {
    return this.permissions.includes('DOTOOR_ACCOUNT_RESET2');
  }

  /**
   * 显示操作栏
   */
  get showEditAccount() {
    return (
      this.canRestAccount || this.canUpdateAccount || this.canDeleteAccount
    );
  }

  /**
   * 状态/删除账号
   */
  get canDeleteAccount() {
    return this.permissions.includes('DOTOOR_ACCOUNT_STATUS');
  }

  /**
   * 能否踢出
   */
  get canKickout() {
    return this.permissions.includes('KICKOUT');
  }

  /**
   * 角色批量停用/启用/删除
   */
  get canBatchUpdateRole() {
    return this.permissions.includes('ROLE_BATCH_UPDATE');
  }

  /**
   * 角色配置菜单
   */
  get canMenuRole() {
    return this.permissions.includes('ROLE_EDIT_PURVIEW');
  }

  /**
   * 角色显示操作
   */
  get showEditRole() {
    return this.canMenuRole || this.canUpdateRole;
  }

  /**
   * 编辑/删除角色
   */
  get canUpdateRole() {
    return this.permissions.includes('ROLE_UPDATE');
  }

  /**
   * 添加角色
   */
  get canAddRole() {
    return this.permissions.includes('ROLE_ADD');
  }

  /**
   * 账户批量停用/启动/删除
   */
  get canBatchUpdateAccount() {
    return this.permissions.includes('ACCOUNT_BATCH_UPDATE');
  }
  /**
   * 是否可以编辑首页的图表布局
   */
  get canEditChartLayout() {
    // eslint-disable-next-line no-irregular-whitespace
    // TODO　等后端加CODE
    // return this.permissions.includes('ROLE_ADD');
    return !window.location.href.includes('/home');
  }
  /**
   * 文章管理-新增
   */
  get canArticleAdd() {
    return (
      this.permissions.includes('ARTICLE_MANAGE_ADD') ||
      env.smartFollowUp ||
      env.articleManage
    );
  }
  /**
   * 文章管理-修改
   */
  get canArticleModify() {
    return (
      this.permissions.includes('ARTICLE_MANAGE_MODIFY') ||
      env.smartFollowUp ||
      env.articleManage
    );
  }
  /**
   * 文章管理-批量删除
   */
  get canArticleDelete() {
    return (
      this.permissions.includes('ARTICLE_MANAGE_BATCH_DELETE') ||
      env.smartFollowUp ||
      env.articleManage
    );
  }
  /**
   * 文章管理-私密
   */
  get canArticleOffline() {
    return (
      this.permissions.includes('ARTICLE_MANAGE_OFFLINE') ||
      env.smartFollowUp ||
      env.articleManage
    );
  }
  /**
   * 文章类型管理-新增
   */
  get canArticleTypeAdd() {
    return (
      this.permissions.includes('ARTICLE_TYPE_MANAGE_ADD') ||
      env.smartFollowUp ||
      env.articleManage
    );
  }
  /**
   * 文章类型管理-修改
   */
  get canArticleTypeModify() {
    return (
      this.permissions.includes('ARTICLE_TYPE_MANAGE_MODIFY') ||
      env.smartFollowUp ||
      env.articleManage
    );
  }
  /**
   * 文章类型管理-删除
   */
  get canArticleTypeDelete() {
    return (
      this.permissions.includes('ARTICLE_TYPE_MANAGE_DELETE') ||
      env.smartFollowUp ||
      env.articleManage
    );
  }
  /**
   * 员工管理-新增-删除-编辑
   */
  get TransferTheOperationOfTheIntroducer() {
    return this.permissions.includes('INTRODUCER_EDIT');
  }
  /**
   * 床位预约-接收/拒绝
   */
  get BedReservationOperation() {
    return this.permissions.includes('BED_REG_MANAGE_EDIT');
  }
  /**
   * 创建排班
   */
  get creatScheduleShift() {
    return this.permissions.includes('DOCTOR_SCHEDULE_MANAGE_CREATE');
  }
  /**
   * 发布排班
   */
  get publishScheduleShift() {
    return this.permissions.includes('DOCTOR_SCHEDULE_MANAGE_PUBLISH');
  }
  /**
   * 复用排班
   */
  get copyScheduleShift() {
    return this.permissions.includes('DOCTOR_SCHEDULE_MANAGE_COPY');
  }
  /**
   * 删除排班
   */
  get deleteScheduleShift() {
    return this.permissions.includes('DOCTOR_SCHEDULE_MANAGE_DELETE');
  }
  get hospitalizationAppointmentCheck() {
    return this.permissions.includes('IN_PATIENT_REG_SELF_DETAIL');
  }
  get hospitalizationAppointmentCancel() {
    return this.permissions.includes('IN_PATIENT_REG_SELF_CANCEL');
  }
  get hospitalizationAppointmentSuccess() {
    return this.permissions.includes('IN_PATIENT_REG_SELF_AGREE');
  }
  /**
   * 添加病历模板
   */
  get addMedicalRecordTemplate() {
    return this.permissions.includes('MEDICAL_RECORD_CONTENT_ADD');
  }
  /**
   * 修改病历模板
   */
  get updateMedicalRecordTemplate() {
    return this.permissions.includes('MEDICAL_RECORD_CONTENT_MODIFY');
  }
  /**
   * 删除病历模板
   */
  get delMedicalRecordTemplate() {
    return this.permissions.includes('MEDICAL_RECORD_CONTENT_DELETE');
  }
  /**
   * 处理vip预约
   */
  get canEditVipReg() {
    return this.permissions.includes('VIP_REG_MANAGE_EDIT');
  }
  /**
   * his参数查看
   */
  get canLookHisParameters() {
    return this.permissions.includes('ORDER_HIS_PARAM_DETAIL');
  }

  /**
   * 挂号科室管理 添加
   */
  get deptConfigAdd() {
    return this.permissions.includes('WISDOW_FRONT_DEPT_ADD');
  }

  /**
   * 挂号科室管理 编辑
   */
  get deptConfigEdit() {
    return this.permissions.includes('WISDOW_FRONT_DEPT_UPDATE');
  }

  /**
   * 挂号科室管理 删除
   */
  get deptConfigDelete() {
    return this.permissions.includes('WISDOW_FRONT_DEPT_DELETE');
  }
}

export default new Permission();
