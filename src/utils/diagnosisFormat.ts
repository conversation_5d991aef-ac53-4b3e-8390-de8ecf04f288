type DiagnosisObj = {
  name: string;
  code: string;
};

/** 中医诊断从对象数组格式为字符串数组*/
export const formatCnList = (diagnosis?: DiagnosisObj[]): string[] => {
  if (!diagnosis || !Array.isArray(diagnosis)) {
    return [];
  }
  return diagnosis.filter(Boolean).map(i => JSON.stringify(i));
};

/** 治法名称、中医辩证从对象转为字符串 */
export const formatCnObj = (diagnosis?: DiagnosisObj | undefined): string => {
  if (!diagnosis) {
    return '';
  }
  return JSON.stringify(diagnosis);
};

/** 中医诊断从字符串数组改为对象数组 */
export const parseCnList = (diagnosis?: string[]): DiagnosisObj[] => {
  if (!diagnosis || !Array.isArray(diagnosis)) {
    return [];
  }
  return diagnosis
    .map(i => {
      try {
        return JSON.parse(i);
      } catch (err) {
        console.log('err', err);
        return undefined;
      }
    })
    .filter(Boolean);
};

/** 治法名称、中医辩证从字符串转为对象 */
export const parseCnObj = (diagnosis?: string): DiagnosisObj | undefined => {
  if (!diagnosis) {
    return;
  }
  try {
    return JSON.parse(diagnosis);
  } catch (err) {
    console.log('err', err);
    return;
  }
};

export const cnListToString = (diagnosis?: DiagnosisObj[]): string => {
  if (!diagnosis || !Array.isArray(diagnosis)) {
    return '';
  }
  return diagnosis.map((i, index) => `${index + 1}、${i.name}`).join('，');
};
