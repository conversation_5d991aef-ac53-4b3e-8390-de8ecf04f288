/**
 * 下载二进制文件工具函数
 * @param data - 二进制数据
 * @param filename - 下载的文件名
 * @param mimeType - 文件的MIME类型（可选，默认为'application/octet-stream'）
 */
const downloadBinaryFile = (
  data: Blob | BlobPart,
  filename: string,
  mimeType = 'application/octet-stream'
) => {
  const blob = new Blob([data], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

export default downloadBinaryFile;
