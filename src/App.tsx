import BasicLayout from '@layouts/basicLayout';
import Login from '@src/pages/login';
import storage from '@utils/storage';
import routes from '@configs/routes';
import App, { request } from 'parsec-admin';
import 'moment/locale/zh-cn';
import { ConfigProvider, Modal } from 'antd';
import { HisConfigProvider, useConfig } from './store/hisConfig';
import zhCN from 'antd/es/locale/zh_CN';
import moment from 'moment';
import useApi from '@apiHooks';
import RoleIdsStore from './store/RoleIdsStore';
import { createHashHistory } from 'history';
import env from './configs/env';
import { StyleSheetManager } from 'styled-components';
import { getApiVersion } from './utils/common';
import { useEffect } from 'react';
import OrganizationListStore from './store/organizationListStore';
import ConfigStore from '@src/store/ConfigStore';
export const history = createHashHistory();

request.interceptors.request.use(config => {
  config.headers = {
    ...config.headers,
    microAppName: 'standard-manage',
    login_access_token: storage.get('token')
  };

  const hisId = env.hisId;

  config.params = {
    ...config.params,
    hisId: hisId
  };

  // 测试环境需要增加api的版本号
  const version = getApiVersion();
  if (version) {
    config.headers['ih-version'] = version;
  }
  if (
    config.url?.includes('/admin/survey') ||
    config.url?.includes('/admin/questions')
  ) {
    if (config.url?.includes('/admin/survey')) {
      config.url = config.url?.replace('/admin/survey', '/mch/survey');
    }
    if (config.url?.includes('/admin/questions')) {
      config.url = config.url?.replace('/admin/questions', '/mch/questions');
    }
    const hisName = sessionStorage.getItem('hisName');
    const account = sessionStorage.getItem('account');

    const doctor = storage.get('doctor' as any)
      ? JSON.parse(storage.get('doctor' as any) as any)
      : {};

    config.headers = {
      ...config.headers,
      hospitalId: hisId,
      // eslint-disable-next-line no-useless-computed-key
      creatorId: doctor?.doctorId || storage.get('userInfo')?.id || 1,
      // header 不能传中文，要转码
      hospitalName: encodeURI(hisName + ''),
      creator: encodeURI(
        doctor?.name || storage.get('userInfo')?.name || account + ''
      )
    };
    if (config.data) {
      config.data.hospitalId = hisId;
      // config.data.fromSystem = 'nt_hospital';
      config.data.fromSystem = hisId;
      config.data.hospitalOfficeId = 181401;
      config.data.hospitalOfficeName = '儿保科';
    }
  }
  // 正则替换{XXXX}内容
  if (/{.*}/.test(config.url || '')) {
    const [envVar] = config.url?.match(/{.*}/) || [];
    if (envVar) {
      config.url = config.url?.replace(
        envVar,
        process.env[envVar.replace(/({)|(})/g, '')] || ''
      );
    }
  }
  const apiFirstPath = env.isDoctor ? 'doctor' : 'mch';
  if (env.isDoctor) {
    config.url = config.url?.replace('/mch/', '/doctor/');
  }
  try {
    if (config.method?.toLowerCase() === 'get' && config.params) {
      Object.keys(config.params).forEach(key => {
        if (moment.isMoment(config.params[key])) {
          config.params[key] = config.url?.includes(
            `/${apiFirstPath}/transform`
          )
            ? moment(config.params[key]).toISOString()
            : moment(config.params[key]).format('YYYY-MM-DD HH:mm:ss');
        }
      });
    }
  } catch (e) {
    console.log(e);
  }
  if (
    config.url?.includes(`/${apiFirstPath}/transform`) &&
    config.data &&
    config.method?.toLowerCase() === 'post'
  ) {
    Object.keys(config.data).forEach(key => {
      if (moment.isMoment(config.data[key])) {
        config.data[key] = moment.isMoment(config.data[key])
          ? moment(config.data[key]).toISOString()
          : config.data[key];
      }
    });
    return config;
  }
  if (
    (config.method?.toLowerCase() === 'post' &&
      config.data &&
      !config.headers['Content-Type'] &&
      !config.url?.includes(`/${apiFirstPath}/survey`) &&
      !config.url?.includes(`/${apiFirstPath}/cooperate`) &&
      !config.url?.includes(`/${apiFirstPath}/follow`)) ||
    config.headers['Content-Type'] === 'formData'
  ) {
    const formData = new FormData();
    Object.keys(config.data).forEach(key => {
      if (moment.isMoment(config.data[key])) {
        console.log(config.data[key]);
        config.data[key] = config.data[key].format('YYYY-MM-DD HH:mm:ss');
      }

      const dataItem = config.data[key];
      if (dataItem === undefined || dataItem === null || dataItem === '') {
        return;
      }
      if (key === 'id' && Array.isArray(dataItem)) {
        for (let i = 0; i < dataItem.length; i++) {
          formData.append('id', dataItem[i]);
        }
      } else {
        formData.append(key, dataItem);
      }
    });
    config.data = formData;
    config.headers['Content-Type'] = 'application/x-www-form-urlencoded';
  }
  return config;
});

let lastErrorTimestamp = 0;

request.interceptors.response.use(response => {
  const { data: { code = 0, msg = '网络请求失败' } = {} } = response || {};
  if (code !== 0) {
    const currentTimestamp = Date.now();
    if (code === 999 || code === 998) {
      history.replace('/login');
      sessionStorage.clear();
    }
    if (currentTimestamp > lastErrorTimestamp) {
      lastErrorTimestamp = currentTimestamp;
      Modal.warning({
        content: msg
      });
    }
    return Promise.reject(response);
  }
  const apiFirstPath = env.isDoctor ? 'doctor' : 'mch';

  if (response?.config?.url?.includes(`/api/${apiFirstPath}/survey`)) {
    const hisId = env.hisId;
    response.data = response.data?.data || response.data;
    const weappHost = 'https://tih.cqkqinfo.com/views/ydwj';

    if (weappHost && response?.config?.url === `/api/${apiFirstPath}/survey`) {
      response.data.list?.forEach((item: any) => {
        if (!item.publishUrl) {
          return;
        }
        item.weappUrl = `${weappHost}?${
          item.publishUrl.split('?')[1]
        }&hisId=${hisId}`;
      });
    }
  }
  return response;
});

const Main = () => {
  const { roleIds } = RoleIdsStore.useContainer();
  const { uploadApi } = useConfig();

  useEffect(() => {
    env.hisId === '40030' && (window.document.title = '智慧医院');
  }, [roleIds]);

  return (
    <ConfigProvider locale={zhCN}>
      <App
        apiHost={path => env.apiHost || ''}
        basename={process.env.PUBLIC_URL}
        storage={storage}
        name={env.hisId === '40030' ? '智慧医院管理后台' : '互联网医院监管端'}
        routes={routes}
        aloneRoutes={[
          {
            path: '/login',
            component: Login,
            exact: true
          }
        ]}
        uploadFn={uploadApi}
        history={history}
        basicLayout={<BasicLayout />}
        permissions={({ permissionsId }) => {
          return (
            process.env.NODE_ENV !== 'production' ||
            // window.location.href.includes('this') ||
            !permissionsId ||
            roleIds.includes(permissionsId as string | number) ||
            env.isMicroApp
          );
        }}
      />
    </ConfigProvider>
  );
};

export default () => (
  <HisConfigProvider>
    <RoleIdsStore.Provider>
      <ConfigStore.Provider>
        <OrganizationListStore.Provider>
          <StyleSheetManager disableCSSOMInjection>
            <Main />
          </StyleSheetManager>
        </OrganizationListStore.Provider>
      </ConfigStore.Provider>
    </RoleIdsStore.Provider>
  </HisConfigProvider>
);
