import './public-path';
import 'react-app-polyfill/ie9';
import 'react-app-polyfill/stable';

import React from 'react';
import App from './App';
import * as serviceWorker from './serviceWorker';
import './index.less';
import './index.css';
import { registerMount, render } from 'parsec-admin/lib/microAppConfig';
import env from './configs/env';

export const mount = props => {
  env.isDoctor = props.isDoctor;
  env.isMicroApp = true;
  env.hisId = props.hisId;
  env.smartFollowUp = props.smartFollowUp;
  env.articleManage = props.articleManage;
  env.healthLearn = props.healthLearn;
  return registerMount(props, <App />);
};
export { unmount, update, bootstrap } from 'parsec-admin/lib/microAppConfig';

render(<App />);

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: http://bit.ly/CRA-PWA
serviceWorker.unregister();

if ((module as any).hot) {
  (module as any).hot.accept();
}
