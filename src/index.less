@import '~parsec-admin/lib/layouts/kqBasicLayout/theme.less';

#root {
  margin: 0;
  padding: 0;
  height: 100%;
  min-height: 100%;
}
.ant-select-selection-item {
  .ant-btn {
    display: none;
  }
}
.ant-form {
  .ant-form-item {
    .ant-form-item-row {
      flex: 1;
      .ant-form-item-control {
        width: inherit;
      }
    }
  }
}

.parsecLibs-row.parsecLibs-form-item-row {
  flex: 1;
}

.statisticsTable {
  .ant-table thead > tr > th {
    background: #fafafa !important;
  }
}
.ant-layout-sider-children {
  > .ant-menu {
    height: calc(~'100vh - 80px');
    overflow-y: auto;
  }
}
main {
  height: calc(~'100vh - 80px');
  overflow: auto;
}
